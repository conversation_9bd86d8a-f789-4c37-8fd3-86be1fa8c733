package com.facishare.rest.channel.admin.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.H5LoginSupportTypeEnum;
import com.facishare.enterprise.common.constant.I18nResources;
import com.facishare.enterprise.common.constant.LoginSupportTypeEnum;
import com.facishare.enterprise.common.constant.WxSmallProgramLoginSupportTypeEnum;
import com.facishare.enterprise.common.enums.LoginTemplateTypeEnum;
import com.facishare.enterprise.common.model.LoginRegisterConfigVO;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.*;
import com.facishare.er.api.model.RegisterERAccountActiveConfigData;
import com.facishare.linkapp.api.arg.BatchUpdateUsageStatusArg;
import com.facishare.linkapp.api.arg.ListByTemplateIdsArg;
import com.facishare.linkapp.api.arg.ListByTenantIdAndLinkAppIdArg;
import com.facishare.linkapp.api.result.LinkAppChannelLoginTemplateAssociationVo;
import com.facishare.linkapp.api.result.LinkAppData;
import com.facishare.linkapp.api.service.HttpLinkAppService;
import com.facishare.linkapp.api.service.LinkAppChannelLoginTemplateAssociationService;
import com.facishare.rest.channel.ServiceContext;
import com.facishare.rest.channel.common.ChannelException;
import com.facishare.rest.channel.dao.pg.ChannelH5LoginTemplateDao;
import com.facishare.rest.channel.dao.pg.ChannelLoginTemplateDao;
import com.facishare.rest.channel.model.args.CreateH5ChannelLoginTemplateArg;
import com.facishare.rest.channel.model.args.ListH5ChannelLoginTemplateArg;
import com.facishare.rest.channel.model.args.ListLoginPageTemplateArg;
import com.facishare.rest.channel.model.args.UpdateH5ChannelLoginTemplateUsageStatusArg;
import com.facishare.rest.channel.model.data.SlideBannerShowData;
import com.facishare.rest.channel.model.entity.pg.ChannelDomainIndexEntity;
import com.facishare.rest.channel.model.entity.pg.ChannelH5LoginTemplateEntity;
import com.facishare.rest.channel.model.entity.pg.ChannelLoginTemplateEntity;
import com.facishare.rest.channel.model.enums.*;
import com.facishare.rest.channel.model.result.*;
import com.facishare.rest.channel.remote.ChannelDomainObjService;
import com.facishare.rest.channel.remote.FileService;
import com.facishare.rest.channel.remote.data.ChannelDomainObj;
import com.facishare.rest.channel.remote.entity.EnterpriseMetaDataEntity;
import com.facishare.rest.channel.service.ERAccountActiveConfigService;
import com.facishare.rest.channel.service.EnterpriseMetaDataService;
import com.facishare.rest.channel.service.LoginRegisterConfigService;
import com.facishare.rest.channel.util.CDNImagePathUtil;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 渠道自定义登录页面模板服务
 *
 * <AUTHOR>
 * @since 2022/09/20
 */
@Slf4j
@Service
public class ChannelLoginTemplateService extends BaseChannelService {
    @Autowired
    private ChannelLoginTemplateDao channelLoginTemplateDao;
    @Autowired
    private ChannelH5LoginTemplateDao channelH5LoginTemplateDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EnterpriseMetaDataService enterpriseMetaDataService;
    @Autowired
    private FileService fileService;
    @Autowired
    private ChannelCustomDomainService channelCustomDomainService;
    @Autowired
    private ChannelDomainObjService channelDomainObjService;
    @Qualifier("jedisSupport")
    @Autowired
    private MergeJedisCmd jedisCmd;
    @Autowired
    private ERAccountActiveConfigService erAccountActiveConfigService;
    @Autowired
    private LinkAppChannelLoginTemplateAssociationService linkAppChannelLoginTemplateAssociationService;
    @Autowired
    private LoginRegisterConfigService loginRegisterConfigService;
    @Autowired
    private HttpLinkAppService httpLinkAppService;

    public ChannelLoginTemplatePageResult listChannelLoginTemplatePage(String upstreamTenantId, String upstreamEa, ListLoginPageTemplateArg arg) {
        ChannelLoginTemplatePageResult result = new ChannelLoginTemplatePageResult();
        result.setDataList(new ArrayList<>());
        result.setTotalCount(0L);
        result.setPageNumber(arg.getPage());
        result.setPageSize(arg.getLimit());
        List<String> templateIds = null;
        // 匹配应用关联的登录模板
        if (StringUtils.isNotEmpty(arg.getLinkAppId())) {
            ListByTenantIdAndLinkAppIdArg listByTemplateIdsArg = new ListByTenantIdAndLinkAppIdArg();
            listByTemplateIdsArg.setTenantId(upstreamTenantId);
            listByTemplateIdsArg.setLinkAppId(arg.getLinkAppId());
            listByTemplateIdsArg.setClientType(ChannelLoginTemplateType.WEB_CLIENT.getType());
            Result<List<LinkAppChannelLoginTemplateAssociationVo>> associationsResult = linkAppChannelLoginTemplateAssociationService.listByTenantIdAndLinkAppId(listByTemplateIdsArg);
            if (!associationsResult.isSuccess()) {
                throw new ChannelException(associationsResult.getErrCode(), associationsResult.getErrDescription());
            }
            if (CollectionUtils.isEmpty(associationsResult.getData())) {
                return result;
            }
            templateIds = associationsResult.getData().stream().map(LinkAppChannelLoginTemplateAssociationVo::getChannelLoginTemplateId).collect(Collectors.toList());
        }

        List<ChannelLoginTemplateEntity> entities = channelLoginTemplateDao.listByConditionWithPage(upstreamTenantId + "", arg.getSearchText(), templateIds, 0, 1000);
        if (CollectionUtils.isEmpty(entities)) {
            return result;
        }

        // 登录注册配置选项
        /*List<ChannelLoginTemplateResult.LoginRegisterConfigOptionVo> loginRegisterConfigOptionVos = Lists.newArrayList();
        Result<List<LoginRegisterConfigVO>> loginRegisterConfigResult = loginRegisterConfigService.listLoginRegisterConfigsSimple(upstreamTenantId);
        List<LoginRegisterConfigVO> loginRegisterConfigVOS = loginRegisterConfigResult.getData();
        if (!CollectionUtils.isEmpty(loginRegisterConfigVOS)) {
            loginRegisterConfigOptionVos = BeanUtil.deepCopyList(loginRegisterConfigVOS, ChannelLoginTemplateResult.LoginRegisterConfigOptionVo.class);
        }*/

        // 分页
        if (entities.size() > arg.getLimit()) {
            int toIndex = arg.getOffset() + arg.getLimit();
            if (toIndex > entities.size()) {
                entities = entities.subList(arg.getOffset(), entities.size());
            } else {
                entities = entities.subList(arg.getOffset(), toIndex);
            }
        }
        // 登录模板ID集合
        templateIds = entities.stream().map(ChannelLoginTemplateEntity::getId).distinct().collect(Collectors.toList());
        // 查询模板关联的linkApp信息
        Map<String, Map<String, String>> templateLinkAppMaps = loginTemplateAssociationLinkApps(upstreamTenantId, templateIds, ChannelLoginTemplateType.WEB_CLIENT.getType());
        EnterpriseMetaDataEntity enterpriseMetaData = enterpriseMetaDataService.getEnterpriseMetaData(upstreamEa);
        //Result<RegisterERAccountActiveConfigData> erAccountActiveConfigResult = erAccountActiveConfigService.getRegisterERAccountActiveConfigs(upstreamEa);
        Integer total = entities.size();
        Collection<Integer> operatorIds = new ArrayList<>();
        List<ChannelLoginTemplateResult> loginTemplateList = BeanUtil.deepCopyList(entities, ChannelLoginTemplateResult.class);
        //List<ChannelLoginTemplateResult.LoginRegisterConfigOptionVo> finalLoginRegisterConfigOptionVos = loginRegisterConfigOptionVos;
        loginTemplateList.forEach(item -> {
            item.setEnterpriseInfoName(enterpriseMetaData.getEnterpriseName());
            item.setEnterpriseInfoLogoPath(CDNImagePathUtil.toCDNPath(enterpriseMetaData.getLogoPath(), upstreamEa));
            if (templateLinkAppMaps.containsKey(item.getId())) {
                Map<String, String> linkAppMap = templateLinkAppMaps.get(item.getId());
                item.setLinkAppId(linkAppMap.get("id"));
                item.setLinkAppName(linkAppMap.get("name"));
            } else {
                item.setLinkAppId("all");
                item.setLinkAppName(I18nResources.ALL_LINK_APP.value());
            }
            if (item.getSlideBannerShows().size() > 0) {
                item.getSlideBannerShows().forEach(slideData -> {
                    if (StringUtils.isNotEmpty(slideData.getImagePath())) {
                        slideData.setImagePath(CDNImagePathUtil.toCDNPath(slideData.getImagePath(), upstreamEa));
                    }
                });
            }
            if (StringUtils.isNotEmpty(item.getBackgroundPath())) {
                item.setBackgroundPath(CDNImagePathUtil.toCDNPath(item.getBackgroundPath(), upstreamEa));
            }
            if (StringUtils.isNotEmpty(item.getLogoPath())) {
                item.setLogoPath(CDNImagePathUtil.toCDNPath(item.getLogoPath(), upstreamEa));
            }
            /*if (erAccountActiveConfigResult.isSuccess() && erAccountActiveConfigResult.getData() != null) {
                item.setIsShowRegisterConfig(BooleanUtils.toBoolean(erAccountActiveConfigResult.getData().isOpenRegiest()));
                item.setRegisterActiveTypeOptions(erAccountActiveConfigService.toRegisterActiveTypeOptions(erAccountActiveConfigResult.getData()));
            }*/
            if (enterpriseMetaData.getVendorLoginProtocolConfig() != null) {
                item.setVendorLoginProtocolSwitch(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolSwitch());
                item.setVendorLoginProtocolTitle(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolTitle());
                item.setVendorLoginProtocolContent(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolContent());
                item.setVendorLoginProtocolContentJson(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolContentJson());
            }
            if (StringUtils.isNotEmpty(item.getCreatedBy())) {
                operatorIds.add(Integer.valueOf(item.getCreatedBy()));
            }
            if (StringUtils.isNotEmpty(item.getLastModifiedBy())) {
                operatorIds.add(Integer.valueOf(item.getLastModifiedBy()));
            }
            //item.setLoginRegisterConfigOptions(finalLoginRegisterConfigOptionVos);
            item.getCreateTimeStr();
            item.getLastModifiedTimeStr();
        });
        Map<String, String> operators = getOperatorByIds(operatorIds, Integer.valueOf(upstreamTenantId));
        loginTemplateList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getCreatedBy())) {
                String createdByName = operators.containsKey(item.getCreatedBy()) ? operators.get(item.getCreatedBy()) : "";
                item.setCreatedByStr(createdByName);
            } else {
                item.setCreatedByStr("");
            }
            if (StringUtils.isNotEmpty(item.getLastModifiedBy())) {
                String modifiedByName = operators.containsKey(item.getLastModifiedBy()) ? operators.get(item.getLastModifiedBy()) : "";
                item.setLastModifiedByStr(modifiedByName);
            } else {
                item.setLastModifiedByStr("");
            }
            if (StringUtils.isNotEmpty(item.getThemeColor())) {
                item.setThemeColor(item.getThemeColor().trim());
            }
        });
        result.setDataList(loginTemplateList);
        result.setTotalCount((long) total);
        return result;
    }

    public List<ChannelLoginTemplateResult> listChannelLoginTemplate(String upstreamTenantId, String upstreamEa) {
        List<ChannelLoginTemplateEntity> data = channelLoginTemplateDao.listChannelLoginTemplate(upstreamTenantId + "");
        List<ChannelLoginTemplateResult> result = BeanUtil.deepCopyList(data, ChannelLoginTemplateResult.class);
        if (data != null && data.size() > 0) {
            result.forEach(item -> {
                if (item.getSlideBannerShows() != null) {
                    item.getSlideBannerShows().forEach(banner -> {
                        if (StringUtils.isNotEmpty(banner.getImagePath())) {
                            banner.setImagePath(CDNImagePathUtil.toCDNPath(banner.getImagePath(), upstreamEa));
                        }
                    });
                }
                if (StringUtils.isNotEmpty(item.getBackgroundPath())) {
                    item.setBackgroundPath(CDNImagePathUtil.toCDNPath(item.getBackgroundPath(), upstreamEa));
                }
                if (StringUtils.isNotEmpty(item.getLogoPath())) {
                    item.setLogoPath(CDNImagePathUtil.toCDNPath(item.getLogoPath(), upstreamEa));
                }
            });
        }
        return result;
    }

    /**
     * 查询登录模板关联的linkApp信息
     *
     * @param tenantId         上游企业Ei
     * @param loginTemplateIds 登录模板id集合
     * @param clientType       登录模板类型
     * @return Map<String, Map < String, String>> key:登录模板id value:Map<String, String> key:linkAppId value:linkAppName
     */
    public Map<String, Map<String, String>> loginTemplateAssociationLinkApps(String tenantId, List<String> loginTemplateIds, Integer clientType) {
        Map<String, Map<String, String>> associationLinkAppMaps = new HashMap<>();
        if (tenantId == null || loginTemplateIds == null || loginTemplateIds.isEmpty()) {
            return associationLinkAppMaps;
        }
        ListByTemplateIdsArg queryArg = new ListByTemplateIdsArg();
        queryArg.setTenantId(tenantId);
        queryArg.setTemplateIds(loginTemplateIds);
        queryArg.setClientType(clientType);
        Result<List<LinkAppChannelLoginTemplateAssociationVo>> result = linkAppChannelLoginTemplateAssociationService.listByTemplateIds(queryArg);
        if (result.isSuccess()) {
            Map<String, String> loginTemplateIdMaps = result.getData().stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getLinkAppId()))
                    .collect(Collectors.toMap(x -> x.getChannelLoginTemplateId(), y -> y.getLinkAppId()));
            if (loginTemplateIdMaps.isEmpty()) {
                return associationLinkAppMaps;
            }
            List<String> linkAppIds = loginTemplateIdMaps.values().stream().filter(x -> !"all".equals(x)).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(linkAppIds)) {
                return associationLinkAppMaps;
            }
            Result<List<LinkAppData>> listLinkAppResult = httpLinkAppService.listLinkApp(linkAppIds);
            if (listLinkAppResult.isSuccess() && listLinkAppResult.getData() != null) {
                Map<String, String> linkAppNameMap = listLinkAppResult.getData().stream()
                        .collect(Collectors.toMap(LinkAppData::getId, x -> StringUtils.isNotEmpty(x.getNameI18nKey()) ? I18nUtil.get(x.getNameI18nKey(), x.getName()) : x.getName()));
                loginTemplateIdMaps.forEach((loginTemplateId, linkAppId) -> {
                    Map<String, String> associationLinkAppMap = Maps.newHashMap();
                    if (linkAppNameMap.containsKey(linkAppId)) {
                        String linkAppName = linkAppNameMap.get(linkAppId);
                        associationLinkAppMap.put("id", linkAppId);
                        associationLinkAppMap.put("name", linkAppName);
                    } else {
                        associationLinkAppMap.put("id", "all");
                        associationLinkAppMap.put("name", I18nResources.ALL_LINK_APP.value());
                    }
                    associationLinkAppMaps.put(loginTemplateId, associationLinkAppMap);
                });
                return associationLinkAppMaps;
            }
        }
        return Maps.newHashMap();
    }

    public ChannelLoginTemplateSimpleDataResult listChannelLoginTemplateSimpleData(String upstreamTenantId, Integer templateType) {
        ChannelLoginTemplateSimpleDataResult result = new ChannelLoginTemplateSimpleDataResult();
        if (templateType == 3 || templateType == 1) {
            List<ChannelLoginTemplateEntity> webLoginTemplateEntityList = channelLoginTemplateDao.listChannelLoginTemplate(upstreamTenantId + "");
            result.setWebLoginTemplateList(BeanUtil.deepCopyList(webLoginTemplateEntityList, ChannelLoginTemplateSimpleDataResult.LoginTemplateSimpleData.class));
        }
        if (templateType == 3 || templateType == 2) {
            List<ChannelH5LoginTemplateEntity> h5LoginTemplateEntityList = channelH5LoginTemplateDao.listChannelH5LoginTemplate(upstreamTenantId + "");
            result.setH5LoginTemplateList(BeanUtil.deepCopyList(h5LoginTemplateEntityList, ChannelLoginTemplateSimpleDataResult.LoginTemplateSimpleData.class));
        }
        return result;
    }

    public Boolean createOrUpdateChannelLoginTemplate(String tenantId, String linkAppId, ChannelLoginTemplateEntity entity) {
        String upstreamEa = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        convertTempFilePathToNPath(upstreamEa, entity);

        entity.setTenantId(tenantId);
        if (StringUtils.isNotEmpty(entity.getThemeColor())) {
            entity.setThemeColor(entity.getThemeColor().trim());
        }
        if (StringUtils.isBlank(entity.getCreatedBy())) {
            entity.setCreatedBy("-10000");
        }
        if (entity.getDefaultLoginType() == null && entity.getChannelLoginType() != null) {
            entity.setDefaultLoginType(entity.getChannelLoginType().get(0));
        }
        ChannelLoginTemplateEntity oldEntity = null;
        if (StringUtils.isNotEmpty(entity.getId())) {
            oldEntity = channelLoginTemplateDao.get(entity.getId());
        }
        if (oldEntity != null) {
            entity.setId(oldEntity.getId());
            entity.setLastModifiedBy(ServiceContext.getEmployeeId());
            entity.setLastModifiedTime(System.currentTimeMillis());
            channelLoginTemplateDao.updateWithNotNull(entity);
        } else {
            entity.init();
            entity.setId(IdUtil.generateId());
            entity.setCreatedBy(ServiceContext.getEmployeeId());
            entity.setLastModifiedBy(ServiceContext.getEmployeeId());
            int i = channelLoginTemplateDao.upsertById(entity);
            if (i > 0 && StringUtils.isNotEmpty(linkAppId)) {
                Result<LinkAppData> linkAppResult = httpLinkAppService.getLinkApp(linkAppId);
                if (linkAppResult.isSuccess() && linkAppResult.getData() != null) {
                    LinkAppChannelLoginTemplateAssociationVo associationVo = new LinkAppChannelLoginTemplateAssociationVo();
                    associationVo.setTenantId(tenantId);
                    associationVo.setLinkAppId(linkAppId);
                    associationVo.setChannelLoginTemplateId(entity.getId());
                    associationVo.setChannelLoginTemplateClientType(ChannelLoginTemplateType.WEB_CLIENT.getType());
                    linkAppChannelLoginTemplateAssociationService.create(associationVo);
                }
            }
        }
        return true;
    }

    private void convertTempFilePathToNPath(String upstreamEa, ChannelLoginTemplateEntity entity) {
        List<String> tempPaths = Lists.newArrayList();
        tempPaths.add(entity.getLogoPath());
        tempPaths.add(entity.getBackgroundPath());
        if (!CollectionUtils.isEmpty(entity.getSlideBannerShows())) {
            List<String> banners = entity.getSlideBannerShows().stream().map(SlideBannerShowData::getImagePath).collect(Collectors.toList());
            tempPaths.addAll(banners);
        }
        tempPaths = tempPaths.stream().filter(x -> x.startsWith(CDNImagePathUtil.prefix_image_TC) || x.startsWith(CDNImagePathUtil.prefix_image_TN)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tempPaths)) {
            return;
        }

        Map<String, String> nPathMap = fileService.saveImageFromTempFiles(upstreamEa, tempPaths);
        String nLogoPath = nPathMap.get(entity.getLogoPath());
        if (!StringUtils.isEmpty(nLogoPath)) {
            entity.setLogoPath(nLogoPath);
        }

        String nBackgroundPath = nPathMap.get(entity.getBackgroundPath());
        if (!StringUtils.isEmpty(nBackgroundPath)) {
            entity.setBackgroundPath(nBackgroundPath);
        }

        if (!CollectionUtils.isEmpty(entity.getSlideBannerShows())) {
            for (SlideBannerShowData banner : entity.getSlideBannerShows()) {
                String nPath = nPathMap.get(banner.getImagePath());
                if (!StringUtils.isEmpty(nPath)) {
                    banner.setImagePath(nPath);
                }
            }
        }
    }

    public Boolean deleteChannelLoginTemplateById(String id) {
        int count = channelLoginTemplateDao.deleteById(id);
        return count >= 0 ? true : false;
    }

    public ChannelLoginTemplateResult findChannelLoginTemplateById(String id) {
        ChannelLoginTemplateEntity entity = channelLoginTemplateDao.get(id);
        if (entity != null) {
            return BeanUtil.deepCopy(entity, ChannelLoginTemplateResult.class);
        }
        return null;
    }

    public void clearChannelLoginTemplateCacheAsync(String templateId) {
        CommonPoolUtil.execute(() -> {
            try {
                List<ChannelDomainIndexEntity> channelDomainIndexEntities = channelCustomDomainService.getChannelCustomDomainByLoginTemplateId(templateId);
                if (CollectionUtils.isEmpty(channelDomainIndexEntities)) {
                    return;
                }
                List<String> domainIds = channelDomainIndexEntities.stream().map(ChannelDomainIndexEntity::getDomainId).collect(Collectors.toList());
                clearChannelLoginTemplateCache(domainIds);
            } catch (Exception e) {
                log.warn("createOrUpdateChannelLoginTemplate -> clearChannelLoginTemplateCacheAsync error.", e);
            }
        });
    }

    public void clearChannelLoginTemplateCacheAsync(List<String> domainIds) {
        CommonPoolUtil.execute(() -> {
            try {
                clearChannelLoginTemplateCache(domainIds);
            } catch (Exception e) {
                log.warn("createOrUpdateChannelLoginTemplate -> clearChannelLoginTemplateCacheAsync error.", e);
            }
        });
    }

    private void clearChannelLoginTemplateCache(List<String> domainIds) {
        List<ObjectData> objectDatas = channelDomainObjService.listChannelDomainsByIds(domainIds);
        List<ChannelDomainObj> channelDomainObjs = ChannelDomainObj.newInstances(ObjectData.convert2MapList(objectDatas));
        if (CollectionUtils.isEmpty(channelDomainObjs)) {
            return;
        }
        for (ChannelDomainObj channelDomainObj : channelDomainObjs) {
            jedisCmd.del(String.format("channel_login_template_config_%s", channelDomainObj.getDomainName()));
            jedisCmd.del(String.format("channel_login_template_%s", channelDomainObj.getDomainName()));
        }
    }

    public H5ChannelLoginTemplatePageResult listH5ChannelLoginTemplate(String tenantId, ListH5ChannelLoginTemplateArg arg) {
        verification(arg);
        H5ChannelLoginTemplatePageResult result = new H5ChannelLoginTemplatePageResult();
        result.setDataList(new ArrayList<>());
        result.setTotalCount(0L);
        result.setPageNumber(arg.getPageNumber());
        result.setPageSize(arg.getPageSize());

        ListByTenantIdAndLinkAppIdArg listByTemplateIdsArg = new ListByTenantIdAndLinkAppIdArg();
        listByTemplateIdsArg.setTenantId(tenantId);
        listByTemplateIdsArg.setLinkAppId(arg.getLinkAppId());
        listByTemplateIdsArg.setClientType(ChannelLoginTemplateType.APP_CLIENT.getType());
        listByTemplateIdsArg.setUsageStatus(arg.getUsageStatus());
        Result<List<LinkAppChannelLoginTemplateAssociationVo>> associationsResult = linkAppChannelLoginTemplateAssociationService.listByTenantIdAndLinkAppId(listByTemplateIdsArg);
        if (!associationsResult.isSuccess()) {
            throw new ChannelException(associationsResult.getErrCode(), associationsResult.getErrDescription());
        }
        if (CollectionUtils.isEmpty(associationsResult.getData())) {
            return result;
        }

        int limit = arg.getPageSize();
        int offset = arg.getPageSize() * (arg.getPageNumber() - 1);
        List<String> templateIds = associationsResult.getData().stream().map(LinkAppChannelLoginTemplateAssociationVo::getChannelLoginTemplateId).distinct().collect(Collectors.toList());
        List<ChannelH5LoginTemplateEntity> entities = channelH5LoginTemplateDao.listByConditionWithPage(tenantId, arg.getSearchText(), ChannelTemplateDeleteStatusEnum.NORMAL.getCode(), arg.getStatus(), templateIds, offset, limit);
        if (CollectionUtils.isEmpty(entities)) {
            return result;
        }

        Set<String> searchTemplateIdSet = entities.stream().map(ChannelH5LoginTemplateEntity::getId).collect(Collectors.toSet());
        List<String> linkAppIdsWithoutAll = associationsResult.getData().stream().filter(x -> searchTemplateIdSet.contains(x.getChannelLoginTemplateId())).map(LinkAppChannelLoginTemplateAssociationVo::getLinkAppId).filter(x -> !"all".equals(x)).distinct().collect(Collectors.toList());
        Result<List<LinkAppData>> linkAppResult = httpLinkAppService.listLinkApp(linkAppIdsWithoutAll);
        if (!linkAppResult.isSuccess()) {
            throw new ChannelException(linkAppResult.getErrCode(), linkAppResult.getErrDescription());
        }
        Map<String, String> linkAppIdNameMap = Collections.EMPTY_MAP;
        if (CollectionUtils.isNotEmpty(linkAppResult.getData())) {
            linkAppIdNameMap = linkAppResult.getData().stream().collect(Collectors.toMap(LinkAppData::getId, LinkAppData::getName));
        }

        String upstreamEa = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        EnterpriseMetaDataEntity enterpriseMetaData = enterpriseMetaDataService.getEnterpriseMetaData(upstreamEa);

        List<Integer> lastModifiedIds = entities.stream().filter(x -> StringUtils.isNotEmpty(x.getLastModifiedBy())).map(x -> Integer.valueOf(x.getLastModifiedBy())).collect(Collectors.toList());
        Map<String, String> operatorIdNameMap = getOperatorByIds(lastModifiedIds, Integer.valueOf(tenantId));
        List<H5ChannelLoginTemplateResult> resultList = Lists.newArrayList();
        Map<String, LinkAppChannelLoginTemplateAssociationVo> id2AssociationVo = associationsResult.getData().stream().collect(Collectors.toMap(LinkAppChannelLoginTemplateAssociationVo::getChannelLoginTemplateId, Function.identity(), (it1, it2) -> it2));
        for (ChannelH5LoginTemplateEntity entity : entities) {
            LinkAppChannelLoginTemplateAssociationVo associationVo = id2AssociationVo.get(entity.getId());
            if (associationVo == null) {
                continue;
            }

            H5ChannelLoginTemplateResult templateResult = bulidH5ChannelLoginTemplateResult(entity, associationVo, upstreamEa, operatorIdNameMap,
                    linkAppIdNameMap, enterpriseMetaData);
            resultList.add(templateResult);
        }

        result.setDataList(resultList);
        result.setTotalCount(ObjectUtils.isEmpty(resultList) ? 0 : entities.get(0).getTotal());
        return result;
    }

    @NotNull
    private H5ChannelLoginTemplateResult bulidH5ChannelLoginTemplateResult(ChannelH5LoginTemplateEntity entity,
                                                                           LinkAppChannelLoginTemplateAssociationVo associationVo,
                                                                           String upstreamEa, Map<String, String> operatorIdNameMap,
                                                                           Map<String, String> linkAppIdNameMap,
                                                                           EnterpriseMetaDataEntity enterpriseMetaData) {
        H5ChannelLoginTemplateResult templateResult = new H5ChannelLoginTemplateResult();
        templateResult.setId(entity.getId());
        templateResult.setTenantId(entity.getTenantId());
        templateResult.setName(entity.getName());
        templateResult.setDeleteStatus(entity.getDeleteStatus());
        templateResult.setStatus(entity.getStatus());
        templateResult.setEnterpriseName(entity.getEnterpriseName());
        templateResult.setPageEnterpriseNameDisplayMode(entity.getEnterpriseNameDisplayMode());
        templateResult.setPageEnterpriseLogoNPath(entity.getEnterpriseLogoNPath());
        templateResult.setPageEnterpriseLogoUrl(CDNImagePathUtil.toCDNPath(entity.getEnterpriseLogoNPath(), upstreamEa));
        templateResult.setPageEnterpriseLogoDisplayMode(entity.getEnterpriseLogoDisplayMode());
        templateResult.setPageTitle(entity.getPageTitle());
        templateResult.setPageContent(entity.getPageContent());
        templateResult.setPageDefaultLanguage(entity.getPageDefaultLanguage());
        templateResult.setLoginSupportTypes(StringUtil.stringToListInt(entity.getLoginSupportTypes(), ","));
        templateResult.setH5LoginSupportTypes(StringUtil.stringToListInt(entity.getH5LoginSupportTypes(), ","));
        templateResult.setWxSmallProgramLoginSupportTypes(StringUtil.stringToListInt(entity.getWxSmallProgramLoginSupportTypes(), ","));
        templateResult.setLastModifiedTime(entity.getLastModifiedTime());
        templateResult.setCreatedBy(entity.getCreatedBy());
        templateResult.setCreateTime(entity.getCreateTime());
        templateResult.setDefaultLoginType(entity.getDefaultLoginType());
        templateResult.setSupportRegister(entity.getSupportRegister());
        templateResult.setRegisterHint(entity.getRegisterHint());
        templateResult.setRegisterActiveType(entity.getRegisterActiveType());
        templateResult.setLoginRegisterConfigId(entity.getLoginRegisterConfigId());
        if (operatorIdNameMap != null) {
            templateResult.setLastModifiedBy(operatorIdNameMap.get(entity.getLastModifiedBy()));
        }
        if (associationVo != null) {
            templateResult.setUsageStatus(associationVo.getUsageStatus());
            // 互联应用
            templateResult.setLinkAppId(associationVo.getLinkAppId());
            if ("all".equals(templateResult.getLinkAppId())) {
                templateResult.setLinkAppName(I18nResources.ALL_LINK_APP.value());
            } else if (linkAppIdNameMap != null) {
                String name = linkAppIdNameMap.get(templateResult.getLinkAppId());
                templateResult.setLinkAppName(name);
            }
        }
        if (enterpriseMetaData != null) {
            // 企业信息
            templateResult.setEnterpriseInfoName(enterpriseMetaData.getEnterpriseName());
            templateResult.setEnterpriseInfoLogoPath(enterpriseMetaData.getLogoPath());
            templateResult.setEnterpriseInfoLogoUrl(CDNImagePathUtil.toCDNPath(enterpriseMetaData.getLogoPath(), upstreamEa));

            // 厂商协议
            if (enterpriseMetaData.getVendorLoginProtocolConfig() != null) {
                templateResult.setVendorLoginProtocolSwitch(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolSwitch());
                templateResult.setVendorLoginProtocolTitle(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolTitle());
                templateResult.setVendorLoginProtocolContent(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolContent());
                templateResult.setVendorLoginProtocolContentJson(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolContentJson());
            }
        }
        List<Integer> h5LoginSupportTypes = templateResult.getH5LoginSupportTypes();
        if (!CollectionUtils.isEmpty(h5LoginSupportTypes)) {
            List<Option> options = h5LoginSupportTypes.stream().map(x -> {
                H5LoginSupportTypeEnum h5LoginSupportTypeEnum = H5LoginSupportTypeEnum.getByType(x);
                return new Option(h5LoginSupportTypeEnum.getType(), h5LoginSupportTypeEnum.getDesc());
            }).collect(Collectors.toList());
            templateResult.setH5LoginSupportTypeOptions(options);
        }

        List<Integer> loginSupportTypes = templateResult.getLoginSupportTypes();
        if (!CollectionUtils.isEmpty(loginSupportTypes)) {
            List<Option> options = loginSupportTypes.stream().map(x -> {
                LoginSupportTypeEnum loginSupportTypeEnum = LoginSupportTypeEnum.getByType(x);
                return new Option(loginSupportTypeEnum.getType(), loginSupportTypeEnum.getDesc());
            }).collect(Collectors.toList());
            templateResult.setLoginSupportTypeOptions(options);
        }

        List<Integer> wxSmallProgramLoginSupportTypes = templateResult.getWxSmallProgramLoginSupportTypes();
        if (!CollectionUtils.isEmpty(wxSmallProgramLoginSupportTypes)) {
            List<Option> options = wxSmallProgramLoginSupportTypes.stream().map(x -> {
                WxSmallProgramLoginSupportTypeEnum wxSmallProgramLoginSupportTypeEnum = WxSmallProgramLoginSupportTypeEnum.getByType(x);
                return new Option(wxSmallProgramLoginSupportTypeEnum.getType(), wxSmallProgramLoginSupportTypeEnum.getDesc());
            }).collect(Collectors.toList());
            templateResult.setWxSmallProgramLoginSupportTypeOptions(options);
        }

        if (StringUtils.isNotEmpty(templateResult.getLastModifiedBy())) {
            String modifiedByName = operatorIdNameMap.getOrDefault(templateResult.getLastModifiedBy(), "");
            templateResult.setLastModifiedByStr(modifiedByName);
        } else {
            templateResult.setLastModifiedByStr("");
        }
        templateResult.getLastModifiedTimeStr();
        return templateResult;
    }

    public H5ChannelLoginTemplateResult findH5LoginTemplateById(String upstreamEa, String id) {
        H5ChannelLoginTemplateResult loginTemplateResult = null;
        ChannelH5LoginTemplateEntity channelH5LoginTemplateEntity = channelH5LoginTemplateDao.get(id);
        if (channelH5LoginTemplateEntity != null) {
            EnterpriseMetaDataEntity enterpriseMetaData = enterpriseMetaDataService.getEnterpriseMetaData(upstreamEa);
            loginTemplateResult = bulidH5ChannelLoginTemplateResult(channelH5LoginTemplateEntity, null, upstreamEa,  null, null, enterpriseMetaData);
        }
        return loginTemplateResult;
    }

    private List<H5ChannelLoginTemplateResult.LoginRegisterConfigOptionVo> listLoginRegisterConfigOptions(String upstreamTenantId) {
        // 登录注册配置选项
        List<H5ChannelLoginTemplateResult.LoginRegisterConfigOptionVo> loginRegisterConfigOptionVos = Lists.newArrayList();
        Result<List<LoginRegisterConfigVO>> loginRegisterConfigResult = loginRegisterConfigService.listLoginRegisterConfigsSimple(upstreamTenantId);
        if (loginRegisterConfigResult.isSuccess()) {
            List<LoginRegisterConfigVO> loginRegisterConfigVOS = loginRegisterConfigResult.getData();
            if (!CollectionUtils.isEmpty(loginRegisterConfigVOS)) {
                loginRegisterConfigOptionVos = BeanUtil.deepCopyList(loginRegisterConfigVOS, H5ChannelLoginTemplateResult.LoginRegisterConfigOptionVo.class);
            }
        }
        return loginRegisterConfigOptionVos;
    }

    private void verification(ListH5ChannelLoginTemplateArg arg) {
        if (arg.getPageSize() <= 0 || arg.getPageNumber() <= 0) {
            throw new ChannelException(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.PARAMS_ERROR.getErrorMessage());
        }
    }

    public Integer createH5ChannelLoginTemplate(String tenantId, CreateH5ChannelLoginTemplateArg arg) {
        ChannelH5LoginTemplateEntity entity = BeanUtil.copy(ChannelH5LoginTemplateEntity.class, arg);
        entity.setId(IdUtil.generateId());
        entity.setTenantId(tenantId);
        entity.setEnterpriseLogoNPath(arg.getPageEnterpriseLogoNPath());
        entity.setEnterpriseLogoDisplayMode(arg.getPageEnterpriseLogoDisplayMode());
        entity.setEnterpriseNameDisplayMode(arg.getPageEnterpriseNameDisplayMode());
        entity.setStatus(ChannelLoginTemplateStatusEnum.STARTED.getType());
        entity.setDeleteStatus(ChannelTemplateDeleteStatusEnum.NORMAL.getCode());
        entity.setCreatedBy(ServiceContext.getEmployeeId());
        entity.setLastModifiedBy(ServiceContext.getEmployeeId());
        if (arg.getTemplateType() != null) {
            entity.setTemplateType(arg.getTemplateType());
        } else {
            entity.setTemplateType(LoginTemplateTypeEnum.NORMAL.getType());
        }
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setLastModifiedTime(now);
        if (!CollectionUtils.isEmpty(arg.getH5LoginSupportTypes())) {
            entity.setH5LoginSupportTypes(StringUtil.splicingIntegersByComma(arg.getH5LoginSupportTypes()));
        }
        if (!CollectionUtils.isEmpty(arg.getLoginSupportTypes())) {
            entity.setLoginSupportTypes(StringUtil.splicingIntegersByComma(arg.getLoginSupportTypes()));
        }
        if (!CollectionUtils.isEmpty(arg.getWxSmallProgramLoginSupportTypes())) {
            entity.setWxSmallProgramLoginSupportTypes(StringUtil.splicingIntegersByComma(arg.getWxSmallProgramLoginSupportTypes()));
        }
        int insert = channelH5LoginTemplateDao.insert(entity);
        if (insert > 0) {
            arg.setId(entity.getId());// 返回ID
            LinkAppChannelLoginTemplateAssociationVo associationVo = new LinkAppChannelLoginTemplateAssociationVo();
            associationVo.setTenantId(tenantId);
            associationVo.setLinkAppId(arg.getLinkAppId());
            associationVo.setChannelLoginTemplateId(entity.getId());
            associationVo.setChannelLoginTemplateClientType(ChannelLoginTemplateType.APP_CLIENT.getType());

            // 处理预置模板的状态，如果已有启用的模板则设为未启用，默认为未启用状态。
            if (arg.getUsageStatus() != null && ChannelLoginTemplateUsageStatusEnum.USED.getType().equals(arg.getUsageStatus())) {
                ListByTenantIdAndLinkAppIdArg listByTenantIdAndLinkAppIdArg = new ListByTenantIdAndLinkAppIdArg();
                listByTenantIdAndLinkAppIdArg.setTenantId(tenantId);
                listByTenantIdAndLinkAppIdArg.setLinkAppId(arg.getLinkAppId());
                listByTenantIdAndLinkAppIdArg.setClientType(ChannelLoginTemplateType.APP_CLIENT.getType());
                listByTenantIdAndLinkAppIdArg.setIsIncludeAll(false);
                listByTenantIdAndLinkAppIdArg.setUsageStatus(ChannelLoginTemplateUsageStatusEnum.USED.getType());
                Result<List<LinkAppChannelLoginTemplateAssociationVo>> listResult = linkAppChannelLoginTemplateAssociationService.listByTenantIdAndLinkAppId(listByTenantIdAndLinkAppIdArg);
                if (listResult.isSuccess() && CollectionUtils.isEmpty(listResult.getData())) {
                    associationVo.setUsageStatus(ChannelLoginTemplateUsageStatusEnum.USED.getType());
                } else {
                    log.info("该应用已有启用模板，设置为未启用");
                    associationVo.setUsageStatus(ChannelLoginTemplateUsageStatusEnum.UNUSED.getType());
                }
            } else {
                associationVo.setUsageStatus(ChannelLoginTemplateUsageStatusEnum.UNUSED.getType());
            }

            Result<Integer> integerResult = linkAppChannelLoginTemplateAssociationService.create(associationVo);
            if (!integerResult.isSuccess() || integerResult.getData() == null || integerResult.getData() == 0) {
                log.warn("linkAppChannelLoginTemplateAssociationService -> create fail. result={}", integerResult);
                throw new ChannelException(ResultCode.SYSTEM_ERROR.getErrorCode(), ResultCode.SYSTEM_ERROR.getErrorMessage());
            }
        }
        return insert;
    }

    public Integer updateH5ChannelLoginTemplate(String tenantId, CreateH5ChannelLoginTemplateArg arg) {
        if (StringUtils.isEmpty(arg.getId())) {
            return 0;
        }
        ChannelH5LoginTemplateEntity entity = BeanUtil.copy(ChannelH5LoginTemplateEntity.class, arg);
        entity.setTenantId(tenantId);
        entity.setEnterpriseLogoNPath(arg.getPageEnterpriseLogoNPath());
        entity.setEnterpriseLogoDisplayMode(arg.getPageEnterpriseLogoDisplayMode());
        entity.setEnterpriseNameDisplayMode(arg.getPageEnterpriseNameDisplayMode());
        if (!CollectionUtils.isEmpty(arg.getH5LoginSupportTypes())) {
            entity.setH5LoginSupportTypes(StringUtil.splicingIntegersByComma(arg.getH5LoginSupportTypes()));
        }
        if (!CollectionUtils.isEmpty(arg.getLoginSupportTypes())) {
            entity.setLoginSupportTypes(StringUtil.splicingIntegersByComma(arg.getLoginSupportTypes()));
        }
        if (!CollectionUtils.isEmpty(arg.getWxSmallProgramLoginSupportTypes())) {
            entity.setWxSmallProgramLoginSupportTypes(StringUtil.splicingIntegersByComma(arg.getWxSmallProgramLoginSupportTypes()));
        }
        return channelH5LoginTemplateDao.updateWithNotNull(entity);
    }

    public Integer deleteH5ChannelLoginTemplateById(String tenantId, String id) {
        if (StringUtils.isEmpty(id)) {
            return 0;
        }
        ChannelH5LoginTemplateEntity existEntity = channelH5LoginTemplateDao.get(id);
        if (existEntity == null) {
            return 0;
        }
        if (existEntity.getDeleteStatus() != null && existEntity.getDeleteStatus() == ChannelTemplateDeleteStatusEnum.DELETED.getCode()) {
            return 0;
        }

        if (existEntity.getTemplateType() != null && LoginTemplateTypeEnum.PRE_SETTING.getType().equals(existEntity.getTemplateType())) {
            throw new ChannelException(BusinessCode.CANNOT_DELETE_STARTED_TEMPLATE);
        }

        ListByTemplateIdsArg listByTemplateIdsArg = new ListByTemplateIdsArg();
        listByTemplateIdsArg.setTenantId(tenantId);
        listByTemplateIdsArg.setTemplateIds(Lists.newArrayList(id));
        listByTemplateIdsArg.setClientType(ChannelLoginTemplateType.APP_CLIENT.getType());
        Result<List<LinkAppChannelLoginTemplateAssociationVo>> assocaitionresult = linkAppChannelLoginTemplateAssociationService.listByTemplateIds(listByTemplateIdsArg);
        if (!assocaitionresult.isSuccess()) {
            throw new ChannelException(assocaitionresult.getErrCode(), assocaitionresult.getErrMessage());
        }
        List<LinkAppChannelLoginTemplateAssociationVo> associationVos = assocaitionresult.getData();
        if (!CollectionUtils.isEmpty(associationVos)) {
            List<LinkAppChannelLoginTemplateAssociationVo> useAssociationVos = associationVos.stream().filter(x -> x.getUsageStatus() != null && x.getUsageStatus().equals(ChannelLoginTemplateUsageStatusEnum.USED.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(useAssociationVos)) {
                throw new ChannelException(BusinessCode.CANNOT_DELETE_STARTED_TEMPLATE);
            }
        }
        ChannelH5LoginTemplateEntity entity = new ChannelH5LoginTemplateEntity();
        entity.setId(id);
        entity.setTenantId(tenantId);
        entity.setDeleteStatus(ChannelTemplateDeleteStatusEnum.DELETED.getCode());
        return channelH5LoginTemplateDao.updateWithNotNull(entity);
    }

    public Integer updateH5ChannelLoginTemplateUsageStatus(String tenantId, UpdateH5ChannelLoginTemplateUsageStatusArg arg) {
        // 查询当前应用下所有登录模板
        List<LinkAppChannelLoginTemplateAssociationVo> appAssociations = listLinkAppChannelLoginTemplateAssociations(tenantId, arg.getLinkAppId(), ChannelLoginTemplateType.APP_CLIENT.getType(), false);
        List<LinkAppChannelLoginTemplateAssociationVo> existsAssociations = appAssociations.stream().filter(x -> x.getChannelLoginTemplateId().equals(arg.getId())).collect(Collectors.toList());
        // 模板没有被引用过则创建一个
        if (CollectionUtils.isEmpty(appAssociations) || CollectionUtils.isEmpty(existsAssociations)) {
            LinkAppChannelLoginTemplateAssociationVo associationVo = new LinkAppChannelLoginTemplateAssociationVo();
            associationVo.setTenantId(tenantId);
            associationVo.setLinkAppId(arg.getLinkAppId());
            associationVo.setChannelLoginTemplateClientType(ChannelLoginTemplateType.APP_CLIENT.getType());
            associationVo.setChannelLoginTemplateId(arg.getId());
            associationVo.setUsageStatus(ChannelLoginTemplateUsageStatusEnum.USED.getType());
            Result<Integer> integerResult = linkAppChannelLoginTemplateAssociationService.create(associationVo);
            if (!integerResult.isSuccess()) {
                throw new ChannelException(integerResult.getErrCode(), integerResult.getErrMessage());
            }
            return integerResult.getData();
        }
        if (!CollectionUtils.isEmpty(existsAssociations)) {
            LinkAppChannelLoginTemplateAssociationVo associationVo = existsAssociations.get(0);
            if (Objects.equals(associationVo.getUsageStatus(), arg.getUsageStatus())) {
                return 1;
            }
            if (Objects.equals(ChannelLoginTemplateUsageStatusEnum.USED.getType(), arg.getUsageStatus())) {
                // 停用其他模板
                List<String> ids = appAssociations.stream().map(LinkAppChannelLoginTemplateAssociationVo::getId).collect(Collectors.toList());
                batchUpdateChannelH5LoginTemplateUsageStatus(ids, ChannelLoginTemplateUsageStatusEnum.UNUSED.getType());
                // 选用当前模板
                return batchUpdateChannelH5LoginTemplateUsageStatus(Lists.newArrayList(associationVo.getId()), ChannelLoginTemplateUsageStatusEnum.USED.getType());
            }
            if (Objects.equals(ChannelLoginTemplateUsageStatusEnum.UNUSED.getType(), arg.getUsageStatus())) {
                return batchUpdateChannelH5LoginTemplateUsageStatus(Lists.newArrayList(associationVo.getId()), ChannelLoginTemplateUsageStatusEnum.UNUSED.getType());
            }
        }
        return 0;
    }

    private List<LinkAppChannelLoginTemplateAssociationVo> listLinkAppChannelLoginTemplateAssociations(String tenantId, String linkAppId, Integer clientType, boolean isIncludeAll) {
        ListByTenantIdAndLinkAppIdArg listByTenantIdAndLinkAppIdArg = new ListByTenantIdAndLinkAppIdArg();
        listByTenantIdAndLinkAppIdArg.setTenantId(tenantId);
        listByTenantIdAndLinkAppIdArg.setLinkAppId(linkAppId);
        listByTenantIdAndLinkAppIdArg.setClientType(clientType);
        listByTenantIdAndLinkAppIdArg.setIsIncludeAll(isIncludeAll);
        Result<List<LinkAppChannelLoginTemplateAssociationVo>> associationResult = linkAppChannelLoginTemplateAssociationService.listByTenantIdAndLinkAppId(listByTenantIdAndLinkAppIdArg);
        if (!associationResult.isSuccess()) {
            throw new ChannelException(associationResult.getErrCode(), associationResult.getErrMessage());
        }
        return CollectionUtils.isEmpty(associationResult.getData()) ? Lists.newArrayList() : associationResult.getData();
    }

    public Integer batchUpdateChannelH5LoginTemplateUsageStatus(List<String> ids, Integer status) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        BatchUpdateUsageStatusArg batchUpdateUsageStatusArg = new BatchUpdateUsageStatusArg();
        batchUpdateUsageStatusArg.setIds(ids);
        batchUpdateUsageStatusArg.setUsageStatus(status);
        Result<Integer> integerResult = linkAppChannelLoginTemplateAssociationService.batchUpdateUsageStatus(batchUpdateUsageStatusArg);
        if (!integerResult.isSuccess()) {
            throw new ChannelException(integerResult.getErrCode(), integerResult.getErrMessage());
        }
        return integerResult.getData();
    }

    /**
     * 刷登录类型， 将 1 2 刷成 1 6， 将 2 3 ，刷成 3 7
     * 之前 2 表示 手机或者邮箱的验证码登录，现在需要把这个类型拆成 6 手机验证码登录 7 邮箱验证码登录
     *
     * @param upstreamEa
     */
    public void flushLoginTypeData(String upstreamEa) {
        int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        List<ChannelLoginTemplateEntity> channelLoginTemplateEntityList = channelLoginTemplateDao.listChannelLoginTemplate(String.valueOf(tenantId));
        List<ChannelH5LoginTemplateEntity> h5LoginTemplateEntityList = channelH5LoginTemplateDao.listChannelH5LoginTemplate(String.valueOf(tenantId));
        for (ChannelLoginTemplateEntity channelLoginTemplateEntity : channelLoginTemplateEntityList) {
            List<Integer> loginTypes = channelLoginTemplateEntity.getChannelLoginType();
            if (CollectionUtils.isNotEmpty(loginTypes)) {
                List<Integer> newLoginTypes = handleLoginType(loginTypes);
                if (!CollectionUtils.isEqualCollection(newLoginTypes, loginTypes)) {
                    log.info("xxx-id-:{}, old-login-types:{}， new-login-types:{}", channelLoginTemplateEntity.getId(), loginTypes, newLoginTypes);
                    channelLoginTemplateEntity.setChannelLoginType(newLoginTypes);
                    channelLoginTemplateDao.updateWithNotNull(channelLoginTemplateEntity);
                }
            }
        }

        for (ChannelH5LoginTemplateEntity channelH5LoginTemplateEntity : h5LoginTemplateEntityList) {
            if (StringUtils.isBlank(channelH5LoginTemplateEntity.getH5LoginSupportTypes())) {
                continue;
            }
            List<Integer> loginTypes = StringUtil.stringToListInt(channelH5LoginTemplateEntity.getH5LoginSupportTypes(), ",");

            if (CollectionUtils.isNotEmpty(loginTypes)) {
                List<Integer> newLoginTypes = handleLoginType(loginTypes);
                if (!CollectionUtils.isEqualCollection(newLoginTypes, loginTypes)) {
                    log.info("xxx-id-h5:{}, old-login-types:{}， new-login-types:{}", channelH5LoginTemplateEntity.getId(), loginTypes, newLoginTypes);
                    channelH5LoginTemplateEntity.setH5LoginSupportTypes(StringUtil.splicingIntegersByComma(newLoginTypes));
                    channelH5LoginTemplateDao.updateWithNotNull(channelH5LoginTemplateEntity);
                }
            }
        }
    }

    private List<Integer> handleLoginType(List<Integer> oldLoginTypes) {
        List<Integer> loginTypes = new ArrayList<>(oldLoginTypes);

        if (CollectionUtils.isNotEmpty(loginTypes)) {
            boolean write6 = false, write7 = false;
            if (loginTypes.contains(ChannelLoginTypeEnum.PHONE_PASSWORD.getType())
                    && loginTypes.contains(ChannelLoginTypeEnum.PHONE_AND_EMAIL_CODE.getType())
                    && !loginTypes.contains(ChannelLoginTypeEnum.PHONE_CODE.getType())) {
                loginTypes.add(ChannelLoginTypeEnum.PHONE_CODE.getType());
                write6 = true;
            }
            if (loginTypes.contains(ChannelLoginTypeEnum.EMAIL_PASSWORD.getType())
                    && loginTypes.contains(ChannelLoginTypeEnum.PHONE_AND_EMAIL_CODE.getType())
                    && !loginTypes.contains(ChannelLoginTypeEnum.EMAIL_CODE.getType())) {
                loginTypes.add(ChannelLoginTypeEnum.EMAIL_CODE.getType());
                write7 = true;
            }

            if (write6 || write7) {
                loginTypes.remove(ChannelLoginTypeEnum.PHONE_AND_EMAIL_CODE.getType());
            }
        }

        return loginTypes;
    }
}
