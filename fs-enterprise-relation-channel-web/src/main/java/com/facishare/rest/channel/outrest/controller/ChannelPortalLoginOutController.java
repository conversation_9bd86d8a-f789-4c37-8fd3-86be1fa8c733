package com.facishare.rest.channel.outrest.controller;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.BeanUtil;
import com.facishare.global.rest.dao.pg.EnterpriseCardEntityDao;
import com.facishare.global.rest.model.entity.EnterpriseCardEntity;
import com.facishare.linkapp.api.arg.ListByTenantIdAndLinkAppIdArg;
import com.facishare.linkapp.api.result.LinkAppChannelLoginTemplateAssociationVo;
import com.facishare.linkapp.api.service.LinkAppChannelLoginTemplateAssociationService;
import com.facishare.rest.channel.ServiceContext;
import com.facishare.rest.channel.admin.service.ChannelLoginTemplateService;
import com.facishare.rest.channel.model.args.*;
import com.facishare.rest.channel.model.data.LoginTemplateIdData;
import com.facishare.rest.channel.model.entity.pg.ChannelDomainIndexEntity;
import com.facishare.rest.channel.model.entity.pg.ChannelLoginTemplateEntity;
import com.facishare.rest.channel.model.enums.ChannelLoginTemplateStatusEnum;
import com.facishare.rest.channel.model.enums.ChannelLoginTemplateType;
import com.facishare.rest.channel.model.enums.ChannelLoginTemplateUsageStatusEnum;
import com.facishare.rest.channel.model.result.ChannelLoginTemplateResult;
import com.facishare.rest.channel.model.result.H5ChannelLoginTemplatePageResult;
import com.facishare.rest.channel.model.result.H5ChannelLoginTemplateResult;
import com.facishare.rest.channel.remote.entity.EnterpriseMetaDataEntity;
import com.facishare.rest.channel.service.EnterpriseMetaDataService;
import com.facishare.rest.channel.util.CDNImagePathUtil;
import com.facishare.rest.channel.util.ConfigUtil;
import com.facishare.rest.channel.web.service.ChannelPortalDomainService;
import com.facishare.rest.channel.web.service.ChannelPortalLoginService;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.trace.TraceContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/outapi/channel/portal")
public class ChannelPortalLoginOutController {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ChannelPortalDomainService channelPortalDomainService;
    @Autowired
    private ChannelPortalLoginService channelPortalLoginService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EnterpriseMetaDataService enterpriseMetaDataService;
    @Autowired
    private ChannelLoginTemplateService channelLoginTemplateService;
    @Autowired
    private EnterpriseCardEntityDao enterpriseCardEntityDao;
    @Autowired
    private LinkAppChannelLoginTemplateAssociationService linkAppChannelLoginTemplateAssociationService;

    /**
     * 渠道专属域名加载自定义登录模板页面
     */
    @RequestMapping(value = {"/login"}, method = {RequestMethod.POST, RequestMethod.GET})
    public Result<LoginPageTemplateConfig> getChannelWebLoginTemplateConfig(@RequestBody GetChannelWebLoginTemplateConfigArg arg) {
        try {
            if (StringUtils.isEmpty(arg.getDomainName()) && StringUtils.isEmpty(arg.getFsAppId())) {
                return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.PARAMS_ERROR.getErrorMessage());
            }
            if (arg.getUpstreamEi() == null) {
                arg.setUpstreamEi(ServiceContext.getUpstreamEi());
            }
            // 先读配置文件
            String upstreamEa;
            LoginPageTemplateConfig loginPageTemplateConfig = null;
            if (arg.getUpstreamEi() != null && StringUtils.isNotEmpty(arg.getFsAppId())) {
                loginPageTemplateConfig = findWebLoginPageTemplateConfigByFsAppId(arg.getUpstreamEi(), arg.getFsAppId());
            }
            if (loginPageTemplateConfig == null && StringUtils.isNotEmpty(arg.getDomainName())) {
                loginPageTemplateConfig = findWebLoginPageTemplateConfigByDomainFullName(arg.getDomainName());
            }
            if (loginPageTemplateConfig != null) {
                ChannelLoginTemplateResult loginTemplateConfig = loginPageTemplateConfig.getLoginPageTemplateConfig();
                upstreamEa = loginPageTemplateConfig.getUpstreamEa();
                EnterpriseMetaDataEntity enterpriseMetaData = enterpriseMetaDataService.getEnterpriseMetaData(upstreamEa);

                String enterpriseNameLang = getEnterpriseNameLang(upstreamEa, enterpriseMetaData);
                loginTemplateConfig.setEnterpriseInfoName(enterpriseNameLang);
                loginTemplateConfig.setEnterpriseInfoLogoPath(CDNImagePathUtil.toCDNPath(enterpriseMetaData.getLogoPath(), upstreamEa));
                if (enterpriseMetaData.getVendorLoginProtocolConfig() != null) {
                    loginTemplateConfig.setVendorLoginProtocolSwitch(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolSwitch());
                    loginTemplateConfig.setVendorLoginProtocolTitle(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolTitle());
                    loginTemplateConfig.setVendorLoginProtocolContent(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolContent());
                    loginTemplateConfig.setVendorLoginProtocolContentJson(enterpriseMetaData.getVendorLoginProtocolConfig().getVendorLoginProtocolContentJson());
                }

                loginPageTemplateConfig.setEnterpriseName(enterpriseNameLang);
                loginPageTemplateConfig.setLoginPageTemplateConfig(loginTemplateConfig);
            }
            return Result.newSuccess(loginPageTemplateConfig);
        } catch (Exception e) {
            log.warn("", e);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), ResultCode.SYSTEM_ERROR.getDescription() + "，traceId=" + TraceContext.get().getTraceId());
        }
    }

    private LoginPageTemplateConfig findWebLoginPageTemplateConfigByFsAppId(Integer upstreamEi, String fsAppId) {
        if (upstreamEi == null || StringUtils.isEmpty(fsAppId)) {
            return null;
        }
        ChannelLoginTemplateResult loginTemplateConfig = null;
        String loginTemplateId = "";
        String upstreamEa = eieaConverter.enterpriseIdToAccount(upstreamEi);
        ListByTenantIdAndLinkAppIdArg arg = new ListByTenantIdAndLinkAppIdArg();
        arg.setTenantId(upstreamEa);
        arg.setLinkAppId(fsAppId);
        arg.setClientType(ChannelLoginTemplateType.WEB_CLIENT.getType());
        arg.setUsageStatus(ChannelLoginTemplateUsageStatusEnum.USED.getType());
        Result<List<LinkAppChannelLoginTemplateAssociationVo>> result = linkAppChannelLoginTemplateAssociationService.listByTenantIdAndLinkAppId(arg);
        if (result.isSuccess() && result.getData() != null && result.getData().size() > 0) {
            loginTemplateId = result.getData().get(0).getChannelLoginTemplateId();
        }
        if (StringUtils.isNotEmpty(loginTemplateId)) {
            loginTemplateConfig = findWebLoginPageTemplateConfigByLoginTemplateId(upstreamEa, loginTemplateId);
        }
        if (loginTemplateConfig != null) {
            LoginPageTemplateConfig loginPageTemplateConfig = new LoginPageTemplateConfig();
            loginPageTemplateConfig.setUpstreamEa(upstreamEa);
            loginPageTemplateConfig.setUpstreamEi(upstreamEi + "");
            loginPageTemplateConfig.setFsAppId(fsAppId);
            loginPageTemplateConfig.setLoginPageTemplateConfig(loginTemplateConfig);
            return loginPageTemplateConfig;
        }
        return null;
    }

    private LoginPageTemplateConfig findWebLoginPageTemplateConfigByDomainFullName(String domainFullName) {
        String loginTemplateId;
        String archivedInformation;
        String upstreamEa;
        String upstreamEi;
        if (!MapUtils.isEmpty(ConfigUtil.domainChannelWebLoginTemplateConfig) && ConfigUtil.domainChannelWebLoginTemplateConfig.get(domainFullName) != null) {
            LoginTemplateIdData loginTemplateIdData = ConfigUtil.domainChannelWebLoginTemplateConfig.get(domainFullName);
            loginTemplateId = loginTemplateIdData.getTemplateId();
            upstreamEa = loginTemplateIdData.getEa();
            upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa) + "";
            archivedInformation = loginTemplateIdData.getArchivedInformation();
        } else {
            ChannelDomainIndexEntity domainConfig = channelPortalDomainService.getDomainConfigByDomainName(domainFullName);
            if (domainConfig == null) {
                return null;
            }
            loginTemplateId = domainConfig.getLoginTemplateId();
            upstreamEi = domainConfig.getTenantId();
            upstreamEa = eieaConverter.enterpriseIdToAccount(Integer.parseInt(upstreamEi));
            archivedInformation = domainConfig.getArchivedInformation();
        }
        if (StringUtils.isEmpty(upstreamEa) || StringUtils.isEmpty(loginTemplateId)) {
            return null;
        }
        ChannelLoginTemplateResult loginTemplateConfig = findWebLoginPageTemplateConfigByLoginTemplateId(upstreamEa, loginTemplateId);
        if (loginTemplateConfig != null) {
            loginTemplateConfig.setArchivedInformation(archivedInformation);

            LoginPageTemplateConfig loginPageTemplateConfig = new LoginPageTemplateConfig();
            loginPageTemplateConfig.setUpstreamEa(upstreamEa);
            loginPageTemplateConfig.setUpstreamEi(upstreamEi);
            loginPageTemplateConfig.setLoginPageTemplateConfig(loginTemplateConfig);
            return loginPageTemplateConfig;
        }
        return null;
    }

    private H5ChannelLoginTemplateResult findH5LoginPageTemplateConfigByDomainFullName(String domainFullName) {
        if (StringUtils.isNotEmpty(domainFullName)) {
            ChannelDomainIndexEntity domainConfig = channelPortalDomainService.getDomainConfigByDomainName(domainFullName);
            if (domainConfig == null) {
                return null;
            }
            String loginTemplateId = domainConfig.getH5LoginTemplateId();
            String upstreamEi = domainConfig.getTenantId();
            String upstreamEa = eieaConverter.enterpriseIdToAccount(Integer.parseInt(upstreamEi));
            H5ChannelLoginTemplateResult loginTemplateConfig = findH5LoginPageTemplateConfigByTemplateId(upstreamEa, loginTemplateId);
            return loginTemplateConfig;
        }
        return null;
    }

    private H5ChannelLoginTemplateResult findH5LoginPageTemplateConfigByTemplateId(String upstreamEa, String loginTemplateId) {
        return channelLoginTemplateService.findH5LoginTemplateById(upstreamEa, loginTemplateId);
    }

    private ChannelLoginTemplateResult findWebLoginPageTemplateConfigByLoginTemplateId(String upstreamEa, String loginTemplateId) {
        ChannelLoginTemplateEntity entity = channelPortalLoginService.getLoginTemplateById(loginTemplateId);
        if (entity != null) {
            ChannelLoginTemplateResult loginTemplateConfig = BeanUtil.deepCopy(entity, ChannelLoginTemplateResult.class);

            if (StringUtils.isNotEmpty(loginTemplateConfig.getThemeColor())) {
                loginTemplateConfig.setThemeColor(loginTemplateConfig.getThemeColor().trim());
            }

            if (StringUtils.isNotEmpty(loginTemplateConfig.getLogoPath())) {
                String logoPath = CDNImagePathUtil.toCDNPath(loginTemplateConfig.getLogoPath(), upstreamEa);
                loginTemplateConfig.setLogoPath(logoPath);
            }
            if (StringUtils.isNotEmpty(loginTemplateConfig.getBackgroundPath())) {
                String backGroundPath = CDNImagePathUtil.toCDNPath(loginTemplateConfig.getBackgroundPath(), upstreamEa);
                loginTemplateConfig.setBackgroundPath(backGroundPath);
            }
            if (loginTemplateConfig.getSlideBannerShows() != null && loginTemplateConfig.getSlideBannerShows().size() > 0) {
                String finalUpstreamEa = upstreamEa;
                loginTemplateConfig.getSlideBannerShows().forEach(slideBannerShowData -> {
                    if (StringUtils.isNotEmpty(slideBannerShowData.getImagePath())) {
                        String imagePath = CDNImagePathUtil.toCDNPath(slideBannerShowData.getImagePath(), finalUpstreamEa);
                        slideBannerShowData.setImagePath(imagePath);
                    }
                });
            }
            return loginTemplateConfig;
        }
        return null;
    }

    public String getEnterpriseNameLang(String upstreamEa, EnterpriseMetaDataEntity enterpriseMetaData) {
        try {
            Long outTenantId = enterpriseCardEntityDao.getOuterTenantIdOrCreate(upstreamEa);
            EnterpriseCardEntity enterpriseCardEntity = enterpriseCardEntityDao.findOrCreate(outTenantId, enterpriseMetaData.getEnterpriseName());
            if (StringUtils.isNotBlank(enterpriseCardEntity.getEnterpriseName())) {
                return enterpriseCardEntity.getEnterpriseName();
            } else if (StringUtils.isNotBlank(enterpriseMetaData.getEnterpriseName())) {
                return enterpriseMetaData.getEnterpriseName();
            }
        } catch (Throwable throwable) {
            log.warn("", throwable);
        }

        if (enterpriseMetaData != null && StringUtils.isNotBlank(enterpriseMetaData.getEnterpriseName())) {
            return enterpriseMetaData.getEnterpriseName();
        } else {
            return getUpstreamEnterpriseName(upstreamEa);
        }
    }

    /**
     * 上游企业账号名称
     */
    public String getUpstreamEnterpriseName(String upstreamEa) {
        GetEnterpriseDataArg getEaDataArg = new GetEnterpriseDataArg();
        getEaDataArg.setEnterpriseAccount(upstreamEa);
        GetEnterpriseDataResult getEaDataResult = enterpriseEditionService.getEnterpriseData(getEaDataArg);
        EnterpriseData enterpriseData = getEaDataResult.getEnterpriseData();
        if (Objects.isNull(enterpriseData)) {
            return "";
        }
        return enterpriseData.getEnterpriseName();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class LoginPageTemplateConfig implements Serializable {
        private String upstreamEa;
        private String upstreamEi;
        private String enterpriseName;
        private String fsAppId;
        private ChannelLoginTemplateResult loginPageTemplateConfig;
    }

    @RequestMapping(value = {"/getChannelH5LoginPageTemplate"}, method = {RequestMethod.POST, RequestMethod.GET})
    public Result<H5ChannelLoginTemplateResult> getChannelH5LoginPageTemplate(@RequestBody GetChannelH5LoginPageTemplateArg arg) {
        int tenantId = eieaConverter.enterpriseAccountToId(arg.getUpstreamEa());
        H5ChannelLoginTemplateResult templateData = null;
        if (StringUtils.isNotEmpty(arg.getLinkAppId())) {
            templateData = getChannelH5LoginPageTemplate(String.valueOf(tenantId), arg.getLinkAppId());
        } else if (StringUtils.isNotEmpty(arg.getDomainName())) {
            templateData = findH5LoginPageTemplateConfigByDomainFullName(arg.getDomainName());
        }
        return Result.newSuccess(templateData);
    }

    @RequestMapping(value = {"/loginTemplate/createWebChannelLoginTemplate"}, method = {RequestMethod.POST, RequestMethod.GET})
    public Result<Boolean> createWebChannelLoginTemplate(@RequestBody CreateOrUpdateLoginPageTemplateArg arg) {
        ChannelLoginTemplateEntity entity = BeanUtil.deepCopy(arg, ChannelLoginTemplateEntity.class);
        entity.setTenantId(arg.getTenantId() + "");
        Boolean result = channelLoginTemplateService.createOrUpdateChannelLoginTemplate(arg.getTenantId(), arg.getLinkAppId(), entity);
        return Result.newSuccess(result);
    }

    @RequestMapping(value = {"/loginTemplate/createH5ChannelLoginTemplate"}, method = {RequestMethod.POST, RequestMethod.GET})
    public Result<Boolean> createH5ChannelLoginTemplate(@RequestBody CreateH5ChannelLoginTemplateArg arg) {
        Integer count = channelLoginTemplateService.createH5ChannelLoginTemplate(arg.getTenantId(), arg);
        return Result.newSuccess(count > 0);
    }

    private H5ChannelLoginTemplateResult getChannelH5LoginPageTemplate(String tenantId, String linkAppId) {
        ListH5ChannelLoginTemplateArg templateArg = new ListH5ChannelLoginTemplateArg();
        templateArg.setLinkAppId(linkAppId);
        templateArg.setStatus(ChannelLoginTemplateStatusEnum.STARTED.getType());
        templateArg.setUsageStatus(ChannelLoginTemplateUsageStatusEnum.USED.getType());
        templateArg.setPageNumber(1);
        templateArg.setPageSize(1);
        H5ChannelLoginTemplatePageResult templatePageResult = channelLoginTemplateService.listH5ChannelLoginTemplate(String.valueOf(tenantId), templateArg);
        if (!CollectionUtils.isEmpty(templatePageResult.getDataList())) {
            return templatePageResult.getDataList().get(0);
        }
        return null;
    }
}
