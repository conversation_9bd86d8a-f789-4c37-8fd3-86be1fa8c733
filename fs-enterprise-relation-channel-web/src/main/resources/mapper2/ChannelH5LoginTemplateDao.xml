<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.rest.channel.dao.pg.ChannelH5LoginTemplateDao">
    <resultMap id="resultObjMap" type="com.facishare.rest.channel.model.entity.pg.ChannelH5LoginTemplateEntity">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="enterprise_name" property="enterpriseName"/>
        <result column="enterprise_logo_npath" property="enterpriseLogoNPath"/>
        <result column="page_title" property="pageTitle"/>
        <result column="login_support_types" property="loginSupportTypes"/>
        <result column="h5_login_support_types" property="h5LoginSupportTypes"/>
        <result column="wx_small_program_login_support_types" property="wxSmallProgramLoginSupportTypes"/>
        <result column="enterprise_logo_display_mode" property="enterpriseLogoDisplayMode"/>
        <result column="page_default_language" property="pageDefaultLanguage"/>
        <result column="status" property="status"/>
        <result column="delete_status" property="deleteStatus"/>
        <result column="total" property="total"/>
    </resultMap>

    <select id="listByConditionWithPage" resultMap="resultObjMap">
        SELECT count(1) over() as total,*
        FROM er_channel_h5_login_template
        WHERE tenant_id = #{tenantId}
        <if test="name != null">AND name LIKE CONCAT('%', #{name}, '%')</if>
        <if test="deleteStatus != null">AND delete_status=#{deleteStatus}</if>
        <if test="status != null">AND status=#{status}</if>
        <if test="ids != null">AND id IN <foreach collection='ids' open='(' close=')' separator=',' item='id'>#{id}</foreach></if>
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="listChannelH5LoginTemplate" resultMap="resultObjMap">
        select *
        from er_channel_h5_login_template
        where tenant_id = #{tenantId}
    </select>
</mapper>