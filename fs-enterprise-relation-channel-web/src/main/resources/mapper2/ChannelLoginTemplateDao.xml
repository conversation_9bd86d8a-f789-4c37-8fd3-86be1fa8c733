<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.rest.channel.dao.pg.ChannelLoginTemplateDao">
  <resultMap id="resultObjMap" type="com.facishare.rest.channel.model.entity.pg.ChannelLoginTemplateEntity">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="custom_enterprise_name" property="customEnterpriseName"/>
    <result column="show_enterprise_name" property="showEnterpriseName"/>
    <result column="theme_color" property="themeColor"/>
    <result column="logo_path" property="logoPath"/>
    <result column="show_logo" property="showLogo"/>
    <result column="background_path" property="backgroundPath"/>
    <result column="slide_shows" property="slideBannerShows" typeHandler="com.facishare.rest.channel.dao.typehandler.SlideBannerShowListTypeHandler"/>
    <result column="foot_link_list" property="footLinkList" typeHandler="com.facishare.rest.channel.dao.typehandler.LinkDataListTypeHandler"/>
    <result column="channel_login_type" property="channelLoginType" typeHandler="com.facishare.rest.channel.dao.typehandler.IntegerListTypeHandler"/>
    <result column="er_language_default" property="erLanguageDefault"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="created_by" property="createdBy"/>
    <result column="create_time" property="createTime"/>
    <result column="last_modified_by" property="lastModifiedBy"/>
    <result column="last_modified_time" property="lastModifiedTime"/>
  </resultMap>
  <select id="listChannelLoginTemplatePage" resultMap="resultObjMap">
    select *
    from er_channel_login_template
    where tenant_id = #{tenantId} limit #{limit}
    offset #{offset}
  </select>
  <select id="listByConditionWithPage" resultMap="resultObjMap">
      select *
      from er_channel_login_template
      where tenant_id = #{tenantId}
      <if test="name != null">AND name LIKE CONCAT('%', #{name}, '%')</if>
      <if test="ids != null">AND id IN
          <foreach collection='ids' open='(' close=')' separator=',' item='id'>#{id}</foreach>
      </if>
      limit #{limit} offset #{offset}
  </select>
  <select id="getByTenantId" resultType="com.facishare.rest.channel.model.entity.pg.ChannelLoginTemplateEntity">
    select *
    from er_channel_login_template
    where tenant_id = #{tenantId}
  </select>
  <select id="countChannelLoginTemplate" resultType="java.lang.Integer">
    select count(*) as ids
    from er_channel_login_template
    where tenant_id = #{tenantId}
  </select>
  <select id="listChannelLoginTemplate" resultMap="resultObjMap">
    select *
    from er_channel_login_template
    where tenant_id = #{tenantId}
  </select>
</mapper>