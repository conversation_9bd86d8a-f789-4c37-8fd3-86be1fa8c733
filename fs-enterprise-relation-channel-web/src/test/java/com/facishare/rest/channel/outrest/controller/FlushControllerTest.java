package com.facishare.rest.channel.outrest.controller;

import com.facishare.enterprise.common.result.Result;
import com.facishare.rest.channel.BaseTest;
import com.facishare.rest.channel.dao.pg.ChannelDomainIndexDao;
import com.facishare.rest.channel.model.args.BaseFlushArg;
import com.facishare.rest.channel.model.args.InitCustomDomainFieldDefaultValuesArg;
import com.facishare.rest.channel.model.args.InitCustomDomainObjArg;
import com.facishare.rest.channel.model.args.InitCustomDomainObjArg.CustomDomainData;
import com.facishare.rest.channel.model.args.InitShowLogoArg;
import com.facishare.rest.channel.model.entity.pg.ChannelDomainIndexEntity;
import com.fxiaoke.crmrestapi.result.BatchObjectDataResult;
import com.fxiaoke.enterpriserelation.objrest.common.ChannelDomainObjConstant;
import com.fxiaoke.enterpriserelation.objrest.common.DomainType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class FlushControllerTest extends BaseTest {
    @Autowired
    private FlushController flushController;
    @Autowired
    private ChannelDomainIndexDao channelDomainIndexDao;

    @Test
    public void test1() {
        CustomDomainData customDomainData = new CustomDomainData();
        customDomainData.setEa("82322");
        customDomainData.setDomain("eshop.sigenergy.com");
        customDomainData.setLoginTemplateId("aec9e999c3914cc39231e3e19af099eb");

        InitCustomDomainObjArg arg = new InitCustomDomainObjArg();
        arg.setExec(true);
        arg.setCustomDomainDatas(Lists.newArrayList(customDomainData));
        Result<Void> result = flushController.initCustomDomainObj(arg);
        System.out.println(result);
    }

    @Test
    public void test2() {
        InitShowLogoArg arg = new InitShowLogoArg();
        arg.setExec(true);
        arg.setEas(Lists.newArrayList("71556"));
        arg.setIsInitAll(true);
        Result<Void> result = flushController.initShowLogo(arg);
        System.out.println(result);
    }

    @Test
    public void test3() {
        ChannelDomainIndexEntity entity = new ChannelDomainIndexEntity();
        entity.init();
        entity.setId("646c65fd12038300015fe244");
        entity.setDomainId("646c65fd12038300015fe244");
        entity.setTenantId("82322");
        entity.setLoginTemplateId("aec9e999c3914cc39231e3e19af099eb");
        entity.setDomainName("eshop.sigenergy.com");
        entity.setDomainFullName("eshop.sigenergy.com");
        entity.setCreatedBy("-10000");
        entity.setLastModifiedBy("-10000");
        channelDomainIndexDao.upsertById(entity);
    }

    @Test
    public void test4() {
        Map<String, Object> fieldDefaultValueMap = Maps.newHashMap();
        fieldDefaultValueMap.put(ChannelDomainObjConstant.TYPE, DomainType.EXCLUSIVE.getType());
        fieldDefaultValueMap.put(ChannelDomainObjConstant.MODIFY_COUNT, 0);

        InitCustomDomainFieldDefaultValuesArg arg = new InitCustomDomainFieldDefaultValuesArg();
        arg.setExec(true);
        arg.setIsInitAll(false);
        arg.setEas(Lists.newArrayList("71556"));
        arg.setFieldDefaultValueMap(fieldDefaultValueMap);
        Result<BatchObjectDataResult> batchObjectDataResult = flushController.initCustomDomainFieldDefaultValues(arg);
        System.out.println(batchObjectDataResult);
    }

    @Test
    public void test5() {
        BaseFlushArg arg = new BaseFlushArg();
        arg.setEas(Lists.newArrayList("90925"));
        arg.setExec(true);
        Result<Map<String, Object>> mapResult = flushController.initChannelH5LoginTemplate(arg);
        System.out.println(mapResult);
    }
}
