package com.facishare.rest.channel.outrest.controller;

import com.facishare.enterprise.common.result.Result;
import com.facishare.rest.channel.BaseTest;
import com.facishare.rest.channel.model.args.GetChannelH5LoginPageTemplateArg;
import com.facishare.rest.channel.model.args.GetChannelWebLoginTemplateConfigArg;
import com.facishare.rest.channel.model.result.H5ChannelLoginTemplateResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ChannelPortalLoginOutControllerTest extends BaseTest {
    @Autowired
    private ChannelPortalLoginOutController channelPortalLoginOutController;

    @Test
    public void getChannelPortalLoginTemplateConfigTest() {
        GetChannelWebLoginTemplateConfigArg arg = new GetChannelWebLoginTemplateConfigArg();
        arg.setDomainName("89303");
        Result<ChannelPortalLoginOutController.LoginPageTemplateConfig> hello800 = channelPortalLoginOutController.getChannelWebLoginTemplateConfig(arg);
        log.info("getChannelPortalLoginTemplateConfig: {}", hello800);
    }

    @Test
    public void getChannelPortalLoginTemplateConfigTest2() {
        GetChannelWebLoginTemplateConfigArg arg = new GetChannelWebLoginTemplateConfigArg();
        arg.setUpstreamEi(89303);
        arg.setFsAppId("FSAID_11490d9e");
        Result<ChannelPortalLoginOutController.LoginPageTemplateConfig> hello800 = channelPortalLoginOutController.getChannelWebLoginTemplateConfig(arg);
        log.info("getChannelPortalLoginTemplateConfig: {}", hello800);
    }

    @Test
    public void getH5LoginTemplateTest() {
        GetChannelH5LoginPageTemplateArg arg = new GetChannelH5LoginPageTemplateArg();
        arg.setLinkAppId("FSAID_11490d9e");
        arg.setUpstreamEa("90925");
        Result<H5ChannelLoginTemplateResult> channelH5LoginPageTemplate = channelPortalLoginOutController.getChannelH5LoginPageTemplate(arg);
        System.out.println(channelH5LoginPageTemplate);
    }

    @Test
    public void getH5LoginTemplateTest1() {
        GetChannelH5LoginPageTemplateArg arg = new GetChannelH5LoginPageTemplateArg();
        arg.setDomainName("90756.qd.ceshi112.com");
        arg.setUpstreamEa("90756");
        Result<H5ChannelLoginTemplateResult> channelH5LoginPageTemplate = channelPortalLoginOutController.getChannelH5LoginPageTemplate(arg);
        System.out.println(channelH5LoginPageTemplate);
    }
}
