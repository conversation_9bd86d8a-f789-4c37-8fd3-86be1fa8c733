[{"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-intelligent-scoring-rating-management", "functionName": "智能评分评级管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-rfmrule", "functionName": "RFM规则", "supportModuleList": ["*"], "supportEnterpriseAccounts": "74860,79337,84903,84902,80089,82094,84904,78060", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "erpdss/monitor", "functionName": "数据监控", "supportModuleList": ["standard_data_sync_app", "ding_ding_crm_data_sync_app", "sap_data_sync_app", "k3_data_sync_app", "u8_data_sync_app", "u8_eai_data_sync_app", "zhi<PERSON>_huabao_data_sync_app", "uc_shenma_data_sync_app", "sogo_xiansuocrm_data_sync_app", "kua<PERSON><PERSON>_xiansuocrm_data_sync_app", "db_data_sync_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-custommenuitem-outer", "functionName": "自定义菜单", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "jumpmanage/=/module-channelcustompage", "functionName": "自定义页面", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "channel-custom", "functionName": "渠道定制", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "channel-custom/channel-login", "functionName": "渠道登录", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "channel-custom/channel-login/domain-manage", "functionName": "域名管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "channel-custom/channel-login/custom-login-page", "functionName": "自定义登录页", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "jumpmanage/=/module-channelnavigation", "functionName": "渠道导航", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "erpdss/interface", "functionName": "接口管理", "supportModuleList": ["standard_data_sync_app", "ding_ding_crm_data_sync_app", "sap_data_sync_app", "k3_data_sync_app", "u8_data_sync_app", "u8_eai_data_sync_app", "zhi<PERSON>_huabao_data_sync_app", "uc_shenma_data_sync_app", "sogo_xiansuocrm_data_sync_app", "kua<PERSON><PERSON>_xiansuocrm_data_sync_app", "db_data_sync_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [""], "functionCode": "erpdss/oabutt", "functionName": "集成平台-OA待办", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [""], "functionCode": "manage/oasync/todos", "functionName": "集成平台-OA待办", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "crmmanage/=/module-electronicsign", "supportModuleList": ["e-signature_app"], "editable": false, "supportEnterpriseIds": "*", "appId": "facishare-system", "show": true, "name": "电子签章"}, {"supportVersionList": ["*"], "functionCode": "enterprise/nationalareamanagement", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true, "name": "国家地区管理"}, {"supportVersionList": ["*"], "functionCode": "datamaintenancetools/datasearch", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true, "name": "数据权限查询工具"}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-watermark", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true, "name": "水印设置"}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-journalsetting", "functionName": "日志管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-schedulemanagement", "functionName": "日程管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [""], "functionCode": "enterprise/multiorg", "functionName": "多组织设置", "supportModuleList": ["multi_business_unit_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "department/deptlist", "functionName": "组织部门列表", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["wechat_strengthen_edition", "strengthen_edition", "enterprise_edition"], "functionCode": "Domain_management", "functionName": "域名管理", "supportModuleList": ["*"], "editable": false, "supportEnterpriseIds": "*", "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "custom-login-page", "functionName": "自定义登录页", "supportModuleList": ["*"], "editable": false, "supportEnterpriseIds": "*", "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "manager_index", "functionName": "管理首页", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "enterprise_setting", "functionName": "企业信息设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "crmmanage/=/module-multicurrency", "functionName": "多币种设置", "supportModuleList": ["multi_currency_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-project-manage-setting", "functionName": "项目管理设置", "supportModuleList": ["project_management_app"], "editable": false, "supportEnterpriseIds": "*", "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "department/enterprisefiscalyear", "functionName": "企业财年设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "enterprise_employee_setting", "functionName": "员工功能设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "enterprise_mobile_setting", "functionName": "手机号隐私设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "enterprise_accountsecuritysettings", "functionName": "账号安全设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-device-bound", "functionName": "设备绑定", "supportModuleList": ["*"], "editable": false, "supportEnterpriseIds": "*", "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "jumpmanage_module_JumpMessage", "functionName": "强制通知设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["wechat_strengthen_edition", "strengthen_edition", "enterprise_edition"], "functionCode": "enterprise/sso", "functionName": "单点登录", "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["strengthen_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "enterprise/ipctrl", "functionName": "访问限制设置", "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 8, 9, 10], "functionCode": "employee_import", "functionName": "员工导入", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 8, 9, 10], "functionCode": "wait_invite", "functionName": "邀请审核", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 9, 10], "functionCode": "employee_device_manage", "functionName": "员工设备管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "department/multidimensionmanage", "functionName": "多维度管理", "supportModuleList": ["multi_organization_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "wechat_standard_edition", "promotion_sales_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "closeByEnterpriseSource": [7, 8, 9], "functionCode": "enterprise_weixin_bind_v2", "functionName": "企业微信对接", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "dingding", "functionName": "钉钉对接", "supportModuleList": ["ding_talk_dock_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["jdy_edition"], "functionCode": "jing<PERSON><PERSON><PERSON>", "functionName": "精斗云对接", "supportModuleList": ["jdy_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "erpdss/home", "functionName": "数据总览", "supportModuleList": ["standard_data_sync_app", "ding_ding_crm_data_sync_app", "sap_data_sync_app", "k3_data_sync_app", "u8_data_sync_app", "u8_eai_data_sync_app", "zhi<PERSON>_huabao_data_sync_app", "uc_shenma_data_sync_app", "sogo_xiansuocrm_data_sync_app", "kua<PERSON><PERSON>_xiansuocrm_data_sync_app", "db_data_sync_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "erpdss/policy", "functionName": "同步策略", "supportModuleList": ["standard_data_sync_app", "ding_ding_crm_data_sync_app", "sap_data_sync_app", "k3_data_sync_app", "u8_data_sync_app", "u8_eai_data_sync_app", "zhi<PERSON>_huabao_data_sync_app", "uc_shenma_data_sync_app", "sogo_xiansuocrm_data_sync_app", "kua<PERSON><PERSON>_xiansuocrm_data_sync_app", "db_data_sync_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "erpdss/process", "functionName": "数据维护", "supportModuleList": ["standard_data_sync_app", "ding_ding_crm_data_sync_app", "sap_data_sync_app", "k3_data_sync_app", "u8_data_sync_app", "u8_eai_data_sync_app", "zhi<PERSON>_huabao_data_sync_app", "uc_shenma_data_sync_app", "sogo_xiansuocrm_data_sync_app", "kua<PERSON><PERSON>_xiansuocrm_data_sync_app", "db_data_sync_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "erpdss/setting", "functionName": "数据同步设置", "supportModuleList": ["standard_data_sync_app", "ding_ding_crm_data_sync_app", "sap_data_sync_app", "k3_data_sync_app", "u8_data_sync_app", "u8_eai_data_sync_app", "zhi<PERSON>_huabao_data_sync_app", "uc_shenma_data_sync_app", "sogo_xiansuocrm_data_sync_app", "kua<PERSON><PERSON>_xiansuocrm_data_sync_app", "db_data_sync_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "openAPI-eventCallBackSetting", "functionName": "事件回调设置", "supportModuleList": ["open_api"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": false}, {"supportVersionList": ["*"], "functionCode": "k3cloud", "functionName": "k3_cloud", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-manageconfignav", "functionName": "系统配置引导", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-system-resource", "functionName": "系统资源概览", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 9], "functionCode": "organization_query", "functionName": "部门员工查看", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 8, 9, 10], "functionCode": "employee_create", "functionName": "员工新建", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 8, 9, 10], "functionCode": "employee_stop", "functionName": "员工停用", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 9], "functionCode": "employee_modify", "functionName": "员工操作", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 8, 9, 10], "functionCode": "employee_export", "functionName": "员工导出", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 9, 10], "functionCode": "department_create", "functionName": "部门新建", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 9, 10], "functionCode": "department_stop", "functionName": "部门停用", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 9], "functionCode": "department_modify", "functionName": "部门编辑操作", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 8, 9, 10], "functionCode": "organization_isolate", "functionName": "通讯录隔离管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 9, 10], "functionCode": "employee_query_stop", "functionName": "已停用员工查看", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 8, 9, 10], "functionCode": "employee_resume", "functionName": "员工启用", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 9, 10], "functionCode": "department_query_stop", "functionName": "已停用部门查看", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": false}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 9, 10], "functionCode": "department_resume", "functionName": "部门启用", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "closeByEnterpriseSource": [7, 9, 10], "functionCode": "department_delete", "functionName": "部门删除", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "permission_query_role", "functionName": "按角色查看", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": false}, {"supportVersionList": ["*"], "functionCode": "permission_role_employee_manage", "functionName": "角色的员工管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": false}, {"supportVersionList": ["*"], "functionCode": "permission_role_create", "functionName": "角色新建", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": false}, {"supportVersionList": ["*"], "functionCode": "permission_role_modify", "functionName": "角色编辑操作", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": false}, {"supportVersionList": ["*"], "functionCode": "permission_role_delete", "functionName": "角色删除", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": false}, {"supportVersionList": ["*"], "functionCode": "permission_query_employee", "functionName": "按员工查看", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": false}, {"supportVersionList": ["*"], "functionCode": "permission_employee_role_manage", "functionName": "员工的角色管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": false}, {"supportVersionList": ["*"], "functionCode": "permission_business_role_query", "functionName": "查看", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "permission_business_role_manage", "functionName": "分配", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "permission_business_role_export", "functionName": "导出", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "permission_bussiness_function_role_setting", "functionName": "角色权限设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "permission_bussiness_function_approve_setting", "functionName": "审批权限设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-usergroup", "functionName": "用户组管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "dingtalk_standardpro_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-sysobject", "functionName": "预设对象管理", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "dingtalk_standardpro_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-myobject", "functionName": "自定义对象管理", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-generaloptionset", "functionName": "通用选项集", "supportEnterpriseIds": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-businessplugin", "isEditable": true, "functionName": "业务插件管理", "supportEnterpriseAccounts": "*", "appId": "facishare-system", "isShow": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-globalfieldalign", "functionName": "全局字段布局配置", "supportEnterpriseIds": "${variables_udobj_gray_product.gray_field_align_comma_gray_ei}", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-mytags", "functionName": "对象名称设置", "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-templatemanage", "functionName": "对象模板管理", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-smartforms", "functionName": "智能表单", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-labelmanage", "functionName": "标签管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "dingtalk_standardpro_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-approval", "functionName": "审批流程管理", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "dingtalk_standardpro_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-businessflow", "functionName": "业务流程管理", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "dingtalk_standardpro_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-workflow", "functionName": "工作流管理", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "enterprise_edition", "dingtalk_standardpro_edition", "kdweibo_edition", "jdy_edition"], "functionCode": "crmmanage/=/module-saleaction2", "functionName": "阶段推进流程", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "crmmanage/=/module-saleaction", "functionName": "商机销售流程", "supportModuleList": ["opportunity"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "dingtalk_standardpro_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-batchchangeapprover", "functionName": "流程待办迁移", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "data_permission_base_data", "functionName": "基础数据权限", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "data_permission_data_share", "functionName": "数据共享", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "data_permission_related_team", "functionName": "相关团队数据权限", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": false}, {"supportVersionList": ["*"], "functionCode": "data_permission_temporary", "functionName": "临时权限", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "data_permission_department_data", "functionName": "部门内数据权限", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "data_permission_others", "functionName": "其他", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["wechat_strengthen_edition", "strengthen_edition", "wechat_standard_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "standardpro_edition", "kis_edition", "basic_edition", "dingtalk_standardpro_edition", "dingtalk_standard_edition", "dingtalk_strengthen_edition"], "functionCode": "crmmanage/=/module-clue", "functionName": "线索和线索池管理", "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition", "dingtalk_standard_edition", "dingtalk_strengthen_edition"], "functionCode": "crmmanage/=/module-customerrule", "functionName": "客户和公海管理", "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-opportunity", "functionName": "商机2.0管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "crmmanage/=/module-oppforecastobj", "functionName": "预测规则设置", "supportModuleList": ["business_opportunity_forecast_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["wechat_strengthen_edition", "strengthen_edition", "wechat_standard_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-partnerobj", "functionName": "合作伙伴管理", "supportModuleList": ["prm_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-objmap", "functionName": "对象映射规则", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-sysemail", "functionName": "邮件管理", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-quote", "functionName": "报价单管理", "supportModuleList": ["quote_control_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-orderrule", "functionName": "销售订单/退货单规则", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-goalruleobj", "functionName": "目标规则", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "kdweibo_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-price-setting", "functionName": "价目表设置", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-salesbrief", "functionName": "销售简报设置", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-salestype", "functionName": "销售记录设置", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["wechat_strengthen_edition", "strengthen_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-servicetype", "functionName": "服务记录类型", "supportEnterpriseAccounts": "*", "editable": true, "closeEnterpriseIds": "*", "appId": "facishare-system", "show": true}, {"supportVersionList": ["wechat_strengthen_edition", "strengthen_edition", "agent_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-servicesearch", "functionName": "服务人员查询类型", "supportEnterpriseAccounts": "*", "editable": true, "closeEnterpriseIds": "*", "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-globalvar", "functionName": "全局变量", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "crmmanage/=/module-acountmanage", "functionName": "客户账户管理", "supportModuleList": ["customer_account_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "crmmanage/=/module-statement", "functionName": "对账单设置", "supportModuleList": ["statement_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "crmmanage/=/module-sales-promotion", "functionName": "促销设置", "supportModuleList": ["promotion_app"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "advanced_dealer_edition", "agent_edition", "enterprise_edition", "office_edition", "kdweibo_edition", "jdy_edition"], "functionCode": "crmmanage/=/module-shiporder", "functionName": "进销存管理", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-da<PERSON>qi", "functionName": "打分器规则设置", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-integral", "functionName": "行为积分", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-intelligent-assessment", "functionName": "线索智能打分管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-exchangegoods", "functionName": "退换货管理", "supportModuleList": ["marketing_strategy_stan_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-marketingattribution", "functionName": "营销归因设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-business", "functionName": "工商信息设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-businessconfig", "functionName": "业务规则配置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-fastsale", "functionName": "快消业务插件", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-businesslink", "functionName": "经销商管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-marketingactivity", "functionName": "市场活动管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "79337,78060,83234,84069,1,85718", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "crmmanage/=/module-cpq", "functionName": "CPQ管理", "supportModuleList": ["cpq_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "crmmanage/=/module-hospital", "functionName": "医疗信息设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["wechat_strengthen_edition", "strengthen_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-casesobjconfig", "functionName": "工单管理", "supportEnterpriseAccounts": "*", "editable": false, "closeEnterpriseIds": "*", "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "crmmanage/=/module-accountreceivable", "functionName": "应收管理", "supportModuleList": ["accounts_receivable_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-myfunction", "functionName": "自定义函数", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "customcomponent/=/module-component", "functionName": "自定义组件", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-custommenuitem", "functionName": "自定义菜单项", "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "ico-sale", "functionName": "数据维护", "supportModuleList": ["standard_data_sync_app", "ding_ding_crm_data_sync_app", "sap_data_sync_app", "k3_data_sync_app", "u8_data_sync_app", "u8_eai_data_sync_app", "zhi<PERSON>_huabao_data_sync_app", "uc_shenma_data_sync_app", "sogo_xiansuocrm_data_sync_app", "kua<PERSON><PERSON>_xiansuocrm_data_sync_app", "db_data_sync_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-customer", "functionName": "全部客户", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "datamigrationtool/initialization", "functionName": "迁移工具", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-recycle", "functionName": "回收站", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-cleanunit", "functionName": "清洗工具", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-rest", "functionName": "CRM初始化", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "datamaintenancetools/Initializationandbackup", "functionName": "数据初始化与备份", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "sandbox_manager", "functionName": "沙盒管理", "supportModuleList": ["developer_sandbox"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "environment/changesetmanage/inbound", "functionName": "入站更改集", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "change_set_deployment_rules", "functionName": "部署规则", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "environment/changesetmanage/outbound", "functionName": "出站更改集", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "environment/deploymanage/deploystatus", "functionName": "部署状态", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-openapi", "functionName": "OPEN API", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-objectapi", "functionName": "对象及字段API", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "crmmanage/=/module-schedulertask", "functionName": "计划任务", "supportModuleList": ["schedule_task"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-translate-workbench", "functionName": "翻译工作台", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["yun<PERSON><PERSON><PERSON>_weixin", "basic_edition"], "functionCode": "crmmanage/=/module-membermanage", "functionName": "会员管理", "supportModuleList": ["marketing_strategy_pro_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["yun<PERSON><PERSON><PERSON>_weixin", "basic_edition"], "functionCode": "crmmanage/=/module-callcenter", "functionName": "呼叫中心", "supportModuleList": ["call_center_app", "customerservice_pro_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-esignature", "functionName": "电子签章", "supportModuleList": ["*"], "supportEnterpriseAccounts": "fs", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["yun<PERSON><PERSON><PERSON>_weixin", "basic_edition"], "functionCode": "crmmanage/=/module-order", "functionName": "订货通管理", "supportModuleList": ["order_plus_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "dds/ddsmanage", "functionName": "渠道通路管理", "supportModuleList": ["service_dds_parent_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition"], "functionCode": "crmmanage/=/module-ddsreportdata", "functionName": "渠道数据上报", "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["wechat_strengthen_edition", "strengthen_edition", "enterprise_edition", "standardpro_edition", "basic_edition"], "functionCode": "crmmanage/=/module-smsmanage", "functionName": "短信服务", "supportModuleList": ["marketing_strategy_pro_app", "marketing_strategy_stan_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-logistics", "functionName": "物流查询服务", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "enterprise_interconnect_setting", "functionName": "企业互联", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "enterpriseinterconnect/account", "functionName": "互联帐号", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "advanced_dealer_edition", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "enterpriseinterconnect/outerroles", "functionName": "互联角色", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "enterpriseinterconnect/eiapp", "functionName": "互联应用", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "enterpriseinterconnect/eisetting", "functionName": "互联设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "enterprise_interconnect_custom", "functionName": "客户互联", "supportModuleList": ["interconnect_app_basic_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "mp/wechat", "functionName": "微信公众号", "supportEnterpriseAccounts": "*", "menuIcon": "wechatpublica<PERSON>unt", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "mp/miniprogram", "functionName": "微信小程序", "supportModuleList": ["interconnect_app_basic_app"], "supportEnterpriseAccounts": "*", "menuIcon": "mp_miniprogram_new", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [""], "functionCode": "jumpmanage/=/module-manufacturer", "functionName": "厂商门户管理", "supportModuleList": ["interconnect_app_basic_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [], "functionCode": "prmmanage/=/module-manage", "functionName": "代理通业务管理", "supportModuleList": ["prm_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "business_customization/appsetting/menus", "functionName": "APP菜单自定义管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "business_customization/appsetting/tmp", "functionName": "APP主页模版管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "business_customization/appsetting/start", "functionName": "APP启动图管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-menumanage", "functionName": "WEB菜单模板管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "closeEnterpriseIds": "${variables_crm_migrate_tenants.goNewCrmEnterpriseIds}", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-indexmanage", "functionName": "WEB首页管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "closeEnterpriseIds": "${variables_crm_migrate_tenants.goNewCrmEnterpriseIds}", "show": true}, {"supportVersionList": ["*"], "functionCode": "jumpmanage/=/module-objectlist", "functionName": "对象列表页自定义", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "paasappsetting/=/mainnav", "functionName": "Web端主导航", "supportModuleList": ["*"], "editable": false, "supportEnterpriseIds": "*", "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "wechat_standardpro_edition", "enterprise_edition", "standardpro_edition"], "functionCode": "jumpmanage/=/module-webcustomerlayout", "functionName": "自定义页面", "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "enterprise_logs_query_all", "functionName": "查询所有日志", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "enterprise_logs_query_login", "functionName": "查询登录日志", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": [""], "functionCode": "enterprise_logs_abnormal_login", "functionName": "查询异常登录", "supportModuleList": [""], "supportEnterpriseAccounts": "", "editable": false, "appId": "facishare-system", "show": false}, {"supportVersionList": ["manufacture_dealer_edition", "dealer_edition", "wechat_standard_edition", "wechat_standardpro_edition", "standardpro_edition", "kis_edition", "wechat_strengthen_edition", "strengthen_edition", "promotion_sales_edition", "yun<PERSON><PERSON><PERSON>_weixin", "agent_edition", "enterprise_edition", "kdweibo_edition", "jdy_edition", "basic_edition"], "functionCode": "crmmanage/=/module-crmlog", "functionName": "CRM日志", "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "enterpriselogs/iorecords", "functionName": "导入导出记录", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["manufacture_dealer_edition", "wechat_strengthen_edition", "dealer_edition", "strengthen_edition", "promotion_sales_edition", "enterprise_edition"], "functionCode": "enterprise/tradeconfigguide", "functionName": "行业套件管理", "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "paaSApplication", "functionName": "Paas应用", "supportModuleList": ["*"], "editable": false, "supportEnterpriseIds": "*", "appId": "facishare-system", "show": false}, {"supportVersionList": ["*"], "functionCode": "organization_observer", "functionName": "部门观察者", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-functionlog", "functionName": "函数日志", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "relation_change_set_manage", "functionName": "部署互联更改集", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "outbound_config_package_manage", "functionName": "配置包管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "inbound_config_package_manage", "functionName": "配置包安装", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "relation_change_set_log", "functionName": "互联更改集部署状态", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "relation_change_set_downstream_log", "functionName": "下游互联部署状态", "supportModuleList": ["*"], "supportEnterpriseAccounts": "74114", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-tpm", "functionName": "营销活动管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-asyncfunction", "functionName": "异步函数监控", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "entservice", "functionName": "企业服务号", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "mp/inservicehome", "functionName": "服务号首页", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "mp/inservicemanage", "functionName": "服务号管理", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-gdpr", "functionName": "合规性设置", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-workflowhistory", "functionName": "工作流执行日志", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "customplugins/=/module-plugins", "functionName": "自定义插件", "supportModuleList": ["*"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "setting-oauth2-config", "functionName": "认证提供商", "supportModuleList": ["*"], "supportEnterpriseIds": "*", "editable": false, "appId": "facishare-system", "show": true}, {"functionCode": "crmmanage/=/module-employeeusageanalysis", "supportModuleList": ["user_usage_stats_app"], "functionName": "员工使用分析", "supportEnterpriseAccounts": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "crmmanage/=/module-procurement", "functionName": "招投标管理", "supportModuleList": ["procurement_customer_acquisition_app"], "supportEnterpriseAccounts": "*", "editable": false, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "modeljar", "functionName": "ModelJar管理", "supportModuleList": ["*"], "supportEnterpriseIds": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "codesecurity", "functionName": "代码安全设置", "supportModuleList": ["*"], "supportEnterpriseIds": "*", "editable": true, "appId": "facishare-system", "show": true}, {"supportVersionList": ["*"], "functionCode": "devjarpackage", "functionName": "开发Jar包管理", "supportEnterpriseAccounts": "*", "supportModuleList": [], "appId": "facishare-system"}, {"supportVersionList": ["*"], "functionCode": "eventlistener", "functionName": "事件监听管理", "supportEnterpriseAccounts": "*", "supportModuleList": [], "appId": "facishare-system"}]