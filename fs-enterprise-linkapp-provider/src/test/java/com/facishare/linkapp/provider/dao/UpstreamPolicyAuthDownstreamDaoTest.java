package com.facishare.linkapp.provider.dao;

import com.facishare.enterprise.common.result.Result;
import com.facishare.linkapp.api.service.HttpUpstreamService;
import com.facishare.linkapp.provider.BaseTest;
import com.google.common.collect.Lists;
import java.util.List;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class UpstreamPolicyAuthDownstreamDaoTest extends BaseTest {
    @Autowired
    HttpUpstreamService upstreamService;

    @Test
    public void test1() {
        List<Long> outerTenantIds = Lists.newArrayList(200072970L);
        Result<Boolean> booleanResult = upstreamService.batchAddUpstreamPolicyAuthDownstream("21465f28-aa13-429a-a607-da018245e03c444", outerTenantIds);
        System.out.println(booleanResult);
    }

    @Test
    public void test2() {
        Result<Boolean> booleanResult = upstreamService.deleteLinkAppDataAuthPolicysByUpstreamAndPolicyId("fsceshi020", "FSAID_127a3981", "21465f28-aa13-429a-a607-da018245e03c444");
        System.out.println(booleanResult);
    }
}
