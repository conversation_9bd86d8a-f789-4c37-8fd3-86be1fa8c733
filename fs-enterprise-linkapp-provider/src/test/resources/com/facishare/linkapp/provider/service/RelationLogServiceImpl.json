[
	{
		
		"callDatas":{
			"com.fxiaoke.enterpriserelation.objrest.service.PublicEmployeeObjService#listByCondition":[
				{
					
					"args":[
						{
							"@type":"com.facishare.paas.appframework.core.model.ServiceContext",
							"requestContext":{
								"attributes":{
									"@type":"java.util.HashMap"
								},
								"batch":true,
								"lang":{
									"value":"zh-CN"
								},
								"paramsIdempotent":false,
								"stackDepth":0,
								"tenantId":"82680",
								"user":{
									"tenantId":"82680",
									"userId":"1001"
								}
							},
							"serviceMethod":"",
							"serviceName":""
						},
						{
							"@type":"com.fxiaoke.enterpriserelation.objrest.arg.ListPublicEmployeeObjByConditionArg",
							"includeInvalid":false,
							"outerTenantIds":[300017149],
							"outerUids":[300098574,300385115,300385115]
						}
					],
					"result":{
						"@type":"com.fxiaoke.enterpriserelation.objrest.result.ListByConditionResult",
						"objectDatas":[
							{
								"@type":"java.util.LinkedHashMap",
								"data_manager":false,
								"main_er_department":[
									"654e155a9b15a200010c0539"
								],
								"er_language":"zh-CN",
								"type":"1",
								"outer_tenant_id":"300017149",
								"total_num":1,
								"searchAfterId":[
									"*************",
									"300385115"
								],
								"name_spell":"ddd",
								"lock_status":"1",
								"package":"CRM",
								"create_time":1690865115251,
								"er_time_zone":"Asia/Shanghai",
								"version":"5",
								"created_by":[
									"-10000"
								],
								"data_own_department":[
									"999999"
								],
								"quota_expired":false,
								"employee_id":"1001",
								"name":"ddd",
								"outer_uid":"300385115",
								"_id":"300385115",
								"tenant_id":"82680",
								"outer_tenant_id__relation_ids":"300017149",
								"is_deleted":false,
								"relation_owner":false,
								"object_describe_api_name":"PublicEmployeeObj",
								"owner":[
									"-10000"
								],
								"last_modified_time":*************,
								"life_status":"under_review",
								"mobile":"***********",
								"last_modified_by":[
									"-10000"
								],
								"record_type":"default__c",
								"register_type":"1",
								"active_state":"1"
							}
						],
						"total":1
					}
				}
			],
			"com.facishare.linkapp.provider.service.FxiaokeEmployeeAssociationServiceImpl#getFsAccountByOuter":[
				{
					
					"args":[
						300385115L
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":{
							"@type":"com.facishare.global.rest.result.FsAccountVo",
							"ea":"82680",
							"employeeId":1001,
							"outerTenantId":300017149L
						},
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-linkapp-provider/67049acb788ceb0cd051a5d5"
					}
				}
			],
			"com.facishare.converter.EIEAConverter#enterpriseAccountToId":[
				{
					
					"args":[
						"82680"
					],
					"result":82680
				}
			],
			"com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService#createRelationLog":[
				{
					
					"args":[
						{
							"@type":"com.facishare.paas.appframework.core.model.ServiceContext",
							"requestContext":{
								"attributes":{
									"@type":"java.util.HashMap"
								},
								"batch":true,
								"lang":{
									"value":"zh-CN"
								},
								"paramsIdempotent":false,
								"stackDepth":0,
								"tenantId":"82680",
								"user":{
									"tenantId":"82680",
									"userId":"1001"
								}
							},
							"serviceMethod":"",
							"serviceName":""
						},
						{
							"@type":"com.fxiaoke.enterpriserelation.objrest.arg.CreateRelationLogArg",
							"contentSourceVars":{
								"@type":"java.util.HashMap",
								"2":{
									"@type":"java.util.HashMap",
									"app_id":Set[
										"FSAID_11490c84"
									]
								}
							},
							"contentVarMap":{
								"@type":"java.util.HashMap",
								"1":"ddd",
								"2":"订货通"
							},
							"detailSourceVar":{
								"@type":"java.util.HashMap",
								"1":{
									"@type":"java.util.HashMap",
									"app_id":Set[
										"FSAID_11490c84"
									]
								}
							},
							"detailVarMap":{
								"@type":"java.util.HashMap",
								"1":"订货通",
								"2":"null、ddd"
							},
							"templateKey":"appAdminChangeLog",
							"type":7
						}
					],
					"result":{
						"@type":"com.fxiaoke.enterpriserelation.objrest.result.Result",
						"errCode":0,
						"errMsg":"success",
						"traceId":"fs-enterprise-linkapp-provider/67049acb788ceb0cd051a5d5"
					}
				}
			],
			"com.facishare.paas.support.service.api.PaasServiceProxySupport#createServiceContext":[
				{
					
					"args":[
						"82680",
						"1001"
					],
					"result":{
						"@type":"com.facishare.paas.appframework.core.model.ServiceContext",
						"requestContext":{
							"attributes":{
								"@type":"java.util.HashMap"
							},
							"batch":true,
							"lang":{
								"value":"zh-CN"
							},
							"paramsIdempotent":false,
							"stackDepth":0,
							"tenantId":"82680",
							"user":{
								"tenantId":"82680",
								"userId":"1001"
							}
						},
						"serviceMethod":"",
						"serviceName":""
					}
				}
			],
			"com.facishare.linkapp.provider.service.FxiaokeEmployeeAssociationServiceImpl#batchGetOuterAccountByFs":[
				{
					
					"args":[
						[
							{
								"@type":"com.facishare.global.rest.data.FsEmployeeVo",
								"employeeId":1001,
								"enterpriseAccount":"82680"
							},
							{
								"@type":"com.facishare.global.rest.data.FsEmployeeVo",
								"employeeId":1000,
								"enterpriseAccount":"82680"
							},
							{
								"@type":"com.facishare.global.rest.data.FsEmployeeVo",
								"employeeId":1001,
								"enterpriseAccount":"82680"
							}
						]
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":[
							{
								"@type":"com.facishare.global.rest.data.OuterAccountWithFsEmployeeVo",
								"employeeId":1001,
								"enterpriseAccount":"82680",
								"outerTenantId":300017149L,
								"outerUid":300385115L
							},
							{
								"@type":"com.facishare.global.rest.data.OuterAccountWithFsEmployeeVo",
								"employeeId":1000,
								"enterpriseAccount":"82680",
								"outerTenantId":300017149L,
								"outerUid":300098574L
							},
							{
								"@type":"com.facishare.global.rest.data.OuterAccountWithFsEmployeeVo",
								"employeeId":1001,
								"enterpriseAccount":"82680",
								"outerTenantId":300017149L,
								"outerUid":300385115L
							}
						],
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-linkapp-provider/67049acb788ceb0cd051a5d5"
					}
				}
			]
		},
		"id":1,
		"method":"com.facishare.linkapp.provider.service.RelationLogServiceImpl#createAppAdminChangeLog",
		"methodData":{
			"args":[
				"82680",
				1001,
				"订货通",
				"FSAID_11490c84",
				[
					1001,
					1000
				]
			],
			"result":{
				"@type":"com.facishare.enterprise.common.result.Result",
				"errCode":0,
				"errMsg":"成功"
			}
		}
	}
]