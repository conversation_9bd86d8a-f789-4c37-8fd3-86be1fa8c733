[
	{
		
		"callDatas":{
			"com.facishare.linkapp.provider.dao.LinkAppDao#getById":[
				{
					
					"args":[
						"FSAID_11490c84"
					],
					"result":{
						"@type":"com.facishare.linkapp.api.result.LinkAppData",
						"addManagerTipsUrl":"",
						"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
						"appManagerUrl":"",
						"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
						"appUrl":"ava://uipaas_custom/pages/crosscustom/index?{\"upstreamEa\":\"${fs_upstream_ea}\", \"appId\":\"${fs_appId}\"}",
						"appVisibility":true,
						"associationCrmObject":true,
						"authByEa":false,
						"authType":0,
						"createTime":1493794175L,
						"dataAuthType":1,
						"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
						"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
						"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
						"enabledByDefault":1,
						"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
						"id":"FSAID_11490c84",
						"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
						"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
						"introductionI18nKey":"eip.app.introduction_dht",
						"managerIcon":"",
						"name":"订货通",
						"nameI18nKey":"eip.app.name_dht",
						"openValidatePostUrl":"",
						"showPaasPage":1,
						"supportExtendObject":true,
						"supportGuestor":false,
						"supportIdentityTypes":"1",
						"supportMapperApiNamesNew":"AccountObj",
						"supportNoFsAccount":false,
						"supportRegisterTypes":"3",
						"supportRoleExt":true,
						"supportSetManager":false,
						"type":1,
						"unVisibleInManager":false,
						"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
						"updateTime":1698811093L,
						"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
						"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
					}
				}
			],
			"com.facishare.linkapp.provider.manager.LinkAppEnterpriseTypeAssociationManager#listSupportMapperApiNamesByLinkAppId":[
				{
					
					"args":[
						"FSAID_11490c84"
					],
					"result":Set[
						"AccountObj"
					]
				}
			]
		},
		"id":1,
		"method":"com.facishare.linkapp.provider.service.LinkAppServiceImpl#getLinkApp",
		"methodData":{
			"args":[
				"FSAID_11490c84"
			],
			"result":{
				"@type":"com.facishare.enterprise.common.result.Result",
				"data":{
					"@type":"com.facishare.linkapp.api.result.LinkAppData",
					"addManagerTipsUrl":"",
					"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
					"appManagerUrl":"",
					"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
					"appUrl":"ava://uipaas_custom/pages/crosscustom/index?{\"upstreamEa\":\"${fs_upstream_ea}\", \"appId\":\"${fs_appId}\"}",
					"appVisibility":true,
					"associationCrmObject":true,
					"authByEa":false,
					"authType":0,
					"createTime":1493794175L,
					"dataAuthType":1,
					"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
					"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
					"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
					"enabledByDefault":1,
					"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
					"id":"FSAID_11490c84",
					"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
					"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
					"introductionI18nKey":"eip.app.introduction_dht",
					"managerIcon":"",
					"name":"订货通",
					"nameI18nKey":"eip.app.name_dht",
					"openValidatePostUrl":"",
					"showPaasPage":1,
					"supportExtendObject":true,
					"supportGuestor":false,
					"supportIdentityTypes":"1",
					"supportMapperApiNames":Set[
						"AccountObj"
					],
					"supportMapperApiNamesNew":"AccountObj",
					"supportNoFsAccount":false,
					"supportRegisterTypes":"3",
					"supportRoleExt":true,
					"supportSetManager":false,
					"type":1,
					"unVisibleInManager":false,
					"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
					"updateTime":1698811093L,
					"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
					"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
				},
				"errCode":0,
				"errMsg":"成功",
				"traceId":"fs-enterprise-linkapp-provider_f01bb01a-4648-4eb5-8148-15761060dba8"
			}
		}
	}
]