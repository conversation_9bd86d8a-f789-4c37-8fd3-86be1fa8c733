[
	{
		
		"callDatas":{
			"com.facishare.linkapp.provider.service.FxiaokeEmployeeAssociationServiceImpl#getFsAccountByOuter":[
				{
					
					"args":[
						300101454L
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":{
							"@type":"com.facishare.global.rest.result.FsAccountVo",
							"ea":"71570",
							"employeeId":1198,
							"outerTenantId":200071570L
						},
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				}
			],
			"com.facishare.converter.EIEAConverter#enterpriseAccountToId":[
				{
					
					"args":[
						"71570"
					],
					"result":71570
				},
				{
					
					"args":[
						"71570"
					],
					"result":71570
				},
				{
					
					"args":[
						"71570"
					],
					"result":71570
				},
				{
					
					"args":[
						"71570"
					],
					"result":71570
				},
				{
					
					"args":[
						"71570"
					],
					"result":71570
				},
				{
					
					"args":[
						"71570"
					],
					"result":71570
				},
				{
					
					"args":[
						"71570"
					],
					"result":71570
				}
			],
			"com.fxiaoke.appcenter.restapi.service.NewComponentService#checkNewStatus":[
				{
					
					"args":[
						{
							"@type":"com.fxiaoke.appcenter.restapi.common.HeaderObj",
							"x-fs-ei":"71570"
						},
						{
							"@type":"com.fxiaoke.appcenter.restapi.arg.CheckNewStatusArg",
							"arg1":{
								"enterpriseAccount":"71570",
								"userId":1198
							},
							"arg2":["UP:FSAID_11490c84","UP:FSAID_11490c82","UP:FSAID_11490c83","UP:FSAID_11490d40","UP:FSAID_11491009","UP:FSAID_11490d9e"],
							"version":"1.0"
						}
					],
					"result":{
						"@type":"com.fxiaoke.appcenter.restapi.common.BaseResult",
						"errCode":0,
						"errDescription":"成功",
						"errMessage":"success",
						"result":[
							2,
							2,
							2,
							2,
							2,
							2
						]
					}
				}
			],
			"com.github.mybatis.mapper.ICrudMapper#findBySelective":[
				{
					
					"args":[
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"status":1,
							"upstreamEa":"71570",
							"upstreamFsUserId":1198
						}
					],
					"result":[
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2022-02-11 17:19:26",
							"id":"106184",
							"linkAppId":"FSAID_11490c84",
							"role":3,
							"status":1,
							"updateTime":"2022-02-11 17:19:26",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:50:57",
							"id":"146609",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:50:57",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:50:57",
							"id":"146615",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:50:57",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:50:57",
							"id":"146621",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:50:57",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:50:57",
							"id":"146627",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:50:57",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:54:52",
							"id":"146725",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:54:52",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:54:52",
							"id":"146731",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:54:52",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:54:52",
							"id":"146737",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:54:52",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:54:52",
							"id":"146743",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:54:52",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:55:02",
							"id":"146749",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:55:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:55:02",
							"id":"146755",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:55:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:55:02",
							"id":"146761",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:55:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 15:55:02",
							"id":"146767",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 15:55:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 16:13:38",
							"id":"147005",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 16:13:38",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 16:13:38",
							"id":"147011",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 16:13:38",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 16:13:38",
							"id":"147017",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 16:13:38",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 16:13:38",
							"id":"147023",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 16:13:38",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 16:55:45",
							"id":"151377",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 16:55:45",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 16:55:45",
							"id":"151383",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 16:55:45",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 16:55:46",
							"id":"151389",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 16:55:46",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 16:55:46",
							"id":"151395",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 16:55:46",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:08:15",
							"id":"151745",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:08:15",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:08:15",
							"id":"151751",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:08:15",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:08:15",
							"id":"151757",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:08:15",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:08:15",
							"id":"151763",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:08:15",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:19:02",
							"id":"152362",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:19:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:19:02",
							"id":"152368",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:19:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:19:02",
							"id":"152374",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:19:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:19:02",
							"id":"152380",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:19:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:20:26",
							"id":"152442",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:20:26",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:20:26",
							"id":"152448",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:20:26",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:20:26",
							"id":"152454",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:20:26",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:20:26",
							"id":"152460",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:20:26",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:21:31",
							"id":"152498",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:21:31",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:21:31",
							"id":"152504",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:21:31",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:21:31",
							"id":"152510",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:21:31",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:21:31",
							"id":"152516",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:21:31",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:51:24",
							"id":"153452",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:51:24",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:51:24",
							"id":"153458",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:51:24",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:51:24",
							"id":"153464",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:51:24",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:51:24",
							"id":"153470",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:51:24",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:51:53",
							"id":"153476",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:51:53",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:51:53",
							"id":"153482",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:51:53",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:51:53",
							"id":"153488",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:51:53",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 17:51:53",
							"id":"153494",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 17:51:53",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 18:52:22",
							"id":"155820",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 18:52:22",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 18:52:22",
							"id":"155826",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 18:52:22",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 18:52:22",
							"id":"155832",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 18:52:22",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-15 18:52:22",
							"id":"155838",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-15 18:52:22",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 09:57:01",
							"id":"157867",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 09:57:01",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 09:57:01",
							"id":"157873",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 09:57:01",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 09:57:01",
							"id":"157879",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 09:57:01",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 09:57:01",
							"id":"157885",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 09:57:01",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 11:23:57",
							"id":"159985",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 11:23:57",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 11:23:57",
							"id":"159991",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 11:23:57",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 11:23:57",
							"id":"159997",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 11:23:57",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 11:23:57",
							"id":"160003",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 11:23:57",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 14:29:40",
							"id":"160789",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 14:29:40",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 14:29:40",
							"id":"160795",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 14:29:40",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 14:29:40",
							"id":"160801",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 14:29:40",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 14:29:40",
							"id":"160807",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 14:29:40",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 14:59:25",
							"id":"161386",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 14:59:25",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 14:59:25",
							"id":"161392",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 14:59:25",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 14:59:25",
							"id":"161398",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 14:59:25",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 14:59:25",
							"id":"161404",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 14:59:25",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 15:07:02",
							"id":"161486",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 15:07:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 15:07:02",
							"id":"161492",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 15:07:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 15:07:02",
							"id":"161498",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 15:07:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 15:07:02",
							"id":"161504",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 15:07:02",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 15:54:00",
							"id":"162358",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 15:54:00",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 15:54:00",
							"id":"162364",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 15:54:00",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 15:54:00",
							"id":"162370",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 15:54:00",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 15:54:00",
							"id":"162376",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 15:54:00",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:01:00",
							"id":"162458",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:01:00",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:01:00",
							"id":"162464",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:01:00",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:01:00",
							"id":"162470",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:01:00",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:01:00",
							"id":"162476",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:01:00",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:01:15",
							"id":"162494",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:01:15",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:01:15",
							"id":"162500",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:01:15",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:01:15",
							"id":"162506",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:01:15",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:01:15",
							"id":"162512",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:01:15",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:21:41",
							"id":"163006",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:21:41",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:21:41",
							"id":"163012",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:21:41",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:21:41",
							"id":"163018",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:21:41",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:21:41",
							"id":"163024",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:21:41",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:44:11",
							"id":"163294",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:44:11",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:44:11",
							"id":"163300",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:44:11",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:44:11",
							"id":"163306",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:44:11",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:44:11",
							"id":"163312",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:44:11",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:45:26",
							"id":"163382",
							"linkAppId":"FSAID_11490c82",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:45:26",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:45:26",
							"id":"163388",
							"linkAppId":"FSAID_11490c83",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:45:26",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:45:26",
							"id":"163394",
							"linkAppId":"FSAID_11490d40",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:45:26",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2023-06-16 16:45:26",
							"id":"163400",
							"linkAppId":"FSAID_11491009",
							"role":1,
							"status":1,
							"updateTime":"2023-06-16 16:45:26",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						},
						{
							"@type":"com.facishare.linkapp.provider.model.entity.UpstreamEmployeeLinkAppRole",
							"createTime":"2024-03-30 16:36:56",
							"id":"177974",
							"linkAppId":"FSAID_11490d9e",
							"role":1,
							"status":1,
							"updateTime":"2024-03-30 16:36:56",
							"upstreamEa":"71570",
							"upstreamFsUserId":1198,
							"version":1L
						}
					]
				}
			],
			"com.facishare.global.rest.service.GlobalService#listAllUpstreamEasByOuterUid":[
				{
					
					"args":[
						{
							"@type":"com.facishare.global.rest.common.HeaderObj",
							"x-fs-ei":1,
							"env":"fs-enterprise-relation-biz"
						},
						{
							"@type":"com.facishare.global.rest.arg.DownstreamOuterUidArg",
							"downstreamOuterUid":300101454L
						}
					],
					"result":{
						"@type":"com.facishare.global.rest.result.Result",
						"code":0,
						"data":[
							
						]
					}
				}
			],
			"com.facishare.linkapp.api.service.HttpUpstreamService#isLinkAppOpen":[
				{
					
					"args":[
						"FSAID_11490c84",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c82",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490c83",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d40",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11491009",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				},
				{
					
					"args":[
						"FSAID_11490d9e",
						"71570"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":true,
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
					}
				}
			],
			"com.facishare.linkapp.provider.manager.LinkAppManager#setLinkAppVos":[
				{
					
					"args":[
						[
							
						],
						"FSAID_11490c84",
						true
					],
					"result":[
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"",
							"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":true,
							"authByEa":false,
							"authType":0,
							"createTime":1493794175L,
							"dataAuthType":1,
							"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
							"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
							"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
							"enabledByDefault":1,
							"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c84",
							"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
							"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
							"introductionI18nKey":"eip.app.introduction_dht",
							"managerIcon":"",
							"name":"订货通",
							"nameI18nKey":"eip.app.name_dht",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":true,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":false,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
							"updateTime":1698811093L,
							"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
							"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
						}
					]
				},
				{
					
					"args":[
						[
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
								"appManagerUrl":"",
								"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"appUrl":"",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":1493794175L,
								"dataAuthType":1,
								"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
								"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
								"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
								"enabledByDefault":1,
								"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c84",
								"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
								"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
								"introductionI18nKey":"eip.app.introduction_dht",
								"managerIcon":"",
								"name":"订货通",
								"nameI18nKey":"eip.app.name_dht",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3,2,1",
								"supportRoleExt":true,
								"supportSetManager":false,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
								"updateTime":1698811093L,
								"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
								"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
							}
						],
						"FSAID_11490c82",
						true
					],
					"result":[
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"",
							"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":true,
							"authByEa":false,
							"authType":0,
							"createTime":1493794175L,
							"dataAuthType":1,
							"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
							"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
							"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
							"enabledByDefault":1,
							"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c84",
							"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
							"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
							"introductionI18nKey":"eip.app.introduction_dht",
							"managerIcon":"",
							"name":"订货通",
							"nameI18nKey":"eip.app.name_dht",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":true,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":false,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
							"updateTime":1698811093L,
							"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
							"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_a71a2994a29342e783446ab8244ef5d8.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appSessionIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"appUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":0,
							"createTime":1493794174L,
							"disablePrompt":"停用后，合作伙伴将不能再看到网盘文件",
							"downstreamMenuJson":"[{\"name\":\"互联网盘\",\"type\":2,\"actionParam\":\"fs://relate/sharedisk?{\\\"appId\\\": \\\"FSAID_11490c82\\\", \\\"ea\\\":\\\"${fs_upstream_ea}\\\"}\"}]",
							"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限1",
							"enabledByDefault":1,
							"icon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c82",
							"internalVisibleRangePrompt":"管理员和对接人默认可使用",
							"introduction":"可以共享文件给合作伙伴。",
							"introductionI18nKey":"eip.app.introduction_hlwp",
							"isDefaultSelect":true,
							"managerIcon":"",
							"name":"互联网盘",
							"nameI18nKey":"eip.app.name_hlwp",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1687758361L,
							"webManagerUrl":"#app/icfiles/send/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
							"webUrl":"#app/icfiles/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c82%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
						}
					]
				},
				{
					
					"args":[
						[
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
								"appManagerUrl":"",
								"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"appUrl":"",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":1493794175L,
								"dataAuthType":1,
								"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
								"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
								"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
								"enabledByDefault":1,
								"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c84",
								"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
								"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
								"introductionI18nKey":"eip.app.introduction_dht",
								"managerIcon":"",
								"name":"订货通",
								"nameI18nKey":"eip.app.name_dht",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3,2,1",
								"supportRoleExt":true,
								"supportSetManager":false,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
								"updateTime":1698811093L,
								"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
								"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_a71a2994a29342e783446ab8244ef5d8.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
								"appManagerUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
								"appSessionIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
								"appUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":0,
								"createTime":1493794174L,
								"disablePrompt":"停用后，合作伙伴将不能再看到网盘文件",
								"downstreamMenuJson":"[{\"name\":\"互联网盘\",\"type\":2,\"actionParam\":\"fs://relate/sharedisk?{\\\"appId\\\": \\\"FSAID_11490c82\\\", \\\"ea\\\":\\\"${fs_upstream_ea}\\\"}\"}]",
								"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限1",
								"enabledByDefault":1,
								"icon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c82",
								"internalVisibleRangePrompt":"管理员和对接人默认可使用",
								"introduction":"可以共享文件给合作伙伴。",
								"introductionI18nKey":"eip.app.introduction_hlwp",
								"isDefaultSelect":true,
								"managerIcon":"",
								"name":"互联网盘",
								"nameI18nKey":"eip.app.name_hlwp",
								"openValidatePostUrl":"",
								"showPaasPage":0,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj,PartnerObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3",
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":1687758361L,
								"webManagerUrl":"#app/icfiles/send/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
								"webUrl":"#app/icfiles/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c82%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
							}
						],
						"FSAID_11490c83",
						true
					],
					"result":[
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"",
							"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":true,
							"authByEa":false,
							"authType":0,
							"createTime":1493794175L,
							"dataAuthType":1,
							"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
							"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
							"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
							"enabledByDefault":1,
							"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c84",
							"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
							"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
							"introductionI18nKey":"eip.app.introduction_dht",
							"managerIcon":"",
							"name":"订货通",
							"nameI18nKey":"eip.app.name_dht",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":true,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":false,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
							"updateTime":1698811093L,
							"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
							"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_a71a2994a29342e783446ab8244ef5d8.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appSessionIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"appUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":0,
							"createTime":1493794174L,
							"disablePrompt":"停用后，合作伙伴将不能再看到网盘文件",
							"downstreamMenuJson":"[{\"name\":\"互联网盘\",\"type\":2,\"actionParam\":\"fs://relate/sharedisk?{\\\"appId\\\": \\\"FSAID_11490c82\\\", \\\"ea\\\":\\\"${fs_upstream_ea}\\\"}\"}]",
							"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限1",
							"enabledByDefault":1,
							"icon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c82",
							"internalVisibleRangePrompt":"管理员和对接人默认可使用",
							"introduction":"可以共享文件给合作伙伴。",
							"introductionI18nKey":"eip.app.introduction_hlwp",
							"isDefaultSelect":true,
							"managerIcon":"",
							"name":"互联网盘",
							"nameI18nKey":"eip.app.name_hlwp",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1687758361L,
							"webManagerUrl":"#app/icfiles/send/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
							"webUrl":"#app/icfiles/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c82%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"appManagerUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
							"appSessionIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_07_0cc5c36d318e4803bd872c8642282de7.png&size=150_150&ea=appCenter",
							"appUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":2,
							"createTime":1493794174L,
							"disablePrompt":"停用后，合作伙伴将不能再看到通知公告",
							"downstreamMenuJson":"[{\"name\":\"通知公告\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/open/wechatconnect/#!/notice/statement?ea=${fs_upstream_ea}\"}]",
							"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限",
							"enabledByDefault":1,
							"icon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c83",
							"internalVisibleRangePrompt":"管理员和对接人默认可使用",
							"introduction":"群发通知给合作伙伴，支持评论，掌握已读状态，对未读联系人一键群发提醒。",
							"introductionI18nKey":"eip.app.introduction_tzgg",
							"isDefaultSelect":true,
							"managerIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"name":"通知公告",
							"nameI18nKey":"eip.app.name_tzgg",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1698291027L,
							"webManagerUrl":"#app/wechatconnect/releasenotice/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
							"webUrl":"#app/wechatconnect/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c83%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
						}
					]
				},
				{
					
					"args":[
						[
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
								"appManagerUrl":"",
								"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"appUrl":"",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":1493794175L,
								"dataAuthType":1,
								"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
								"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
								"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
								"enabledByDefault":1,
								"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c84",
								"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
								"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
								"introductionI18nKey":"eip.app.introduction_dht",
								"managerIcon":"",
								"name":"订货通",
								"nameI18nKey":"eip.app.name_dht",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3,2,1",
								"supportRoleExt":true,
								"supportSetManager":false,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
								"updateTime":1698811093L,
								"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
								"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_a71a2994a29342e783446ab8244ef5d8.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
								"appManagerUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
								"appSessionIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
								"appUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":0,
								"createTime":1493794174L,
								"disablePrompt":"停用后，合作伙伴将不能再看到网盘文件",
								"downstreamMenuJson":"[{\"name\":\"互联网盘\",\"type\":2,\"actionParam\":\"fs://relate/sharedisk?{\\\"appId\\\": \\\"FSAID_11490c82\\\", \\\"ea\\\":\\\"${fs_upstream_ea}\\\"}\"}]",
								"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限1",
								"enabledByDefault":1,
								"icon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c82",
								"internalVisibleRangePrompt":"管理员和对接人默认可使用",
								"introduction":"可以共享文件给合作伙伴。",
								"introductionI18nKey":"eip.app.introduction_hlwp",
								"isDefaultSelect":true,
								"managerIcon":"",
								"name":"互联网盘",
								"nameI18nKey":"eip.app.name_hlwp",
								"openValidatePostUrl":"",
								"showPaasPage":0,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj,PartnerObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3",
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":1687758361L,
								"webManagerUrl":"#app/icfiles/send/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
								"webUrl":"#app/icfiles/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c82%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
								"appManagerUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
								"appSessionIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_07_0cc5c36d318e4803bd872c8642282de7.png&size=150_150&ea=appCenter",
								"appUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":2,
								"createTime":1493794174L,
								"disablePrompt":"停用后，合作伙伴将不能再看到通知公告",
								"downstreamMenuJson":"[{\"name\":\"通知公告\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/open/wechatconnect/#!/notice/statement?ea=${fs_upstream_ea}\"}]",
								"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限",
								"enabledByDefault":1,
								"icon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c83",
								"internalVisibleRangePrompt":"管理员和对接人默认可使用",
								"introduction":"群发通知给合作伙伴，支持评论，掌握已读状态，对未读联系人一键群发提醒。",
								"introductionI18nKey":"eip.app.introduction_tzgg",
								"isDefaultSelect":true,
								"managerIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
								"name":"通知公告",
								"nameI18nKey":"eip.app.name_tzgg",
								"openValidatePostUrl":"",
								"showPaasPage":0,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj,PartnerObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3",
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":1698291027L,
								"webManagerUrl":"#app/wechatconnect/releasenotice/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
								"webUrl":"#app/wechatconnect/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c83%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
							}
						],
						"FSAID_11490d40",
						true
					],
					"result":[
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"",
							"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":true,
							"authByEa":false,
							"authType":0,
							"createTime":1493794175L,
							"dataAuthType":1,
							"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
							"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
							"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
							"enabledByDefault":1,
							"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c84",
							"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
							"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
							"introductionI18nKey":"eip.app.introduction_dht",
							"managerIcon":"",
							"name":"订货通",
							"nameI18nKey":"eip.app.name_dht",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":true,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":false,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
							"updateTime":1698811093L,
							"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
							"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_a71a2994a29342e783446ab8244ef5d8.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appSessionIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"appUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":0,
							"createTime":1493794174L,
							"disablePrompt":"停用后，合作伙伴将不能再看到网盘文件",
							"downstreamMenuJson":"[{\"name\":\"互联网盘\",\"type\":2,\"actionParam\":\"fs://relate/sharedisk?{\\\"appId\\\": \\\"FSAID_11490c82\\\", \\\"ea\\\":\\\"${fs_upstream_ea}\\\"}\"}]",
							"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限1",
							"enabledByDefault":1,
							"icon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c82",
							"internalVisibleRangePrompt":"管理员和对接人默认可使用",
							"introduction":"可以共享文件给合作伙伴。",
							"introductionI18nKey":"eip.app.introduction_hlwp",
							"isDefaultSelect":true,
							"managerIcon":"",
							"name":"互联网盘",
							"nameI18nKey":"eip.app.name_hlwp",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1687758361L,
							"webManagerUrl":"#app/icfiles/send/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
							"webUrl":"#app/icfiles/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c82%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"appManagerUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
							"appSessionIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_07_0cc5c36d318e4803bd872c8642282de7.png&size=150_150&ea=appCenter",
							"appUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":2,
							"createTime":1493794174L,
							"disablePrompt":"停用后，合作伙伴将不能再看到通知公告",
							"downstreamMenuJson":"[{\"name\":\"通知公告\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/open/wechatconnect/#!/notice/statement?ea=${fs_upstream_ea}\"}]",
							"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限",
							"enabledByDefault":1,
							"icon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c83",
							"internalVisibleRangePrompt":"管理员和对接人默认可使用",
							"introduction":"群发通知给合作伙伴，支持评论，掌握已读状态，对未读联系人一键群发提醒。",
							"introductionI18nKey":"eip.app.introduction_tzgg",
							"isDefaultSelect":true,
							"managerIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"name":"通知公告",
							"nameI18nKey":"eip.app.name_tzgg",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1698291027L,
							"webManagerUrl":"#app/wechatconnect/releasenotice/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
							"webUrl":"#app/wechatconnect/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c83%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"appManagerUrl":"",
							"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":0,
							"createTime":1501663657L,
							"dataAuthType":1,
							"disablePrompt":"",
							"downstreamMenuJson":"",
							"enablePrompt":"",
							"enabledByDefault":2,
							"icon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"id":"FSAID_11490d40",
							"internalVisibleRangePrompt":"",
							"introduction":"面向合作伙伴的在线学习考试移动应用",
							"introductionI18nKey":"eip.app.introduction_hbxt",
							"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"name":"伙伴学堂",
							"nameI18nKey":"eip.app.name_hbxt",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1701075707L,
							"webManagerUrl":"#app/train/index/=/trainType-1",
							"webUrl":"#app/manage/eiauth/=/param-%7B%22upstreamEa%22%3A%22${fs_upstream_ea_encode}%22%2C%22url%22%3A%22%23app%2Ftrain%2Findex%2F%3D%2FtrainType-1%2FupstreamEa-${fs_upstream_ea_encode}%22%7D"
						}
					]
				},
				{
					
					"args":[
						[
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
								"appManagerUrl":"",
								"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"appUrl":"",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":1493794175L,
								"dataAuthType":1,
								"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
								"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
								"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
								"enabledByDefault":1,
								"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c84",
								"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
								"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
								"introductionI18nKey":"eip.app.introduction_dht",
								"managerIcon":"",
								"name":"订货通",
								"nameI18nKey":"eip.app.name_dht",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3,2,1",
								"supportRoleExt":true,
								"supportSetManager":false,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
								"updateTime":1698811093L,
								"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
								"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_a71a2994a29342e783446ab8244ef5d8.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
								"appManagerUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
								"appSessionIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
								"appUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":0,
								"createTime":1493794174L,
								"disablePrompt":"停用后，合作伙伴将不能再看到网盘文件",
								"downstreamMenuJson":"[{\"name\":\"互联网盘\",\"type\":2,\"actionParam\":\"fs://relate/sharedisk?{\\\"appId\\\": \\\"FSAID_11490c82\\\", \\\"ea\\\":\\\"${fs_upstream_ea}\\\"}\"}]",
								"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限1",
								"enabledByDefault":1,
								"icon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c82",
								"internalVisibleRangePrompt":"管理员和对接人默认可使用",
								"introduction":"可以共享文件给合作伙伴。",
								"introductionI18nKey":"eip.app.introduction_hlwp",
								"isDefaultSelect":true,
								"managerIcon":"",
								"name":"互联网盘",
								"nameI18nKey":"eip.app.name_hlwp",
								"openValidatePostUrl":"",
								"showPaasPage":0,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj,PartnerObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3",
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":1687758361L,
								"webManagerUrl":"#app/icfiles/send/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
								"webUrl":"#app/icfiles/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c82%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
								"appManagerUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
								"appSessionIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_07_0cc5c36d318e4803bd872c8642282de7.png&size=150_150&ea=appCenter",
								"appUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":2,
								"createTime":1493794174L,
								"disablePrompt":"停用后，合作伙伴将不能再看到通知公告",
								"downstreamMenuJson":"[{\"name\":\"通知公告\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/open/wechatconnect/#!/notice/statement?ea=${fs_upstream_ea}\"}]",
								"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限",
								"enabledByDefault":1,
								"icon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c83",
								"internalVisibleRangePrompt":"管理员和对接人默认可使用",
								"introduction":"群发通知给合作伙伴，支持评论，掌握已读状态，对未读联系人一键群发提醒。",
								"introductionI18nKey":"eip.app.introduction_tzgg",
								"isDefaultSelect":true,
								"managerIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
								"name":"通知公告",
								"nameI18nKey":"eip.app.name_tzgg",
								"openValidatePostUrl":"",
								"showPaasPage":0,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj,PartnerObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3",
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":1698291027L,
								"webManagerUrl":"#app/wechatconnect/releasenotice/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
								"webUrl":"#app/wechatconnect/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c83%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"appManagerUrl":"",
								"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"appUrl":"",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":0,
								"createTime":1501663657L,
								"dataAuthType":1,
								"disablePrompt":"",
								"downstreamMenuJson":"",
								"enablePrompt":"",
								"enabledByDefault":2,
								"icon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"id":"FSAID_11490d40",
								"internalVisibleRangePrompt":"",
								"introduction":"面向合作伙伴的在线学习考试移动应用",
								"introductionI18nKey":"eip.app.introduction_hbxt",
								"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"name":"伙伴学堂",
								"nameI18nKey":"eip.app.name_hbxt",
								"openValidatePostUrl":"",
								"showPaasPage":0,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj,PartnerObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3",
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":1701075707L,
								"webManagerUrl":"#app/train/index/=/trainType-1",
								"webUrl":"#app/manage/eiauth/=/param-%7B%22upstreamEa%22%3A%22${fs_upstream_ea_encode}%22%2C%22url%22%3A%22%23app%2Ftrain%2Findex%2F%3D%2FtrainType-1%2FupstreamEa-${fs_upstream_ea_encode}%22%7D"
							}
						],
						"FSAID_11491009",
						true
					],
					"result":[
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"",
							"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":true,
							"authByEa":false,
							"authType":0,
							"createTime":1493794175L,
							"dataAuthType":1,
							"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
							"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
							"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
							"enabledByDefault":1,
							"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c84",
							"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
							"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
							"introductionI18nKey":"eip.app.introduction_dht",
							"managerIcon":"",
							"name":"订货通",
							"nameI18nKey":"eip.app.name_dht",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":true,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":false,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
							"updateTime":1698811093L,
							"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
							"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_a71a2994a29342e783446ab8244ef5d8.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appSessionIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"appUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":0,
							"createTime":1493794174L,
							"disablePrompt":"停用后，合作伙伴将不能再看到网盘文件",
							"downstreamMenuJson":"[{\"name\":\"互联网盘\",\"type\":2,\"actionParam\":\"fs://relate/sharedisk?{\\\"appId\\\": \\\"FSAID_11490c82\\\", \\\"ea\\\":\\\"${fs_upstream_ea}\\\"}\"}]",
							"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限1",
							"enabledByDefault":1,
							"icon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c82",
							"internalVisibleRangePrompt":"管理员和对接人默认可使用",
							"introduction":"可以共享文件给合作伙伴。",
							"introductionI18nKey":"eip.app.introduction_hlwp",
							"isDefaultSelect":true,
							"managerIcon":"",
							"name":"互联网盘",
							"nameI18nKey":"eip.app.name_hlwp",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1687758361L,
							"webManagerUrl":"#app/icfiles/send/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
							"webUrl":"#app/icfiles/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c82%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"appManagerUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
							"appSessionIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_07_0cc5c36d318e4803bd872c8642282de7.png&size=150_150&ea=appCenter",
							"appUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":2,
							"createTime":1493794174L,
							"disablePrompt":"停用后，合作伙伴将不能再看到通知公告",
							"downstreamMenuJson":"[{\"name\":\"通知公告\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/open/wechatconnect/#!/notice/statement?ea=${fs_upstream_ea}\"}]",
							"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限",
							"enabledByDefault":1,
							"icon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c83",
							"internalVisibleRangePrompt":"管理员和对接人默认可使用",
							"introduction":"群发通知给合作伙伴，支持评论，掌握已读状态，对未读联系人一键群发提醒。",
							"introductionI18nKey":"eip.app.introduction_tzgg",
							"isDefaultSelect":true,
							"managerIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"name":"通知公告",
							"nameI18nKey":"eip.app.name_tzgg",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1698291027L,
							"webManagerUrl":"#app/wechatconnect/releasenotice/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
							"webUrl":"#app/wechatconnect/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c83%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"appManagerUrl":"",
							"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":0,
							"createTime":1501663657L,
							"dataAuthType":1,
							"disablePrompt":"",
							"downstreamMenuJson":"",
							"enablePrompt":"",
							"enabledByDefault":2,
							"icon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"id":"FSAID_11490d40",
							"internalVisibleRangePrompt":"",
							"introduction":"面向合作伙伴的在线学习考试移动应用",
							"introductionI18nKey":"eip.app.introduction_hbxt",
							"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"name":"伙伴学堂",
							"nameI18nKey":"eip.app.name_hbxt",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1701075707L,
							"webManagerUrl":"#app/train/index/=/trainType-1",
							"webUrl":"#app/manage/eiauth/=/param-%7B%22upstreamEa%22%3A%22${fs_upstream_ea_encode}%22%2C%22url%22%3A%22%23app%2Ftrain%2Findex%2F%3D%2FtrainType-1%2FupstreamEa-${fs_upstream_ea_encode}%22%7D"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"appManagerUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&fsAppId=FSAID_11491009&isRedirect=true&resourceUrl=https%3A%2F%2Fcrm.ceshi112.com%2Ffsh5%2Fpage-portal%2Findex.html%3FauthType%3D%24%7Bfs_authType%7D%26fsAppId%3D%24%7Bfs_appId%7D%26context%3D%24%7Bfs_content_encode%7D%26upStreamEa%3D%24%7Bfs_upstream_ea%7D%23%2Fapp%2Fportal-custom",
							"appSessionIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"appUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&fsAppId=FSAID_11491009&isRedirect=true&resourceUrl=https%3A%2F%2Fcrm.ceshi112.com%2Ffsh5%2Fpage-portal%2Findex.html%3FauthType%3D%24%7Bfs_authType%7D%26fsAppId%3D%24%7Bfs_appId%7D%26context%3D%24%7Bfs_content_encode%7D%26upStreamEa%3D%24%7Bfs_upstream_ea%7D%23%2Fapp%2Fportal-custom",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":1,
							"createTime":1592807556L,
							"disablePrompt":"",
							"enablePrompt":"",
							"enabledByDefault":2,
							"icon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"id":"FSAID_11491009",
							"internalVisibleRangePrompt":"",
							"introduction":"",
							"introductionI18nKey":"",
							"managerIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"name":"渠道门户",
							"nameI18nKey":"eip.app.name_qdmh",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1698047240L,
							"webManagerUrl":"",
							"webUrl":"https://crm.ceshi112.com/XV/Home/Index#portal"
						}
					]
				},
				{
					
					"args":[
						[
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
								"appManagerUrl":"",
								"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"appUrl":"",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":1493794175L,
								"dataAuthType":1,
								"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
								"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
								"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
								"enabledByDefault":1,
								"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c84",
								"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
								"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
								"introductionI18nKey":"eip.app.introduction_dht",
								"managerIcon":"",
								"name":"订货通",
								"nameI18nKey":"eip.app.name_dht",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3,2,1",
								"supportRoleExt":true,
								"supportSetManager":false,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
								"updateTime":1698811093L,
								"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
								"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_a71a2994a29342e783446ab8244ef5d8.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
								"appManagerUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
								"appSessionIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
								"appUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":0,
								"createTime":1493794174L,
								"disablePrompt":"停用后，合作伙伴将不能再看到网盘文件",
								"downstreamMenuJson":"[{\"name\":\"互联网盘\",\"type\":2,\"actionParam\":\"fs://relate/sharedisk?{\\\"appId\\\": \\\"FSAID_11490c82\\\", \\\"ea\\\":\\\"${fs_upstream_ea}\\\"}\"}]",
								"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限1",
								"enabledByDefault":1,
								"icon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c82",
								"internalVisibleRangePrompt":"管理员和对接人默认可使用",
								"introduction":"可以共享文件给合作伙伴。",
								"introductionI18nKey":"eip.app.introduction_hlwp",
								"isDefaultSelect":true,
								"managerIcon":"",
								"name":"互联网盘",
								"nameI18nKey":"eip.app.name_hlwp",
								"openValidatePostUrl":"",
								"showPaasPage":0,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj,PartnerObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3",
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":1687758361L,
								"webManagerUrl":"#app/icfiles/send/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
								"webUrl":"#app/icfiles/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c82%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
								"appManagerUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
								"appSessionIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_07_0cc5c36d318e4803bd872c8642282de7.png&size=150_150&ea=appCenter",
								"appUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":2,
								"createTime":1493794174L,
								"disablePrompt":"停用后，合作伙伴将不能再看到通知公告",
								"downstreamMenuJson":"[{\"name\":\"通知公告\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/open/wechatconnect/#!/notice/statement?ea=${fs_upstream_ea}\"}]",
								"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限",
								"enabledByDefault":1,
								"icon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c83",
								"internalVisibleRangePrompt":"管理员和对接人默认可使用",
								"introduction":"群发通知给合作伙伴，支持评论，掌握已读状态，对未读联系人一键群发提醒。",
								"introductionI18nKey":"eip.app.introduction_tzgg",
								"isDefaultSelect":true,
								"managerIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
								"name":"通知公告",
								"nameI18nKey":"eip.app.name_tzgg",
								"openValidatePostUrl":"",
								"showPaasPage":0,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj,PartnerObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3",
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":1698291027L,
								"webManagerUrl":"#app/wechatconnect/releasenotice/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
								"webUrl":"#app/wechatconnect/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c83%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"appManagerUrl":"",
								"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"appUrl":"",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":0,
								"createTime":1501663657L,
								"dataAuthType":1,
								"disablePrompt":"",
								"downstreamMenuJson":"",
								"enablePrompt":"",
								"enabledByDefault":2,
								"icon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"id":"FSAID_11490d40",
								"internalVisibleRangePrompt":"",
								"introduction":"面向合作伙伴的在线学习考试移动应用",
								"introductionI18nKey":"eip.app.introduction_hbxt",
								"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"name":"伙伴学堂",
								"nameI18nKey":"eip.app.name_hbxt",
								"openValidatePostUrl":"",
								"showPaasPage":0,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj,PartnerObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3",
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":1701075707L,
								"webManagerUrl":"#app/train/index/=/trainType-1",
								"webUrl":"#app/manage/eiauth/=/param-%7B%22upstreamEa%22%3A%22${fs_upstream_ea_encode}%22%2C%22url%22%3A%22%23app%2Ftrain%2Findex%2F%3D%2FtrainType-1%2FupstreamEa-${fs_upstream_ea_encode}%22%7D"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"addManagerTipsUrl":"",
								"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"appManagerUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&fsAppId=FSAID_11491009&isRedirect=true&resourceUrl=https%3A%2F%2Fcrm.ceshi112.com%2Ffsh5%2Fpage-portal%2Findex.html%3FauthType%3D%24%7Bfs_authType%7D%26fsAppId%3D%24%7Bfs_appId%7D%26context%3D%24%7Bfs_content_encode%7D%26upStreamEa%3D%24%7Bfs_upstream_ea%7D%23%2Fapp%2Fportal-custom",
								"appSessionIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"appUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&fsAppId=FSAID_11491009&isRedirect=true&resourceUrl=https%3A%2F%2Fcrm.ceshi112.com%2Ffsh5%2Fpage-portal%2Findex.html%3FauthType%3D%24%7Bfs_authType%7D%26fsAppId%3D%24%7Bfs_appId%7D%26context%3D%24%7Bfs_content_encode%7D%26upStreamEa%3D%24%7Bfs_upstream_ea%7D%23%2Fapp%2Fportal-custom",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":1,
								"createTime":1592807556L,
								"disablePrompt":"",
								"enablePrompt":"",
								"enabledByDefault":2,
								"icon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"id":"FSAID_11491009",
								"internalVisibleRangePrompt":"",
								"introduction":"",
								"introductionI18nKey":"",
								"managerIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"name":"渠道门户",
								"nameI18nKey":"eip.app.name_qdmh",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportMapperApiNamesNew":"AccountObj,PartnerObj",
								"supportNoFsAccount":false,
								"supportRegisterTypes":"3,2,1",
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":1698047240L,
								"webManagerUrl":"",
								"webUrl":"https://crm.ceshi112.com/XV/Home/Index#portal"
							}
						],
						"FSAID_11490d9e",
						true
					],
					"result":[
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"",
							"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":true,
							"authByEa":false,
							"authType":0,
							"createTime":1493794175L,
							"dataAuthType":1,
							"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
							"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
							"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
							"enabledByDefault":1,
							"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c84",
							"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
							"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
							"introductionI18nKey":"eip.app.introduction_dht",
							"managerIcon":"",
							"name":"订货通",
							"nameI18nKey":"eip.app.name_dht",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":true,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":false,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
							"updateTime":1698811093L,
							"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
							"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_a71a2994a29342e783446ab8244ef5d8.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appSessionIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"appUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":0,
							"createTime":1493794174L,
							"disablePrompt":"停用后，合作伙伴将不能再看到网盘文件",
							"downstreamMenuJson":"[{\"name\":\"互联网盘\",\"type\":2,\"actionParam\":\"fs://relate/sharedisk?{\\\"appId\\\": \\\"FSAID_11490c82\\\", \\\"ea\\\":\\\"${fs_upstream_ea}\\\"}\"}]",
							"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限1",
							"enabledByDefault":1,
							"icon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c82",
							"internalVisibleRangePrompt":"管理员和对接人默认可使用",
							"introduction":"可以共享文件给合作伙伴。",
							"introductionI18nKey":"eip.app.introduction_hlwp",
							"isDefaultSelect":true,
							"managerIcon":"",
							"name":"互联网盘",
							"nameI18nKey":"eip.app.name_hlwp",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1687758361L,
							"webManagerUrl":"#app/icfiles/send/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
							"webUrl":"#app/icfiles/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c82%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"appManagerUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
							"appSessionIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_07_0cc5c36d318e4803bd872c8642282de7.png&size=150_150&ea=appCenter",
							"appUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":2,
							"createTime":1493794174L,
							"disablePrompt":"停用后，合作伙伴将不能再看到通知公告",
							"downstreamMenuJson":"[{\"name\":\"通知公告\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/open/wechatconnect/#!/notice/statement?ea=${fs_upstream_ea}\"}]",
							"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限",
							"enabledByDefault":1,
							"icon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c83",
							"internalVisibleRangePrompt":"管理员和对接人默认可使用",
							"introduction":"群发通知给合作伙伴，支持评论，掌握已读状态，对未读联系人一键群发提醒。",
							"introductionI18nKey":"eip.app.introduction_tzgg",
							"isDefaultSelect":true,
							"managerIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"name":"通知公告",
							"nameI18nKey":"eip.app.name_tzgg",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1698291027L,
							"webManagerUrl":"#app/wechatconnect/releasenotice/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
							"webUrl":"#app/wechatconnect/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c83%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"appManagerUrl":"",
							"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":0,
							"createTime":1501663657L,
							"dataAuthType":1,
							"disablePrompt":"",
							"downstreamMenuJson":"",
							"enablePrompt":"",
							"enabledByDefault":2,
							"icon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"id":"FSAID_11490d40",
							"internalVisibleRangePrompt":"",
							"introduction":"面向合作伙伴的在线学习考试移动应用",
							"introductionI18nKey":"eip.app.introduction_hbxt",
							"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"name":"伙伴学堂",
							"nameI18nKey":"eip.app.name_hbxt",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1701075707L,
							"webManagerUrl":"#app/train/index/=/trainType-1",
							"webUrl":"#app/manage/eiauth/=/param-%7B%22upstreamEa%22%3A%22${fs_upstream_ea_encode}%22%2C%22url%22%3A%22%23app%2Ftrain%2Findex%2F%3D%2FtrainType-1%2FupstreamEa-${fs_upstream_ea_encode}%22%7D"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"appManagerUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&fsAppId=FSAID_11491009&isRedirect=true&resourceUrl=https%3A%2F%2Fcrm.ceshi112.com%2Ffsh5%2Fpage-portal%2Findex.html%3FauthType%3D%24%7Bfs_authType%7D%26fsAppId%3D%24%7Bfs_appId%7D%26context%3D%24%7Bfs_content_encode%7D%26upStreamEa%3D%24%7Bfs_upstream_ea%7D%23%2Fapp%2Fportal-custom",
							"appSessionIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"appUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&fsAppId=FSAID_11491009&isRedirect=true&resourceUrl=https%3A%2F%2Fcrm.ceshi112.com%2Ffsh5%2Fpage-portal%2Findex.html%3FauthType%3D%24%7Bfs_authType%7D%26fsAppId%3D%24%7Bfs_appId%7D%26context%3D%24%7Bfs_content_encode%7D%26upStreamEa%3D%24%7Bfs_upstream_ea%7D%23%2Fapp%2Fportal-custom",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":1,
							"createTime":1592807556L,
							"disablePrompt":"",
							"enablePrompt":"",
							"enabledByDefault":2,
							"icon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"id":"FSAID_11491009",
							"internalVisibleRangePrompt":"",
							"introduction":"",
							"introductionI18nKey":"",
							"managerIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"name":"渠道门户",
							"nameI18nKey":"eip.app.name_qdmh",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1698047240L,
							"webManagerUrl":"",
							"webUrl":"https://crm.ceshi112.com/XV/Home/Index#portal"
						},
						{
							"@type":"com.facishare.linkapp.api.result.LinkAppData",
							"addManagerTipsUrl":"",
							"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/webapp/%E4%BB%A3%E7%90%86%E9%80%9A.svg",
							"appManagerUrl":"",
							"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/webapp/%E4%BB%A3%E7%90%86%E9%80%9A.svg",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":true,
							"authByEa":false,
							"authType":0,
							"createTime":1533554604L,
							"dataAuthType":1,
							"disablePrompt":"",
							"enablePrompt":"",
							"enabledByDefault":1,
							"icon":"https://a9.fspage.com/FSR/fs-qixin/static/webapp/%E4%BB%A3%E7%90%86%E9%80%9A.svg",
							"id":"FSAID_11490d9e",
							"internalVisibleRangePrompt":"",
							"introduction":"PRM",
							"introductionI18nKey":"",
							"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/webapp/%E4%BB%A3%E7%90%86%E9%80%9A.svg",
							"name":"代理通",
							"nameI18nKey":"eip.app.name_prm",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":true,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1699456273L,
							"webManagerUrl":"https://crm.ceshi112.com/XV/Home/Index?upstreamEa=${fs_upstream_ea}&fsAppId=${fs_appId}#prm/index",
							"webUrl":"https://crm.ceshi112.com/XV/Home/Index?upstreamEa=${fs_upstream_ea}&fsAppId=FSAID_11490d9e#prm/index"
						}
					]
				}
			]
		},
		"id":1,
		"method":"com.facishare.linkapp.provider.service.DownstreamServiceImpl#listBenchAndDownStreamApps",
		"methodData":{
			"args":[
				200071570L,
				300101454L
			],
			"result":{
				"@type":"com.facishare.enterprise.common.result.Result",
				"data":{
					"@type":"com.facishare.linkapp.api.vo.BenchAndDownstreamAppsVo",
					"downstreamLinkApps":[
						
					],
					"linkAppVos":[
						{
							"addManagerTipsUrl":"",
							"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"appManagerUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
							"appSessionIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_07_0cc5c36d318e4803bd872c8642282de7.png&size=150_150&ea=appCenter",
							"appUrl":"https://www.ceshi112.com/open/wechatconnect/#!/notice/history",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":2,
							"createTime":1493794174L,
							"disablePrompt":"停用后，合作伙伴将不能再看到通知公告",
							"downstreamMenuJson":"[{\"name\":\"通知公告\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/open/wechatconnect/#!/notice/statement?ea=${fs_upstream_ea}\"}]",
							"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限",
							"enabledByDefault":1,
							"icon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c83",
							"internalVisibleRangePrompt":"管理员和对接人默认可使用",
							"introduction":"群发通知给合作伙伴，支持评论，掌握已读状态，对未读联系人一键群发提醒。",
							"introductionI18nKey":"eip.app.introduction_tzgg",
							"isDefaultSelect":true,
							"isNew":2,
							"managerIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
							"name":"通知公告",
							"nameI18nKey":"eip.app.name_tzgg",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1698291027L,
							"webManagerUrl":"#app/wechatconnect/releasenotice/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
							"webUrl":"#app/wechatconnect/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c83%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
						},
						{
							"addManagerTipsUrl":"",
							"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_a71a2994a29342e783446ab8244ef5d8.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appSessionIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"appUrl":"fs://relate/sharedisk?{\"ea\":\"${fs_upstream_ea}\"}",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":0,
							"createTime":1493794174L,
							"disablePrompt":"停用后，合作伙伴将不能再看到网盘文件",
							"downstreamMenuJson":"[{\"name\":\"互联网盘\",\"type\":2,\"actionParam\":\"fs://relate/sharedisk?{\\\"appId\\\": \\\"FSAID_11490c82\\\", \\\"ea\\\":\\\"${fs_upstream_ea}\\\"}\"}]",
							"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限1",
							"enabledByDefault":1,
							"icon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c82",
							"internalVisibleRangePrompt":"管理员和对接人默认可使用",
							"introduction":"可以共享文件给合作伙伴。",
							"introductionI18nKey":"eip.app.introduction_hlwp",
							"isDefaultSelect":true,
							"isNew":2,
							"managerIcon":"",
							"name":"互联网盘",
							"nameI18nKey":"eip.app.name_hlwp",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1687758361L,
							"webManagerUrl":"#app/icfiles/send/=/param-%7b%22appId%22%3a%22${fs_appId_encode}%22%7d",
							"webUrl":"#app/icfiles/receive/=/param-%7b%22appId%22%3a%22FSAID_11490c82%22%2c%22ea%22%3a%22${fs_upstream_ea_encode}%22%2c%22eaName%22%3a%22${fs_upstream_eaName_encode}%22%7d"
						},
						{
							"addManagerTipsUrl":"",
							"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
							"appManagerUrl":"",
							"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":true,
							"authByEa":false,
							"authType":0,
							"createTime":1493794175L,
							"dataAuthType":1,
							"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
							"downstreamMenuJson":"[{\"name\":\"订单列表\",\"type\":2,\"actionParam\":\"https://www.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&context=${fs_upstream_ea}&resourceUrl=fs-sail-common&_hash=/order/\"}]",
							"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
							"enabledByDefault":1,
							"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
							"id":"FSAID_11490c84",
							"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
							"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
							"introductionI18nKey":"eip.app.introduction_dht",
							"isNew":2,
							"managerIcon":"",
							"name":"订货通",
							"nameI18nKey":"eip.app.name_dht",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":true,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":false,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
							"updateTime":1698811093L,
							"webManagerUrl":"#paasapp/index/=/appId_FSAID_PaaS_6841442477632",
							"webUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?upstreamEa=${fs_upstream_ea}&authType=2&isRedirect=true&resourceUrl=fs-sail-portal-web%3Fea%3D${fs_upstream_ea}"
						},
						{
							"addManagerTipsUrl":"",
							"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"appManagerUrl":"",
							"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":0,
							"createTime":1501663657L,
							"dataAuthType":1,
							"disablePrompt":"",
							"downstreamMenuJson":"",
							"enablePrompt":"",
							"enabledByDefault":2,
							"icon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"id":"FSAID_11490d40",
							"internalVisibleRangePrompt":"",
							"introduction":"面向合作伙伴的在线学习考试移动应用",
							"introductionI18nKey":"eip.app.introduction_hbxt",
							"isNew":2,
							"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
							"name":"伙伴学堂",
							"nameI18nKey":"eip.app.name_hbxt",
							"openValidatePostUrl":"",
							"showPaasPage":0,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1701075707L,
							"webManagerUrl":"#app/train/index/=/trainType-1",
							"webUrl":"#app/manage/eiauth/=/param-%7B%22upstreamEa%22%3A%22${fs_upstream_ea_encode}%22%2C%22url%22%3A%22%23app%2Ftrain%2Findex%2F%3D%2FtrainType-1%2FupstreamEa-${fs_upstream_ea_encode}%22%7D"
						},
						{
							"addManagerTipsUrl":"",
							"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/webapp/%E4%BB%A3%E7%90%86%E9%80%9A.svg",
							"appManagerUrl":"",
							"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/webapp/%E4%BB%A3%E7%90%86%E9%80%9A.svg",
							"appUrl":"",
							"appVisibility":true,
							"associationCrmObject":true,
							"authByEa":false,
							"authType":0,
							"createTime":1533554604L,
							"dataAuthType":1,
							"disablePrompt":"",
							"enablePrompt":"",
							"enabledByDefault":1,
							"icon":"https://a9.fspage.com/FSR/fs-qixin/static/webapp/%E4%BB%A3%E7%90%86%E9%80%9A.svg",
							"id":"FSAID_11490d9e",
							"internalVisibleRangePrompt":"",
							"introduction":"PRM",
							"introductionI18nKey":"",
							"isNew":2,
							"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/webapp/%E4%BB%A3%E7%90%86%E9%80%9A.svg",
							"name":"代理通",
							"nameI18nKey":"eip.app.name_prm",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":true,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1699456273L,
							"webManagerUrl":"https://crm.ceshi112.com/XV/Home/Index?upstreamEa=${fs_upstream_ea}&fsAppId=${fs_appId}#prm/index",
							"webUrl":"https://crm.ceshi112.com/XV/Home/Index?upstreamEa=${fs_upstream_ea}&fsAppId=FSAID_11490d9e#prm/index"
						},
						{
							"addManagerTipsUrl":"",
							"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"appManagerUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&fsAppId=FSAID_11491009&isRedirect=true&resourceUrl=https%3A%2F%2Fcrm.ceshi112.com%2Ffsh5%2Fpage-portal%2Findex.html%3FauthType%3D%24%7Bfs_authType%7D%26fsAppId%3D%24%7Bfs_appId%7D%26context%3D%24%7Bfs_content_encode%7D%26upStreamEa%3D%24%7Bfs_upstream_ea%7D%23%2Fapp%2Fportal-custom",
							"appSessionIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"appUrl":"https://crm.ceshi112.com/fs-er-biz/er/auth/connect?authType=2&fsAppId=FSAID_11491009&isRedirect=true&resourceUrl=https%3A%2F%2Fcrm.ceshi112.com%2Ffsh5%2Fpage-portal%2Findex.html%3FauthType%3D%24%7Bfs_authType%7D%26fsAppId%3D%24%7Bfs_appId%7D%26context%3D%24%7Bfs_content_encode%7D%26upStreamEa%3D%24%7Bfs_upstream_ea%7D%23%2Fapp%2Fportal-custom",
							"appVisibility":true,
							"associationCrmObject":false,
							"authByEa":false,
							"authType":1,
							"createTime":1592807556L,
							"disablePrompt":"",
							"enablePrompt":"",
							"enabledByDefault":2,
							"icon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"id":"FSAID_11491009",
							"internalVisibleRangePrompt":"",
							"introduction":"",
							"introductionI18nKey":"",
							"isNew":2,
							"managerIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
							"name":"渠道门户",
							"nameI18nKey":"eip.app.name_qdmh",
							"openValidatePostUrl":"",
							"showPaasPage":1,
							"supportExtendObject":false,
							"supportGuestor":false,
							"supportIdentityTypes":"1",
							"supportMapperApiNamesNew":"AccountObj,PartnerObj",
							"supportNoFsAccount":false,
							"supportRegisterTypes":"3,2,1",
							"supportRoleExt":true,
							"supportSetManager":true,
							"type":1,
							"unVisibleInManager":false,
							"unableSetManagerText":"",
							"updateTime":1698047240L,
							"webManagerUrl":"",
							"webUrl":"https://crm.ceshi112.com/XV/Home/Index#portal"
						}
					]
				},
				"errCode":0,
				"errMsg":"成功",
				"traceId":"fs-enterprise-relation-link-app/6699c9a6377df43cdc5416c9"
			}
		}
	}
]