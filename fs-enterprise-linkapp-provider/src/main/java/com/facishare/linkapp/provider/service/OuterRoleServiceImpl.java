package com.facishare.linkapp.provider.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.AppRoleType;
import com.facishare.enterprise.common.constant.CrmObjectApiNameEnum;
import com.facishare.enterprise.common.constant.FieldDefineType;
import com.facishare.enterprise.common.constant.FieldPermissionEnum;
import com.facishare.enterprise.common.constant.FunctionCodeGrayConfigConstant;
import com.facishare.enterprise.common.constant.GlobalRoleConstants;
import com.facishare.enterprise.common.constant.GuestorIdentityInfoConstants;
import com.facishare.enterprise.common.model.OuterRoleVo;
import com.facishare.enterprise.common.model.paas.FunctionInfo;
import com.facishare.enterprise.common.model.paas.ObjectFunctionInfo;
import com.facishare.enterprise.common.model.paas.OperationPermissionVO;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.enterprise.common.util.CommonPoolUtil;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.global.rest.service.GlobalService;
import com.facishare.linkapp.api.result.FunctionCodeGrayConfigData;
import com.facishare.linkapp.api.result.LinkAppAssociationObjectData;
import com.facishare.linkapp.api.service.HttpUpstreamService;
import com.facishare.linkapp.api.service.LinkAppAssociationObjectService;
import com.facishare.linkapp.api.service.LinkAppOuterRoleService;
import com.facishare.linkapp.provider.manager.OuterRoleRemoteBaseManager;
import com.facishare.linkapp.provider.manager.OuterRoleRemoteManager;
import com.facishare.linkapp.provider.manager.RemoteManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.privilege.exception.PrivilegeException;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.enterpriserelation.objrest.arg.AddOutErDepOrOrgForApiNameArg;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: jiangxch
 * @date: 2021/4/14 17:40
 */
@Slf4j
@Service("outerRoleService")
public class OuterRoleServiceImpl {
    /** 数据结构见{@link FunctionCodeGrayConfigData} */
    private static Map<String, FunctionCodeGrayConfigData> includeFunctionCodesGrayDataMap;
    private static Map<String, FunctionCodeGrayConfigData> excludeFunctionCodesGrayDataMap;
    /** 游客灰度的对象对应的功能权限 eg:Map[AccountObj,Set["","Add","Clone"]] **/
    private static Map<String, Set<String>> guestorApiNameOperationCodeSuffix = new HashMap<>();
    // paas侧存在该功能权限就显示
    private static List<String> paasShowFunctionCodes = new ArrayList<>();

    static {
        ConfigFactory.getConfig("fs-enterprise-linkapp-provider", config -> {
            includeFunctionCodesGrayDataMap = JsonUtil.fromJson(config.get("includeFunctionCodesGrayDataMap", "{}"), new com.google.gson.reflect.TypeToken<Map<String, FunctionCodeGrayConfigData>>() {
            }.getType());
            excludeFunctionCodesGrayDataMap = JsonUtil.fromJson(config.get("excludeFunctionCodesGrayDataMap", "{}"), new com.google.gson.reflect.TypeToken<Map<String, FunctionCodeGrayConfigData>>() {
            }.getType());
            paasShowFunctionCodes = Splitter.on(",").splitToList(config.get("paasShowFunctionCodes", ""));

            String body = config.get("guestorApiNameOperationCodeSuffix", "{}");
            guestorApiNameOperationCodeSuffix = JsonUtil.fromJson(body, new TypeToken<Map<String, Set<String>>>() {
            }.getType());
        });
    }

    @Autowired
    private RemoteManager remoteManager;
    @Autowired
    private EnterpriseRelationObjService enterpriseRelationObjService;
    @Autowired
    private LinkAppOuterRoleService linkAppOuterRoleService;
    @Autowired
    private HttpUpstreamService upstreamService;
    @Autowired
    private LinkAppAssociationObjectService linkAppAssociationObjectService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterRoleRemoteManager outerRoleRemoteManager;
    @Autowired
    private OuterRoleRemoteBaseManager outerRoleRemoteBaseManager;
    @Autowired
    private GlobalService globalService;
    @ReloadableProperty("cuostomObjectBasicOperationCodes")
    private String cuostomObjectBasicOperationCodes;

    private static void sortObjectFunctionInSequenceWithAppObjects(List<ObjectFunctionInfo> objectFunctionInfoList, List<String> appObjects) {
        AtomicInteger orderCounter = new AtomicInteger(0);
        Map<String, Integer> objectOrderMap = appObjects.stream().distinct().collect(Collectors.toMap(Function.identity(), x -> orderCounter.getAndIncrement()));
        if (objectOrderMap.containsKey(CrmObjectApiNameEnum.DUPLICATECHECK.getName())) {
            objectOrderMap.put(CrmObjectApiNameEnum.DUPLICATECHECK.getName(), -1);
        }
        Collections.sort(objectFunctionInfoList, (x, y) -> {
            Integer orderX = objectOrderMap.get(x.getApiName());
            Integer orderY = objectOrderMap.get(y.getApiName());
            if (Objects.isNull(orderX)) {  //should not happen
                orderX = 0;
            }
            if (Objects.isNull(orderY)) {  //should not happen
                orderY = 0;
            }
            return orderX - orderY;
        });
    }

    /**
     * 根据 functionCodes 生产 OperationPermissionVO
     */
    private static List<OperationPermissionVO> getObjectOperationPermissionVoByFunctionCodes(String objectApiName, List<String> functionCodes) {
        List<OperationPermissionVO> operationList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(functionCodes)) {
            operationList = functionCodes.stream().map(x -> {
                OperationPermissionVO operationPermissionVO = new OperationPermissionVO();
                operationPermissionVO.setIsDefaultSelect(Boolean.FALSE);
                operationPermissionVO.setIsModifiable(Boolean.TRUE);
                operationPermissionVO.setOperationCode(objectApiName + "||" + x);
                return operationPermissionVO;
            }).collect(Collectors.toList());
        }

        OperationPermissionVO listPermissionVO = new OperationPermissionVO();
        listPermissionVO.setIsDefaultSelect(Boolean.TRUE);
        listPermissionVO.setIsModifiable(Boolean.TRUE);
        listPermissionVO.setOperationCode(objectApiName);
        operationList.add(listPermissionVO);

        return operationList;
    }



    // 修改角色对应的功能权限
    public Result<Void> batchUpdateRoleFunctionPermission(String ea, String outerRoleId, Map<String, Boolean> functionCodeHasOpenMap, boolean isAsync) {
        if (GlobalRoleConstants.isSpecialRole(outerRoleId)) {
            outerRoleRemoteManager.updateRoleFunctionPermission(eieaConverter.enterpriseAccountToId(ea), outerRoleId, functionCodeHasOpenMap, isAsync);
            return Result.newSuccess();
        }
        outerRoleRemoteManager.updateRoleFunctionPermission(eieaConverter.enterpriseAccountToId(ea), outerRoleId, functionCodeHasOpenMap, isAsync);
        return Result.newSuccess();
    }

    // 修改角色对应的功能权限
    public Result<Void> batchUpdateRoleFunctionPermission(String ea, String outerRoleId, Map<String, Boolean> functionCodeHasOpenMap) {
        return batchUpdateRoleFunctionPermission(ea, outerRoleId, functionCodeHasOpenMap, false);
    }

    public Result<String> addRoleAndInit(String ea, String linkAppId, OuterRoleVo outerRoleVo) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        if (!Strings.isEmpty(outerRoleVo.getRoleId())) {
            List<OuterRoleVo> roleVos = outerRoleRemoteManager.batchGetRoleInfos(eieaConverter.enterpriseAccountToId(ea), Lists.newArrayList(outerRoleVo.getRoleId()));
            if (!CollectionUtil.isEmpty(roleVos)) {
                log.warn("addRoleAndInit had roleId,ea={},outerRoleVo={}", ea, outerRoleVo);
                return Result.newSuccess(outerRoleVo.getRoleId());
            }
        }
        String roleId = null;
        try {
            roleId = outerRoleRemoteManager.addRole(tenantId, outerRoleVo.getRoleId(), outerRoleVo.getRoleName(), outerRoleVo.getDescription(), AppRoleType.getByType(outerRoleVo.getRoleType()));
        } catch (PrivilegeException e) {
            return Result.newError(e.getErrorCode(), e.getMessage());
        }
        //获取应用关联对象
        Result<List<LinkAppAssociationObjectData>> objectResult = linkAppAssociationObjectService.listAssociationObjects(ea, linkAppId);
        if (!objectResult.isSuccess()) {
            return Result.newError(objectResult.getErrCode(), objectResult.getErrMessage());
        }
        if (CollectionUtils.isEmpty(objectResult.getData())) {
            return Result.newSuccess(roleId);
        }
        List<String> appObjectApiNameList = objectResult.getData().stream().map(LinkAppAssociationObjectData::getObjectApiName).collect(Collectors.toList());
        initObjectApiNameLayoutRecordFieldPermissions(ea, roleId, linkAppId, appObjectApiNameList);
        //初始化功能权限
        this.initOuterRoleFunctionCodes(eieaConverter.enterpriseAccountToId(ea), roleId, linkAppId, appObjectApiNameList);
        return Result.newSuccess(roleId);
    }

    private void initFieldPermission(String ea, String outerRoleId, String linkAppId, List<String> objectApiNames) {
        if (!outerRoleId.equals(GuestorIdentityInfoConstants.ROLE_CODE)) {
            Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
            //初始化字段权限
            for (String objectApiName : objectApiNames) {
                Map<String, FieldDescribe> fieldDescribeMap = remoteManager.getObjectDescribe(ea, objectApiName).getFields();
                initAppRoleFieldPermissionByDescribe(tenantId, linkAppId, outerRoleId, objectApiName, fieldDescribeMap, FieldPermissionEnum.read_write);
            }
        }
    }

    public Result<Void> initObjectApiNameLayoutRecordFieldPermissions(String ea, String outerRoleId, String linkAppId, List<String> objectApiNames) {
        if (CollectionUtils.isEmpty(objectApiNames)) {
            return Result.newSuccess();
        }
        CommonPoolUtil.execute(() -> {
            Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
            try {
                ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(tenantId + "", SuperUserConstants.USER_ID + "");
                AddOutErDepOrOrgForApiNameArg arg = new AddOutErDepOrOrgForApiNameArg();
                arg.setApiNames(objectApiNames);
                enterpriseRelationObjService.addOutErDepOrOrgForApiName(serviceContext, arg);
            } catch (Exception e) {
                log.warn("", e);
            }
            //初始化布局
            remoteManager.initObjectViews(tenantId, linkAppId, outerRoleId, objectApiNames);
            //初始化业务类型
            remoteManager.initObjectRecordTypes(tenantId, linkAppId, outerRoleId, objectApiNames);
            initFieldPermission(ea, outerRoleId, linkAppId, objectApiNames);
        });
        return Result.newSuccess();
    }

    private Map<String, Integer> initAppRoleFieldPermissionByDescribe(int fsEi, String appId, String roleId, String objectApiName, Map<String, FieldDescribe> fieldDescribeMap,
        FieldPermissionEnum permission) {
        Map<String, Integer> oldFieldInfos = outerRoleRemoteManager.listOuterRoleFieldPermission(fsEi, roleId, objectApiName);
        if (!MapUtils.isEmpty(oldFieldInfos)) {
            log.info("RoleFieldPermission had inited,fsEi={},roleId={},objectApiName={}", fsEi, roleId, objectApiName);
            return oldFieldInfos;
        }
        Map<String, Integer> permissionMap = fieldDescribeMap.values().stream().filter(x -> !FieldDefineType.SYSTEM.equals(x.getDefineType()))
            .collect(Collectors.toMap(FieldDescribe::getApiName, x -> permission.getPermission()));
        if (!permissionMap.isEmpty()) {
            outerRoleRemoteManager.updateRoleFieldPermission(fsEi, roleId, objectApiName, permissionMap);
        }
        return permissionMap;
    }

    private void initOuterRoleFunctionCodes(int fsEi, String outerRoleId, String linkAppId, List<String> appObjectApiNameList) {
        Map<String, Boolean> functionPermissionsMap = new HashMap<>();
        Map<String, List<OperationPermissionVO>> data = linkAppOuterRoleService.getPresentRoleFunctionByLinkAppIdAndRoleId(linkAppId, outerRoleId).getData();
        List<ObjectFunctionInfo> objectFunctionInfos = remoteManager.listFunctionsByRoleAndObject(fsEi, outerRoleId, Lists.newArrayList(data.keySet()));
        Set<String> hadFunctionCodes = new HashSet<>();
        for (ObjectFunctionInfo objectFunctionInfo : objectFunctionInfos) {
            List<FunctionInfo> functionInfos = objectFunctionInfo.getFunctionInfoList();
            if (functionInfos != null) {
                for (FunctionInfo functionInfo : functionInfos) {
                    hadFunctionCodes.add(functionInfo.getFunctionCode());
                }
            }
        }
        for (Entry<String, List<OperationPermissionVO>> entry : data.entrySet()) {
            if (!appObjectApiNameList.contains(entry.getKey())) {
                continue;
            }
            List<OperationPermissionVO> functionCodes = entry.getValue();
            for (OperationPermissionVO functionCode : functionCodes) {
                if (StringUtils.isEmpty(functionCode.getOperationCode()) || !hadFunctionCodes.contains(functionCode.getOperationCode())) {
                    log.info("not in functionCode,functionCode={},tenantId={},outerRoleId={}", functionCode.getOperationCode(), fsEi, outerRoleId);
                    continue;
                }
                if (BooleanUtils.isTrue(functionCode.getIsDefaultSelect())) {
                    functionPermissionsMap.put(functionCode.getOperationCode(), true);
                }
            }
        }
        if (!functionPermissionsMap.isEmpty()) {
            outerRoleRemoteManager.updateRoleFunctionPermission(fsEi, outerRoleId, functionPermissionsMap, true);
        }
    }

    private Map<String, ObjectFunctionInfo> getPassRoleFunction(int ei, Set<String> objectApiNames, String outerRoleId) {
        Map<String, ObjectFunctionInfo> apiNameObjectFunctionInfoMap = new HashMap<>();
        List<ObjectFunctionInfo> objectFunctionInfoList = remoteManager.listFunctionsByRoleAndObject(ei, outerRoleId, new ArrayList<>(objectApiNames));
        // 应用相同对象直接覆盖
        if (!CollectionUtil.isEmpty(objectFunctionInfoList)) {
            for (ObjectFunctionInfo objectFunctionInfo : objectFunctionInfoList) {
                apiNameObjectFunctionInfoMap.put(objectFunctionInfo.getApiName(), objectFunctionInfo);
            }
        }
        return apiNameObjectFunctionInfoMap;
    }

    /**
     * 合并应用对象功能权限 兼容白名单而重载方法
     *
     * @param objectFunctionSpaceMap map[objectApiName, map[functionCode, permissionVo]]
     */
    private List<ObjectFunctionInfo> mergePaasObjectFunctionAndSpaceObjectFunction(String ea, String outerRoleId, List<ObjectFunctionInfo> objectFunctionList,
        Map<String, Map<String, OperationPermissionVO>> objectFunctionSpaceMap) {
        if (objectFunctionSpaceMap.isEmpty() || objectFunctionList.isEmpty()) {
            return new ArrayList<>(0);
        }
        Map<String, List<String>> apiNameIncludeFunctionCodeMap = this.getGrayIncludeFunctionCodes(ea, objectFunctionSpaceMap.keySet());
        Map<String, List<String>> apiNameExcludeFunctionCodeMap = this.getGrayExcludeFunctionCodes(ea, objectFunctionSpaceMap.keySet());
        Iterator<ObjectFunctionInfo> objFunIterator = objectFunctionList.iterator();
        while (objFunIterator.hasNext()) {
            ObjectFunctionInfo objFun = objFunIterator.next();
            String apiName = objFun.getApiName();

            // 灰度的功能权限
            List<String> includeFunctionCodes = apiNameIncludeFunctionCodeMap.get(apiName);
            List<String> excludeFunctionCodes = apiNameExcludeFunctionCodeMap.get(apiName);

            List<FunctionInfo> functionList = objFun.getFunctionInfoList();
            if (Objects.isNull(functionList)) {
                objFunIterator.remove();
                continue;
            }

            Map<String, OperationPermissionVO> functionSpace = objectFunctionSpaceMap.get(objFun.getApiName());
            if (functionSpace == null) {  //不在空间内,并且没有额外的功能权限，直接过滤
                if (CollectionUtils.isEmpty(includeFunctionCodes)) {
                    objFunIterator.remove();
                    continue;
                }
            }

            Iterator<FunctionInfo> funIterator = functionList.iterator();
            while (funIterator.hasNext()) {
                FunctionInfo function = funIterator.next();
                String functionCode = function.getFunctionCode();//AccountObj||View
                String code = null;//View
                if (functionCode != null && functionCode.contains("||")) {
                    code = Splitter.on("||").splitToList(functionCode).get(1);
                }
                OperationPermissionVO functionInSpace = functionSpace.get(functionCode);

                // 若paas功能权限选中，切该功能权限在 paasShowFunctionCodes,不去除该功能权限
                if (function.getIsEnabled() && code != null && paasShowFunctionCodes.contains(code)) {
                    continue;
                }
                if (code != null && excludeFunctionCodes.contains(code)) {
                    // 该 functionCode 在空间内不包含
                    funIterator.remove();
                    continue;
                }

                if (!function.getFunctionCode().endsWith("__c")) {
                    if (Objects.isNull(functionInSpace)) { //不在空间内
                        if (!includeFunctionCodes.contains(code)) {
                            funIterator.remove();
                            continue;
                        }
                    }
                }

                if (functionInSpace != null) {
                    function.setIsModifiable(function.getIsModifiable() ? functionInSpace.getIsModifiable() : function.getIsModifiable()); //仅当底层允许可变更时以应用侧为准
                }
            }
            if (functionList.isEmpty()) {
                objFunIterator.remove();
            }
        }
        //处理特殊的角色游客 GuestorIdentityInfoConstants.ROLE_CODE
        if (GuestorIdentityInfoConstants.ROLE_CODE.equals(outerRoleId)) {
            //游客角色只有只读权限，不支持其他权限
            Iterator<ObjectFunctionInfo> objectFunctionInfoIterator = objectFunctionList.iterator();
            while (objectFunctionInfoIterator.hasNext()) {
                ObjectFunctionInfo objectFunctionInfo = objectFunctionInfoIterator.next();
                Iterator<FunctionInfo> iterator = objectFunctionInfo.getFunctionInfoList().iterator();
                String apiName = objectFunctionInfo.getApiName();
                while (iterator.hasNext()) {
                    FunctionInfo functionInfo = iterator.next();
                    String functionCode = functionInfo.getFunctionCode();
                    List<String> apiNameOperatorCodeSuffix = Splitter.on("||").splitToList(functionCode);
                    if (!apiName.equals(functionCode) && !(apiName + "||View").equals(functionCode)) {
                        // 灰度开放出来的权限
                        if (apiNameOperatorCodeSuffix.size() == 2 && guestorApiNameOperationCodeSuffix.getOrDefault(apiName, new HashSet<>()).contains(apiNameOperatorCodeSuffix.get(1))) {
                            continue;
                        }
                        iterator.remove();
                    }
                }
                if (objectFunctionInfo.getFunctionInfoList().isEmpty()) {
                    objectFunctionInfoIterator.remove();
                }
            }
        }
        this.sortObjectFunctionInSequenceWithAppObjects(objectFunctionList, new ArrayList<>(objectFunctionSpaceMap.keySet()));
        return objectFunctionList;
    }

    private Map<String, List<String>> getGrayIncludeFunctionCodes(String enterpriseAccount, Set<String> objectApiNames) {
        return getGrayFunctionCodes(includeFunctionCodesGrayDataMap, enterpriseAccount, objectApiNames);
    }

    private Map<String, List<String>> getGrayExcludeFunctionCodes(String enterpriseAccount, Set<String> objectApiNames) {
        return getGrayFunctionCodes(excludeFunctionCodesGrayDataMap, enterpriseAccount, objectApiNames);
    }

    private Map<String, List<String>> getGrayFunctionCodes(Map<String, FunctionCodeGrayConfigData> grayFunctionCodesGrayDataMap, String ea, Set<String> objectApiNames) {
        Map<String, Set<String>> res = new HashMap<>();
        for (Entry<String, FunctionCodeGrayConfigData> entry : grayFunctionCodesGrayDataMap.entrySet()) {
            List<String> grayFunctionCodes = Splitter.on(",").splitToList(entry.getKey());
            FunctionCodeGrayConfigData grayData = entry.getValue();
            if (grayData.getEas().contains(ea) || grayData.getEas().contains(FunctionCodeGrayConfigConstant.ALL_EA)) { // 企业在灰度中
                for (String apiName : grayData.getApiNames()) {
                    Set<String> functionCodes = res.computeIfAbsent(apiName, k -> new HashSet<>());
                    functionCodes.addAll(grayFunctionCodes);
                }
            }
        }
        Map<String, List<String>> objectFunctions = res.entrySet().stream().collect(Collectors.toMap(Entry::getKey, k -> new ArrayList<>(k.getValue())));
        for (String objectApiName : objectApiNames) {
            List<String> apiNameFunctionCodes = objectFunctions.getOrDefault(objectApiName, new ArrayList<>());
            List<String> allFunctionCodes;
            if (objectApiName != null && objectApiName.contains("__c")) {
                allFunctionCodes = objectFunctions.getOrDefault(FunctionCodeGrayConfigConstant.ALL_CUSTOMER_APINAME, new ArrayList<>());
            } else {
                allFunctionCodes = objectFunctions.getOrDefault(FunctionCodeGrayConfigConstant.ALL_PRESENT_APINAME, new ArrayList<>());
            }
            Set<String> mergeFunctionCodes = new HashSet<>();
            mergeFunctionCodes.addAll(apiNameFunctionCodes);
            mergeFunctionCodes.addAll(allFunctionCodes);
            objectFunctions.put(objectApiName, new ArrayList<>(mergeFunctionCodes));
        }
        return objectFunctions;
    }

    public Result<AppRoleType> getOuterRoleType(String upstreamEa, String roleId) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        List<OuterRoleVo> data = outerRoleRemoteManager.batchGetRoleInfos(tenantId, Lists.newArrayList(roleId));
        if (data.isEmpty()) {
            return Result.newSuccess();
        }
        AppRoleType appRoleType = AppRoleType.getByType(data.get(0).getRoleType());
        return Result.newSuccess(appRoleType);
    }

    public Result<String> getMajorRoleCodeByUserId(String upstreamEa, Long outerUId) {
        String roleId = outerRoleRemoteBaseManager.getMajorRoleCodeByUserId(upstreamEa, outerUId);
        return Result.newSuccess(roleId);
    }

    public Result<List<OuterRoleVo>> batchGetRoleInfos(Integer tenantId, Collection<String> roleIds) {
        List<OuterRoleVo> data = outerRoleRemoteManager.batchGetRoleInfos(tenantId, roleIds);
        return Result.newSuccess(data);
    }

    public Result<List<OuterRoleVo>> batchGetAllOuterRoleInfos(Integer tenantId) {
        List<OuterRoleVo> data = outerRoleRemoteManager.batchGetAllOuterRoleInfos(tenantId);
        return Result.newSuccess(data);
    }
}
