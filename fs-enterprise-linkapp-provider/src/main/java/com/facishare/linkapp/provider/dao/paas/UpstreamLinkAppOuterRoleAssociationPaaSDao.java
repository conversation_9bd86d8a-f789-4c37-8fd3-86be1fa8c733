package com.facishare.linkapp.provider.dao.paas;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.model.OuterRoleVo;
import com.facishare.linkapp.api.enums.LinkAppTypeEnum;
import com.facishare.linkapp.provider.model.entity.UpstreamLinkAppOuterRoleAssociation;
import com.facishare.linkapp.provider.util.PaaSHeaderMapUtil;
import com.facishare.webpage.customer.api.model.SimpleLinkAppRoleVO;
import com.facishare.webpage.customer.api.model.arg.BathCreateOrUpdatelinkAppRoleArg;
import com.facishare.webpage.customer.api.model.arg.DeleteLinkAppRoleAra;
import com.facishare.webpage.customer.api.model.arg.GetlinkAppRoleListArg;
import com.facishare.webpage.customer.api.service.LinkAppRestService;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
public class UpstreamLinkAppOuterRoleAssociationPaaSDao {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private LinkAppRestService linkAppRestService;

    public int batchInsert(Collection<UpstreamLinkAppOuterRoleAssociation> associations) {
        if (CollectionUtils.isEmpty(associations)) {
            return 0;
        }
        int upstreamEi = eieaConverter.enterpriseAccountToId(associations.iterator().next().getUpstreamEa());
        BathCreateOrUpdatelinkAppRoleArg arg = new BathCreateOrUpdatelinkAppRoleArg();
        try {
            arg.setType(2);
            List<SimpleLinkAppRoleVO> simpleLinkAppRoleVOS = Lists.newArrayList();
            for (UpstreamLinkAppOuterRoleAssociation association : associations) {
                SimpleLinkAppRoleVO simpleLinkAppRoleVO = new SimpleLinkAppRoleVO();
                simpleLinkAppRoleVO.setTenantId(upstreamEi);
                simpleLinkAppRoleVO.setLinkAppId(association.getLinkAppId());
                simpleLinkAppRoleVO.setRoleId(association.getRoleId());
                simpleLinkAppRoleVO.setType(LinkAppTypeEnum.PRIVATE.getType());
                simpleLinkAppRoleVO.setRoleType(association.getType());
                simpleLinkAppRoleVOS.add(simpleLinkAppRoleVO);
            }
            arg.setAppRoleVOList(simpleLinkAppRoleVOS);
            linkAppRestService.bathCreateOrUpdatelinkAppRole(PaaSHeaderMapUtil.newInstance(upstreamEi), arg);
            return associations.size();
        } catch (Exception ex) {
            log.warn("linkAppRestService.bathCreateOrUpdatelinkAppRole->arg:{} error:", arg, ex);
        }
        return 0;
    }

    public void batchAdd(String upstreamEa, String linkAppId, Collection<OuterRoleVo> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return;
        }
        int upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
        BathCreateOrUpdatelinkAppRoleArg arg = new BathCreateOrUpdatelinkAppRoleArg();
        try {
            arg.setType(2);
            List<SimpleLinkAppRoleVO> simpleLinkAppRoleVOS = Lists.newArrayList();
            for (OuterRoleVo role : roles) {
                SimpleLinkAppRoleVO simpleLinkAppRoleVO = new SimpleLinkAppRoleVO();
                simpleLinkAppRoleVO.setTenantId(upstreamEi);
                simpleLinkAppRoleVO.setRoleId(role.getRoleId());
                simpleLinkAppRoleVO.setLinkAppId(linkAppId);
                simpleLinkAppRoleVO.setRoleType(role.getRoleType());
                simpleLinkAppRoleVO.setType(LinkAppTypeEnum.PRIVATE.getType());
                simpleLinkAppRoleVOS.add(simpleLinkAppRoleVO);
            }
            arg.setAppRoleVOList(simpleLinkAppRoleVOS);
            linkAppRestService.bathCreateOrUpdatelinkAppRole(PaaSHeaderMapUtil.newInstance(upstreamEi), arg);
        } catch (Exception ex) {
            log.warn("linkAppRestService.bathCreateOrUpdatelinkAppRole->arg:{} error:", arg, ex);
        }
    }

    public List<UpstreamLinkAppOuterRoleAssociation> listByEaAppWithTimeDescOrder(String upstreamEa, String linkAppId) {
        return batchGetLinkAppRoleList(upstreamEa, Lists.newArrayList(linkAppId), null, true, null);
    }

    public List<UpstreamLinkAppOuterRoleAssociation> listByEaAppsWithTimeDescOrder(String upstreamEa, List<String> linkAppIds) {
        return batchGetLinkAppRoleList(upstreamEa, linkAppIds, null, true, null);
    }

    public List<UpstreamLinkAppOuterRoleAssociation> listByEaRoleIdsWithTimeDescOrder(String upstreamEa, Collection<String> roleIds) {
        return batchGetLinkAppRoleList(upstreamEa, null, roleIds, true, null);
    }

    public List<UpstreamLinkAppOuterRoleAssociation> listByEaAppType(String upstreamEa, String linkAppId, Integer roleType) {
        return batchGetLinkAppRoleList(upstreamEa, Lists.newArrayList(linkAppId), null, false, Lists.newArrayList(roleType));
    }

    public List<UpstreamLinkAppOuterRoleAssociation> listByEaRoleIdAndType(String upstreamEa, Collection<String> roleIds, Integer roleType) {
        return batchGetLinkAppRoleList(upstreamEa, null, roleIds, false, Lists.newArrayList(roleType));
    }

    public List<UpstreamLinkAppOuterRoleAssociation> listByEaAppRoles(String upstreamEa, String linkAppId, Collection<String> roleIds, Integer roleType) {
        return batchGetLinkAppRoleList(upstreamEa, Lists.newArrayList(linkAppId), roleIds, false, Lists.newArrayList(roleType));
    }

    public List<UpstreamLinkAppOuterRoleAssociation> listByEaRoles(String upstreamEa, Collection<String> roleIds, List<String> linkAppIds) {
        return batchGetLinkAppRoleList(upstreamEa, linkAppIds, roleIds, false, null);
    }

    public List<UpstreamLinkAppOuterRoleAssociation> listByEaAppIds(String upstreamEa, Collection<String> linkAppIds) {
        return batchGetLinkAppRoleList(upstreamEa, new ArrayList<>(linkAppIds), null, false, null);
    }

    public void batchDeleteByEaAppRoles(String upstreamEa, String linkAppId, Collection<String> roleIds) {
        batchDeleteLinkAppRole(upstreamEa, Lists.newArrayList(linkAppId), roleIds);
    }

    public void batchDeleteByEaRoles(String upstreamEa, Collection<String> roleIds, List<String> toDeletedLinkAppIds) {
        batchDeleteLinkAppRole(upstreamEa, toDeletedLinkAppIds, roleIds);
    }

    public List<UpstreamLinkAppOuterRoleAssociation> listByEaAppIdsWithTimeDescOrder(String upstreamEa, List<String> linkAppIds) {
        return batchGetLinkAppRoleList(upstreamEa, linkAppIds, null, true, null);
    }

    public void deleteByUpstreamEaAndLinkAppId(String upstreamEa, String linkAppId) {
        batchDeleteLinkAppRole(upstreamEa, Lists.newArrayList(linkAppId), null);
    }

    public int deleteByUpstreamEa(String upstreamEa) {
        batchDeleteLinkAppRole(upstreamEa, null, null);
        return 1;
    }

    private List<UpstreamLinkAppOuterRoleAssociation> batchGetLinkAppRoleList(String upstreamEa, List<String> linkAppIds, Collection<String> roleIds, boolean orderByUpdateTimeDesc, List<Integer> roleTypes) {
        int upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
        GetlinkAppRoleListArg getlinkAppRoleListArg = new GetlinkAppRoleListArg();
        getlinkAppRoleListArg.setTenantId(upstreamEi);
        getlinkAppRoleListArg.setLinkAppIds(linkAppIds);
        getlinkAppRoleListArg.setRoleTypes(roleTypes);
        getlinkAppRoleListArg.setRoleIds(roleIds == null ? null : new ArrayList<>(roleIds));
        getlinkAppRoleListArg.setType(LinkAppTypeEnum.PRIVATE.getType());
        getlinkAppRoleListArg.setOrderByUpdateTimeDesc(orderByUpdateTimeDesc);
        List<SimpleLinkAppRoleVO> linkAppRoleList = linkAppRestService.getLinkAppRoleList(PaaSHeaderMapUtil.newInstance(upstreamEi), getlinkAppRoleListArg).getLinkAppRoleList();
        List<UpstreamLinkAppOuterRoleAssociation> upstreamLinkAppOuterRoleAssociations = Lists.newArrayList();
        if (CollectionUtils.isEmpty(linkAppRoleList)) {
            return upstreamLinkAppOuterRoleAssociations;
        }
        for (SimpleLinkAppRoleVO simpleLinkAppRoleVO : linkAppRoleList) {
            UpstreamLinkAppOuterRoleAssociation association = new UpstreamLinkAppOuterRoleAssociation();
            association.setUpstreamEa(eieaConverter.enterpriseIdToAccount(simpleLinkAppRoleVO.getTenantId()));
            association.setLinkAppId(simpleLinkAppRoleVO.getLinkAppId());
            association.setRoleId(simpleLinkAppRoleVO.getRoleId());
            association.setType(simpleLinkAppRoleVO.getType());
            upstreamLinkAppOuterRoleAssociations.add(association);
        }
        return upstreamLinkAppOuterRoleAssociations;
    }

    private void batchDeleteLinkAppRole(String upstreamEa, List<String> linkAppIds, Collection<String> roleIds) {
        int upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
        DeleteLinkAppRoleAra deleteLinkAppRoleAra = new DeleteLinkAppRoleAra();
        try {
            deleteLinkAppRoleAra.setTenantId(upstreamEi);
            deleteLinkAppRoleAra.setLinkAppIds(linkAppIds);
            deleteLinkAppRoleAra.setRoleIds(roleIds == null ? null : new ArrayList<>(roleIds));
            deleteLinkAppRoleAra.setType(LinkAppTypeEnum.PRIVATE.getType());// 预制应用
            linkAppRestService.deleteLinkAppRole(PaaSHeaderMapUtil.newInstance(upstreamEi), deleteLinkAppRoleAra);
        } catch (Exception ex) {
            log.warn("linkAppRestService.deleteLinkAppRole->arg:{} error:", deleteLinkAppRoleAra, ex);
        }
    }
}
