package com.facishare.linkapp.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.GlobalRoleConstants;
import com.facishare.enterprise.common.constant.IdentityType;
import com.facishare.enterprise.common.model.OuterAccountVo;
import com.facishare.enterprise.common.model.OuterRoleVo;
import com.facishare.enterprise.common.model.OuterUserOuterRoleVo;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.global.rest.arg.DownstreamOuterUidArg;
import com.facishare.global.rest.common.HeaderObj;
import com.facishare.global.rest.service.GlobalService;
import com.facishare.linkapp.api.service.LinkAppOuterRoleService;
import com.facishare.linkapp.provider.model.data.UserRoleData;
import com.facishare.linkapp.provider.service.EmployeeCardServiceImpl;
import com.facishare.linkapp.provider.service.FxiaokeEnterpriseAssociationServiceImpl;
import com.facishare.linkapp.provider.service.OuterRoleRemoteBaseServiceImpl;
import com.facishare.linkapp.provider.service.OuterUserRoleServiceImpl;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.privilege.exception.PrivilegeException;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.enterpriserelation.objrest.arg.ListPublicEmployeesByOuterRoleIdsByPageArg;
import com.fxiaoke.enterpriserelation.objrest.common.MapOffsetResult;
import com.fxiaoke.enterpriserelation.objrest.common.OffsetArg;
import com.fxiaoke.enterpriserelation.objrest.service.PublicEmployeeObjService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by ligang on 2017/4/13.
 */
@Component
@Slf4j
public class DownstreamEmployeeLinkAppRoleManager {
    @Autowired
    private FxiaokeEnterpriseAssociationServiceImpl fxiaokeEnterpriseAssociationService;
    @Autowired
    private LinkAppOuterRoleService linkAppOuterRoleService;
    @Autowired
    private OuterRoleRemoteBaseServiceImpl outerRoleRemoteBaseService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EmployeeCardServiceImpl employeeCardService;
    @Autowired
    private OuterUserRoleServiceImpl outerRoleService;
    @Autowired
    private GlobalService globalService;
    @Autowired
    private PublicEmployeeObjService publicEmployeeObjService;

    public List<String> listUpstreamEaByLinkAppIdAndDownstreamOuterTenantIdAndDownstreamOuterUid(String linkAppId, Long downstreamOuterUid) {
        DownstreamOuterUidArg downstreamOuterUidArg = new DownstreamOuterUidArg();
        downstreamOuterUidArg.setDownstreamOuterUid(downstreamOuterUid);
        List<Long> upstreamOuterTenantIds = globalService.listAllUpstreamOuterTenantIds(HeaderObj.newInstance(), downstreamOuterUidArg).getData();
        Map<Long, String> upstreamOuterTenantIdAndEaMap = fxiaokeEnterpriseAssociationService.batchGetEaByOuterTenantId(upstreamOuterTenantIds).getData();
        Collection<String> upstreamEas = upstreamOuterTenantIdAndEaMap.values();
        List<String> upstreamEasResult = Lists.newArrayList();
        for (String upstreamEa : upstreamEas) {
            List<OuterRoleVo> roleVos = linkAppOuterRoleService.listAppRoles(upstreamEa, linkAppId).getData();
            List<String> roleIds = roleVos.stream().map(OuterRoleVo::getRoleId).collect(Collectors.toList());
            Boolean isAuth = this.isAuthByUpstreamEaAndRoleIdsAndDownstreamOuterTenantIdAndDownstreamOuterUid(upstreamEa, roleIds, downstreamOuterUid);
            if (isAuth) {
                upstreamEasResult.add(upstreamEa);
            }
        }
        return upstreamEasResult;
    }

    public List<OuterAccountVo> listDownstreamOuterUids(String linkAppId, String upstreamEa, List<Long> downstreamOuterTenantIds, OffsetArg offsetArg, boolean checkLimit) {
        List<OuterAccountVo> downstreamOuterUids = Lists.newArrayList();
        List<OuterRoleVo> roleVos = linkAppOuterRoleService.listAppRoles(upstreamEa, linkAppId).getData();
        List<String> roleIds = roleVos.stream().map(OuterRoleVo::getRoleId).collect(Collectors.toList());
        ListPublicEmployeesByOuterRoleIdsByPageArg pageArg = new ListPublicEmployeesByOuterRoleIdsByPageArg();
        pageArg.setOuterRoleIds(roleIds);
        pageArg.setDownstreamOuterTenantIds(downstreamOuterTenantIds);
        pageArg.setOffset(offsetArg.getOffset());
        pageArg.setLimit(offsetArg.getLimit());
        pageArg.setCheckLimit(checkLimit);
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(eieaConverter.enterpriseAccountToId(upstreamEa) + "", "-10000");
        MapOffsetResult mapOffsetResult = publicEmployeeObjService.listPublicEmployeeObjectDatasByOuterRoleIdsByPage(serviceContext, pageArg);
        for (Map<String, Object> objectData : mapOffsetResult.getDataList()) {
            PublicEmployeeObj obj = PublicEmployeeObj.newInstance(objectData);
            OuterAccountVo outerAccountVo = new OuterAccountVo(obj.getOuterTenantId(), obj.getOuterUid());
            downstreamOuterUids.add(outerAccountVo);
        }
        return downstreamOuterUids;
    }

    public List<Long> listDownstreamOuterUids(String linkAppId, String upstreamEa, Long downstreamOuterTenantId, OffsetArg offsetArg, boolean checkLimit) {
        List<OuterAccountVo> outerAccountVoList = listDownstreamOuterUids(linkAppId, upstreamEa, Lists.newArrayList(downstreamOuterTenantId), offsetArg, checkLimit);
        return outerAccountVoList.stream().map(x -> x.getOuterUid()).collect(Collectors.toList());
    }

    public Map<Long, Boolean> getOuterUserAuthResultByUpstreamEaAndRoleIdsAndOuterTenantIdAndOuterUids(String upstreamEa, List<String> roleIds, List<Long> outerUids) {
        Map<Long, Boolean> OuterUidAuthList = Maps.newHashMap();
        Boolean isAllPersonalRoleId = roleIds.contains(GlobalRoleConstants.PERSONAL_ROLE_CODE);
        Boolean isAllEnterpriseRoleId = roleIds.contains(GlobalRoleConstants.ENTERPRISE_ROLE_CODE);
        Map<Long, Integer> outerUidAndIdentityTypeMap = Maps.newHashMap();
        if (isAllEnterpriseRoleId || isAllPersonalRoleId) {
            outerUidAndIdentityTypeMap = employeeCardService.batchGetIdentityType(upstreamEa, outerUids).getData();
        }
        for (Long outerUid : outerUids) {
            if (isAllEnterpriseRoleId && outerUidAndIdentityTypeMap.get(outerUid) != null && outerUidAndIdentityTypeMap.get(outerUid).equals(IdentityType.CORP.getType())) {
                OuterUidAuthList.put(outerUid, true);
                continue;
            }
            if (isAllPersonalRoleId && outerUidAndIdentityTypeMap.get(outerUid) != null && outerUidAndIdentityTypeMap.get(outerUid).equals(IdentityType.PERSONAL.getType())) {
                OuterUidAuthList.put(outerUid, true);
                continue;
            }
            Boolean isAuth = isAuthByUpstreamEaAndRoleIdsAndDownstreamOuterTenantIdAndDownstreamOuterUid(upstreamEa, roleIds, outerUid);
            OuterUidAuthList.put(outerUid, isAuth);
        }
        return OuterUidAuthList;
    }

    public List<Long> listDownstreamOuterUidsByUpstreamEaAndRoleIdsAndDownstreamOuterTenantIds(String upstreamEa, String linkAppId, Collection<Long> downstreamOuterTenantIds, Integer offset,
        Integer limit) {
        List<OuterRoleVo> roleVos = linkAppOuterRoleService.listAppRoles(upstreamEa, linkAppId).getData();
        List<String> roleIds = roleVos.stream().map(OuterRoleVo::getRoleId).collect(Collectors.toList());
        return this.listDownstreamOuterUidsByUpstreamEaAndRoleIdsAndDownstreamOuterTenantIds(upstreamEa, roleIds, downstreamOuterTenantIds, offset, limit);
    }

    public List<Long> listDownstreamOuterUidsByUpstreamEaAndRoleIdsAndDownstreamOuterTenantIds(String upstreamEa, Collection<String> roleIds, Collection<Long> downstreamOuterTenantIds, Integer offset,
        Integer limit) {
        Integer fsEi = eieaConverter.enterpriseAccountToId(upstreamEa);
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(fsEi + "", CrmConstants.SYSTEM_USER.toString());
        ListPublicEmployeesByOuterRoleIdsByPageArg arg = new ListPublicEmployeesByOuterRoleIdsByPageArg();
        if (CollectionUtils.isNotEmpty(roleIds)) {
            arg.setOuterRoleIds(Lists.newArrayList(roleIds));
        }
        arg.setLimit(limit);
        arg.setOffset(offset);
        if (CollectionUtils.isNotEmpty(downstreamOuterTenantIds)) {
            arg.setDownstreamOuterTenantIds(Lists.newArrayList(downstreamOuterTenantIds));
        }
        return publicEmployeeObjService.listOuterUidsByOuterRoleIdsByPage(serviceContext, arg).getDataList();
    }

    public List<String> listLinkAppIdsByUpstreamEaAndDownstreamOuterTenantIdAndDownstreamOuterUid(String upstreamEa, Long downstreamOuterTenantId, Long downstreamOuterUid) {
        List<OuterUserOuterRoleVo> outerRoleVos = outerRoleService.listOuterUserOuterRolesByOuterUids(upstreamEa, Lists.newArrayList(downstreamOuterUid)).getData();
        List<String> roleIds = outerRoleVos.stream().map(OuterUserOuterRoleVo::getRoleId).collect(Collectors.toList());
        return linkAppOuterRoleService.listLinkAppIdByRoleIds(upstreamEa, roleIds).getData();
    }

    public List<Long> listDownstreamOuterUidsByUpstreamEaAndLinkAppId(String upstreamEa, String linkAppId, List<Long> downstreamOuterTenantIds, Integer offset, Integer limit) {
        List<OuterRoleVo> roleVos = linkAppOuterRoleService.listAppRoles(upstreamEa, linkAppId).getData();
        if (CollectionUtil.isEmpty(roleVos)) {
            return Lists.newArrayList();
        }
        List<String> roleIds = roleVos.stream().map(OuterRoleVo::getRoleId).collect(Collectors.toList());
        return listDownstreamOuterUidsByUpstreamEaAndRoleIdsAndDownstreamOuterTenantIds(upstreamEa, roleIds, downstreamOuterTenantIds, offset, limit);
    }

    public List<Long> listDownstreamOuterUidsByUpstreamEaAndLinkAppId(String upstreamEa, String linkAppId, Integer offset, Integer limit) {
        return listDownstreamOuterUidsByUpstreamEaAndLinkAppId(upstreamEa, linkAppId, null, offset, limit);
    }

    public Boolean isAuthByUpstreamEaAndLinkAppIdAndDownstreamOuterTenantIdAndDownstreamOuterUid(String enterpriseAccount, String linkAppId, Long outerTenantId, Long outerUid) {
        List<OuterRoleVo> roleVos = linkAppOuterRoleService.listAppRoles(enterpriseAccount, linkAppId).getData();
        List<String> roleIds = roleVos.stream().map(OuterRoleVo::getRoleId).collect(Collectors.toList());
        return isAuthByUpstreamEaAndRoleIdsAndDownstreamOuterTenantIdAndDownstreamOuterUid(enterpriseAccount, roleIds, outerUid);
    }

    public Map<Long, Boolean> batchIsAuthByUpstreamEaAndLinkAppIdAndDownstreamOuterUids(String enterpriseAccount, String linkAppId, List<Long> outerUids) {
        List<OuterRoleVo> roleVos = linkAppOuterRoleService.listAppRoles(enterpriseAccount, linkAppId).getData();
        List<String> roleIds = roleVos.stream().map(OuterRoleVo::getRoleId).collect(Collectors.toList());
        return batchIsAuthByUpstreamEaAndRoleIdsAndDownstreamOuterUid(enterpriseAccount, roleIds, outerUids);
    }

    public Boolean isAuthByUpstreamEaAndRoleIdsAndDownstreamOuterTenantIdAndDownstreamOuterUid(String enterpriseAccount, List<String> roleIds, Long outerUid) {
        Boolean isAllPersonalRoleId = roleIds.contains(GlobalRoleConstants.PERSONAL_ROLE_CODE);
        Boolean isAllEnterpriseRoleId = roleIds.contains(GlobalRoleConstants.ENTERPRISE_ROLE_CODE);
        if (isAllEnterpriseRoleId || isAllPersonalRoleId) {
            Integer identityType = employeeCardService.batchGetIdentityType(enterpriseAccount, Lists.newArrayList(outerUid)).getData().get(outerUid);
            if (isAllEnterpriseRoleId && identityType.equals(IdentityType.CORP.getType())) {
                return true;
            }
            if (isAllPersonalRoleId && identityType.equals(IdentityType.PERSONAL.getType())) {
                return true;
            }
        }
        Integer fsEi = eieaConverter.enterpriseAccountToId(enterpriseAccount);
        List<UserRoleData> userRoleDatas = outerRoleRemoteBaseService.queryOuterUserByUpsteramEiAndDownstreamOuterUids(fsEi, Lists.newArrayList(outerUid)).getData();
        List<String> hasRoleIds = userRoleDatas.stream().map(UserRoleData::getRoleCode).collect(Collectors.toList());
        hasRoleIds.retainAll(roleIds);
        return hasRoleIds.size() > 0;
    }

    public Map<Long, Boolean> batchIsAuthByUpstreamEaAndRoleIdsAndDownstreamOuterUid(String enterpriseAccount, List<String> roleIds, List<Long> outerUids) {
        Map<Long, Boolean> res = new HashMap<>();
        for (Long outerUid : outerUids) {
            res.put(outerUid, false);
        }
        Boolean isAllPersonalRoleId = roleIds.contains(GlobalRoleConstants.PERSONAL_ROLE_CODE);
        Boolean isAllEnterpriseRoleId = roleIds.contains(GlobalRoleConstants.ENTERPRISE_ROLE_CODE);
        if (isAllEnterpriseRoleId || isAllPersonalRoleId) {
            Map<Long, Integer> outerUidIdentityTypeMap = employeeCardService.batchGetIdentityType(enterpriseAccount, outerUids).getData();
            for (Long outerUid : outerUids) {
                Integer identityType = outerUidIdentityTypeMap.get(outerUid);
                if (identityType != null && isAllEnterpriseRoleId && identityType.equals(IdentityType.CORP.getType())) {
                    res.put(outerUid, true);
                }
                if (identityType != null && isAllPersonalRoleId && identityType.equals(IdentityType.PERSONAL.getType())) {
                    res.put(outerUid, true);
                }
            }
        }
        Integer fsEi = eieaConverter.enterpriseAccountToId(enterpriseAccount);
        List<UserRoleData> userRoleDatas = new ArrayList<>();
        try {
            userRoleDatas = outerRoleRemoteBaseService.queryOuterUserByUpsteramEiAndDownstreamOuterUids(fsEi, outerUids).getData();
        } catch (PrivilegeException e) {
            log.warn("queryOuterUserByUpsteramEiAndDownstreamOuterUids error,fsEi={},outerUids={},e={}", fsEi, outerUids, e);
        }
        Map<Long, List<String>> outerUidRoleIdsMap = userRoleDatas.stream()
            .collect(Collectors.groupingBy(x -> Long.valueOf(x.getUserId()), Collectors.mapping(x -> x.getRoleCode(), Collectors.toList())));
        for (Long outerUid : outerUids) {
            List<String> hasRoleIds = outerUidRoleIdsMap.get(outerUid);
            if (hasRoleIds != null) {
                hasRoleIds.retainAll(roleIds);
                if (hasRoleIds.size() > 0) {
                    res.put(outerUid, true);
                }
            }
        }
        return res;
    }
}
