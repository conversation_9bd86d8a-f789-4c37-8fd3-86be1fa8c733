package com.facishare.linkapp.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.GlobalRoleConstants;
import com.facishare.enterprise.common.context.RequestContext;
import com.facishare.enterprise.common.exception.RelationException;
import com.facishare.enterprise.common.innerevent.event.CreateDownstreamOuterUserLinkAppRoleInnerEvent;
import com.facishare.enterprise.common.innerevent.event.CreateLinkAppOuterRoleInnerEvent;
import com.facishare.enterprise.common.innerevent.event.DeleteDownstreamOuterUserRoleInnerEvent;
import com.facishare.enterprise.common.innerevent.event.DeleteLinkAppOuterRoleInnerEvent;
import com.facishare.enterprise.common.innerevent.producer.DownstreamOuterUserLinkAppRoleInnerEventProducer;
import com.facishare.enterprise.common.innerevent.producer.LinkAppOuterRoleInnerEventProducer;
import com.facishare.enterprise.common.model.OuterAccountVo;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.linkapp.api.service.DownstreamEmployeeLinkAppRoleService;
import com.facishare.linkapp.api.service.HttpUpstreamService;
import com.facishare.linkapp.provider.dao.LinkAppOuterRoleAssociationDao;
import com.facishare.linkapp.provider.dao.ShareRuleDao;
import com.facishare.linkapp.provider.dao.UpstreamLinkAppOuterRoleAssociationDao;
import com.facishare.linkapp.provider.model.data.UserRoleData;
import com.facishare.linkapp.provider.model.entity.LinkAppOuterRoleAssociation;
import com.facishare.linkapp.provider.model.entity.ShareRule;
import com.facishare.linkapp.provider.model.entity.UpstreamLinkAppOuterRoleAssociation;
import com.facishare.linkapp.provider.service.EmployeeCardServiceImpl;
import com.facishare.linkapp.provider.service.OuterRoleRemoteBaseServiceImpl;
import com.facishare.linkapp.provider.service.OuterRoleRemoteServiceImpl;
import com.fxiaoke.paasauthrestapi.arg.DeleteByCodeArg;
import com.fxiaoke.paasauthrestapi.common.data.ShareRuleData;
import com.fxiaoke.paasauthrestapi.common.enums.ShareScopeEnum;
import com.fxiaoke.paasauthrestapi.common.enums.ShareTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LinkAppOuterRoleManager {
    @Autowired
    private EIEAConverter eiEaConverter;
    @Autowired
    private LinkAppOuterRoleAssociationDao appOuterRoleDao;
    @Autowired
    private UpstreamLinkAppOuterRoleAssociationDao upstreamLinkAppOuterRoleAssociationDao;
    @Autowired
    private LinkAppOuterRoleManager linkAppOuterRoleManager;
    @Autowired
    private DownstreamOuterUserLinkAppRoleInnerEventProducer downstreamOuterUserLinkAppRoleInnerEventProducer;
    @Autowired
    private HttpUpstreamService upstreamService;
    @Autowired
    private DownstreamEmployeeLinkAppRoleService downstreamEmployeeLinkAppRoleService;
    @Autowired
    private LinkAppOuterRoleInnerEventProducer linkAppOuterRoleInnerEventProducer;
    @Autowired
    private OuterRoleRemoteServiceImpl outerRoleRemoteService;
    @Autowired
    private EmployeeCardServiceImpl employeeCardService;
    @Autowired
    private ShareRuleDao shareRuleDao;
    @Autowired
    private OuterRoleRemoteBaseServiceImpl outerRoleRemoteBaseService;

    public Map<String, List<String>> getLinkAppIdAndRoleIdsMap(String upstreamEa, Collection<String> roleIds) {
        if (CollectionUtil.isEmpty(roleIds)) {
            return new HashMap<>();
        }
        Map<String, List<String>> result = Maps.newHashMap();
        List<UpstreamLinkAppOuterRoleAssociation> eaAppRoleList = upstreamLinkAppOuterRoleAssociationDao.listByEaRoles(upstreamEa, roleIds, null);
        //获取应用级的角色
        if (!eaAppRoleList.isEmpty()) {
            for (UpstreamLinkAppOuterRoleAssociation upstreamLinkAppOuterRoleAssociation : eaAppRoleList) {
                result.computeIfAbsent(upstreamLinkAppOuterRoleAssociation.getLinkAppId(), k -> Lists.newArrayList());
                result.get(upstreamLinkAppOuterRoleAssociation.getLinkAppId()).add(upstreamLinkAppOuterRoleAssociation.getRoleId());
            }
        }
        List<LinkAppOuterRoleAssociation> appRoleList = appOuterRoleDao.listByRoleIdWithTimeDescOrder(CollectionUtils.isEmpty(roleIds) ? null : Lists.newArrayList(roleIds));
        if (!appRoleList.isEmpty()) {
            for (LinkAppOuterRoleAssociation linkAppOuterRoleAssociation : appRoleList) {
                result.computeIfAbsent(linkAppOuterRoleAssociation.getLinkAppId(), k -> Lists.newArrayList());
                result.get(linkAppOuterRoleAssociation.getLinkAppId()).add(linkAppOuterRoleAssociation.getRoleId());
            }
        }
        return result;
    }

    public Map<String, List<String>> listLinkAppIdAndRoleIdsMap(String upstreamEa, List<String> linkAppIds) {
        //获取应用企业级角色
        List<UpstreamLinkAppOuterRoleAssociation> upAppRoleList = upstreamLinkAppOuterRoleAssociationDao.listByEaAppIdsWithTimeDescOrder(upstreamEa, linkAppIds);
        //获取应用级的角色
        List<LinkAppOuterRoleAssociation> appRoleList = appOuterRoleDao.listByAppIdsWithTimeDescOrder(linkAppIds);
        Map<String, List<String>> linkAppIdAndRoleMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(upAppRoleList)) {
            for (UpstreamLinkAppOuterRoleAssociation upstreamLinkAppOuterRoleAssociation : upAppRoleList) {
                linkAppIdAndRoleMap.computeIfAbsent(upstreamLinkAppOuterRoleAssociation.getLinkAppId(), k -> Lists.newArrayList());
                linkAppIdAndRoleMap.get(upstreamLinkAppOuterRoleAssociation.getLinkAppId()).add(upstreamLinkAppOuterRoleAssociation.getRoleId());
            }
        }
        //应用级角色在后
        if (!appRoleList.isEmpty()) {
            for (LinkAppOuterRoleAssociation linkAppOuterRoleAssociation : appRoleList) {
                linkAppIdAndRoleMap.computeIfAbsent(linkAppOuterRoleAssociation.getLinkAppId(), k -> Lists.newArrayList());
                linkAppIdAndRoleMap.get(linkAppOuterRoleAssociation.getLinkAppId()).add(linkAppOuterRoleAssociation.getRoleId());
            }
        }
        return linkAppIdAndRoleMap;
    }

    public List<String> getRoleIdsByLinkAppId(String upstreamEa, String linkAppId) {
        List<String> roleIds = Lists.newArrayList();
        Map<String, List<String>> appIdRoleIdsMap = listLinkAppIdAndRoleIdsMap(upstreamEa, Lists.newArrayList(linkAppId));
        if (!CollectionUtil.isEmpty(appIdRoleIdsMap)) {
            roleIds = appIdRoleIdsMap.get(linkAppId);
        }
        return roleIds;
    }

    public String getLinkAppIdByRoleId(String upstreamEa, String roleId) {
        List<String> roles = Collections.singletonList(roleId);
        List<UpstreamLinkAppOuterRoleAssociation> eaAppRoleList = upstreamLinkAppOuterRoleAssociationDao.listByEaRoles(upstreamEa, roles, null);
        //获取应用级的角色
        if (!eaAppRoleList.isEmpty()) {
            return eaAppRoleList.get(0).getLinkAppId();
        }
        List<LinkAppOuterRoleAssociation> appRoleList = appOuterRoleDao.listByRoleIdWithTimeDescOrder(roles);
        if (appRoleList.isEmpty()) {
            throw new RelationException(ResultCode.ROLE_NOT_EXIST);
        }
        return appRoleList.get(0).getLinkAppId();
    }

    public List<String> getLinkAppIdsByRoleIds(String upstreamEa, List<String> roleIds) {
        List<UpstreamLinkAppOuterRoleAssociation> entities = upstreamLinkAppOuterRoleAssociationDao.listByEaRoles(upstreamEa, Lists.newArrayList(roleIds), null);
        List<LinkAppOuterRoleAssociation> linkAppOuterRoleAssociations = appOuterRoleDao.listByAppRolesWithoutLinkAppId(roleIds, null);
        Set<String> result = new HashSet<>();
        if (!CollectionUtil.isEmpty(entities)) {
            result.addAll(entities.stream().map(UpstreamLinkAppOuterRoleAssociation::getLinkAppId).distinct().collect(Collectors.toList()));
        }
        if (!CollectionUtil.isEmpty(linkAppOuterRoleAssociations)) {
            result.addAll(linkAppOuterRoleAssociations.stream().map(LinkAppOuterRoleAssociation::getLinkAppId).distinct().collect(Collectors.toList()));
        }
        return new ArrayList<>(result);
    }

    public Map<String, Set<String>> getRoleIdLinkAppIdsByRoleIds(String upstreamEa, List<String> roleIds) {
        List<UpstreamLinkAppOuterRoleAssociation> entities = upstreamLinkAppOuterRoleAssociationDao.listByEaRoles(upstreamEa, Lists.newArrayList(roleIds), null);
        List<LinkAppOuterRoleAssociation> linkAppOuterRoleAssociations = appOuterRoleDao.listByAppRolesWithoutLinkAppId(roleIds, null);
        Map<String, Set<String>> result = new HashMap<>();
        for (String roleId : roleIds) {
            Set<String> allLinkAppIds = result.computeIfAbsent(roleId, k -> new HashSet<>());
            if (!CollectionUtil.isEmpty(entities)) {
                allLinkAppIds.addAll(entities.stream().filter(x -> roleId.equals(x.getRoleId())).map(UpstreamLinkAppOuterRoleAssociation::getLinkAppId).distinct().collect(Collectors.toList()));
            }
            if (!CollectionUtil.isEmpty(linkAppOuterRoleAssociations)) {
                allLinkAppIds
                    .addAll(linkAppOuterRoleAssociations.stream().filter(x -> roleId.equals(x.getRoleId())).map(LinkAppOuterRoleAssociation::getLinkAppId).distinct().collect(Collectors.toList()));
            }
        }
        return result;
    }

    public Map<String, Map<String, Set<String>>> buildBeforeOuterRoleIdLinkAppIdsOuterRoleIdMap(Map<String, Set<String>> outerRoleIdLinkAppIdsMap,
        Map<String, Set<String>> beforeLinkAppIdOuterRoleIdsMap) {
        Map<String, Map<String, Set<String>>> beforeAddOuterRoleIdLinkAppIdsOuterRoleIdMap = new HashMap<>();
        for (Entry<String, Set<String>> addOuterRoleIdLinkAppIdsEntry : outerRoleIdLinkAppIdsMap.entrySet()) {
            String outerRoleId = addOuterRoleIdLinkAppIdsEntry.getKey();
            Set<String> linkAppIds = addOuterRoleIdLinkAppIdsEntry.getValue();
            Map<String, Set<String>> outerRoleLinkAppIdOuterRoleIdsMap = new HashMap<>();
            for (String linkAppId : linkAppIds) {
                Set<String> outerRoleIds = outerRoleLinkAppIdOuterRoleIdsMap.computeIfAbsent(linkAppId, k -> new HashSet<>());
                outerRoleIds.addAll(beforeLinkAppIdOuterRoleIdsMap.getOrDefault(linkAppId, new HashSet<>()));
            }
            beforeAddOuterRoleIdLinkAppIdsOuterRoleIdMap.put(outerRoleId, outerRoleLinkAppIdOuterRoleIdsMap);
        }
        return beforeAddOuterRoleIdLinkAppIdsOuterRoleIdMap;
    }

    public List<Long> batchAddUserRole(Integer tenantId, String outerRoleId, Collection<Long> addDownstreamOuterUids, Boolean isMainRoleId) {
        String upstreamEa = eiEaConverter.enterpriseIdToAccount(tenantId);
        Map<Long, Set<String>> beforeUserRolesMap = new HashMap<>();
        //对比看是否角色已新增
        List<Long> newAddDownstreamOuterUids = new ArrayList<>(addDownstreamOuterUids);
        if (!GlobalRoleConstants.isSpecialRole(outerRoleId)) {
            beforeUserRolesMap = downstreamEmployeeLinkAppRoleService.batchGetOuterRoleIds(upstreamEa, addDownstreamOuterUids).getData();
            for (Long addDownstreamOuterUid : addDownstreamOuterUids) {
                Set<String> oldRoles = beforeUserRolesMap.get(addDownstreamOuterUid);
                if (oldRoles != null && oldRoles.contains(outerRoleId)) {
                    newAddDownstreamOuterUids.remove(addDownstreamOuterUid);
                    beforeUserRolesMap.remove(addDownstreamOuterUid);
                }
            }
        } else {
            for (Long addDownstreamOuterUid : newAddDownstreamOuterUids) {
                beforeUserRolesMap.put(addDownstreamOuterUid, Sets.newHashSet());
            }
        }
        if (newAddDownstreamOuterUids.isEmpty()) {
            return newAddDownstreamOuterUids;
        }
        Map<Long, List<String>> addOuterUidOuterRoleIdsMap = newAddDownstreamOuterUids.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.mapping(x -> {
            return outerRoleId;
        }, Collectors.toList())));

        //查询之前的角色和应用的关系
        Map<String, Set<String>> roleIdLinkAppIdsMap = linkAppOuterRoleManager.getRoleIdLinkAppIdsByRoleIds(upstreamEa, Lists.newArrayList(outerRoleId));
        // 列出授权应用的所有角色
        Map<String, Set<String>> linkAppIdOuterRoleIdsMap = upstreamService
            .listUpstreamAppOuterRoleIdsByLinkAppIds(upstreamEa, roleIdLinkAppIdsMap.values().stream().flatMap(Set::stream).collect(Collectors.toList())).getData();

        if (!GlobalRoleConstants.isSpecialRole(outerRoleId)) {
            Map<Long, Long> outerUidOuterTenantIdMap = employeeCardService.batchGetOuterTenantIds(upstreamEa, addDownstreamOuterUids).getData();
            List<OuterAccountVo> outerUsers = addDownstreamOuterUids.stream().map(y -> new OuterAccountVo(outerUidOuterTenantIdMap.get(y), y)).collect(Collectors.toList());
            outerRoleRemoteService.batchAddOuterUserToRole(tenantId, outerRoleId, outerUsers, isMainRoleId);
            synchCreateShareRule(upstreamEa, outerRoleId, newAddDownstreamOuterUids);
        }
        RequestContext context = RequestContext.get();
        String operatorEa = "-10000";
        Integer operatorEmployeeId = -10000;
        if (context != null) {
            operatorEa = context.getOperatorEa();
            operatorEmployeeId = context.getOperatorEmployeeId();
        }
        CreateDownstreamOuterUserLinkAppRoleInnerEvent instance = CreateDownstreamOuterUserLinkAppRoleInnerEvent
            .getInstance(this, operatorEa, operatorEmployeeId, addOuterUidOuterRoleIdsMap, beforeUserRolesMap, tenantId,
                linkAppOuterRoleManager.buildBeforeOuterRoleIdLinkAppIdsOuterRoleIdMap(roleIdLinkAppIdsMap, linkAppIdOuterRoleIdsMap));
        downstreamOuterUserLinkAppRoleInnerEventProducer.sendCreateDownstreamOuterUserLinkAppRoleInnerEvent(instance);
        return newAddDownstreamOuterUids;
    }

    public void syncDeleteShareRule(String upstreamEa, String roleId, Set<Long> outerTenantIds) {
        if (CollectionUtils.isEmpty(outerTenantIds)) {
            return;
        }
        List<ShareRule> shareRules = shareRuleDao.listByRoleId(upstreamEa, roleId);
        if (CollectionUtils.isEmpty(shareRules)) {
            return;
        }
        Integer ei = eiEaConverter.enterpriseAccountToId(upstreamEa);
        List<UserRoleData> newOuterUserRoles = outerRoleRemoteBaseService.queryOuterUserByUpsteramEiAndOuterRoleIdsAndDownstreamOuterTenantIds(ei, Lists.newArrayList(roleId), outerTenantIds)
            .getData();
        for (UserRoleData newOuterUserRole : newOuterUserRoles) {
            outerTenantIds.remove(Long.valueOf(newOuterUserRole.getOuterTenantId()));
        }
        if (CollectionUtils.isNotEmpty(outerTenantIds)) {
            List<DeleteByCodeArg.RoleOuterTenantData> roleTenantDatas = Lists.newArrayList();
            DeleteByCodeArg.RoleOuterTenantData roleTenantData = new DeleteByCodeArg.RoleOuterTenantData();
            roleTenantData.setCode(roleId);
            roleTenantData.setCodeType(ShareScopeEnum.ROLE.getType());
            roleTenantData.setOuterTenantIds(outerTenantIds.stream().map(x -> x.toString()).collect(Collectors.toList()));
            roleTenantDatas.add(roleTenantData);
            outerRoleRemoteService.deleteByCode(ei, roleTenantDatas);
        }
    }

    private void synchCreateShareRule(String upstreamEa, String roleId, Collection<Long> downstreamOuterUids) {
        Integer ei = eiEaConverter.enterpriseAccountToId(upstreamEa);
        if (CollectionUtils.isEmpty(downstreamOuterUids)) {
            return;
        }
        List<ShareRule> shareRules = shareRuleDao.listByRoleId(upstreamEa, roleId);
        if (CollectionUtils.isEmpty(shareRules)) {
            return;
        }

        Map<Long, Long> outerUidsTenantIdMap = employeeCardService.batchGetOuterTenantIds(upstreamEa, downstreamOuterUids).getData();
        List<ShareRuleData> shareRuleDatas = Lists.newArrayList();
        for (Long outerTenantId : outerUidsTenantIdMap.values()) {
            shareRules.forEach(x -> {
                ShareRuleData shareRuleData = new ShareRuleData();
                shareRuleData.setStatus(x.getStatus());
                shareRuleData.setPermission(x.getPermission());
                shareRuleData.setEntityId(x.getObjectApiName());
                shareRuleData.setEntityShareType(ShareTypeEnum.OUTER.getType());
                shareRuleData.setShareType(ShareScopeEnum.ROLE.getType());
                shareRuleData.setReceiveType(ShareScopeEnum.ROLE.getType());
                shareRuleData.setShareTenantId(outerTenantId + "");
                shareRuleData.setReceiveTenantId(outerTenantId + "");
                if (x.getShareRoleId().equalsIgnoreCase(roleId)) {
                    shareRuleData.setShareId(roleId);
                    shareRuleData.setReceiveId(x.getReceiveRoleId());
                } else {
                    shareRuleData.setShareId(x.getShareRoleId());
                    shareRuleData.setReceiveId(roleId);
                }
                shareRuleDatas.add(shareRuleData);
            });
        }
        outerRoleRemoteService.batchCreateEntityShare(ei, null, shareRuleDatas);
    }

    public void doInDeleteDownstreamOuterUserRoleInnerEvent(String upstreamEa, List<Long> deleteDownstreamOuterUids, String outerRoleId, com.facishare.enterprise.common.util.Function function) {
        int upEi = eiEaConverter.enterpriseAccountToId(upstreamEa);
        Map<Long, Set<String>> beforeOuterUidRolesMap = downstreamEmployeeLinkAppRoleService.batchGetOuterRoleIds(upstreamEa, deleteDownstreamOuterUids).getData();
        Map<Long, List<String>> deleteOuterUidOuterRoleIdsMap = deleteDownstreamOuterUids.stream()
            .collect(Collectors.groupingBy(Function.identity(), Collectors.mapping(x -> outerRoleId, Collectors.toList())));
        Map<String, Set<String>> roleIdLinkAppIdsMap = linkAppOuterRoleManager.getRoleIdLinkAppIdsByRoleIds(upstreamEa, Lists.newArrayList(outerRoleId));
        if (GlobalRoleConstants.isSpecialRole(outerRoleId) && CollectionUtils.isEmpty(roleIdLinkAppIdsMap.get(outerRoleId))) {
            return;
        }
        // 列出授权应用的所有角色
        Map<String, Set<String>> linkAppIdOuterRoleIdsMap = upstreamService
            .listUpstreamAppOuterRoleIdsByLinkAppIds(upstreamEa, roleIdLinkAppIdsMap.values().stream().flatMap(Set::stream).collect(Collectors.toList())).getData();

        // ==============
        // 角色人员持久化操作
        if (GlobalRoleConstants.isSpecialRole(outerRoleId)) {
            if (function != null) {
                function.apply(null);
            }
        }
        RequestContext context = RequestContext.get();
        String operatorEa = "-10000";
        Integer operatorEmployeeId = -10000;
        if (context != null) {
            operatorEa = context.getOperatorEa();
            operatorEmployeeId = context.getOperatorEmployeeId();
        }
        DeleteDownstreamOuterUserRoleInnerEvent instance = DeleteDownstreamOuterUserRoleInnerEvent
            .getInstance(this, operatorEa, operatorEmployeeId, deleteOuterUidOuterRoleIdsMap, beforeOuterUidRolesMap, upEi,
                linkAppOuterRoleManager.buildBeforeOuterRoleIdLinkAppIdsOuterRoleIdMap(roleIdLinkAppIdsMap, linkAppIdOuterRoleIdsMap));
        downstreamOuterUserLinkAppRoleInnerEventProducer.sendDeleteDownstreamOuterUserRoleInnerEvent(instance);
    }

    public void doInCreateLinkAppOuterRoleInnerEvent(String upstreamEa, Map<String, Set<String>> addOuterRoleIdLinkAppIdsMap, Map<String, Set<String>> beforeLinkAppIdOuterRoleIdsMap,
        com.facishare.enterprise.common.util.Function function) {
        CreateLinkAppOuterRoleInnerEvent instance = CreateLinkAppOuterRoleInnerEvent.getInstance(this, addOuterRoleIdLinkAppIdsMap, beforeLinkAppIdOuterRoleIdsMap, upstreamEa);
        // ==============
        // 角色人员持久化操作
        if (function != null) {
            function.apply(null);
        }
        // ==============
        linkAppOuterRoleInnerEventProducer.sendCreateLinkAppOuterRoleInnerEvent(instance);
    }

    public void doInDeleteLinkAppOuterRoleInnerEvent(String upstreamEa, Map<String, Set<String>> deleteOuterRoleIdLinkAppIdsMap, com.facishare.enterprise.common.util.Function function) {
        Set<String> linkAppIds = deleteOuterRoleIdLinkAppIdsMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        Map<String, Set<String>> beforeLinkAppIdOuterRoleIdsMap = upstreamService.listUpstreamAppOuterRoleIdsByLinkAppIds(upstreamEa, linkAppIds).getData();

        // ==============
        // 角色人员持久化操作
        if (function != null) {
            function.apply(null);
        }
        // ==============

        DeleteLinkAppOuterRoleInnerEvent instance = DeleteLinkAppOuterRoleInnerEvent.getInstance(this, beforeLinkAppIdOuterRoleIdsMap, deleteOuterRoleIdLinkAppIdsMap, upstreamEa);
        linkAppOuterRoleInnerEventProducer.sendDeleteLinkAppOuterRoleInnerEvent(instance);
    }
}
