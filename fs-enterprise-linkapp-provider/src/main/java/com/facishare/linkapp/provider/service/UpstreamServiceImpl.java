package com.facishare.linkapp.provider.service;

import com.alibaba.druid.util.StringUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.*;
import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.facishare.enterprise.common.enums.QXMessageEnums;
import com.facishare.enterprise.common.innerevent.event.LinkAppObjectAssociationInnerEvent;
import com.facishare.enterprise.common.innerevent.producer.LinkAppObjectAssociationInnerEventProducer;
import com.facishare.enterprise.common.model.CloudConfigVo;
import com.facishare.enterprise.common.model.OuterRoleVo;
import com.facishare.enterprise.common.model.paas.ObjectDataRangeVO;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.*;
import com.facishare.er.cross.cloud.mq.syncdata.SyncCrossCloudDataManager;
import com.facishare.er.cross.cloud.mq.syncdata.dao.DaoOperateEnum;
import com.facishare.linkapp.api.arg.*;
import com.facishare.linkapp.api.enums.LinkAppTypeEnum;
import com.facishare.linkapp.api.result.*;
import com.facishare.linkapp.api.service.*;
import com.facishare.linkapp.api.vo.AllPublicEmployeeVo;
import com.facishare.linkapp.api.vo.SystemAppVo;
import com.facishare.linkapp.api.vo.UpstreamEmployeeRolesVo;
import com.facishare.linkapp.api.vo.WechatServiceAppVo;
import com.facishare.linkapp.provider.dao.*;
import com.facishare.linkapp.provider.manager.*;
import com.facishare.linkapp.provider.model.entity.*;
import com.facishare.linkapp.provider.model.entity.LinkAppObjectAssociation;
import com.facishare.linkapp.provider.model.result.EnterpriseCardResult;
import com.facishare.linkapp.provider.mq.MQSender;
import com.facishare.linkapp.provider.mq.event.CreateUpstreamEmployeeLinkAppRoleEvent;
import com.facishare.linkapp.provider.mq.event.DeleteUpstreamEmployeeLinkAppRoleEvent;
import com.facishare.linkapp.provider.mq.event.OuterRoleIdLinkAppIdChangeEvent;
import com.facishare.linkapp.provider.mq.event.UpdateUpstreamEnterpriseLinkAppStatusEvent;
import com.facishare.linkapp.provider.remote.FsEmployeeRemote;
import com.facishare.linkapp.provider.util.ConfigUtil;
import com.facishare.linkapp.provider.util.WeChatCustomAppWeChatUrlUtils;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.qixin.api.model.message.content.OTTemplateMessage;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.enterpriserelation.objrest.arg.NoArg;
import com.fxiaoke.enterpriserelation.objrest.result.GetErVisitorResult;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.fxiaoke.model.InternationalItem;
import com.fxiaoke.paasauthrestapi.common.data.HeaderObj;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfigFactory;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.enterprise.common.constant.LinkAppAuthTypeEnum.ALL;

/**
 * Created by huangzhw on 2017/4/17.
 */
@Service("httpUpstreamService")
@Slf4j
public class UpstreamServiceImpl implements HttpUpstreamService {
    @Autowired
    EnterpriseCardServiceImpl enterpriseCardService;
    @Autowired
    private CloudUtil cloudUtil;
    @Autowired
    private DownstreamEmployeeLinkAppRoleManager downstreamEmployeeLinkAppRoleManager;
    @Autowired
    private LinkAppDao linkAppDao;
    @Autowired
    private HttpLinkAppService linkAppService;
    @Autowired
    private WechatLinkAppDao wechatLinkAppDao;
    @Autowired
    private UpstreamEmployeeLinkAppRoleDao upstreamEmployeeLinkAppRoleDao;
    @Autowired
    private UpstreamEnterpriseLinkAppRelationDao upstreamEnterpriseLinkAppRelationDao;
    @Autowired
    private UpstreamLinkAppObjectAssociationDao upstreamLinkAppObjectAssociationDao;
    @Autowired
    private FsEmployeeRemote fsEmployeeRemote;
    @Autowired
    private MQSender MQSender;
    @Autowired
    private EnterpriseMessageService enterpriseMessageService;
    @Autowired
    private HttpComponentSignService httpComponentSignService;
    @Autowired
    private FxiaokeAccountManager fxiaokeAcccountManager;
    @Autowired
    private RelationLogServiceImpl relationLogService;
    /**
     * 默认开通的应用列表
     */
    private String upsteamDefaultApps;
    private String importExcelApps;
    @Autowired
    private UpstreamPolicyAuthDownstreamDao upstreamPolicyAuthDownstreamDao;
    @Autowired
    private UpstreamPolicyDataAuthRangeDao upstreamPolicyDataAuthRangeDao;
    @Autowired
    private UpstreamLinkAppDataAuthPolicyDao upstreamLinkAppDataAuthPolicyDao;
    @Autowired
    private LinkAppAssociationObjectService linkAppAssociationObjectService;
    @Autowired
    private LinkAppObjectAssociationInnerEventProducer linkAppObjectAssociationInnerEventProducer;
    @Autowired
    private LinkAppManager linkAppManager;
    @Autowired
    private LinkAppRangeAssociationDao linkAppRangeAssociationDao;
    @Autowired
    private FxiaokeEmployeeAssociationServiceImpl fxiaokeEmployeeAssociationService;
    @Autowired
    private UpstreamAppFlowOptionConfigDao upstreamAppFlowOptionConfigDao;
    @Autowired
    private LinkAppOuterRoleAssociationDao linkAppOuterRoleAssociationDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private UpstreamLinkAppOuterRoleAssociationDao upstreamLinkAppOuterRoleAssociationDao;
    @Autowired
    private UpstreamLinkAppDownEmployeeOuterRoleAssignDao upstreamLinkAppDownEmployeeOuterRoleAssignDao;
    @Autowired
    private UpstreamCustomerAppDataAuthDao upstreamCustomerAppDataAuthDao;
    @Autowired
    private UpstreamCustomerAppBizRoleAssociationDao upstreamCustomerAppBizRoleAssociationDao;
    @Autowired
    private UpstreamCustomerAppObjectAssociationDao upstreamCustomerAppObjectAssociationDao;
    @Autowired
    private UpstreamEmployeeCustomerAppRoleDao upstreamEmployeeCustomerAppRoleDao;
    @Autowired
    private OuterRoleServiceImpl outerRoleService;
    @Autowired
    private OuterRoleRemoteServiceImpl outerRoleRemoteService;
    @Autowired
    private OuterUserRoleServiceImpl outerUserRoleService;
    @Autowired
    private EmployeeCardServiceImpl employeeCardService;
    @Autowired
    private EnterpriseRelationObjService enterpriseRelationObjService;
    @Autowired
    private LinkAppOuterRoleService linkAppOuterRoleService;
    @Autowired
    private MQSender mqSender;
    @Resource(name = "linkSyncCrossCloudDataManager")
    private SyncCrossCloudDataManager syncCrossCloudDataManager;
    @Autowired
    private LinkAppEnterpriseTypeAssociationManager linkAppEnterpriseTypeAssociationManager;

    private List<SystemAppVo> systemAppVoApps;
    private static String PRM_LINK_APP_ID;

    @PostConstruct
    private void init() {
        IConfigFactory factory = ConfigFactory.getInstance();
        factory.getConfig("fs-er-provider-application", iConfig -> {
            upsteamDefaultApps = iConfig.get("upsteam_default_apps", "");
            upsteamDefaultApps = upsteamDefaultApps.replaceAll("，", ",");
            upsteamDefaultApps = upsteamDefaultApps.replaceAll("：", ":");
            importExcelApps = iConfig.get("import_excel_apps", "");

            String systemAppendApps = iConfig.get("wechat_system_append_apps", "");
            if (!Strings.isNullOrEmpty(systemAppendApps)) {
                systemAppVoApps = JsonUtil.getDisableHtmlEscapInstance().fromJson(systemAppendApps, new TypeToken<List<SystemAppVo>>() {
                }.getType());
            }
            log.info("reload from cms, upstreamDefaultApps: {},importExcelApps:{}", upsteamDefaultApps, importExcelApps);
        });

        ConfigFactory.getConfig("fs-er-key-gray", config -> {
            PRM_LINK_APP_ID = config.get("PRM_APP_ID");
        });
    }

    @Override
    public Result<String> getFirstUpstreamEaByApp(String linkAppId) {
        String authedUpstreamEa = upstreamEnterpriseLinkAppRelationDao.getFirstUpstreamEaByApp(linkAppId);
        return Result.newSuccess(authedUpstreamEa);
    }

    @Override
    public Result<Map<String, Set<String>>> listUpstreamAppOuterRoleIdsByLinkAppIds(String upstreamEa, Collection<String> linkAppIds) {
        if (CollectionUtil.isEmpty(linkAppIds)) {
            return Result.newSuccess(new HashMap<>());
        }
        List<UpstreamLinkAppOuterRoleAssociation> upstreamLinkAppOuterRoleAssociations = upstreamLinkAppOuterRoleAssociationDao.listByEaAppIds(upstreamEa, linkAppIds);
        List<LinkAppOuterRoleAssociation> linkAppOuterRoleAssociations = linkAppOuterRoleAssociationDao.listByAppsWithTimeDescOrder(linkAppIds);
        // System.out.println(Lists.newArrayList("FSAID_AAA","FSAID_BBB").stream().collect(Collectors.toMap(val -> val, Sets::newHashSet)));
        // 输出 {FSAID_AAA=[FSAID_AAA], FSAID_BBB=[FSAID_BBB]}
        Map<String, Set<String>> res = linkAppIds.stream().collect(Collectors.toMap(val -> val, x -> {
            return new HashSet<>();
        }));
        if (!CollectionUtils.isEmpty(upstreamLinkAppOuterRoleAssociations)) {
            for (UpstreamLinkAppOuterRoleAssociation upstreamLinkAppOuterRoleAssociation : upstreamLinkAppOuterRoleAssociations) {
                String linkAppId = upstreamLinkAppOuterRoleAssociation.getLinkAppId();
                String roleId = upstreamLinkAppOuterRoleAssociation.getRoleId();
                res.get(linkAppId).add(roleId);
            }
        }
        if (!CollectionUtils.isEmpty(linkAppOuterRoleAssociations)) {
            for (LinkAppOuterRoleAssociation upstreamLinkAppOuterRoleAssociation : linkAppOuterRoleAssociations) {
                String linkAppId = upstreamLinkAppOuterRoleAssociation.getLinkAppId();
                String roleId = upstreamLinkAppOuterRoleAssociation.getRoleId();
                res.get(linkAppId).add(roleId);
            }
        }
        return Result.newSuccess(res);
    }

    @Override
    public Result<List<SystemAppVo>> getSystemAppVos() {
        return Result.newSuccess(systemAppVoApps);
    }

    @Override
    public Result<Boolean> isSystemApp(String fsAppId) {
        if (StringUtils.isEmpty(fsAppId)) {
            return Result.newSuccess(false);
        }
        Set<String> systemAppIds = systemAppVoApps.stream().map(SystemAppVo::getLinkAppId).collect(Collectors.toSet());
        if (systemAppIds.contains(fsAppId)) {
            return Result.newSuccess(true);
        }
        return Result.newSuccess(false);
    }

    @Override
    public Result<String> getSystemAppNameByAppId(String fsAppId) {
        for (SystemAppVo systemAppVoApp : systemAppVoApps) {
            if (systemAppVoApp.getLinkAppId().equals(fsAppId)) {
                return Result.newSuccess(systemAppVoApp.getLinkAppName());
            }
        }
        return Result.newSuccess("");
    }

    @Override
    public Result<com.facishare.linkapp.api.result.UpstreamEmployeeRolesVo> getEmployeeLinkAppRoles(LinkAppIdArg linkAppIdArg) {
        boolean linkAppOpen = isLinkAppOpen(linkAppIdArg.getLinkAppId(), linkAppIdArg.getAuthContext().getEa()).getData();
        UpstreamEmployeeLinkAppRole selectCondition = new UpstreamEmployeeLinkAppRole();
        selectCondition.setUpstreamEa(linkAppIdArg.getAuthContext().getEa());
        selectCondition.setUpstreamFsUserId(linkAppIdArg.getAuthContext().getFsUserId());
        selectCondition.setLinkAppId(linkAppIdArg.getLinkAppId());
        List<UpstreamEmployeeLinkAppRole> roles = upstreamEmployeeLinkAppRoleDao.findByEntity(selectCondition);
        com.facishare.linkapp.api.result.UpstreamEmployeeRolesVo upstreamEmployeeIdentitiesVo = LinkAppManager.buildUpstreamEmployeeRolesVo(linkAppIdArg.getAuthContext().getFsUserId(), roles,
                linkAppOpen);
        return Result.newSuccess(upstreamEmployeeIdentitiesVo);
    }

    @Override
    public Result<Boolean> isAppVisitor(String upstreamEa, Integer employeeId) {
        UpstreamEmployeeLinkAppRole selective = UpstreamEmployeeLinkAppRole.newInstanceWithoutDate(upstreamEa, employeeId, null, UpstreamEmployeeLinkAppRoleEnum.COMMON.getRole(),
                MagicNumberConstants.STATUS_OPEN);
        List<UpstreamEmployeeLinkAppRole> roles = upstreamEmployeeLinkAppRoleDao.findBySelective(selective);
        return Result.newSuccess(roles != null && !roles.isEmpty());
    }

    @Override
    public Result<String> batchDeleteAllLinkAppEmployeeRole(BatchDeleteAllLinkAppEmployeeRoleArg arg) {
        arg.getUpstreamFsUserIds().forEach(upstreamFsUserId -> {
            UpstreamDeleteEmployeeRoleArg deleteEmployeeRoleArg = new UpstreamDeleteEmployeeRoleArg();
            deleteEmployeeRoleArg.setAuthContext(arg.getAuthContext());
            deleteEmployeeRoleArg.setDestEnterpriseAccount(arg.getUpstreamEa());
            deleteEmployeeRoleArg.setDestEmployeeId(upstreamFsUserId);
            deleteEmployeeRoleArg.setRole(arg.getRole());
            deleteAllAppIdEmployeeRole(deleteEmployeeRoleArg);
        });
        //通知应用中心
        enterpriseMessageService.notifyCrossHelpEntryVisibility(arg.getUpstreamEa(), arg.getUpstreamFsUserIds());
        return Result.newSuccess();
    }

    @Override
    public Result<List<LinkAppData>> listEnterpriseLinkApps(BaseArg baseArg) {
        List<UpstreamEnterpriseLinkAppRelation> upstreamEnterpriseLinkAppRelations = upstreamEnterpriseLinkAppRelationDao.listByEaWithoutStatus(baseArg.getAuthContext().getEa());
        Map<String, UpstreamEnterpriseLinkAppRelation> linkAppRelationMap = upstreamEnterpriseLinkAppRelations.stream()
                .collect(Collectors.toMap(UpstreamEnterpriseLinkAppRelation::getLinkAppId, r -> r));
        List<String> linkAppIds = upstreamEnterpriseLinkAppRelations.stream().map(UpstreamEnterpriseLinkAppRelation::getLinkAppId).collect(Collectors.toList());
        List<LinkAppData> linkAppVos = new ArrayList<>();
        if (linkAppIds != null && !linkAppIds.isEmpty()) {
            linkAppVos = linkAppDao.findByIds(linkAppIds);
        }

        Map<String, Set<String>> linkAppIdSupportMapperApiNameSetMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(linkAppIds)) {
            linkAppIds = linkAppVos.stream().map(LinkAppData::getId).collect(Collectors.toList());
            linkAppIdSupportMapperApiNameSetMap = linkAppEnterpriseTypeAssociationManager.batchListSupportMapperApiNamesByLinkAppIds(linkAppIds);
        }

        Map<String, Set<String>> finalLinkAppIdSupportMapperApiNameSetMap = linkAppIdSupportMapperApiNameSetMap;
        linkAppVos.forEach(linkAppVo -> {
            String linkAppId = linkAppVo.getId();
            UpstreamEnterpriseLinkAppRelation upstreamEnterpriseLinkAppRelation = linkAppRelationMap.get(linkAppId);
            linkAppVo.setAuthType(upstreamEnterpriseLinkAppRelation.getAuthType());
            linkAppVo.setStatus(upstreamEnterpriseLinkAppRelation.getStatus());
            linkAppVo.setInternalVisibleRangePrompt(I18nUtil.get(("erapp_" + linkAppId + "_internalVisibleRangePrompt"), linkAppVo.getInternalVisibleRangePrompt()));

            Set<String> supportMapperApiNames = finalLinkAppIdSupportMapperApiNameSetMap.get(linkAppId);
            if (supportMapperApiNames != null && !supportMapperApiNames.isEmpty()) {
                linkAppVo.setSupportMapperApiNames(supportMapperApiNames);
            }
        });

        linkAppVos.sort(LinkAppData.getLinkAppVoComparator());
        return Result.newSuccess(linkAppVos);
    }

    @Override
    public Result initAppRolesForUpstream(Collection<String> upstreamEas, String linkAppId) {
        linkAppManager.initAppRolesForUpstream(upstreamEas, linkAppId);
        return Result.newSuccess();
    }

    @Override
    public Result initAppRolesForUpstreamNew(Collection<String> upstreamEas, String linkAppId) {
        linkAppManager.initAppRolesForUpstreamNew(upstreamEas, linkAppId);
        return Result.newSuccess();
    }

    public Result<List<LinkAppData>> listBenchApps(String ea, Integer employeeId) {
        List<LinkAppData> linkAppDataList = null;
        if (employeeId == null || employeeId < 0) {
            BaseArg baseArg = new BaseArg();
            baseArg.setAuthContextWithEaAndFsUserId(ea, employeeId);
            linkAppDataList = listEnterpriseLinkApps(baseArg).getData();
        } else {
            List<UpstreamEmployeeLinkAppRole> upstreamEmployeeLinkAppRoles = upstreamEmployeeLinkAppRoleDao.listByEmployeeId(ea, employeeId, MagicNumberConstants.STATUS_OPEN);
            Set<String> linkAppIds = upstreamEmployeeLinkAppRoles.stream().map(x -> x.getLinkAppId()).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(linkAppIds)) {
                linkAppDataList = linkAppDao.findByIds(linkAppIds);
            } else {
                linkAppDataList = new ArrayList<>();
            }
        }
        CloudConfigVo cloudConfigVo = cloudUtil.getCloudConfig(ea).getData();
        List<LinkAppData> result = new ArrayList<>();
        for (LinkAppData linkAppVo : linkAppDataList) {
            if (Strings.isNullOrEmpty(linkAppVo.getAppManagerUrl()) && Strings.isNullOrEmpty(linkAppVo.getWebManagerUrl())) {
                continue;
            }
            linkAppVo.setAppUrl(null);
            linkAppVo.setWebUrl(null);
            linkAppVo.setWebManagerUrl(CloudUtil.convertCloudAppUrl(cloudConfigVo, linkAppVo.getId(), linkAppVo.getWebManagerUrl()));
            linkAppVo.setAppManagerUrl(CloudUtil.convertCloudAppUrl(cloudConfigVo, linkAppVo.getId(), linkAppVo.getAppManagerUrl()));
            result.add(linkAppVo);
        }
        result.sort(LinkAppData.getLinkAppVoComparator());
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<LinkAppData>> listLinkAppDatas(List<String> linkAppIds, String ea) {
        List<LinkAppData> linkAppVos = new ArrayList<>();
        if (linkAppIds != null && !linkAppIds.isEmpty()) {
            linkAppVos = linkAppDao.findByIds(linkAppIds);
        }

        List<UpstreamEnterpriseLinkAppRelation> upstreamEnterpriseLinkAppRelations = upstreamEnterpriseLinkAppRelationDao.listByEaWithoutStatus(ea);
        Map<String, UpstreamEnterpriseLinkAppRelation> linkAppRelationMap = upstreamEnterpriseLinkAppRelations.stream()
                .collect(Collectors.toMap(UpstreamEnterpriseLinkAppRelation::getLinkAppId, r -> r));

        linkAppVos.forEach(linkAppVo -> {
            String linkAppId = linkAppVo.getId();
            UpstreamEnterpriseLinkAppRelation upstreamEnterpriseLinkAppRelation = linkAppRelationMap.get(linkAppId);
            if (upstreamEnterpriseLinkAppRelation != null) {
                linkAppVo.setAuthType(upstreamEnterpriseLinkAppRelation.getAuthType());
                linkAppVo.setStatus(upstreamEnterpriseLinkAppRelation.getStatus());
            }
        });
        linkAppVos.sort(LinkAppData.getLinkAppVoComparator());
        return Result.newSuccess(linkAppVos);
    }

    @Override
    public Result<List<LinkAppData>> listEnterpriseLinkAppsByEnterpriseType(BaseArg baseArg, Boolean onlySupportNoFsAccount) {
        List<UpstreamEnterpriseLinkAppRelation> upstreamEnterpriseLinkAppRelations = upstreamEnterpriseLinkAppRelationDao.listByEaWithoutStatus(baseArg.getAuthContext().getEa());
        Map<String, UpstreamEnterpriseLinkAppRelation> linkAppRelationMap = upstreamEnterpriseLinkAppRelations.stream()
                .collect(Collectors.toMap(UpstreamEnterpriseLinkAppRelation::getLinkAppId, r -> r));
        List<String> linkAppIds = upstreamEnterpriseLinkAppRelations.stream().map(UpstreamEnterpriseLinkAppRelation::getLinkAppId).collect(Collectors.toList());
        List<LinkAppData> linkAppVos = new ArrayList<>();
        if (linkAppIds != null && !linkAppIds.isEmpty()) {
            linkAppVos = linkAppDao.findByIds(linkAppIds);
        }
        List<LinkAppData> filteredLinkAppVos = linkAppVos.stream().filter(x -> isMatchType(x, onlySupportNoFsAccount)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredLinkAppVos)) {
            return Result.newSuccess(new ArrayList<>());
        }

        filteredLinkAppVos.forEach(linkAppVo -> {
            String linkAppId = linkAppVo.getId();
            boolean valid = !baseArg.isOnlyOpen() || isLinkAppOpen(linkAppId, baseArg.getAuthContext().getEa()).getData();
            UpstreamEnterpriseLinkAppRelation upstreamEnterpriseLinkAppRelation = linkAppRelationMap.get(linkAppId);
            linkAppVo.setAuthType(upstreamEnterpriseLinkAppRelation.getAuthType());
            if (valid && upstreamEnterpriseLinkAppRelation != null) {
                linkAppVo.setStatus(upstreamEnterpriseLinkAppRelation.getStatus());
            }
        });
        filteredLinkAppVos.sort(LinkAppData.getLinkAppVoComparator());
        return Result.newSuccess(filteredLinkAppVos);
    }

    private Boolean isMatchType(LinkAppData linkApp, Boolean onlySupprotNoFsAccount) {
        if (("" + IdentityType.PERSONAL.getType()).equals(linkApp.getSupportIdentityTypes())) {
            return false;
        }

        Boolean typeSupport = false;
        if (typeSupport && BooleanUtils.isTrue(onlySupprotNoFsAccount)) {
            return BooleanUtils.isTrue(linkApp.getSupportNoFsAccount());
        }
        return typeSupport;
    }

    @Override
    public Result<List<UpstreamEmployeeVo>> listAdminsByLinkAppId(LinkAppIdArg arg) {
        LinkAppIdAndRoleArg linkAppIdAndRoleArg = new LinkAppIdAndRoleArg();
        linkAppIdAndRoleArg.setAuthContextWithEaAndFsUserId(arg.getAuthContext().getEa(), arg.getAuthContext().getFsUserId());
        linkAppIdAndRoleArg.setRole(UpstreamEmployeeRoleEnum.MANAGER.getRole());
        linkAppIdAndRoleArg.setLinkAppId(arg.getLinkAppId());
        return listEmployeesByLinkAppIdAndRole(linkAppIdAndRoleArg);
    }

    @Override
    public Result<List<String>> listLinkAppIdsByAdminId(String upstreamEa, Integer employeeId) {
        if (Strings.isNullOrEmpty(upstreamEa) || employeeId == null) {
            return Result.newSuccess(new ArrayList<>());
        }
        UpstreamEmployeeLinkAppRole selectUpstreamEmployeeLinkAppRole = UpstreamEmployeeLinkAppRole.newInstanceWithoutDate(upstreamEa, employeeId, null, UpstreamEmployeeRoleEnum.MANAGER.getRole(),
                null);
        List<UpstreamEmployeeLinkAppRole> upstreamEmployeeLinkAppRoles = upstreamEmployeeLinkAppRoleDao.findBySelective(selectUpstreamEmployeeLinkAppRole);
        if (upstreamEmployeeLinkAppRoles == null) {
            return Result.newSuccess(new ArrayList<>());
        }
        return Result.newSuccess(upstreamEmployeeLinkAppRoles.stream().map(UpstreamEmployeeLinkAppRole::getLinkAppId).distinct().collect(Collectors.toList()));
    }

    @Override
    public Result<List<Integer>> listAdminIdsByLinkAppId(LinkAppIdArg arg) {
        UpstreamEmployeeLinkAppRole selectUpstreamEmployeeLinkAppRole = UpstreamEmployeeLinkAppRole.newInstanceWithoutDate(arg.getAuthContext().getEa(), null, arg.getLinkAppId(),
                UpstreamEmployeeRoleEnum.MANAGER.getRole(), 1);
        List<UpstreamEmployeeLinkAppRole> upstreamEmployeeLinkAppRoles = upstreamEmployeeLinkAppRoleDao.findByEntity(selectUpstreamEmployeeLinkAppRole);
        return Result.newSuccess(upstreamEmployeeLinkAppRoles.stream().map(UpstreamEmployeeLinkAppRole::getUpstreamFsUserId).collect(Collectors.toList()));
    }

    @Override
    public Result<List<UpstreamEmployeeVo>> listEmployeesByLinkAppIdAndRole(LinkAppIdAndRoleArg arg) {
        boolean linkAppOpen = isLinkAppOpen(arg.getLinkAppId(), arg.getAuthContext().getEa()).getData();
        List<Integer> employeeIds = listEmployeeIdsByLinkAppIdAndRole(arg.getAuthContext().getEa(), arg.getLinkAppId(), arg.getRole());
        if (CollectionUtils.isEmpty(employeeIds)) {
            return Result.newSuccess(new ArrayList<>(0));
        }
        List<UpstreamEmployeeVo> upstreamEmployeeVos = fsEmployeeRemote.transferFsUserIdsToUpstreamEmployeeVos(arg.getAuthContext().getEa(), employeeIds);
        for (UpstreamEmployeeVo upstreamEmployeeVo : upstreamEmployeeVos) {
            upstreamEmployeeVo.setLinkAppOpen(linkAppOpen);
            upstreamEmployeeVo.setNameSpell(JPinYinUtil.createSpell(upstreamEmployeeVo.getName()));
        }
        return Result.newSuccess(upstreamEmployeeVos);
    }

    private List<Integer> listEmployeeIdsByLinkAppIdAndRole(String upstreamEa, String linkAppId, Integer role) {
        UpstreamEmployeeLinkAppRole selectUpstreamEmployeeLinkAppRole = UpstreamEmployeeLinkAppRole.newInstanceWithoutDate(upstreamEa, null, linkAppId, role, 1);
        List<UpstreamEmployeeLinkAppRole> upstreamEmployeeLinkAppRoles = upstreamEmployeeLinkAppRoleDao.findBySelective(selectUpstreamEmployeeLinkAppRole);
        if (upstreamEmployeeLinkAppRoles == null) {
            return new ArrayList<>();
        }
        return upstreamEmployeeLinkAppRoles.stream().map(UpstreamEmployeeLinkAppRole::getUpstreamFsUserId).distinct().collect(Collectors.toList());
    }

    @Override
    public Result<List<String>> createUpstreamEmployeeLinkAppRoles(UpstreamEmployeeAppRelationsArg arg) {
        AssertUtil.assertIfEmpty(arg);
        log.info(arg.toString());
        arg.getLinkAppIds().forEach(linkAppId -> {
            List<Integer> dbEmployeeIdList = listEmployeeIdsByLinkAppIdAndRole(arg.getAuthContext().getEa(), linkAppId, arg.getRole());
            List<Integer> addedAdminList = Lists.newArrayList(arg.getFsUserIds());
            addedAdminList.removeAll(dbEmployeeIdList);
            log.info("trace createUpstreamEmployeeLinkAppRoles linkAppId:{}, addedAdminList:{}", linkAppId, addedAdminList);
            addedAdminList.forEach(fsUserId -> {
                UpstreamEmployeeLinkAppRole relation = UpstreamEmployeeLinkAppRole.newInstanceWithoutDate(arg.getAuthContext().getEa(), fsUserId, linkAppId, arg.getRole(), null);
                List<UpstreamEmployeeLinkAppRole> linkAppRoles = upstreamEmployeeLinkAppRoleDao.findBySelective(relation);
                if (CollectionUtils.isEmpty(linkAppRoles)) {
                    relation.init();
                    relation.setStatus(MagicNumberConstants.STATUS_OPEN);
                    upstreamEmployeeLinkAppRoleDao.insertAndSetObjectId(relation);
                    if (UpstreamEmployeeRoleEnum.MANAGER.getRole().equals(relation.getRole())) {
                        sendAdmainTipsMessage(relation);
                        log.info("sendAdmainTipsMessage,UpstreamEmployeeLinkAppRole:{}", relation.toString());
                    }
                } else {
                    if (MagicNumberConstants.STATUS_ClOSE.equals(linkAppRoles.get(0).getStatus())) {
                        relation.setStatus(MagicNumberConstants.STATUS_OPEN);
                        relation.setVersion(relation.getVersion() == null ? 1 : relation.getVersion() + 1);
                        upstreamEmployeeLinkAppRoleDao.updateAutoIncrVersion(relation);
                    }
                }
            });
            ComponentSignArg componentSignArg = new ComponentSignArg();
            BaseArg.AuthContext authContext = arg.getAuthContext();
            componentSignArg.setUserIds(addedAdminList);
            componentSignArg.setCurrentEnterpriseAccount(authContext.getEa());
            componentSignArg.setUserId(authContext.getFsUserId());
            componentSignArg.setDownEnterprise(false);
            componentSignArg.setAppId(linkAppId);
            //设置new标记
            if (!arg.isMigrate()) {
                httpComponentSignService.setNewStatusForApp(componentSignArg);
            }
            //通知应用中心
            enterpriseMessageService.notifyCrossHelpEntryVisibility(arg.getAuthContext().getEa(), addedAdminList);
            if (!addedAdminList.isEmpty()) {
                //MQSender.send(CreateUpstreamEmployeeLinkAppRoleEvent.TAG,
                //        CreateUpstreamEmployeeLinkAppRoleEvent.newInstance(linkAppId, arg.getRole(), arg.getAuthContext().getEa(), Sets.newHashSet(addedAdminList)));
                CloudContextUtil.executeInCloudContext("" + eieaConverter.enterpriseAccountToId(arg.getAuthContext().getEa()), () -> MQSender.send(CreateUpstreamEmployeeLinkAppRoleEvent.TAG,
                        CreateUpstreamEmployeeLinkAppRoleEvent.newInstance(linkAppId, arg.getRole(), arg.getAuthContext().getEa(), Sets.newHashSet(addedAdminList))));
            }
        });
        //记录应用管理员变更
        if (!arg.isMigrate() && arg.getRole().equals(UpstreamEmployeeRoleEnum.MANAGER.getRole()) && Objects.nonNull(arg.getAuthContext().getFsUserId())) {
            CommonPoolUtil.execute(() -> recordAppAdminChange(arg.getAuthContext().getEa(), arg.getAuthContext().getFsUserId(), arg.getLinkAppIds(), arg.getFsUserIds()));
        }
        return Result.newSuccess();
    }

    /**
     * 记录应用管理员变更
     *
     * @param upstreamEa   上游
     * @param operatorId   操作者ID
     * @param linkAppIds   应用ID列表
     * @param tarAdminList 目标管理员列表
     */
    private void recordAppAdminChange(String upstreamEa, Integer operatorId, List<String> linkAppIds, List<Integer> tarAdminList) {
        List<LinkAppData> appList = linkAppDao.findByIds(linkAppIds);
        Map<String, String> appIdNameMap = appList.stream().collect(Collectors.toMap(LinkAppData::getId, LinkAppData::getName));
        linkAppIds.forEach(appId -> relationLogService.createAppAdminChangeLog(upstreamEa, operatorId, appIdNameMap.get(appId), appId, tarAdminList));
    }

    private void sendAdmainTipsMessage(UpstreamEmployeeLinkAppRole upstreamEmployeeLinkAppRole) {
        try {
            String linkAppId = upstreamEmployeeLinkAppRole.getLinkAppId();
            LinkAppData app = linkAppDao.getById(linkAppId);
            AssertUtil.assertIfEmpty(app);
            if (StringUtil.isNullOrEmpty(app.getAddManagerTipsUrl())) {
                return;
            }
            String appName = app.getName();
            OTTemplateMessage message = new OTTemplateMessage();
            OTTemplateMessage.Title title = new OTTemplateMessage.Title();
            title.content = appName + I18nUtil.get(I18nKeyEnum.EIP_CUSTOMERAPP_MESSAGESENDER_91);
            title.time = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
            message.title = title;
            message.button = new OTTemplateMessage.Button();
            message.button.title = I18nUtil.get(I18nKeyEnum.EIP_ERADMIN_MSGMANAGER_99);
            message.button.url = app.getAddManagerTipsUrl();
            message.first = new OTTemplateMessage.Frist();
            message.first.content = I18nUtil.get(I18nKeyEnum.EIP_LINKAPP_DOWNSTREAMSERVICEIMPL_325);
            enterpriseMessageService.sendCrossHelperMessages(upstreamEmployeeLinkAppRole.getUpstreamEa(), Lists.newArrayList(upstreamEmployeeLinkAppRole.getUpstreamFsUserId()), message);
        } catch (Exception e) {
            log.warn("sendAdmainTipsMessage:" + upstreamEmployeeLinkAppRole, e);
        }
    }

    @Override
    public Result<String> updateEmployeeLinkAppRoles(UpstreamEmployeeAppRelationsArg arg) {
        arg.getLinkAppIds().forEach(linkAppId -> {
            UpstreamEmployeeLinkAppRole selectRole = UpstreamEmployeeLinkAppRole.newInstanceWithoutDate(arg.getAuthContext().getEa(), null, linkAppId, arg.getRole(), null);
            Set<Integer> dbEmployeeIds = upstreamEmployeeLinkAppRoleDao.findBySelective(selectRole).stream().map(UpstreamEmployeeLinkAppRole::getUpstreamFsUserId)
                    .collect(Collectors.toCollection(HashSet::new));
            Set<Integer> employeeIdToRemove = new HashSet<>();
            employeeIdToRemove.addAll(dbEmployeeIds);
            employeeIdToRemove.removeAll(arg.getFsUserIds());
            if (!employeeIdToRemove.isEmpty()) {
                int i = upstreamEmployeeLinkAppRoleDao.batchDelete(arg.getAuthContext().getEa(), employeeIdToRemove, linkAppId, arg.getRole());
                if (i > 0) {
                    syncDataAfterBatchDelete(arg.getAuthContext().getEa(), arg.getRole(), linkAppId, employeeIdToRemove);
                }
                //MQSender
                //        .send(DeleteUpstreamEmployeeLinkAppRoleEvent.TAG, DeleteUpstreamEmployeeLinkAppRoleEvent.newInstance(linkAppId, arg.getRole(), arg.getAuthContext().getEa(), employeeIdToRemove));
                CloudContextUtil.executeInCloudContext("" + eieaConverter.enterpriseAccountToId(arg.getAuthContext().getEa()), () -> MQSender.send(DeleteUpstreamEmployeeLinkAppRoleEvent.TAG,
                        DeleteUpstreamEmployeeLinkAppRoleEvent.newInstance(linkAppId, arg.getRole(), arg.getAuthContext().getEa(), employeeIdToRemove)));
            }
            Set<Integer> employeeIdToCreate = new HashSet<>();
            employeeIdToCreate.addAll(arg.getFsUserIds());
            employeeIdToCreate.removeAll(dbEmployeeIds);
            if (!employeeIdToCreate.isEmpty()) {
                employeeIdToCreate.forEach(fsUserId -> {
                    UpstreamEmployeeLinkAppRole relation = UpstreamEmployeeLinkAppRole.newInstanceWithDate(arg.getAuthContext().getEa(), fsUserId, linkAppId, arg.getRole(), 1);
                    int i = upstreamEmployeeLinkAppRoleDao.insertAndSetObjectId(relation);
                    if (i > 0) {
                        syncCrossCloudDataManager.sendSyncCrossDataMsg(relation.getId(), "upstream_employee_link_app_role", DaoOperateEnum.UPSERT.getType());
                    }
                });
                //MQSender
                //        .send(CreateUpstreamEmployeeLinkAppRoleEvent.TAG, CreateUpstreamEmployeeLinkAppRoleEvent.newInstance(linkAppId, arg.getRole(), arg.getAuthContext().getEa(), employeeIdToCreate));
                CloudContextUtil.executeInCloudContext("" + eieaConverter.enterpriseAccountToId(arg.getAuthContext().getEa()), () -> MQSender.send(CreateUpstreamEmployeeLinkAppRoleEvent.TAG,
                        CreateUpstreamEmployeeLinkAppRoleEvent.newInstance(linkAppId, arg.getRole(), arg.getAuthContext().getEa(), employeeIdToCreate)));
                CommonPoolUtil.execute(() -> {
                    ComponentSignArg componentSignArg = new ComponentSignArg();
                    BaseArg.AuthContext authContext = arg.getAuthContext();
                    componentSignArg.setUserIds(Lists.newArrayList(employeeIdToCreate));
                    componentSignArg.setCurrentEnterpriseAccount(authContext.getEa());
                    componentSignArg.setUserId(authContext.getFsUserId());
                    componentSignArg.setDownEnterprise(false);
                    componentSignArg.setAppId(linkAppId);
                    //设置new标记
                    httpComponentSignService.setNewStatusForApp(componentSignArg);
                });
            }
        });
        //记录应用管理员变更
        if (arg.getRole().equals(UpstreamEmployeeRoleEnum.MANAGER.getRole()) && Objects.nonNull(arg.getAuthContext().getFsUserId())) {
            CommonPoolUtil.execute(() -> recordAppAdminChange(arg.getAuthContext().getEa(), arg.getAuthContext().getFsUserId(), arg.getLinkAppIds(), arg.getFsUserIds()));
        }
        //通知应用中心
        enterpriseMessageService.notifyCrossHelpEntryVisibility(arg.getAuthContext().getEa(), arg.getFsUserIds());
        return Result.newSuccess();
    }

    private void syncDataAfterBatchDelete(String upstreamEa, Integer role, String linkAppId, Set<Integer> fsUserIds) {
        try {
            StringBuilder sb = new StringBuilder();
            for (Integer fsUserId : fsUserIds) {
                if (!StringUtil.isNullOrEmpty(sb.toString())) {
                    sb.append(",");
                }
                sb.append(fsUserId);
            }
            String sql =
                    "delete from upstream_employee_link_app_role where upstream_ea='" + upstreamEa + "' and role=" + role + " and link_app_id='" + linkAppId + "' and upstream_fs_user_id in (" + sb + ")";
            syncCrossCloudDataManager.sendSqlSyncCrossDataMsg(sql, "upstream_employee_link_app_role");
        } catch (Exception e) {
            log.warn("syncDataAfterBatchDelete error.", e);
        }
    }

    @Override
    public Result<String> deleteUpstreamEmployeeLinkAppRoles(UpstreamEmployeeAppRelationsArg arg) {
        Set<Integer> employeeIdsToRemove = new HashSet<>(arg.getFsUserIds());
        arg.getLinkAppIds().forEach(linkAppId -> {
            upstreamEmployeeLinkAppRoleDao.batchDelete(arg.getAuthContext().getEa(), employeeIdsToRemove, linkAppId, arg.getRole());
            if (UpstreamEmployeeRoleEnum.MANAGER.getRole().equals(arg.getRole())) {
                for (String linkApp : arg.getLinkAppIds()) {
                    for (Integer fsUserId : arg.getFsUserIds()) {
                        log.info("sendCancelAdmainTipsMessage,arg:{}", arg.toString());
                        LinkAppData app = linkAppDao.getById(linkApp);
                        String content = I18nUtil.get(I18nKeyEnum.EIP_LINKAPP_LINKAPPOUTSERVICEIMPL_222);
                        content = String.format(content, app.getName());
                        InternationalItem internationalItem = new InternationalItem(QXMessageEnums.CANCEL_APP_ADMIN.getKey(), app.getName());

                        enterpriseMessageService.sendCrossHelpTextMessage(arg.getAuthContext().getEa(), Collections.singletonList(fsUserId), content, internationalItem);
                    }
                }
            }
            //MQSender.send(DeleteUpstreamEmployeeLinkAppRoleEvent.TAG, DeleteUpstreamEmployeeLinkAppRoleEvent.newInstance(linkAppId, arg.getRole(), arg.getAuthContext().getEa(), employeeIdsToRemove));
            CloudContextUtil.executeInCloudContext("" + eieaConverter.enterpriseAccountToId(arg.getAuthContext().getEa()), () -> MQSender.send(DeleteUpstreamEmployeeLinkAppRoleEvent.TAG,
                    DeleteUpstreamEmployeeLinkAppRoleEvent.newInstance(linkAppId, arg.getRole(), arg.getAuthContext().getEa(), employeeIdsToRemove)));
        });
        //记录应用管理员变更
        if (arg.getRole().equals(UpstreamEmployeeRoleEnum.MANAGER.getRole()) && Objects.nonNull(arg.getAuthContext().getFsUserId())) {
            CommonPoolUtil.execute(() -> {
                //查询当前范围
                List<UpstreamEmployeeLinkAppRole> linkAppRoles = upstreamEmployeeLinkAppRoleDao.listEmployeesByLinkAppIds(arg.getAuthContext().getEa(), arg.getLinkAppIds(), arg.getRole());
                Map<String, List<Integer>> appIdAdminMap = linkAppRoles.stream()
                        .collect(Collectors.groupingBy(UpstreamEmployeeLinkAppRole::getLinkAppId, Collectors.mapping(UpstreamEmployeeLinkAppRole::getUpstreamFsUserId, Collectors.toList())));
                List<LinkAppData> appList = linkAppDao.findByIds(arg.getLinkAppIds());
                Map<String, String> appIdNameMap = appList.stream().collect(Collectors.toMap(LinkAppData::getId, LinkAppData::getName));
                appIdAdminMap.forEach((appId, admins) -> relationLogService.createAppAdminChangeLog(arg.getAuthContext().getEa(), arg.getAuthContext().getFsUserId(), appIdNameMap.get(appId), appId, admins));
            });
        }
        //通知应用中心
        enterpriseMessageService.notifyCrossHelpEntryVisibility(arg.getAuthContext().getEa(), arg.getFsUserIds());
        return Result.newSuccess();
    }

    @Override
    public Result<String> deleteAllAppIdEmployeeRole(UpstreamDeleteEmployeeRoleArg arg) {
        UpstreamEmployeeLinkAppRole role = new UpstreamEmployeeLinkAppRole();
        role.setRole(arg.getRole());
        role.setUpstreamFsUserId(arg.getDestEmployeeId());
        role.setUpstreamEa(arg.getDestEnterpriseAccount());
        List<UpstreamEmployeeLinkAppRole> list = upstreamEmployeeLinkAppRoleDao.findBySelective(role);
        list = list == null ? new ArrayList<>() : list;
        list.forEach(entity -> {
            upstreamEmployeeLinkAppRoleDao.deleteById(entity.getId());
            //MQSender.send(DeleteUpstreamEmployeeLinkAppRoleEvent.TAG,
            //        DeleteUpstreamEmployeeLinkAppRoleEvent.newInstance(entity.getLinkAppId(), arg.getRole(), arg.getDestEnterpriseAccount(), Sets.newHashSet(arg.getDestEmployeeId())));
            CloudContextUtil.executeInCloudContext("" + eieaConverter.enterpriseAccountToId(arg.getDestEnterpriseAccount()), () -> MQSender.send(DeleteUpstreamEmployeeLinkAppRoleEvent.TAG,
                    DeleteUpstreamEmployeeLinkAppRoleEvent.newInstance(entity.getLinkAppId(), arg.getRole(), arg.getDestEnterpriseAccount(), Sets.newHashSet(arg.getDestEmployeeId()))));
            //通知应用中心
            enterpriseMessageService.notifyCrossHelpEntryVisibility(entity.getUpstreamEa(), Lists.newArrayList(entity.getUpstreamFsUserId()));
        });
        return Result.newSuccess();
    }

    @Override
    public Result<String> batchCreateEnterpriseLinkAppRelations(String linkAppId, List<String> enterpriseAccounts, Integer fsUserId) {
        return this.batchCreateEnterpriseLinkAppRelations(linkAppId, enterpriseAccounts, fsUserId, true);
    }

    @Override
    public Result<String> batchCreateEnterpriseLinkAppRelations(String linkAppId, List<String> enterpriseAccounts, Integer fsUserId, boolean isOpen) {
        LinkAppData linkAppVo = linkAppService.getLinkApp(linkAppId).getData();
        Set<String> appNewAuthEaSet = new HashSet<>();
        Integer authType = linkAppVo.getAuthType();
        enterpriseAccounts.forEach(ea -> {
            if (linkAppId.equals(PRM_LINK_APP_ID)) {
                ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(eieaConverter.enterpriseAccountToId(ea) + "", SuperUserConstants.USER_ID + "");
                enterpriseRelationObjService.initEnterpriseRelationObjMapperPartnerField(serviceContext, true); // 支持幂等操作
            }

            UpstreamEnterpriseLinkAppRelation relation = UpstreamEnterpriseLinkAppRelation.newInstanceWithDate(ea, linkAppId, isOpen ? 1 : 2);
            UpstreamEnterpriseLinkAppRelation old = upstreamEnterpriseLinkAppRelationDao.findByUpstreamEaAndLinkAppId(ea, linkAppId);
            if (old == null) {
                upstreamEnterpriseLinkAppRelationDao.insertAndSetObjectId(relation);
                appNewAuthEaSet.add(ea);
                // 根据authType给用户授权
                updateOuterRole(ea, linkAppId, authType);
                if (isOpen) {
                    //MQSender.send(UpdateUpstreamEnterpriseLinkAppStatus   Event.TAG, UpdateUpstreamEnterpriseLinkAppStatusEvent.newInstance(linkAppId, ea, fsUserId, MagicNumberConstants.STATUS_OPEN));
                    CloudContextUtil.executeInCloudContext("" + eieaConverter.enterpriseAccountToId(ea), () -> MQSender.send(UpdateUpstreamEnterpriseLinkAppStatusEvent.TAG,
                            UpdateUpstreamEnterpriseLinkAppStatusEvent.newInstance(linkAppId, ea, fsUserId, MagicNumberConstants.STATUS_OPEN)));
                }
            }
        });
        // 初始化互联角色数据（支持幂等）
        if ("1".equals(ConfigUtil.linkAppSystemRoleGraySwitch) && ConfigUtil.linkAppOuterRoleMap.containsKey(linkAppId)) {
            linkAppManager.initAppRolesForUpstreamNew(appNewAuthEaSet, linkAppId);     //初始化各企业的应用级系统预置外部角色，从配置中心获取
        } else {
            linkAppManager.initAppRolesForUpstream(appNewAuthEaSet, linkAppId);        //初始化各企业的应用级系统预置外部角色
        }
        if (!appNewAuthEaSet.isEmpty()) {
            // 发送应用预设对象的MQ消息
            List<LinkAppAssociationObjectData> linkAppAssociationObjectDatas = linkAppAssociationObjectService.listSystemAssociationObjects(linkAppId).getData();
            if (CollectionUtils.isNotEmpty(linkAppAssociationObjectDatas)) {
                for (String ea : appNewAuthEaSet) {
                    for (LinkAppAssociationObjectData linkAppAssociationObjectData : linkAppAssociationObjectDatas) {
                        LinkAppObjectAssociationInnerEvent instance = LinkAppObjectAssociationInnerEvent.getInstance(this, ea, linkAppId, linkAppAssociationObjectData.getObjectApiName());
                        linkAppObjectAssociationInnerEventProducer.sendLinkAppObjectAssociationInnerEvent(instance);
                    }
                }
            }
        }

        return Result.newSuccess();
    }

    private void updateOuterRole(String ea, String linkAppId, Integer authType) {
        if (LinkAppAuthTypeEnum.NONE.getType() == authType) {
            return;
        }
        List<OuterRoleVo> roleVo = Lists.newLinkedList();
        if (ALL.getType() == authType) {
            roleVo.add(buildVo(GlobalRoleConstants.PERSONAL_ROLE_CODE, AppRoleType.OUTER_CUSTOM_ROLE.getType()));
            roleVo.add(buildVo(GlobalRoleConstants.ENTERPRISE_ROLE_CODE, AppRoleType.OUTER_CUSTOM_ROLE.getType()));
        } else if (LinkAppAuthTypeEnum.ALL_ER_ROLE.getType() == authType) {
            roleVo.add(buildVo(GlobalRoleConstants.ENTERPRISE_ROLE_CODE, AppRoleType.OUTER_CUSTOM_ROLE.getType()));
        } else {
            roleVo.add(buildVo(GlobalRoleConstants.PERSONAL_ROLE_CODE, AppRoleType.OUTER_CUSTOM_ROLE.getType()));
        }
        linkAppOuterRoleService.batchAddEaAppRoles(ea, linkAppId, roleVo);
    }

    private OuterRoleVo buildVo(String roleId, int roleType) {
        OuterRoleVo outerRoleVo = new OuterRoleVo();
        outerRoleVo.setRoleType(roleType);
        outerRoleVo.setRoleId(roleId);
        return outerRoleVo;
    }

    @Override
    public Result<String> createEnterpriseDefaultAppRelations(String enterpriseAccount, Integer fsUserId) {
        SystemAppInfo systemAppInfo = new SystemAppInfo().resolve(upsteamDefaultApps);
        if (systemAppInfo.resolveFail()) {
            return Result.newError(ResultCode.EMPTY_APP_LIST);
        }
        List<String> appIds = systemAppInfo.getAppIds();
        Map<String, Boolean> statusMap = systemAppInfo.getStatusMap();
        log.info("createEnterpriseDefaultAppRelations appIds:{},enterpriseAccount:{}", appIds, enterpriseAccount);
        List<LinkAppData> exitApps = linkAppDao.findByIds(appIds);
        if (exitApps == null || exitApps.isEmpty()) {
            log.info("createEnterpriseDefaultAppRelations exitApps empty");
            return Result.newError(ResultCode.EMPTY_APP_LIST);
        }
        List<Integer> adminIds = fxiaokeAcccountManager.listRelationAdmins(enterpriseAccount);
        exitApps.forEach(app -> {
            this.batchCreateEnterpriseLinkAppRelations(app.getId(), Lists.newArrayList(enterpriseAccount), fsUserId, statusMap.get(app.getId()));
            if (adminIds != null && !adminIds.isEmpty()) {
                setLinkAdminsToAppAdmins(enterpriseAccount, adminIds, Arrays.asList(app.getId()));
            } else {
                log.warn("Init set linkApp:{} ea:{} error, get linkAdmins Error", app.getId(), enterpriseAccount);
            }
        });
        return Result.newSuccess();
    }

    private void setLinkAdminsToAppAdmins(String upstreamEa, List<Integer> adminIds, List<String> linkAppIds) {
        List<LinkAppData> linkAppVos = linkAppDao.findByIds(linkAppIds);
        List<String> linkAppIdToAdd = linkAppVos.stream().filter(Objects::nonNull).filter(linkAppVo -> BooleanUtils.isTrue(linkAppVo.getSupportSetManager())).map(LinkAppData::getId)
                .collect(Collectors.toList());
        final List<UpstreamEmployeeLinkAppRole> upstreamEmployeeLinkAppRoles = new ArrayList<>();
        linkAppIdToAdd.forEach(linkAppId -> {
            adminIds.forEach(adminId -> {
                UpstreamEmployeeLinkAppRole role = UpstreamEmployeeLinkAppRole.newInstanceWithDate(upstreamEa, adminId, linkAppId, UpstreamEmployeeRoleEnum.MANAGER.getRole(),
                        MagicNumberConstants.STATUS_OPEN);
                upstreamEmployeeLinkAppRoles.add(role);
            });
        });
        if (!upstreamEmployeeLinkAppRoles.isEmpty()) {
            int i = upstreamEmployeeLinkAppRoleDao.batchInsert(upstreamEmployeeLinkAppRoles);
            if (i > 0) {
                List ids = upstreamEmployeeLinkAppRoles.stream().map(BaseEntity::getId).collect(Collectors.toList());
                syncCrossCloudDataManager.sendSyncCrossDataMsgByIds(ids, "upstream_employee_link_app_role", DaoOperateEnum.UPSERT.getType());
            }
        }
    }

    @Override
    public Result<String> updateEnterpriseLinkAppRelationStatus(UpstreamEnterpriseAppRelationsArg arg) {
        List<String> appNameList = new ArrayList<>(arg.getLinkAppIds().size());
        arg.getLinkAppIds().forEach(linkAppId -> {
            UpstreamEnterpriseLinkAppRelation relation = upstreamEnterpriseLinkAppRelationDao.findByUpstreamEaAndLinkAppId(arg.getAuthContext().getEa(), linkAppId);
            if (relation != null) {
                relation.setStatus(arg.getStatus());
                upstreamEnterpriseLinkAppRelationDao.batchUpdateStatus(arg.getAuthContext().getEa(), Lists.newArrayList(linkAppId), arg.getStatus());
            }
            //MQSender.send(UpdateUpstreamEnterpriseLinkAppStatusEvent.TAG,
            //        UpdateUpstreamEnterpriseLinkAppStatusEvent.newInstance(linkAppId, arg.getAuthContext().getEa(), arg.getAuthContext().getFsUserId(), arg.getStatus()));
            CloudContextUtil.executeInCloudContext("" + eieaConverter.enterpriseAccountToId(arg.getAuthContext().getEa()), () -> MQSender.send(UpdateUpstreamEnterpriseLinkAppStatusEvent.TAG,
                    UpdateUpstreamEnterpriseLinkAppStatusEvent.newInstance(linkAppId, arg.getAuthContext().getEa(), arg.getAuthContext().getFsUserId(), arg.getStatus())));
            LinkAppData app = linkAppDao.getById(linkAppId);
            if (arg.getStatus() == MagicNumberConstants.STATUS_ClOSE) {
                UpstreamEmployeeLinkAppRole upstreamEmployeeLinkAppRole = new UpstreamEmployeeLinkAppRole();
                upstreamEmployeeLinkAppRole.setLinkAppId(linkAppId);
                upstreamEmployeeLinkAppRole.setUpstreamEa(arg.getAuthContext().getEa());
                upstreamEmployeeLinkAppRole.setStatus(MagicNumberConstants.STATUS_OPEN);
                List<UpstreamEmployeeLinkAppRole> list = upstreamEmployeeLinkAppRoleDao.findBySelective(upstreamEmployeeLinkAppRole);
                Iterator<UpstreamEmployeeLinkAppRole> iterator = list.iterator();
                HashSet<Integer> set = new HashSet<>();
                while (iterator.hasNext()) {
                    if (!set.add(iterator.next().getUpstreamFsUserId())) {
                        iterator.remove();
                    }
                }
                String content = String.format(I18nUtil.get(I18nKeyEnum.EIP_LINKAPP_UPSTREAMSERVICEIMPL_650), app.getName());
                InternationalItem internationalItem = new InternationalItem(QXMessageEnums.STOP_ER_APP.getKey(), app.getName());
                for (UpstreamEmployeeLinkAppRole role : list) {
                    enterpriseMessageService.sendCrossHelpTextMessage(arg.getAuthContext().getEa(), Arrays.asList(role.getUpstreamFsUserId()), content, internationalItem);
                }
                if (!set.contains(arg.getAuthContext().getFsUserId())) {
                    enterpriseMessageService.sendCrossHelpTextMessage(arg.getAuthContext().getEa(), Arrays.asList(arg.getAuthContext().getFsUserId()), content, internationalItem);
                }
            }
            appNameList.add(app.getName());
        });
        CommonPoolUtil.execute(() -> relationLogService.createAppStatusChangeLog(arg.getAuthContext().getEa(), arg.getAuthContext().getFsUserId(), appNameList, arg.getStatus() == 1));
        return Result.newSuccess();
    }

    @Override
    public Result<String> createEmployeeLinkAppRolesByDefault(CreateDefaultRelationsArg arg) {
        List<UpstreamEnterpriseLinkAppRelation> linkAppRelations = upstreamEnterpriseLinkAppRelationDao.listByEaWithoutStatus(arg.getUpstreamEa());
        List<String> defaultAddLinkAppIds = linkAppRelations.stream().map(UpstreamEnterpriseLinkAppRelation::getLinkAppId).filter(this::isDefaultSetPublicEmployee).collect(Collectors.toList());
        UpstreamEmployeeAppRelationsArg appRelationsArg = new UpstreamEmployeeAppRelationsArg();
        appRelationsArg.setFsUserIds(arg.getFsUserIds());
        appRelationsArg.setLinkAppIds(defaultAddLinkAppIds);
        appRelationsArg.setAuthContextWithEaAndFsUserId(arg.getUpstreamEa(), arg.getOperatorId());
        appRelationsArg.setRole(arg.getRole());
        createUpstreamEmployeeLinkAppRoles(appRelationsArg);
        return Result.newSuccess();
    }

    private boolean isDefaultSetPublicEmployee(String linkAppId) {
        LinkAppData linkAppVo = linkAppDao.getById(linkAppId);
        return linkAppVo != null && MagicNumberConstants.APP_DEFAULT.equals(linkAppVo.getEnabledByDefault());
    }

    @Override
    public Result<Boolean> isLinkAppOpen(String linkAppId, String upEa) {
        UpstreamEnterpriseLinkAppRelation relation = new UpstreamEnterpriseLinkAppRelation();
        relation.setLinkAppId(linkAppId);
        relation.setUpstreamEa(upEa);
        List<UpstreamEnterpriseLinkAppRelation> list = upstreamEnterpriseLinkAppRelationDao.batchFindByUpstreamEaAndLinkAppIds(upEa, Lists.newArrayList(linkAppId));
        if (CollectionUtils.isNotEmpty(list)) {
            if (MagicNumberConstants.STATUS_OPEN.equals(list.get(0).getStatus())) {
                return Result.newSuccess(true);
            }
        }
        return Result.newSuccess(false);
    }

    @Override
    public Result<Map<String, Boolean>> batchListLinkAppStatus(String upstreamEa, List<String> linkAppIds) {
        if (CollectionUtils.isEmpty(linkAppIds)) {
            return Result.newSuccess(new HashMap<>());
        }
        List<UpstreamEnterpriseLinkAppRelation> list = upstreamEnterpriseLinkAppRelationDao.batchFindByUpstreamEaAndLinkAppIds(upstreamEa, linkAppIds);
        Map<String, Boolean> upstreamLinkAppStatusMap = Maps.newHashMap();
        Map<String, UpstreamEnterpriseLinkAppRelation> relationMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(re -> relationMap.put(re.getLinkAppId(), re));
        }
        linkAppIds.forEach(appId -> {
            if (relationMap.containsKey(appId)) {
                upstreamLinkAppStatusMap.put(appId, relationMap.get(appId).getStatus() == 1 ? Boolean.TRUE : Boolean.FALSE);
            } else {
                upstreamLinkAppStatusMap.put(appId, Boolean.FALSE);
            }
        });
        return Result.newSuccess(upstreamLinkAppStatusMap);
    }

    @Override
    public Result<Boolean> isAppAdmin(String enterpriseAccount, Integer employeeId) {
        UpstreamEmployeeLinkAppRole upstreamEmployeeLinkAppRole = new UpstreamEmployeeLinkAppRole();
        upstreamEmployeeLinkAppRole.setUpstreamEa(enterpriseAccount);
        upstreamEmployeeLinkAppRole.setUpstreamFsUserId(employeeId);
        upstreamEmployeeLinkAppRole.setRole(UpstreamEmployeeRoleEnum.MANAGER.getRole());
        upstreamEmployeeLinkAppRole.setStatus(MagicNumberConstants.STATUS_OPEN);
        List<UpstreamEmployeeLinkAppRole> linkAppRelations = upstreamEmployeeLinkAppRoleDao.findBySelective(upstreamEmployeeLinkAppRole);
        if (linkAppRelations.isEmpty()) {
            return Result.newSuccess(false);
        }
        return Result.newSuccess(true);
    }

    @Override
    public Result<List<String>> listUpstreamEaByApp(String linkAppId) {
        return Result.newSuccess(upstreamEnterpriseLinkAppRelationDao.listUpstreamEasByLinkAppIdWithEntityDao(linkAppId));
    }

    @Override
    public Result<List<String>> listWxEnterpriseLinkAppIdsByEa(String ea) {
        List<String> linkAppIds;
        if (ea != null) {
            List<UpstreamEnterpriseLinkAppRelation> upstreamEnterpriseLinkAppRelations = upstreamEnterpriseLinkAppRelationDao.listByEaWithoutStatus(ea);
            linkAppIds = upstreamEnterpriseLinkAppRelations.stream().map(UpstreamEnterpriseLinkAppRelation::getLinkAppId).collect(Collectors.toList());
        } else {
            List<LinkAppData> linkApps = linkAppDao.listSystemApps();
            linkAppIds = linkApps.stream().map(LinkAppData::getId).collect(Collectors.toList());
        }
        if (!linkAppIds.isEmpty()) {
            List<String> wxLinkAppIds = wechatLinkAppDao.listByLinkAppIds(linkAppIds).stream().map(WechatLinkApp::getLinkAppId).collect(Collectors.toList());
            return Result.newSuccess(wxLinkAppIds);
        }
        return Result.newSuccess(new ArrayList<>(0));
    }

    @Override
    public Result<List<String>> listWxEnterpriseLinkAppIdsByUser(String ea, Integer fsUserId) {
        UpstreamEmployeeLinkAppRole selectRelation = UpstreamEmployeeLinkAppRole.newInstanceWithoutDate(ea, fsUserId, null, null, null);
        List<UpstreamEmployeeLinkAppRole> upstreamEmployeeLinkAppRoles = upstreamEmployeeLinkAppRoleDao.findByEntity(selectRelation);
        List<String> linkAppIds = upstreamEmployeeLinkAppRoles.stream().map(UpstreamEmployeeLinkAppRole::getLinkAppId).collect(Collectors.toList());
        if (!linkAppIds.isEmpty()) {
            List<String> wxLinkAppIds = wechatLinkAppDao.listByLinkAppIds(linkAppIds).stream().map(WechatLinkApp::getLinkAppId).collect(Collectors.toList());
            return Result.newSuccess(wxLinkAppIds);
        }
        return Result.newSuccess(new ArrayList<>(0));
    }

    @Override
    public Result<List<LinkAppRangeAssociationVo>> listLinkAppRangeAssociationByAppId(String appId) {
        List<LinkAppRangeAssociation> rangeLists = linkAppRangeAssociationDao.listByAppIds(Lists.newArrayList(appId));
        if (rangeLists.size() > 0) {
            return Result.newSuccess(BeanUtil.deepCopyList(rangeLists, LinkAppRangeAssociationVo.class));
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<LinkAppDataAuthPolicyVo>> listLinkAppDataAuthPolicysByupstreamAndAppId(String upstreamEa, String linkAppId) {
        List<UpstreamLinkAppDataAuthPolicy> dataAuthPolicys = upstreamLinkAppDataAuthPolicyDao.listByUpstreamAndAppIds(upstreamEa, Lists.newArrayList(linkAppId));
        List<LinkAppRangeAssociation> rangeLists = linkAppRangeAssociationDao.listByAppIds(Lists.newArrayList(linkAppId));
        Map<String, LinkAppRangeAssociation> rangeMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(rangeLists)) {
            rangeMap = rangeLists.stream().collect(Collectors.toMap(LinkAppRangeAssociation::getObjectApiName, x -> x));
        }

        //无方案则创建一个默认方案
        if (CollectionUtils.isEmpty(dataAuthPolicys)) {
            dataAuthPolicys = Lists.newArrayList();
            initDefaultPolicy(upstreamEa, linkAppId, dataAuthPolicys, rangeMap);
        }
        List<LinkAppDataAuthPolicyVo> retPolicys = Lists.newArrayList();

        Map<String, List<SimpleEnterpriseVo>> policyAuthEasMap = Maps.newHashMap();
        Map<String, List<CrmObjectAuthRangeVo>> policyAuthRangesMap = Maps.newHashMap();
        List<String> policyIds = dataAuthPolicys.stream().map(x -> x.getPolicyId()).collect(Collectors.toList());

        List<UpstreamPolicyAuthDownstream> authDownstreams = upstreamPolicyAuthDownstreamDao.listByPolicyIds(policyIds);
        if (CollectionUtils.isNotEmpty(authDownstreams)) {
            Result<List<EnterpriseCardResult>> enterpriseCardResult = enterpriseCardService.batchGetEnterpriseCardByOuterTenantIds(upstreamEa,
                    authDownstreams.stream().map(x -> x.getDownstreamOuterTenantId()).collect(Collectors.toList()));
            Map<Long, String> eaNameMap = Maps.newHashMap();
            if (enterpriseCardResult.isSuccess() && CollectionUtils.isNotEmpty(enterpriseCardResult.getData())) {
                enterpriseCardResult.getData().forEach(z -> {
                    eaNameMap.put(z.getOuterTenantId(), z.getEnterpriseName());
                });
            }
            authDownstreams.forEach(y -> {
                List<SimpleEnterpriseVo> authEas = policyAuthEasMap.get(y.getPolicyId());
                if (authEas == null) {
                    authEas = Lists.newArrayList();
                    policyAuthEasMap.put(y.getPolicyId(), authEas);
                }
                SimpleEnterpriseVo simpleEnterpriseVo = new SimpleEnterpriseVo();
                //simpleEnterpriseVo.setEnterpriseAccount(y.getDownstreamEa());
                simpleEnterpriseVo.setOuterTenantId(y.getDownstreamOuterTenantId());
                simpleEnterpriseVo.setEnterpriseName(eaNameMap.get(y.getDownstreamOuterTenantId()));

                authEas.add(simpleEnterpriseVo);
            });
        }

        List<UpstreamPolicyDataAuthRange> authRanges = upstreamPolicyDataAuthRangeDao.listByPolicyIds(policyIds);
        if (CollectionUtils.isNotEmpty(authRanges)) {
            authRanges.forEach(z -> {
                List<CrmObjectAuthRangeVo> authObjects = policyAuthRangesMap.get(z.getPolicyId());
                if (authObjects == null) {
                    authObjects = Lists.newArrayList();
                    policyAuthRangesMap.put(z.getPolicyId(), authObjects);
                }
                CrmObjectAuthRangeVo crmObjectAuthRangeVo = new CrmObjectAuthRangeVo();
                crmObjectAuthRangeVo.setObjectApiName(z.getObjectApiName());
                crmObjectAuthRangeVo.setDownstreamAuthRange(z.getDownstreamAuthRange());

                authObjects.add(crmObjectAuthRangeVo);
            });
        }

        Set<String> allApiNames = getAssociationObjects(upstreamEa, linkAppId);

        for (UpstreamLinkAppDataAuthPolicy policy : dataAuthPolicys) {
            LinkAppDataAuthPolicyVo linkAppDataAuthPolicyVo = new LinkAppDataAuthPolicyVo();
            linkAppDataAuthPolicyVo.setPolicyId(policy.getPolicyId());
            linkAppDataAuthPolicyVo.setIsDefault(policy.getIsDefault());
            if (BooleanUtils.isTrue(policy.getIsDefault())) {
                linkAppDataAuthPolicyVo.setPolicyName(I18nUtil.get(I18nKeyEnum.EIP_LINKAPP_UPSTREAMSERVICEIMPL_941));
            } else {
                linkAppDataAuthPolicyVo.setPolicyName(policy.getPolicyName());
            }
            linkAppDataAuthPolicyVo.setDownstreamEas(policyAuthEasMap.get(policy.getPolicyId()));

            //填充未设置访问权限的对象
            if (!CollectionUtils.isEmpty(allApiNames)) {
                Map<String, CrmObjectAuthRangeVo> setedMap = Maps.newHashMap();
                if (!CollectionUtils.isEmpty(policyAuthRangesMap.get(policy.getPolicyId()))) {
                    setedMap = policyAuthRangesMap.get(policy.getPolicyId()).stream().collect(Collectors.toMap(CrmObjectAuthRangeVo::getObjectApiName, x -> x));
                }

                List<CrmObjectAuthRangeVo> authRangeList = generalAllSetedObjects(allApiNames, setedMap, rangeMap);
                linkAppDataAuthPolicyVo.setCrmObjectAuthRanges(authRangeList);
            } else {
                linkAppDataAuthPolicyVo.setCrmObjectAuthRanges(new ArrayList<>(0));
            }

            retPolicys.add(linkAppDataAuthPolicyVo);
        }

        return Result.newSuccess(retPolicys);
    }

    //初始化对象
    private void initDefaultPolicy(String upstreamEa, String linkAppId, List<UpstreamLinkAppDataAuthPolicy> dataAuthPolicys, Map<String, LinkAppRangeAssociation> rangeMap) {
        String policyId = UUID.randomUUID().toString();
        UpstreamLinkAppDataAuthPolicy upstreamLinkAppDataAuthPolicy = new UpstreamLinkAppDataAuthPolicy();
        upstreamLinkAppDataAuthPolicy.setIsDefault(true);
        upstreamLinkAppDataAuthPolicy.setLinkAppId(linkAppId);
        upstreamLinkAppDataAuthPolicy.setPolicyId(policyId);
        upstreamLinkAppDataAuthPolicy.setUpstreamEa(upstreamEa);
        upstreamLinkAppDataAuthPolicy.setPolicyName(I18nUtil.get(I18nKeyEnum.EIP_LINKAPP_UPSTREAMSERVICEIMPL_941));
        Date now = new Date();
        upstreamLinkAppDataAuthPolicy.setCreateTime(now);
        upstreamLinkAppDataAuthPolicy.setUpdateTime(now);
        upstreamLinkAppDataAuthPolicy.setVersion(1L);
        int i1 = upstreamLinkAppDataAuthPolicyDao.insert(upstreamLinkAppDataAuthPolicy);
        if (i1 > 0) {
            syncCrossCloudDataManager.sendSyncCrossDataMsg(upstreamLinkAppDataAuthPolicy.getId(), "upstream_link_app_data_auth_policy", DaoOperateEnum.UPSERT.getType());
        }

        List<CrmObjectAuthRangeVo> rangeDatas = Lists.newArrayList();

        Set<String> apiNameSet = getAssociationObjects(upstreamEa, linkAppId);
        if (CollectionUtils.isNotEmpty(apiNameSet)) {
            apiNameSet.forEach(apiName -> {
                CrmObjectAuthRangeVo crmObjectAuthRangeVo = new CrmObjectAuthRangeVo();
                crmObjectAuthRangeVo.setObjectApiName(apiName);
                LinkAppRangeAssociation linkAppRangeAssociation = rangeMap.get(apiName);
                crmObjectAuthRangeVo.setDownstreamAuthRange(linkAppRangeAssociation == null ? LinkAppDataAuthRange.PERSONAL.getType() : linkAppRangeAssociation.getDefaultAuthRange());
                rangeDatas.add(crmObjectAuthRangeVo);
            });
        }

        if (CollectionUtils.isNotEmpty(rangeDatas)) {
            if (rangeDatas.size() > 0) {
                List<UpstreamPolicyDataAuthRange> policies = Lists.newArrayList();
                for (CrmObjectAuthRangeVo rangeData : rangeDatas) {
                    UpstreamPolicyDataAuthRange range = new UpstreamPolicyDataAuthRange();
                    range.setPolicyId(policyId);
                    range.setObjectApiName(rangeData.getObjectApiName());
                    range.setDownstreamAuthRange(rangeData.getDownstreamAuthRange());
                    policies.add(range);
                }
                int i = upstreamPolicyDataAuthRangeDao.batchAdd(policies);
                if (i > 0) {
                    List ids = policies.stream().map(BaseEntity::getId).collect(Collectors.toList());
                    syncCrossCloudDataManager.sendSyncCrossDataMsgByIds(ids, "upstream_policy_data_auth_range", DaoOperateEnum.UPSERT.getType());
                }
            }
        }

        dataAuthPolicys.add(upstreamLinkAppDataAuthPolicy);
    }

    //获取功能权限处的所有对象
    private Set<String> getAssociationObjects(String upstreamEa, String linkAppId) {
        Set<String> apiNameSet = Sets.newLinkedHashSet();
        Result<List<LinkAppAssociationObjectData>> objectsResult = linkAppAssociationObjectService.listAssociationObjects(upstreamEa, linkAppId);
        if (objectsResult.isSuccess() && CollectionUtils.isNotEmpty(objectsResult.getData())) {
            objectsResult.getData().forEach(range -> {
                apiNameSet.add(range.getObjectApiName());
            });
        }
        return apiNameSet;
    }

    //填充未设置访问权限的对象
    private List<CrmObjectAuthRangeVo> generalAllSetedObjects(Set<String> allObjects, Map<String, CrmObjectAuthRangeVo> setedObjects, Map<String, LinkAppRangeAssociation> rangeMap) {
        List<CrmObjectAuthRangeVo> authRangeList = Lists.newArrayList();
        allObjects.forEach(obj -> {
            LinkAppRangeAssociation linkAppRangeAssociation = rangeMap.get(obj);
            CrmObjectAuthRangeVo crmObjectAuthRangeVo = setedObjects.get(obj);
            if (crmObjectAuthRangeVo == null) {
                crmObjectAuthRangeVo = new CrmObjectAuthRangeVo();
                crmObjectAuthRangeVo.setObjectApiName(obj);
                crmObjectAuthRangeVo.setDownstreamAuthRange(linkAppRangeAssociation == null ? LinkAppDataAuthRange.PERSONAL.getType() : linkAppRangeAssociation.getDefaultAuthRange());
            }
            crmObjectAuthRangeVo.setAllowedAuthRanges(linkAppRangeAssociation == null ? Lists.newArrayList(LinkAppDataAuthRange.PERSONAL.getType(), LinkAppDataAuthRange.READ_SAME_CORP.getType(),
                    LinkAppDataAuthRange.READ_WRITE_SAME_CORP.getType(), LinkAppDataAuthRange.READ_OPEN.getType(), LinkAppDataAuthRange.READ_WRITE_OPEN.getType())
                    : genAllowedRanges(linkAppRangeAssociation.getAllowedRanges()));

            authRangeList.add(crmObjectAuthRangeVo);
        });

        return authRangeList;
    }

    private List<Integer> genAllowedRanges(String allowedRanges) {
        List<String> allowStrs = Splitter.on(",").splitToList(allowedRanges);
        return allowStrs.stream().map(x -> Integer.parseInt(x)).collect(Collectors.toList());
    }

    @Override
    public Result<Boolean> updateLinkAppDataAuthPolicy(UpdateLinkAppDataAuthPolicyArg arg) {
        List<CrmObjectAuthRangeVo> rangeDatas = arg.getCrmObjectAuthRanges();
        if (rangeDatas != null) {
            int i1 = upstreamPolicyDataAuthRangeDao.deleteByPolicyIds(Lists.newArrayList(arg.getPolicyId()));
            if (i1 > 0) {
                syncDataAfterDeleteByPolicyIds(Lists.newArrayList(arg.getPolicyId()));
            }
            if (rangeDatas.size() > 0) {
                List<UpstreamPolicyDataAuthRange> policies = Lists.newArrayList();
                for (CrmObjectAuthRangeVo rangeData : rangeDatas) {
                    UpstreamPolicyDataAuthRange range = new UpstreamPolicyDataAuthRange();
                    range.setPolicyId(arg.getPolicyId());
                    range.setObjectApiName(rangeData.getObjectApiName());
                    range.setDownstreamAuthRange(rangeData.getDownstreamAuthRange());
                    policies.add(range);
                }
                int i = upstreamPolicyDataAuthRangeDao.batchAdd(policies);
                if (i > 0) {
                    List ids = policies.stream().map(BaseEntity::getId).collect(Collectors.toList());
                    syncCrossCloudDataManager.sendSyncCrossDataMsgByIds(ids, "upstream_policy_data_auth_range", DaoOperateEnum.UPSERT.getType());
                }
            }
        }
        return Result.newSuccess(true);
    }

    private void syncDataAfterDeleteByPolicyIds(List<String> policyIds) {
        String sql = "DELETE FROM upstream_policy_data_auth_range WHERE policy_id IN (" + com.facishare.enterprise.common.util.StringUtil.splicingByComma(policyIds) + ")";
        syncCrossCloudDataManager.sendSqlSyncCrossDataMsg(sql, "upstream_policy_data_auth_range");
    }

    @Override
    public Result<Boolean> mergeLinkAppDataAuthPolicy(String upstreamEa, MergeLinkAppDataAuthPolicyArg mergeLinkAppDataAuthPolicy) {
        String policyId = mergeLinkAppDataAuthPolicy.getPolicyId();

        UpstreamLinkAppDataAuthPolicy upstreamLinkAppDataAuthPolicy = new UpstreamLinkAppDataAuthPolicy();
        upstreamLinkAppDataAuthPolicy.setLinkAppId(mergeLinkAppDataAuthPolicy.getLinkAppId());
        upstreamLinkAppDataAuthPolicy.setPolicyId(policyId);
        upstreamLinkAppDataAuthPolicy.setUpstreamEa(upstreamEa);
        upstreamLinkAppDataAuthPolicy.setPolicyName(mergeLinkAppDataAuthPolicy.getPolicyName());
        upstreamLinkAppDataAuthPolicy.setVersion(1L);

        if (Strings.isNullOrEmpty(policyId)) {
            policyId = UUID.randomUUID().toString();
            upstreamLinkAppDataAuthPolicy.setIsDefault(BooleanUtils.isTrue(mergeLinkAppDataAuthPolicy.getIsDefault()));
            upstreamLinkAppDataAuthPolicy.setPolicyId(policyId);
            upstreamLinkAppDataAuthPolicy.setCreateTime(new Date());
            upstreamLinkAppDataAuthPolicy.setUpdateTime(new Date());
            int i = upstreamLinkAppDataAuthPolicyDao.insert(upstreamLinkAppDataAuthPolicy);
            if (i > 0) {
                syncCrossCloudDataManager.sendSyncCrossDataMsg(upstreamLinkAppDataAuthPolicy.getId(), "upstream_link_app_data_auth_policy", DaoOperateEnum.UPSERT.getType());
            }
        } else {
            upstreamLinkAppDataAuthPolicy.setUpdateTime(new Date());
            int i = upstreamLinkAppDataAuthPolicyDao.updateByPolicyId(upstreamLinkAppDataAuthPolicy);
            if (i > 0) {
                syncDataAfterUpdateByPolicyId(upstreamEa, mergeLinkAppDataAuthPolicy);
            }
        }

        List<CrmObjectAuthRangeVo> rangeDatas = mergeLinkAppDataAuthPolicy.getCrmObjectAuthRanges();
        if (rangeDatas != null) {
            int i1 = upstreamPolicyDataAuthRangeDao.deleteByPolicyIds(Lists.newArrayList(policyId));
            if (i1 > 0) {
                syncDataAfterDeleteByPolicyIds(Lists.newArrayList(policyId));
            }
            if (rangeDatas.size() > 0) {
                List<UpstreamPolicyDataAuthRange> policies = Lists.newArrayList();
                for (CrmObjectAuthRangeVo rangeData : rangeDatas) {
                    UpstreamPolicyDataAuthRange range = new UpstreamPolicyDataAuthRange();
                    range.setPolicyId(policyId);
                    range.setObjectApiName(rangeData.getObjectApiName());
                    range.setDownstreamAuthRange(rangeData.getDownstreamAuthRange());
                    policies.add(range);
                }
                int i = upstreamPolicyDataAuthRangeDao.batchAdd(policies);
                if (i > 0) {
                    List ids = policies.stream().map(BaseEntity::getId).collect(Collectors.toList());
                    syncCrossCloudDataManager.sendSyncCrossDataMsgByIds(ids, "upstream_policy_data_auth_range", DaoOperateEnum.UPSERT.getType());
                }
            }
        }

        List<Long> downstreamOuterTenantIds = mergeLinkAppDataAuthPolicy.getDownstreamOuterTenantIds();
        if (downstreamOuterTenantIds != null) {
            int i1 = upstreamPolicyAuthDownstreamDao.deleteByPolicyIds(Lists.newArrayList(policyId), null);
            if (i1 > 0) {
                syncDataAfterDeleteByPolicyIds(Lists.newArrayList(policyId), null);
            }
            //将授权公司从其它方案列表清除
            if (downstreamOuterTenantIds.size() > 0) {
                List<UpstreamLinkAppDataAuthPolicy> policys = upstreamLinkAppDataAuthPolicyDao.listByUpstreamAndAppIds(upstreamEa, Lists.newArrayList(mergeLinkAppDataAuthPolicy.getLinkAppId()));
                List<String> policyIds = policys.stream().map(y -> y.getPolicyId()).collect(Collectors.toList());
                policyIds.remove(policyId);
                int i2 = upstreamPolicyAuthDownstreamDao.deleteByPolicyIds(policyIds, downstreamOuterTenantIds);
                if (i2 > 0) {
                    syncDataAfterDeleteByPolicyIds(policyIds, downstreamOuterTenantIds);
                }
                List<UpstreamPolicyAuthDownstream> policiesToAdd = Lists.newArrayList();
                for (Long downstreamOuterTenantId : downstreamOuterTenantIds) {
                    UpstreamPolicyAuthDownstream policy = new UpstreamPolicyAuthDownstream();
                    policy.setPolicyId(policyId);
                    policy.setDownstreamOuterTenantId(downstreamOuterTenantId);
                    policiesToAdd.add(policy);
                }
                int i = upstreamPolicyAuthDownstreamDao.batchAdd(policiesToAdd);
                if (i > 0) {
                    List ids = policiesToAdd.stream().map(BaseEntity::getId).collect(Collectors.toList());
                    syncCrossCloudDataManager.sendSyncCrossDataMsgByIds(ids, "upstream_policy_auth_downstream", DaoOperateEnum.UPSERT.getType());
                }
            }
        }

        return Result.newSuccess(true);
    }

    private void syncDataAfterDeleteByPolicyIds(List<String> policyIds, List<Long> downstreamOuterTenantIds) {
        String sql = "DELETE FROM upstream_policy_auth_downstream WHERE policy_id IN (" + com.facishare.enterprise.common.util.StringUtil.splicingByComma(policyIds) + ")";

        if (!CollectionUtils.isEmpty(downstreamOuterTenantIds)) {
            StringBuilder sb = new StringBuilder();
            for (Long downstreamOuterTenantId : downstreamOuterTenantIds) {
                if (!StringUtils.isEmpty(sb.toString())) {
                    sb.append(",");
                }
                sb.append(downstreamOuterTenantId);
            }
            sql += " AND downstream_outer_tenant_id IN (" + sb + ")";
        }
        syncCrossCloudDataManager.sendSqlSyncCrossDataMsg(sql, "upstream_policy_auth_downstream");
    }

    private void syncDataAfterUpdateByPolicyId(String upstreamEa, MergeLinkAppDataAuthPolicyArg mergeLinkAppDataAuthPolicy) {
        String sql = "UPDATE upstream_link_app_data_auth_policy SET policy_name='" + mergeLinkAppDataAuthPolicy.getPolicyName() + "', update_time=now(), version=version+1" + " where upstream_ea='" + upstreamEa
                + "' and link_app_id='" + mergeLinkAppDataAuthPolicy.getLinkAppId() + "' and policy_id='" + mergeLinkAppDataAuthPolicy.getPolicyId() + "'";
        syncCrossCloudDataManager.sendSqlSyncCrossDataMsg(sql, "upstream_link_app_data_auth_policy");
    }

    @Override
    public Result<Void> batchAddObjectDataRanges(String appId, List<ObjectDataRangeVO> objectDataRanges, Boolean isCoverd) {
        if (isCoverd) {
            int i = linkAppRangeAssociationDao.batchRemove(appId);
            if (i > 0) {
                syncCrossCloudDataManager.sendSqlSyncCrossDataMsg("DELETE FROM link_app_range_association WHERE link_app_id='" + appId + "'", "link_app_range_association");
            }
        }

        if (CollectionUtils.isNotEmpty(objectDataRanges)) {
            List<LinkAppRangeAssociation> linkAppRangeAssociations = Lists.newArrayList();
            linkAppRangeAssociations = objectDataRanges.stream().map(range -> {
                LinkAppRangeAssociation linkAppRangeAssociation = new LinkAppRangeAssociation();
                linkAppRangeAssociation.setDefaultAuthRange(range.getDefaultAuthRange());
                linkAppRangeAssociation.setLinkAppId(appId);
                linkAppRangeAssociation.setAllowedRanges(range.getAllowedAuthRanges());
                linkAppRangeAssociation.setObjectApiName(range.getObjectApiName());
                return linkAppRangeAssociation;
            }).collect(Collectors.toList());

            int i = linkAppRangeAssociationDao.batchAdd(linkAppRangeAssociations);
            if (i > 0) {
                List ids = linkAppRangeAssociations.stream().map(BaseEntity::getId).collect(Collectors.toList());
                syncCrossCloudDataManager.sendSyncCrossDataMsgByIds(ids, "link_app_range_association", DaoOperateEnum.UPSERT.getType());
            }
        }

        return Result.newSuccess();
    }

    @Override
    public Result<Map<String, Boolean>> listIntelligentAppStatus(List<String> linkAppIds) {
        Preconditions.checkState(linkAppIds != null && !linkAppIds.isEmpty());
        List<String> list = upstreamEnterpriseLinkAppRelationDao.listAllOpenedIntelligentAppId(linkAppIds);
        Map<String, Boolean> map = linkAppIds.stream().collect(Collectors.toMap(id -> id, list::contains, (v1, v2) -> v1));
        return Result.newSuccess(map);
    }

    @Override
    public Result<List<UpstreamEmployeeRolesVo>> batchGetEmployeeLinkAppRoles(String upstreamEa, List<Integer> fsUserIds, String linkAppId) {

        boolean linkAppOpen = isLinkAppOpen(linkAppId, upstreamEa).getData();
        UpstreamEmployeeLinkAppRole selectCondition = new UpstreamEmployeeLinkAppRole();
        selectCondition.setUpstreamEa(upstreamEa);
        selectCondition.setLinkAppId(linkAppId);
        List<UpstreamEmployeeLinkAppRole> roles = upstreamEmployeeLinkAppRoleDao.findByEntity(selectCondition);
        if (roles == null) {
            roles = new ArrayList<>();
        }

        Map<Integer, List<UpstreamEmployeeLinkAppRole>> fsUserIdToRoleMap = new HashMap<>();
        for (UpstreamEmployeeLinkAppRole upstreamEmployeeLinkAppRole : roles) {
            Integer upstreamFsUserId = upstreamEmployeeLinkAppRole.getUpstreamFsUserId();
            if (fsUserIdToRoleMap.get(upstreamFsUserId) == null) {
                fsUserIdToRoleMap.put(upstreamFsUserId, Lists.newArrayList());
            }
            fsUserIdToRoleMap.get(upstreamFsUserId).add(upstreamEmployeeLinkAppRole);
        }

        List<UpstreamEmployeeRolesVo> upstreamEmployeeRolesVos = new ArrayList<>();
        for (Integer fsUserId : fsUserIds) {
            List<UpstreamEmployeeLinkAppRole> upstreamEmployeeLinkAppRoles = fsUserIdToRoleMap.get(fsUserId);

            UpstreamEmployeeRolesVo upstreamEmployeeRolesVo = new UpstreamEmployeeRolesVo();
            upstreamEmployeeRolesVo.setFsUserId(fsUserId);
            upstreamEmployeeRolesVo.setLinkAppOpen(linkAppOpen);
            if (upstreamEmployeeLinkAppRoles != null) {
                List<Integer> upstreamEmployeeRoles = upstreamEmployeeLinkAppRoles.stream().map(UpstreamEmployeeLinkAppRole::getRole).collect(Collectors.toList());
                upstreamEmployeeRolesVo.setRoles(upstreamEmployeeRoles);
            } else {
                upstreamEmployeeRolesVo.setRoles(Lists.newArrayList());
            }

            upstreamEmployeeRolesVos.add(upstreamEmployeeRolesVo);
        }
        return Result.newSuccess(upstreamEmployeeRolesVos);
    }

    @Override
    public Result<Map<String, List<Integer>>> batchListFsUserIdsByLinkAppIdsAndRole(String upstreamEa, List<String> linkAppIds, Integer role) {
        Preconditions.checkNotNull(linkAppIds);
        Preconditions.checkNotNull(role);
        List<UpstreamEmployeeLinkAppRole> upstreamEmployeeLinkAppRoles = upstreamEmployeeLinkAppRoleDao.listEmployeesByLinkAppIdsAndRole(upstreamEa, linkAppIds, role);
        Map<String, List<Integer>> maps = new HashMap<>();
        linkAppIds.forEach(linkAppId -> maps.put(linkAppId, new ArrayList<>()));
        if (upstreamEmployeeLinkAppRoles != null) {
            upstreamEmployeeLinkAppRoles.forEach(upstreamEmployeeLinkAppRole -> maps.get(upstreamEmployeeLinkAppRole.getLinkAppId()).add(upstreamEmployeeLinkAppRole.getUpstreamFsUserId()));
        }
        return Result.newSuccess(maps);
    }

    @Override
    public Result<List<WechatServiceAppVo>> listEnterpriseWechatServiceApps(String upstreamEa) {
        com.facishare.linkapp.api.arg.BaseArg baseArg = new com.facishare.linkapp.api.arg.BaseArg();
        baseArg.setOnlyOpen(false);
        baseArg.setAuthContext(com.facishare.linkapp.api.arg.BaseArg.AuthContext.getFakeAuth(upstreamEa));
        Result<List<LinkAppData>> listResult = this.listEnterpriseLinkApps(baseArg);
        if (!listResult.isSuccess()) {
            return Result.newError(listResult.getErrCode(), listResult.getErrMessage());
        }
        List<LinkAppData> linkAppVos = listResult.getData();
        if (linkAppVos == null || linkAppVos.isEmpty()) {
            return Result.newSuccess(new ArrayList<>());
        }
        List<String> candidateLinkAppIds = linkAppVos.stream().map(LinkAppData::getId).collect(Collectors.toList());

        Set<String> privateAppIds = linkAppVos.stream()
                .filter(linkAppVo -> Objects.equals(LinkAppTypeEnum.PRIVATE.getType(), linkAppVo.getType()))
                .map(LinkAppData::getId)
                .collect(Collectors.toSet());
        List<WechatLinkApp> wechatServiceAppList = wechatLinkAppDao.listByLinkAppIds(candidateLinkAppIds);
        if (wechatServiceAppList == null || wechatServiceAppList.isEmpty()) {
            return Result.newSuccess(new ArrayList<>());
        }
        Map<String, WechatLinkApp> wechatLinkAppMap = wechatServiceAppList.stream().collect(Collectors.toMap(WechatLinkApp::getLinkAppId, app -> app, (v1, v2) -> v1));
        Set<String> wechatLinkAppIds = wechatLinkAppMap.keySet();
        List<WechatServiceAppVo> wechatServiceAppVoList = linkAppVos.stream().filter(linkAppVo -> wechatLinkAppIds.contains(linkAppVo.getId()))
                .filter(linkAppVo -> MagicNumberConstants.STATUS_OPEN.equals(linkAppVo.getStatus())).map(linkAppVo -> {
                    WechatServiceAppVo wechatServiceAppVo = new WechatServiceAppVo();
                    wechatServiceAppVo.setLinkAppId(linkAppVo.getId());
                    if (StringUtils.isEmpty(linkAppVo.getNameI18nKey())) {
                        wechatServiceAppVo.setLinkAppName(linkAppVo.getName());
                    } else {
                        wechatServiceAppVo.setLinkAppName(I18nUtil.get(linkAppVo.getNameI18nKey(), linkAppVo.getName()));
                    }
                    wechatServiceAppVo.setNameI18nKey(linkAppVo.getNameI18nKey());
                    WechatLinkApp wechatLinkApp = wechatLinkAppMap.get(linkAppVo.getId());

                    String url = wechatLinkApp.getWechatServiceUrl();
                    if (privateAppIds.contains(wechatLinkApp.getLinkAppId())) {
                        url = WeChatCustomAppWeChatUrlUtils.parserCustomAppWechatUrl(upstreamEa, wechatLinkApp.getLinkAppId(), url);
                    } else {
                        url = AppUtil.parsedVariables(url, upstreamEa, null, null, null);
                    }
                    wechatServiceAppVo.setUrl(url);
                    return wechatServiceAppVo;
                }).collect(Collectors.toList());
        List<WechatServiceAppVo> wechatLinkAppsCopy = Lists.newArrayList();
        BeanUtil.copyList(WechatServiceAppVo.class, wechatLinkAppsCopy, systemAppVoApps);
        wechatServiceAppVoList.addAll(0, wechatLinkAppsCopy);
        return Result.newSuccess(wechatServiceAppVoList);
    }

    @Override
    public Result<String> createEnterpriseLinkAppRelations(EnterpriseAppRelationArg arg) {
        arg.getLinkAppIds().forEach(linkAppId -> {
            UpstreamEnterpriseLinkAppRelation relation = UpstreamEnterpriseLinkAppRelation.newInstanceWithDate(arg.getUpstreamEa(), linkAppId, 1);
            UpstreamEnterpriseLinkAppRelation old = upstreamEnterpriseLinkAppRelationDao.findByUpstreamEaAndLinkAppId(arg.getUpstreamEa(), linkAppId);
            if (old == null) {
                LinkAppData linkAppVo = linkAppDao.getById(linkAppId);
                if (linkAppVo.getUnVisibleInManager()) {
                    relation.setAuthType(LinkAppAuthTypeEnum.ALL_ER_ROLE.getType());
                } else {
                    relation.setAuthType(LinkAppAuthTypeEnum.ALL_PERSONAL_ROLE.getType());
                }
                if (("" + IdentityType.PERSONAL.getType()).equals(linkAppVo.getSupportIdentityTypes())) {
                    relation.setAuthType(ALL.getType());
                }
                if (arg.getAuthType() != null) {
                    relation.setAuthType(arg.getAuthType());
                }
                upstreamEnterpriseLinkAppRelationDao.insertAndSetObjectId(relation);
                //MQSender.send(UpdateUpstreamEnterpriseLinkAppStatusEvent.TAG,
                //        UpdateUpstreamEnterpriseLinkAppStatusEvent.newInstance(linkAppId, arg.getUpstreamEa(), arg.getOperatorFsUserId(), MagicNumberConstants.STATUS_OPEN));
                CloudContextUtil.executeInCloudContext("" + eieaConverter.enterpriseAccountToId(arg.getUpstreamEa()), () -> MQSender.send(UpdateUpstreamEnterpriseLinkAppStatusEvent.TAG,
                        UpdateUpstreamEnterpriseLinkAppStatusEvent.newInstance(linkAppId, arg.getUpstreamEa(), arg.getOperatorFsUserId(), MagicNumberConstants.STATUS_OPEN)));
            }
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> isOpenProcessLog(String upstreamEa, String appId, String apiName, String flowId) {
        List<UpstreamAppFlowOptionConfig> upstreamAppFlowOptionConfigs = upstreamAppFlowOptionConfigDao.batchGetByFlowIds(upstreamEa, appId, apiName, Lists.newArrayList(flowId));
        if (upstreamAppFlowOptionConfigs == null || upstreamAppFlowOptionConfigs.size() == 0) {
            //默认打开
            return Result.newSuccess(true);
        }
        return Result.newSuccess(upstreamAppFlowOptionConfigs.get(0).getOptionVisible());
    }

    @Override
    public Result<List<String>> listUpstreamEasByLinkAppId(String linkAppId) {
        List<String> upstreamEas = upstreamEnterpriseLinkAppRelationDao.listUpstreamEasByApp(linkAppId);
        return Result.newSuccess(upstreamEas);
    }

    @Override
    public Result<AllPublicEmployeeVo> listAllPublicEmployees(LinkAppIdArg arg) {
        AllPublicEmployeeVo allPublicEmployeeVo = new AllPublicEmployeeVo();
        allPublicEmployeeVo.setLinkAppOpen(isLinkAppOpen(arg.getLinkAppId(), arg.getAuthContext().getEa()).getData());
        UpstreamEmployeeLinkAppRole upstreamSelect = new UpstreamEmployeeLinkAppRole();
        upstreamSelect.setLinkAppId(arg.getLinkAppId());
        upstreamSelect.setRole(UpstreamEmployeeRoleEnum.PUBLIC_EMPLOYEE.getRole());
        upstreamSelect.setUpstreamEa(arg.getAuthContext().getEa());
        List<UpstreamEmployeeLinkAppRole> upstreamEmployeeLinkAppRoles = upstreamEmployeeLinkAppRoleDao.findBySelective(upstreamSelect);
        allPublicEmployeeVo.setUpstreamEmployeeVos(upstreamEmployeeLinkAppRoles.stream().map(upstreamEmployeeLinkAppRole -> {
            UpstreamEmployeeVo upstreamEmployeeVo = new UpstreamEmployeeVo();
            upstreamEmployeeVo.setEa(upstreamEmployeeLinkAppRole.getUpstreamEa());
            upstreamEmployeeVo.setFsUserId(upstreamEmployeeLinkAppRole.getUpstreamFsUserId());
            return upstreamEmployeeVo;
        }).collect(Collectors.toList()));

        List<Long> downstreamOuterUids = downstreamEmployeeLinkAppRoleManager.listDownstreamOuterUidsByUpstreamEaAndLinkAppId(arg.getAuthContext().getEa(), arg.getLinkAppId(), arg.getOffset(),
                arg.getLimit());
        Map<Long, Long> outerUidOuterTenantIdMap = employeeCardService.batchGetOuterTenantIds(arg.getAuthContext().getEa(), downstreamOuterUids).getData();
        Map<Long, com.facishare.global.rest.result.FsAccountVo> downstreamFsAccountMap = fxiaokeEmployeeAssociationService.batchGetFsAccountByOuterUids(downstreamOuterUids).getData();
        allPublicEmployeeVo.setDownstreamEmployeeVos(downstreamOuterUids.stream().map(downstreamOuterUid -> {
            DownstreamEmployeeVo downstreamEmployeeVo = new DownstreamEmployeeVo();
            downstreamEmployeeVo.setAuthorized(true);
            downstreamEmployeeVo.setOuterTenantId(outerUidOuterTenantIdMap.get(downstreamOuterUid));
            downstreamEmployeeVo.setOuterUid(downstreamOuterUid);
            com.facishare.global.rest.result.FsAccountVo fsAccountVo = downstreamFsAccountMap.get(downstreamOuterUid);
            if (fsAccountVo != null) {
                downstreamEmployeeVo.setEa(fsAccountVo.getEa());
                downstreamEmployeeVo.setFsUserId(fsAccountVo.getEmployeeId());
            }
            return downstreamEmployeeVo;
        }).collect(Collectors.toList()));
        return Result.newSuccess(allPublicEmployeeVo);
    }

    @Override
    public Result<EnterpriseLinkAppRelationGuestorData> getEnterpriseLinkAppRelationGuestorInfo(String upstreamEa, String linkAppId) {
        String tenantId;
        try {
            tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(upstreamEa));
        } catch (Exception e) {
            return Result.newError(ResultCode.EA_NOT_EXIST);
        }
        ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(tenantId, String.valueOf(SuperUserConstants.USER_ID));
        GetErVisitorResult erVisitor = enterpriseRelationObjService.getErVisitor(serviceContext, NoArg.instance);

        Boolean isOpenERVisitor = erVisitor.getIsOpenERVisitor();
        EnterpriseLinkAppRelationGuestorData enterpriseLinkAppRelationGuestorData = new EnterpriseLinkAppRelationGuestorData();
        enterpriseLinkAppRelationGuestorData.setLinkAppId(linkAppId);
        enterpriseLinkAppRelationGuestorData.setGuestorAccountId(erVisitor.getErVisitorBindAccountId());
        enterpriseLinkAppRelationGuestorData.setOpenGuestorSwitch(false);
        enterpriseLinkAppRelationGuestorData.setUseGuestorDefault(false);
        enterpriseLinkAppRelationGuestorData.setEnterpriseOpenGuestorSwitch(isOpenERVisitor);
        enterpriseLinkAppRelationGuestorData.setVisitorLoginPrompt(erVisitor.getVisitorLoginPrompt());
        enterpriseLinkAppRelationGuestorData.setLogoPath(erVisitor.getLogoPath());
        enterpriseLinkAppRelationGuestorData.setIsShowLogo(erVisitor.getIsShowLogo());
        enterpriseLinkAppRelationGuestorData.setEnterpriseName(erVisitor.getEnterpriseName());
        enterpriseLinkAppRelationGuestorData.setEnterpriseNameDisplayMode(erVisitor.getEnterpriseNameDisplayMode());
        enterpriseLinkAppRelationGuestorData.setErLoginWelcomePageDefaultLanguage(erVisitor.getErLoginWelcomePageDefaultLanguage());
        if (isOpenERVisitor) {
            List<String> linkAppIds = linkAppOuterRoleService.getLinkAppIdsByRoleId(upstreamEa, GuestorIdentityInfoConstants.ROLE_CODE).getData();
            for (String appId : linkAppIds) {
                if (appId.equals(linkAppId)) {
                    enterpriseLinkAppRelationGuestorData.setUseGuestorDefault(true);
                    enterpriseLinkAppRelationGuestorData.setOpenGuestorSwitch(true);
                    break;
                }
            }
        }
        return Result.newSuccess(enterpriseLinkAppRelationGuestorData);
    }

    @Override
    public Result<Void> updateEnterpriseLinkAppRelationGuestorInfo(String upstreamEa, EnterpriseLinkAppRelationGuestorData arg) {
        upstreamEnterpriseLinkAppRelationDao.updateGuestorInfoByEa(upstreamEa, arg);
        //创建paas角色
        int fsEi = eieaConverter.enterpriseAccountToId(upstreamEa);
        HeaderObj headerObj = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(fsEi);
        if (BooleanUtils.isTrue(arg.getOpenGuestorSwitch())) {
            List<OuterRoleVo> guestRoles = outerUserRoleService.batchGetOuterRoles(upstreamEa, Lists.newArrayList(GuestorIdentityInfoConstants.ROLE_CODE)).getData();
            boolean existsRole = CollectionUtils.isNotEmpty(guestRoles);
            if (!existsRole) {
                OuterRoleVo outerRoleVo = new OuterRoleVo();
                outerRoleVo.setRoleType(AppRoleType.OUTER_PRESET_ROLE.getType());
                outerRoleVo.setRoleId(GuestorIdentityInfoConstants.ROLE_CODE);
                outerRoleVo.setRoleName(GuestorIdentityInfoConstants.ROLE_NAME);
                outerRoleVo.setDescription(GuestorIdentityInfoConstants.ROLE_DESC);
                outerRoleService.addRoleAndInit(upstreamEa, arg.getLinkAppId(), outerRoleVo);
            }
            //添加应用关联
            List<UpstreamLinkAppOuterRoleAssociation> roleList = upstreamLinkAppOuterRoleAssociationDao.listByEaAppRoles(upstreamEa, arg.getLinkAppId(),
                    Collections.singleton(GuestorIdentityInfoConstants.ROLE_CODE), null);
            if (CollectionUtils.isEmpty(roleList)) {
                OuterRoleVo role = new OuterRoleVo(GuestorIdentityInfoConstants.ROLE_CODE, GuestorIdentityInfoConstants.ROLE_NAME, AppRoleType.OUTER_PRESET_ROLE.getType());
                upstreamLinkAppOuterRoleAssociationDao.batchAdd(upstreamEa, arg.getLinkAppId(), Collections.singleton(role));
                OuterRoleIdLinkAppIdChangeEvent instance = OuterRoleIdLinkAppIdChangeEvent.getInstance(fsEi + "", role.getRoleId(), Lists.newArrayList(arg.getLinkAppId()), null);
                CloudContextUtil.executeInCloudContext("" + fsEi, () -> mqSender.send(OuterRoleIdLinkAppIdChangeEvent.TAG, instance));
            }
        } else {
            upstreamLinkAppOuterRoleAssociationDao.batchDeleteByEaAppRoles(upstreamEa, arg.getLinkAppId(), Collections.singleton(GuestorIdentityInfoConstants.ROLE_CODE));
            OuterRoleIdLinkAppIdChangeEvent instance = OuterRoleIdLinkAppIdChangeEvent.getInstance(fsEi + "", GuestorIdentityInfoConstants.ROLE_CODE, null, Lists.newArrayList(arg.getLinkAppId()));
            CloudContextUtil.executeInCloudContext("" + fsEi, () -> mqSender.send(OuterRoleIdLinkAppIdChangeEvent.TAG, instance));
            outerRoleRemoteService.delDefinedRole(fsEi, GuestorIdentityInfoConstants.ROLE_CODE);
        }
        return Result.newSuccess();
    }

    @Override
    public Result closeUpstreamSmartFormLinkApp(String ea, List<String> smartFormLinkAppIds) {
        List<UpstreamEnterpriseLinkAppRelation> queryUpstreamEnterpriseLinkAppRelations = upstreamEnterpriseLinkAppRelationDao.batchFindByUpstreamEaAndLinkAppIds(ea, smartFormLinkAppIds);

        List<String> filterLinkAppIds = queryUpstreamEnterpriseLinkAppRelations.stream().filter(x -> MagicNumberConstants.STATUS_OPEN.equals(x.getStatus()))
                .map(UpstreamEnterpriseLinkAppRelation::getLinkAppId).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(filterLinkAppIds)) {
            upstreamEnterpriseLinkAppRelationDao.batchUpdateStatus(ea, smartFormLinkAppIds, MagicNumberConstants.STATUS_ClOSE);
            for (String smartFormLinkAppId : filterLinkAppIds) {
                try {
                    CloudContextUtil.executeInCloudContext("" + eieaConverter.enterpriseAccountToId(ea), () -> MQSender.send(UpdateUpstreamEnterpriseLinkAppStatusEvent.TAG,
                            UpdateUpstreamEnterpriseLinkAppStatusEvent.newInstance(smartFormLinkAppId, ea, 1000, MagicNumberConstants.STATUS_ClOSE)));
                } catch (Exception e) {
                    log.warn("closeUpstreamSmartFormLinkApp service error,ea={},appId={}", ea, smartFormLinkAppId, e);
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> upstreamEnterpriseDeleteLinkApp(String ea, List<String> linkAppIds) {
        if (CollectionUtils.isEmpty(linkAppIds)) {
            return Result.newSuccess(true);
        }

        for (String linkAppId : linkAppIds) {
            try {
                upstreamEnterpriseLinkAppRelationDao.deleteByUpstreamEaAndLinkAppId(ea, linkAppId);
                upstreamLinkAppOuterRoleAssociationDao.deleteByUpstreamEaAndLinkAppId(ea, linkAppId);
            } catch (Exception e) {
                log.warn("id={}", linkAppId, e);
            }
        }
        return Result.newSuccess(true);
    }

    @Override
    public Result<List<UpstreamEmployeeVo>> listAllUpstreamPublicEmployees(Integer upstreamTenantId, String linkAppId) {
        String upstreamEa = eieaConverter.enterpriseIdToAccount(upstreamTenantId);
        UpstreamEmployeeLinkAppRole upstreamSelect = new UpstreamEmployeeLinkAppRole();
        upstreamSelect.setLinkAppId(linkAppId);
        upstreamSelect.setRole(UpstreamEmployeeRoleEnum.PUBLIC_EMPLOYEE.getRole());
        upstreamSelect.setUpstreamEa(upstreamEa);
        List<UpstreamEmployeeLinkAppRole> upstreamEmployeeLinkAppRoles = upstreamEmployeeLinkAppRoleDao.findBySelective(upstreamSelect);
        List<UpstreamEmployeeVo> upstreamEmployeeVos = upstreamEmployeeLinkAppRoles.stream().map(upstreamEmployeeLinkAppRole -> {
            UpstreamEmployeeVo upstreamEmployeeVo = new UpstreamEmployeeVo();
            upstreamEmployeeVo.setEa(upstreamEmployeeLinkAppRole.getUpstreamEa());
            upstreamEmployeeVo.setFsUserId(upstreamEmployeeLinkAppRole.getUpstreamFsUserId());
            return upstreamEmployeeVo;
        }).collect(Collectors.toList());
        return Result.newSuccess(upstreamEmployeeVos);
    }

    @Override
    public Result<Boolean> deleteLinkAppDataAuthPolicysByUpstreamAndPolicyId(String upstreamEa, String linkAppId, String policyId) {
        UpstreamLinkAppDataAuthPolicy upstreamLinkAppDataAuthPolicy = upstreamLinkAppDataAuthPolicyDao.getByPolicyId(upstreamEa, policyId, linkAppId);
        //判断方案是否存在/是否可以删除
        if (upstreamLinkAppDataAuthPolicy == null) {
            return Result.newError(ResultCode.POLICY_NOT_EXISTS);
        }
        if (upstreamLinkAppDataAuthPolicy.getIsDefault()) {
            return Result.newError(ResultCode.DEFAULT_POLICY_NOT_REMOVE);
        }

        //删除方案
        int i = upstreamLinkAppDataAuthPolicyDao.deleteByPolicyId(upstreamEa, policyId, linkAppId);
        if (i > 0) {
            syncDataAfterDeleteByPolicyId(upstreamEa, linkAppId, policyId);
        }
        //删除对象权限
        int i2 = upstreamPolicyDataAuthRangeDao.deleteByPolicyIds(Lists.newArrayList(policyId));
        if (i2 > 0) {
            syncDataAfterDeleteByPolicyIds(Lists.newArrayList(policyId));
        }
        //删除授权企业
        int i1 = upstreamPolicyAuthDownstreamDao.deleteByPolicyIds(Lists.newArrayList(policyId), null);
        if (i1 > 0) {
            syncDataAfterDeleteByPolicyIds(Lists.newArrayList(policyId), null);
        }
        return Result.newSuccess(true);
    }

    private void syncDataAfterDeleteByPolicyId(String upstreamEa, String linkAppId, String policyId) {
        String sql = "DELETE FROM upstream_link_app_data_auth_policy WHERE policy_id='" + policyId + "' and upstream_ea='" + upstreamEa + "' and link_app_id='" + linkAppId + "'";
        syncCrossCloudDataManager.sendSqlSyncCrossDataMsg(sql, "upstream_link_app_data_auth_policy");
    }

    @Override
    public Result<Integer> batchAddUpstreamEnterpriseLinkAppRelationData(List<UpstreamEnterpriseLinkAppRelationData> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return Result.newSuccess(0);
        }
        List<UpstreamEnterpriseLinkAppRelation> linkAppRelations = BeanUtil.deepCopyList(datas, UpstreamEnterpriseLinkAppRelation.class);
        int count = upstreamEnterpriseLinkAppRelationDao.batchInsert(linkAppRelations);
        return Result.newSuccess(count);
    }

    @Override
    public Result<List<UpstreamEnterpriseLinkAppRelationData>> listByUpstreamEaAndLinkAppIds(String upstreamEa, List<String> linkAppIds) {
        List<UpstreamEnterpriseLinkAppRelation> linkAppRelations = upstreamEnterpriseLinkAppRelationDao.batchFindByUpstreamEaAndLinkAppIds(upstreamEa, linkAppIds);
        if (CollectionUtils.isEmpty(linkAppRelations)) {
            return Result.newSuccess(Lists.newArrayList());
        }
        List<UpstreamEnterpriseLinkAppRelationData> datas = BeanUtil.deepCopyList(linkAppRelations, UpstreamEnterpriseLinkAppRelationData.class);
        return Result.newSuccess(datas);
    }

    /**
     * 查询上游企业的互联应用数据共享策略
     *
     * @param upstreamEa 上游企业
     */
    @Override
    public Result<List<UpstreamLinkAppDataAuthPolicyData>> listUpstreamLinkAppDataAuthPolicyByUpstreamEa(String upstreamEa) {
        if (StringUtil.isNullOrEmpty(upstreamEa)) {
            return Result.newSuccess(Lists.newLinkedList());
        }
        List<UpstreamLinkAppDataAuthPolicy> upstreamLinkAppDataAuthPolicies = upstreamLinkAppDataAuthPolicyDao.listByUpstreamEa(upstreamEa);
        List<UpstreamLinkAppDataAuthPolicyData> data = BeanUtil.deepCopyList(upstreamLinkAppDataAuthPolicies, UpstreamLinkAppDataAuthPolicyData.class);
        return Result.newSuccess(data);
    }

    /**
     * 新增互联应用数据共享策略
     *
     * @param entity 数据共享策略
     */
    @Override
    public Result<Integer> insertUpstreamLinkAppDataAuthPolicy(UpstreamLinkAppDataAuthPolicyData entity) {
        if (entity == null) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        UpstreamLinkAppDataAuthPolicy data = BeanUtil.deepCopy(entity, UpstreamLinkAppDataAuthPolicy.class);
        data.setVersion(1L);
        int insert = upstreamLinkAppDataAuthPolicyDao.insert(data);
        if (insert > 0) {
            syncCrossCloudDataManager.sendSyncCrossDataMsg(data.getId(), "upstream_link_app_data_auth_policy", DaoOperateEnum.UPSERT.getType());
        }
        return Result.newSuccess(insert);
    }

    /**
     * 批量新增上游企业授权下游企业规则
     *
     * @param policyId                 策略ID
     * @param downstreamOuterTenantIds 下游企业ID
     */
    @Override
    public Result<Boolean> batchAddUpstreamPolicyAuthDownstream(String policyId, List<Long> downstreamOuterTenantIds) {
        if (StringUtil.isNullOrEmpty(policyId) || CollectionUtils.isEmpty(downstreamOuterTenantIds)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        List<UpstreamPolicyAuthDownstream> policies = Lists.newArrayList();
        for (Long downstreamOuterTenantId : downstreamOuterTenantIds) {
            UpstreamPolicyAuthDownstream policy = new UpstreamPolicyAuthDownstream();
            policy.setPolicyId(policyId);
            policy.setDownstreamOuterTenantId(downstreamOuterTenantId);
            policies.add(policy);
        }
        int i = upstreamPolicyAuthDownstreamDao.batchAdd(policies);
        if (i > 0) {
            List ids = policies.stream().map(BaseEntity::getId).collect(Collectors.toList());
            syncCrossCloudDataManager.sendSyncCrossDataMsgByIds(ids, "upstream_policy_auth_downstream", DaoOperateEnum.UPSERT.getType());
        }
        return Result.newSuccess(Boolean.TRUE);
    }

    /**
     * 查询上游企业共享的下游企业互敬互爱
     *
     * @param policyIds 策略ID
     * @return 共享的下游企业列表
     */
    @Override
    public Result<List<UpstreamPolicyAuthDownstreamData>> listUpstreamPolicyAuthDownstreamByPolicyIds(List<String> policyIds) {
        if (CollectionUtils.isEmpty(policyIds)) {
            return Result.newSuccess(Lists.newArrayList());
        }
        List<UpstreamPolicyAuthDownstream> upstreamPolicyAuthDownstreams = upstreamPolicyAuthDownstreamDao.listByPolicyIds(policyIds);
        List<UpstreamPolicyAuthDownstreamData> data = BeanUtil.deepCopyList(upstreamPolicyAuthDownstreams, UpstreamPolicyAuthDownstreamData.class);
        return Result.newSuccess(data);
    }

    /**
     * 批量新增数据共享策略数据可见范围
     *
     * @param policyId            策略ID
     * @param crmObjectAuthRanges 对象可见范围
     * @return 新增结果
     */
    @Override
    public Result<Boolean> batchAddUpstreamPolicyDataAuthRange(String policyId, List<CrmObjectAuthRangeVo> crmObjectAuthRanges) {
        if (StringUtil.isNullOrEmpty(policyId) || CollectionUtils.isEmpty(crmObjectAuthRanges)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        List<UpstreamPolicyDataAuthRange> policies = Lists.newArrayList();
        for (CrmObjectAuthRangeVo rangeData : crmObjectAuthRanges) {
            UpstreamPolicyDataAuthRange range = new UpstreamPolicyDataAuthRange();
            range.setPolicyId(policyId);
            range.setObjectApiName(rangeData.getObjectApiName());
            range.setDownstreamAuthRange(rangeData.getDownstreamAuthRange());
            policies.add(range);
        }
        int i = upstreamPolicyDataAuthRangeDao.batchAdd(policies);
        if (i > 0) {
            List ids = policies.stream().map(BaseEntity::getId).collect(Collectors.toList());
            syncCrossCloudDataManager.sendSyncCrossDataMsgByIds(ids, "upstream_policy_data_auth_range", DaoOperateEnum.UPSERT.getType());
        }
        return Result.newSuccess(Boolean.TRUE);
    }

    /**
     * 查询数据共享策略中对象可见范围
     *
     * @param policyIds 共享策略ID
     * @return 数据授权可见范围
     */
    @Override
    public Result<List<UpstreamPolicyDataAuthRangeData>> listUpstreamPolicyDataAuthRangeByPolicyIds(List<String> policyIds) {
        if (CollectionUtils.isEmpty(policyIds)) {
            return Result.newSuccess(Lists.newArrayList());
        }
        List<UpstreamPolicyDataAuthRange> upstreamPolicyDataAuthRanges = upstreamPolicyDataAuthRangeDao.listByPolicyIds(policyIds);
        List<UpstreamPolicyDataAuthRangeData> data = BeanUtil.deepCopyList(upstreamPolicyDataAuthRanges, UpstreamPolicyDataAuthRangeData.class);
        return Result.newSuccess(data);
    }

    @Override
    public Result<List<UpstreamEmployeeLinkAppRoleData>> listUpstreamEmployeeLinkAppRoleByCondition(ListUpstreamEmployeeLinkAppRoleByConditionArg arg) {
        UpstreamEmployeeLinkAppRole copy = BeanUtil.copy(UpstreamEmployeeLinkAppRole.class, arg);
        List<UpstreamEmployeeLinkAppRole> upstreamEmployeeLinkAppRoles = upstreamEmployeeLinkAppRoleDao.findByEntity(copy);
        List<UpstreamEmployeeLinkAppRoleData> upstreamEmployeeLinkAppRoleDatas = Lists.newArrayList();
        BeanUtil.copyList(UpstreamEmployeeLinkAppRoleData.class, upstreamEmployeeLinkAppRoleDatas, upstreamEmployeeLinkAppRoles);
        return Result.newSuccess(upstreamEmployeeLinkAppRoleDatas);
    }

    @Override
    public Result<List<UpstreamEnterpriseLinkAppRelationData>> batchFindByUpstreamEaAndLinkAppIds(String upstreamEa, List<String> linkAppIds) {
        List<UpstreamEnterpriseLinkAppRelation> appRelations = upstreamEnterpriseLinkAppRelationDao.batchFindByUpstreamEaAndLinkAppIds(upstreamEa, linkAppIds);
        List<UpstreamEnterpriseLinkAppRelationData> appRelationDatas = Lists.newArrayList();
        BeanUtil.copyList(UpstreamEnterpriseLinkAppRelationData.class, appRelationDatas, appRelations);
        return Result.newSuccess(appRelationDatas);
    }

    private static class SystemAppInfo {
        private boolean myResult;
        private List<String> appIds;
        private Map<String, Boolean> statusMap;

        boolean resolveFail() {
            return myResult;
        }

        public List<String> getAppIds() {
            return appIds;
        }

        public Map<String, Boolean> getStatusMap() {
            return statusMap;
        }

        public SystemAppInfo resolve(String upstreamDefaultApps) {
            if (StringUtils.isEmpty(upstreamDefaultApps)) {
                log.warn("createEnterpriseDefaultAppRelations upstreamDefaultApps empty");
                myResult = true;
                return this;
            }
            appIds = Lists.newArrayList();
            String[] appIdsArr = upstreamDefaultApps.split(",");
            statusMap = Maps.newHashMap();
            for (String appIdStr : appIdsArr) {
                if (StringUtils.isEmpty(appIdStr)) {
                    continue;
                }
                String[] arr = appIdStr.split(":");
                String realAppId = arr[0];
                if (arr.length > 1 && "2".equals(arr[1])) {
                    statusMap.put(realAppId, false);
                } else {
                    statusMap.put(realAppId, true);
                }
                appIds.add(realAppId);
            }
            myResult = false;
            return this;
        }
    }

    @Override
    public Result<List<com.facishare.linkapp.api.result.LinkAppObjectAssociation>> queryLinkAppAssocObjectMapByLinkAppIds(List<String> linkAppIds) {
        List<LinkAppObjectAssociation> linkAppObjectAssociations = upstreamEnterpriseLinkAppRelationDao.queryLinkAppAssocObjectMapByLinkAppIds(linkAppIds);
        return Result.newSuccess(BeanUtil.deepCopyList(linkAppObjectAssociations, com.facishare.linkapp.api.result.LinkAppObjectAssociation.class));
    }

    @Override
    public Result<List<String>> listAllAssignLinkAppByUpstreamEaAndLinkAppIds(String upstreamEa, List<String> linkAppIds, Integer status) {
        List<String> openedAppIds = upstreamEnterpriseLinkAppRelationDao.listAllAssignLinkAppByUpstreamEaAndLinkAppIds(upstreamEa, linkAppIds, status);
        return Result.newSuccess(openedAppIds);
    }

    @Override
    public Result<List<String>> listUpstreamEasByPage(Integer offset, Integer pageSize) {
        return Result.newSuccess(upstreamEnterpriseLinkAppRelationDao.listUpstreamEasByPage(offset, pageSize));
    }

    /**
     * 清理已停用的企业相关数据
     *
     * @param upstreamEa 已停用企业Ea
     * @return 清理结果
     */
    @Override
    public Result<Boolean> deleteUpstreamEnterpriseLinkAppRelationByUpstreamEa(String upstreamEa) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upstreamEa)) {
            int rows = upstreamEnterpriseLinkAppRelationDao.deleteByUpstreamEa(upstreamEa);
            log.info("deleteUpstreamEnterpriseLinkAppRelationByUpstreamEa:{},upstreamEa:{}", rows, upstreamEa);
        }
        return Result.newSuccess();
    }

    /**
     * 清理已停用的企业相关数据
     *
     * @param upstreamEa 已停用企业Ea
     * @return 清理结果
     */
    @Override
    public Result<Boolean> deleteUpstreamEmployeeLinkAppRolesByUpstreamEa(String upstreamEa) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upstreamEa)) {
            int rows = upstreamEmployeeLinkAppRoleDao.deleteByUpstreamEa(upstreamEa);
            log.info("deleteUpstreamEmployeeLinkAppRolesByUpstreamEa:{},upstreamEa:{}", rows, upstreamEa);
            if (rows > 0) {
                syncDataAfterDeleteByUpstreamEa3(upstreamEa);
            }
        }
        return Result.newSuccess();
    }

    private void syncDataAfterDeleteByUpstreamEa3(String upstreamEa) {
        String sql = "DELETE FROM upstream_employee_link_app_role WHERE upstream_ea='" + upstreamEa + "'";
        syncCrossCloudDataManager.sendSqlSyncCrossDataMsg(sql, "upstream_employee_link_app_role");
    }

    /**
     * 清理已停用的企业相关数据
     *
     * @param upstreamEa 已停用企业Ea
     * @return 清理结果
     */
    @Override
    public Result<Boolean> deleteUpstreamEmployeeCustomerAppRoleByUpstreamEa(String upstreamEa) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upstreamEa)) {
            int rows = upstreamEmployeeCustomerAppRoleDao.deleteByUpstreamEa(upstreamEa);
            log.info("deleteUpstreamEmployeeCustomerAppRoleByUpstreamEa:{},upstreamEa:{}", rows, upstreamEa);
        }
        return Result.newSuccess();
    }

    /**
     * 清理已停用的企业相关数据
     *
     * @param upstreamEa 已停用企业Ea
     * @return 清理结果
     */
    @Override
    public Result<Boolean> deleteUpstreamLinkAppOuterRoleAssociationByUpstreamEa(String upstreamEa) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upstreamEa)) {
            int rows = upstreamLinkAppOuterRoleAssociationDao.deleteByUpstreamEa(upstreamEa);
            log.info("deleteUpstreamLinkAppOuterRoleAssociationByUpstreamEa:{},upstreamEa:{}", rows, upstreamEa);
        }
        return Result.newSuccess();
    }

    /**
     * 清理已停用的企业相关数据
     *
     * @param upstreamEa 已停用企业Ea
     * @return 清理结果
     */
    @Override
    public Result<Boolean> deleteUpstreamCustomerAppObjectAssociationByByUpstreamEa(String upstreamEa) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upstreamEa)) {
            int rows = upstreamCustomerAppObjectAssociationDao.deleteByUpstreamEa(upstreamEa);
            log.info("deleteUpstreamCustomerAppObjectAssociationByByUpstreamEa:{},upstreamEa:{}", rows, upstreamEa);
        }
        return Result.newSuccess();
    }

    /**
     * 清理已停用的企业相关数据
     *
     * @param upstreamEa 已停用企业Ea
     * @return 清理结果
     */
    @Override
    public Result<Boolean> deleteUpstreamLinkAppObjectAssociationByUpstreamEa(String upstreamEa) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upstreamEa)) {
            int rows = upstreamLinkAppObjectAssociationDao.deleteByUpstreamEa(upstreamEa);
            log.info("deleteUpstreamLinkAppObjectAssociationByUpstreamEa:{},upstreamEa:{}", rows, upstreamEa);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> deleteUpstreamLinkAppDownEmployeeOuterRoleAssignByUpstreamEa(String upstreamEa) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upstreamEa)) {
            int rows = upstreamLinkAppDownEmployeeOuterRoleAssignDao.deleteByUpstreamEa(upstreamEa);
            log.info("deleteUpstreamLinkAppDownEmployeeOuterRoleAssignByUpstreamEa:{},upstreamEa:{}", rows, upstreamEa);
            if (rows > 0) {
                syncDataAfterDeleteByUpstreamEa2(upstreamEa);
            }
        }
        return Result.newSuccess();
    }

    private void syncDataAfterDeleteByUpstreamEa2(String upstreamEa) {
        String sql = "DELETE FROM upstream_link_app_down_employee_outer_role_assign WHERE upstream_ea='" + upstreamEa + "'";
        syncCrossCloudDataManager.sendSqlSyncCrossDataMsg(sql, "upstream_link_app_down_employee_outer_role_assign");
    }

    /**
     * 清理已停用的企业相关数据
     *
     * @param upstreamEa 已停用企业Ea
     * @return 清理结果
     */
    @Override
    public Result<Boolean> deleteUpstreamCustomerAppObjectAssociationByUpstreamEa(String upstreamEa) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upstreamEa)) {
            int rows = upstreamCustomerAppObjectAssociationDao.deleteByUpstreamEa(upstreamEa);
            log.info("deleteUpstreamCustomerAppObjectAssociationByUpstreamEa:{},upstreamEa:{}", rows, upstreamEa);
        }
        return Result.newSuccess();
    }

    /**
     * 清理已停用的企业相关数据
     *
     * @param upstreamEa 已停用企业Ea
     * @return 清理结果
     */
    @Override
    public Result<Boolean> deleteUpstreamLinkAppDataAuthPolicyByUpstreamEa(String upstreamEa) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upstreamEa)) {
            int rows = upstreamLinkAppDataAuthPolicyDao.deleteByUpstreamEa(upstreamEa);
            log.info("deleteUpstreamLinkAppDataAuthPolicyByUpstreamEa:{},upstreamEa:{}", rows, upstreamEa);
            if (rows > 0) {
                syncDataAfterDeleteByUpstreamEa(upstreamEa);
            }
        }
        return Result.newSuccess();
    }

    private void syncDataAfterDeleteByUpstreamEa(String upstreamEa) {
        String sql = "DELETE FROM upstream_link_app_data_auth_policy WHERE upstream_ea='" + upstreamEa + "'";
        syncCrossCloudDataManager.sendSqlSyncCrossDataMsg(sql, "upstream_link_app_data_auth_policy");
    }

    /**
     * 清理已停用的企业相关数据
     *
     * @param upstreamEa 已停用企业Ea
     * @return 清理结果
     */
    @Override
    public Result<Boolean> deleteUpstreamCustomerAppDataAuthByUpstreamEa(String upstreamEa) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upstreamEa)) {
            int rows = upstreamCustomerAppDataAuthDao.deleteByUpstreamEa(upstreamEa);
            log.info("deleteUpstreamCustomerAppDataAuthByUpstreamEa:{},upstreamEa:{}", rows, upstreamEa);
        }
        return Result.newSuccess();
    }

    /**
     * 清理已停用的企业相关数据
     *
     * @param upstreamEa 已停用企业Ea
     * @return 清理结果
     */
    @Override
    public Result<Boolean> deleteUpstreamCustomerAppBizRoleAssociationByUpstreamEa(String upstreamEa) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upstreamEa)) {
            int rows = upstreamCustomerAppBizRoleAssociationDao.deleteByUpstreamEa(upstreamEa);
            log.info("deleteUpstreamCustomerAppBizRoleAssociationByUpstreamEa:{},upstreamEa:{}", rows, upstreamEa);
        }
        return Result.newSuccess();
    }

    /**
     * 查询指定状态的上游企业Ea
     *
     * @param status 应用开通状态
     * @return 开通的企业列表
     */
    @Override
    public Result<List<String>> listUpstreamEnterpriseLinkAppRelationByStatus(Integer status) {
        if (status != null) {
            List<String> eas = upstreamEnterpriseLinkAppRelationDao.listByStatus(status);
            return Result.newSuccess(eas);
        }
        return Result.newSuccess(Lists.newArrayList());
    }
}
