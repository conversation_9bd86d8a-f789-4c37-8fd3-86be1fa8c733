package com.facishare.er.admin.provider.manager;

import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.alibaba.fastjson.JSON;
import com.facishare.enterprise.common.exception.RelationException;
import com.facishare.enterprise.common.model.MsgVo;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.I18nUtil;
import com.facishare.er.api.service.EnterpriseMetaDataService;
import com.fxiaoke.otherrestapi.openmessage.arg.SendMsgToEaConnWithoutUpEaArg;
import com.fxiaoke.otherrestapi.openmessage.common.BaseResult;
import com.fxiaoke.otherrestapi.openmessage.common.MessageTypeEnum;
import com.fxiaoke.otherrestapi.openmessage.service.SendMessageService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Created by zhangjh on 2017/10/12.
 */
@Slf4j(topic = "OPERATION_SERVICE")
@Service
public class MsgManager {
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private EnterpriseMetaDataService metaDataService;

    /**
     * 向目标企业互联管理员发送图文消息
     *
     * @param acceptorEa 接受企业
     * @param appId 通过哪个应用发送
     * @param msgVo 消息格式
     * @return 是否推送成功
     * @throws RelationException 企业不能存在时会抛出异常
     */
    public boolean sendOperationMsg(String acceptorEa, String appId, MsgVo msgVo) {
        //获取互联管理员
        Result<List<Integer>> adminResult = metaDataService.listRelationAdmins(acceptorEa);
        if (!adminResult.isSuccess()) {
            log.warn("fail to get relation admins,ea:{},result:{}", acceptorEa, adminResult);
            if (adminResult.getErrCode() == ResultCode.EA_NOT_EXIST.getErrorCode()) { //企业不存在或被删除则抛出异常
                throw new RelationException(ResultCode.EA_NOT_EXIST);
            }
            return false;
        }
        List<Integer> relationAdmins = adminResult.getData();
        if (CollectionUtils.isEmpty(relationAdmins)) {
            return true;
        }

        SendMsgToEaConnWithoutUpEaArg messageVo = new SendMsgToEaConnWithoutUpEaArg();
        messageVo.setAppId(appId);
        messageVo.setAdminUserId("");
        messageVo.setPostId(UUID.randomUUID().toString());
        messageVo.setLowPriority(true);
        messageVo.setType(MessageTypeEnum.OPEN_MESSAGE.getType());
        messageVo.setEnterpriseAccount(acceptorEa);
        messageVo.setToUserList(relationAdmins);
        messageVo.setContent(buildMsgContent(msgVo.getTile(), msgVo.getSummary(), msgVo.getMsgUrl(), msgVo.getImageUrl(), msgVo.getMsgTime()));
        try {
            BaseResult result = sendMessageService.sendMsgToEaConnWithoutUpEa(messageVo);
            if (!result.isSuccess()) {
                log.warn("fail to send operation message,messageVo:{},result:{}", messageVo, result);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.warn("fail to call sendMessageService.sendMsgToEaConnService,messageVo:{},err:", messageVo, e);
            return false;
        }
    }

    public String buildMsgContent(String title, String summary, String msgUrl, String imageUrl, String msgTime) {
        Map<String, Object> data = new HashMap<>(4);
        data.put("Type", "IMAGE_TEXT");
        data.put("Title", title);
        data.put("DefaultSummary", title);//助手提示显示的摘要

        Map<String, Object> MessageContentMap = new HashMap<>();
        MessageContentMap.put("createTime", msgTime);
        MessageContentMap.put("messageId", UUID.randomUUID().toString());

        Map<String, Object> imageText = new HashMap<>(9);
        imageText.put("summary", summary);
        imageText.put("contentUrl", msgUrl);
        imageText.put("thirdPartyUrl", "");
        imageText.put("imageUrl", imageUrl);
        imageText.put("title", title);
        imageText.put("buttonText", I18nUtil.get(I18nKeyEnum.EIP_ERADMIN_MSGMANAGER_99));
        imageText.put("buttonUrl", msgUrl);
        imageText.put("contentType", 1);
        List<Map<String, Object>> imageTextList = new ArrayList<>(1);
        imageTextList.add(imageText);
        MessageContentMap.put("imageTextList", imageTextList);

        data.put("MessageContent", MessageContentMap);
        return JSON.toJSONString(data);
    }
}
