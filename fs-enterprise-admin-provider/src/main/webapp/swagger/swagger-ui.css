.swagger-ui {
  /*! normalize.css v7.0.0 | MIT License | github.com/necolas/normalize.css */
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui html {
  line-height: 1.15;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%
}

.swagger-ui body {
  margin: 0
}

.swagger-ui article, .swagger-ui aside, .swagger-ui footer, .swagger-ui header, .swagger-ui nav, .swagger-ui section {
  display: block
}

.swagger-ui h1 {
  font-size: 2em;
  margin: .67em 0
}

.swagger-ui figcaption, .swagger-ui figure, .swagger-ui main {
  display: block
}

.swagger-ui figure {
  margin: 1em 40px
}

.swagger-ui hr {
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
  overflow: visible
}

.swagger-ui pre {
  font-family: monospace, monospace;
  font-size: 1em
}

.swagger-ui a {
  background-color: transparent;
  -webkit-text-decoration-skip: objects
}

.swagger-ui abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted
}

.swagger-ui b, .swagger-ui strong {
  font-weight: inherit;
  font-weight: bolder
}

.swagger-ui code, .swagger-ui kbd, .swagger-ui samp {
  font-family: monospace, monospace;
  font-size: 1em
}

.swagger-ui dfn {
  font-style: italic
}

.swagger-ui mark {
  background-color: #ff0;
  color: #000
}

.swagger-ui small {
  font-size: 80%
}

.swagger-ui sub, .swagger-ui sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline
}

.swagger-ui sub {
  bottom: -.25em
}

.swagger-ui sup {
  top: -.5em
}

.swagger-ui audio, .swagger-ui video {
  display: inline-block
}

.swagger-ui audio:not([controls]) {
  display: none;
  height: 0
}

.swagger-ui img {
  border-style: none
}

.swagger-ui svg:not(:root) {
  overflow: hidden
}

.swagger-ui button, .swagger-ui input, .swagger-ui optgroup, .swagger-ui select, .swagger-ui textarea {
  font-family: sans-serif;
  font-size: 100%;
  line-height: 1.15;
  margin: 0
}

.swagger-ui button, .swagger-ui input {
  overflow: visible
}

.swagger-ui button, .swagger-ui select {
  text-transform: none
}

.swagger-ui [type=reset], .swagger-ui [type=submit], .swagger-ui button, .swagger-ui html [type=button] {
  -webkit-appearance: button
}

.swagger-ui [type=button]::-moz-focus-inner, .swagger-ui [type=reset]::-moz-focus-inner, .swagger-ui [type=submit]::-moz-focus-inner, .swagger-ui button::-moz-focus-inner {
  border-style: none;
  padding: 0
}

.swagger-ui [type=button]:-moz-focusring, .swagger-ui [type=reset]:-moz-focusring, .swagger-ui [type=submit]:-moz-focusring, .swagger-ui button:-moz-focusring {
  outline: 1px dotted ButtonText
}

.swagger-ui fieldset {
  padding: .35em .75em .625em
}

.swagger-ui legend {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: inherit;
  display: table;
  max-width: 100%;
  padding: 0;
  white-space: normal
}

.swagger-ui progress {
  display: inline-block;
  vertical-align: baseline
}

.swagger-ui textarea {
  overflow: auto
}

.swagger-ui [type=checkbox], .swagger-ui [type=radio] {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0
}

.swagger-ui [type=number]::-webkit-inner-spin-button, .swagger-ui [type=number]::-webkit-outer-spin-button {
  height: auto
}

.swagger-ui [type=search] {
  -webkit-appearance: textfield;
  outline-offset: -2px
}

.swagger-ui [type=search]::-webkit-search-cancel-button, .swagger-ui [type=search]::-webkit-search-decoration {
  -webkit-appearance: none
}

.swagger-ui ::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit
}

.swagger-ui details, .swagger-ui menu {
  display: block
}

.swagger-ui summary {
  display: list-item
}

.swagger-ui canvas {
  display: inline-block
}

.swagger-ui [hidden], .swagger-ui template {
  display: none
}

.swagger-ui .debug * {
  outline: 1px solid gold
}

.swagger-ui .debug-white * {
  outline: 1px solid #fff
}

.swagger-ui .debug-black * {
  outline: 1px solid #000
}

.swagger-ui .debug-grid {
  background: transparent url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTExIDc5LjE1ODMyNSwgMjAxNS8wOS8xMC0wMToxMDoyMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MTRDOTY4N0U2N0VFMTFFNjg2MzZDQjkwNkQ4MjgwMEIiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MTRDOTY4N0Q2N0VFMTFFNjg2MzZDQjkwNkQ4MjgwMEIiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo3NjcyQkQ3NjY3QzUxMUU2QjJCQ0UyNDA4MTAwMjE3MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo3NjcyQkQ3NzY3QzUxMUU2QjJCQ0UyNDA4MTAwMjE3MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PsBS+GMAAAAjSURBVHjaYvz//z8DLsD4gcGXiYEAGBIKGBne//fFpwAgwAB98AaF2pjlUQAAAABJRU5ErkJggg==) repeat 0 0
}

.swagger-ui .debug-grid-16 {
  background: transparent url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTExIDc5LjE1ODMyNSwgMjAxNS8wOS8xMC0wMToxMDoyMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6ODYyRjhERDU2N0YyMTFFNjg2MzZDQjkwNkQ4MjgwMEIiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6ODYyRjhERDQ2N0YyMTFFNjg2MzZDQjkwNkQ4MjgwMEIiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo3NjcyQkQ3QTY3QzUxMUU2QjJCQ0UyNDA4MTAwMjE3MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo3NjcyQkQ3QjY3QzUxMUU2QjJCQ0UyNDA4MTAwMjE3MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PvCS01IAAABMSURBVHjaYmR4/5+BFPBfAMFm/MBgx8RAGWCn1AAmSg34Q6kBDKMGMDCwICeMIemF/5QawEipAWwUhwEjMDvbAWlWkvVBwu8vQIABAEwBCph8U6c0AAAAAElFTkSuQmCC) repeat 0 0
}

.swagger-ui .debug-grid-8-solid {
  background: #fff url(data:image/jpeg;base64,/9j/4QAYRXhpZgAASUkqAAgAAAAAAAAAAAAAAP/sABFEdWNreQABAAQAAAAAAAD/4QMxaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLwA8P3hwYWNrZXQgYmVnaW49Iu+7vyIgaWQ9Ilc1TTBNcENlaGlIenJlU3pOVGN6a2M5ZCI/PiA8eDp4bXBtZXRhIHhtbG5zOng9ImFkb2JlOm5zOm1ldGEvIiB4OnhtcHRrPSJBZG9iZSBYTVAgQ29yZSA1LjYtYzExMSA3OS4xNTgzMjUsIDIwMTUvMDkvMTAtMDE6MTA6MjAgICAgICAgICI+IDxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+IDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PSIiIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCBDQyAyMDE1IChNYWNpbnRvc2gpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkIxMjI0OTczNjdCMzExRTZCMkJDRTI0MDgxMDAyMTcxIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkIxMjI0OTc0NjdCMzExRTZCMkJDRTI0MDgxMDAyMTcxIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QjEyMjQ5NzE2N0IzMTFFNkIyQkNFMjQwODEwMDIxNzEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QjEyMjQ5NzI2N0IzMTFFNkIyQkNFMjQwODEwMDIxNzEiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7/7gAOQWRvYmUAZMAAAAAB/9sAhAAbGhopHSlBJiZBQi8vL0JHPz4+P0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHAR0pKTQmND8oKD9HPzU/R0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0f/wAARCAAIAAgDASIAAhEBAxEB/8QAWQABAQAAAAAAAAAAAAAAAAAAAAYBAQEAAAAAAAAAAAAAAAAAAAIEEAEBAAMBAAAAAAAAAAAAAAABADECA0ERAAEDBQAAAAAAAAAAAAAAAAARITFBUWESIv/aAAwDAQACEQMRAD8AoOnTV1QTD7JJshP3vSM3P//Z) repeat 0 0
}

.swagger-ui .debug-grid-16-solid {
  background: #fff url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTExIDc5LjE1ODMyNSwgMjAxNS8wOS8xMC0wMToxMDoyMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NzY3MkJEN0U2N0M1MTFFNkIyQkNFMjQwODEwMDIxNzEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NzY3MkJEN0Y2N0M1MTFFNkIyQkNFMjQwODEwMDIxNzEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo3NjcyQkQ3QzY3QzUxMUU2QjJCQ0UyNDA4MTAwMjE3MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo3NjcyQkQ3RDY3QzUxMUU2QjJCQ0UyNDA4MTAwMjE3MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pve6J3kAAAAzSURBVHjaYvz//z8D0UDsMwMjSRoYP5Gq4SPNbRjVMEQ1fCRDg+in/6+J1AJUxsgAEGAA31BAJMS0GYEAAAAASUVORK5CYII=) repeat 0 0
}

.swagger-ui .border-box, .swagger-ui a, .swagger-ui article, .swagger-ui body, .swagger-ui code, .swagger-ui dd, .swagger-ui div, .swagger-ui dl, .swagger-ui dt, .swagger-ui fieldset, .swagger-ui footer, .swagger-ui form, .swagger-ui h1, .swagger-ui h2, .swagger-ui h3, .swagger-ui h4, .swagger-ui h5, .swagger-ui h6, .swagger-ui header, .swagger-ui html, .swagger-ui input[type=email], .swagger-ui input[type=number], .swagger-ui input[type=password], .swagger-ui input[type=tel], .swagger-ui input[type=text], .swagger-ui input[type=url], .swagger-ui legend, .swagger-ui li, .swagger-ui main, .swagger-ui ol, .swagger-ui p, .swagger-ui pre, .swagger-ui section, .swagger-ui table, .swagger-ui td, .swagger-ui textarea, .swagger-ui th, .swagger-ui tr, .swagger-ui ul {
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

.swagger-ui .aspect-ratio {
  height: 0;
  position: relative
}

.swagger-ui .aspect-ratio--16x9 {
  padding-bottom: 56.25%
}

.swagger-ui .aspect-ratio--9x16 {
  padding-bottom: 177.77%
}

.swagger-ui .aspect-ratio--4x3 {
  padding-bottom: 75%
}

.swagger-ui .aspect-ratio--3x4 {
  padding-bottom: 133.33%
}

.swagger-ui .aspect-ratio--6x4 {
  padding-bottom: 66.6%
}

.swagger-ui .aspect-ratio--4x6 {
  padding-bottom: 150%
}

.swagger-ui .aspect-ratio--8x5 {
  padding-bottom: 62.5%
}

.swagger-ui .aspect-ratio--5x8 {
  padding-bottom: 160%
}

.swagger-ui .aspect-ratio--7x5 {
  padding-bottom: 71.42%
}

.swagger-ui .aspect-ratio--5x7 {
  padding-bottom: 140%
}

.swagger-ui .aspect-ratio--1x1 {
  padding-bottom: 100%
}

.swagger-ui .aspect-ratio--object {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100
}

@media screen and (min-width: 30em) {
  .swagger-ui .aspect-ratio-ns {
    height: 0;
    position: relative
  }

  .swagger-ui .aspect-ratio--16x9-ns {
    padding-bottom: 56.25%
  }

  .swagger-ui .aspect-ratio--9x16-ns {
    padding-bottom: 177.77%
  }

  .swagger-ui .aspect-ratio--4x3-ns {
    padding-bottom: 75%
  }

  .swagger-ui .aspect-ratio--3x4-ns {
    padding-bottom: 133.33%
  }

  .swagger-ui .aspect-ratio--6x4-ns {
    padding-bottom: 66.6%
  }

  .swagger-ui .aspect-ratio--4x6-ns {
    padding-bottom: 150%
  }

  .swagger-ui .aspect-ratio--8x5-ns {
    padding-bottom: 62.5%
  }

  .swagger-ui .aspect-ratio--5x8-ns {
    padding-bottom: 160%
  }

  .swagger-ui .aspect-ratio--7x5-ns {
    padding-bottom: 71.42%
  }

  .swagger-ui .aspect-ratio--5x7-ns {
    padding-bottom: 140%
  }

  .swagger-ui .aspect-ratio--1x1-ns {
    padding-bottom: 100%
  }

  .swagger-ui .aspect-ratio--object-ns {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .aspect-ratio-m {
    height: 0;
    position: relative
  }

  .swagger-ui .aspect-ratio--16x9-m {
    padding-bottom: 56.25%
  }

  .swagger-ui .aspect-ratio--9x16-m {
    padding-bottom: 177.77%
  }

  .swagger-ui .aspect-ratio--4x3-m {
    padding-bottom: 75%
  }

  .swagger-ui .aspect-ratio--3x4-m {
    padding-bottom: 133.33%
  }

  .swagger-ui .aspect-ratio--6x4-m {
    padding-bottom: 66.6%
  }

  .swagger-ui .aspect-ratio--4x6-m {
    padding-bottom: 150%
  }

  .swagger-ui .aspect-ratio--8x5-m {
    padding-bottom: 62.5%
  }

  .swagger-ui .aspect-ratio--5x8-m {
    padding-bottom: 160%
  }

  .swagger-ui .aspect-ratio--7x5-m {
    padding-bottom: 71.42%
  }

  .swagger-ui .aspect-ratio--5x7-m {
    padding-bottom: 140%
  }

  .swagger-ui .aspect-ratio--1x1-m {
    padding-bottom: 100%
  }

  .swagger-ui .aspect-ratio--object-m {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .aspect-ratio-l {
    height: 0;
    position: relative
  }

  .swagger-ui .aspect-ratio--16x9-l {
    padding-bottom: 56.25%
  }

  .swagger-ui .aspect-ratio--9x16-l {
    padding-bottom: 177.77%
  }

  .swagger-ui .aspect-ratio--4x3-l {
    padding-bottom: 75%
  }

  .swagger-ui .aspect-ratio--3x4-l {
    padding-bottom: 133.33%
  }

  .swagger-ui .aspect-ratio--6x4-l {
    padding-bottom: 66.6%
  }

  .swagger-ui .aspect-ratio--4x6-l {
    padding-bottom: 150%
  }

  .swagger-ui .aspect-ratio--8x5-l {
    padding-bottom: 62.5%
  }

  .swagger-ui .aspect-ratio--5x8-l {
    padding-bottom: 160%
  }

  .swagger-ui .aspect-ratio--7x5-l {
    padding-bottom: 71.42%
  }

  .swagger-ui .aspect-ratio--5x7-l {
    padding-bottom: 140%
  }

  .swagger-ui .aspect-ratio--1x1-l {
    padding-bottom: 100%
  }

  .swagger-ui .aspect-ratio--object-l {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100
  }
}

.swagger-ui img {
  max-width: 100%
}

.swagger-ui .cover {
  background-size: cover !important
}

.swagger-ui .contain {
  background-size: contain !important
}

@media screen and (min-width: 30em) {
  .swagger-ui .cover-ns {
    background-size: cover !important
  }

  .swagger-ui .contain-ns {
    background-size: contain !important
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .cover-m {
    background-size: cover !important
  }

  .swagger-ui .contain-m {
    background-size: contain !important
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .cover-l {
    background-size: cover !important
  }

  .swagger-ui .contain-l {
    background-size: contain !important
  }
}

.swagger-ui .bg-center {
  background-repeat: no-repeat;
  background-position: 50%
}

.swagger-ui .bg-top {
  background-repeat: no-repeat;
  background-position: top
}

.swagger-ui .bg-right {
  background-repeat: no-repeat;
  background-position: 100%
}

.swagger-ui .bg-bottom {
  background-repeat: no-repeat;
  background-position: bottom
}

.swagger-ui .bg-left {
  background-repeat: no-repeat;
  background-position: 0
}

@media screen and (min-width: 30em) {
  .swagger-ui .bg-center-ns {
    background-repeat: no-repeat;
    background-position: 50%
  }

  .swagger-ui .bg-top-ns {
    background-repeat: no-repeat;
    background-position: top
  }

  .swagger-ui .bg-right-ns {
    background-repeat: no-repeat;
    background-position: 100%
  }

  .swagger-ui .bg-bottom-ns {
    background-repeat: no-repeat;
    background-position: bottom
  }

  .swagger-ui .bg-left-ns {
    background-repeat: no-repeat;
    background-position: 0
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .bg-center-m {
    background-repeat: no-repeat;
    background-position: 50%
  }

  .swagger-ui .bg-top-m {
    background-repeat: no-repeat;
    background-position: top
  }

  .swagger-ui .bg-right-m {
    background-repeat: no-repeat;
    background-position: 100%
  }

  .swagger-ui .bg-bottom-m {
    background-repeat: no-repeat;
    background-position: bottom
  }

  .swagger-ui .bg-left-m {
    background-repeat: no-repeat;
    background-position: 0
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .bg-center-l {
    background-repeat: no-repeat;
    background-position: 50%
  }

  .swagger-ui .bg-top-l {
    background-repeat: no-repeat;
    background-position: top
  }

  .swagger-ui .bg-right-l {
    background-repeat: no-repeat;
    background-position: 100%
  }

  .swagger-ui .bg-bottom-l {
    background-repeat: no-repeat;
    background-position: bottom
  }

  .swagger-ui .bg-left-l {
    background-repeat: no-repeat;
    background-position: 0
  }
}

.swagger-ui .outline {
  outline: 1px solid
}

.swagger-ui .outline-transparent {
  outline: 1px solid transparent
}

.swagger-ui .outline-0 {
  outline: 0
}

@media screen and (min-width: 30em) {
  .swagger-ui .outline-ns {
    outline: 1px solid
  }

  .swagger-ui .outline-transparent-ns {
    outline: 1px solid transparent
  }

  .swagger-ui .outline-0-ns {
    outline: 0
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .outline-m {
    outline: 1px solid
  }

  .swagger-ui .outline-transparent-m {
    outline: 1px solid transparent
  }

  .swagger-ui .outline-0-m {
    outline: 0
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .outline-l {
    outline: 1px solid
  }

  .swagger-ui .outline-transparent-l {
    outline: 1px solid transparent
  }

  .swagger-ui .outline-0-l {
    outline: 0
  }
}

.swagger-ui .ba {
  border-style: solid;
  border-width: 1px
}

.swagger-ui .bt {
  border-top-style: solid;
  border-top-width: 1px
}

.swagger-ui .br {
  border-right-style: solid;
  border-right-width: 1px
}

.swagger-ui .bb {
  border-bottom-style: solid;
  border-bottom-width: 1px
}

.swagger-ui .bl {
  border-left-style: solid;
  border-left-width: 1px
}

.swagger-ui .bn {
  border-style: none;
  border-width: 0
}

@media screen and (min-width: 30em) {
  .swagger-ui .ba-ns {
    border-style: solid;
    border-width: 1px
  }

  .swagger-ui .bt-ns {
    border-top-style: solid;
    border-top-width: 1px
  }

  .swagger-ui .br-ns {
    border-right-style: solid;
    border-right-width: 1px
  }

  .swagger-ui .bb-ns {
    border-bottom-style: solid;
    border-bottom-width: 1px
  }

  .swagger-ui .bl-ns {
    border-left-style: solid;
    border-left-width: 1px
  }

  .swagger-ui .bn-ns {
    border-style: none;
    border-width: 0
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .ba-m {
    border-style: solid;
    border-width: 1px
  }

  .swagger-ui .bt-m {
    border-top-style: solid;
    border-top-width: 1px
  }

  .swagger-ui .br-m {
    border-right-style: solid;
    border-right-width: 1px
  }

  .swagger-ui .bb-m {
    border-bottom-style: solid;
    border-bottom-width: 1px
  }

  .swagger-ui .bl-m {
    border-left-style: solid;
    border-left-width: 1px
  }

  .swagger-ui .bn-m {
    border-style: none;
    border-width: 0
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .ba-l {
    border-style: solid;
    border-width: 1px
  }

  .swagger-ui .bt-l {
    border-top-style: solid;
    border-top-width: 1px
  }

  .swagger-ui .br-l {
    border-right-style: solid;
    border-right-width: 1px
  }

  .swagger-ui .bb-l {
    border-bottom-style: solid;
    border-bottom-width: 1px
  }

  .swagger-ui .bl-l {
    border-left-style: solid;
    border-left-width: 1px
  }

  .swagger-ui .bn-l {
    border-style: none;
    border-width: 0
  }
}

.swagger-ui .b--black {
  border-color: #000
}

.swagger-ui .b--near-black {
  border-color: #111
}

.swagger-ui .b--dark-gray {
  border-color: #333
}

.swagger-ui .b--mid-gray {
  border-color: #555
}

.swagger-ui .b--gray {
  border-color: #777
}

.swagger-ui .b--silver {
  border-color: #999
}

.swagger-ui .b--light-silver {
  border-color: #aaa
}

.swagger-ui .b--moon-gray {
  border-color: #ccc
}

.swagger-ui .b--light-gray {
  border-color: #eee
}

.swagger-ui .b--near-white {
  border-color: #f4f4f4
}

.swagger-ui .b--white {
  border-color: #fff
}

.swagger-ui .b--white-90 {
  border-color: hsla(0, 0%, 100%, .9)
}

.swagger-ui .b--white-80 {
  border-color: hsla(0, 0%, 100%, .8)
}

.swagger-ui .b--white-70 {
  border-color: hsla(0, 0%, 100%, .7)
}

.swagger-ui .b--white-60 {
  border-color: hsla(0, 0%, 100%, .6)
}

.swagger-ui .b--white-50 {
  border-color: hsla(0, 0%, 100%, .5)
}

.swagger-ui .b--white-40 {
  border-color: hsla(0, 0%, 100%, .4)
}

.swagger-ui .b--white-30 {
  border-color: hsla(0, 0%, 100%, .3)
}

.swagger-ui .b--white-20 {
  border-color: hsla(0, 0%, 100%, .2)
}

.swagger-ui .b--white-10 {
  border-color: hsla(0, 0%, 100%, .1)
}

.swagger-ui .b--white-05 {
  border-color: hsla(0, 0%, 100%, .05)
}

.swagger-ui .b--white-025 {
  border-color: hsla(0, 0%, 100%, .025)
}

.swagger-ui .b--white-0125 {
  border-color: hsla(0, 0%, 100%, .0125)
}

.swagger-ui .b--black-90 {
  border-color: rgba(0, 0, 0, .9)
}

.swagger-ui .b--black-80 {
  border-color: rgba(0, 0, 0, .8)
}

.swagger-ui .b--black-70 {
  border-color: rgba(0, 0, 0, .7)
}

.swagger-ui .b--black-60 {
  border-color: rgba(0, 0, 0, .6)
}

.swagger-ui .b--black-50 {
  border-color: rgba(0, 0, 0, .5)
}

.swagger-ui .b--black-40 {
  border-color: rgba(0, 0, 0, .4)
}

.swagger-ui .b--black-30 {
  border-color: rgba(0, 0, 0, .3)
}

.swagger-ui .b--black-20 {
  border-color: rgba(0, 0, 0, .2)
}

.swagger-ui .b--black-10 {
  border-color: rgba(0, 0, 0, .1)
}

.swagger-ui .b--black-05 {
  border-color: rgba(0, 0, 0, .05)
}

.swagger-ui .b--black-025 {
  border-color: rgba(0, 0, 0, .025)
}

.swagger-ui .b--black-0125 {
  border-color: rgba(0, 0, 0, .0125)
}

.swagger-ui .b--dark-red {
  border-color: #e7040f
}

.swagger-ui .b--red {
  border-color: #ff4136
}

.swagger-ui .b--light-red {
  border-color: #ff725c
}

.swagger-ui .b--orange {
  border-color: #ff6300
}

.swagger-ui .b--gold {
  border-color: #ffb700
}

.swagger-ui .b--yellow {
  border-color: gold
}

.swagger-ui .b--light-yellow {
  border-color: #fbf1a9
}

.swagger-ui .b--purple {
  border-color: #5e2ca5
}

.swagger-ui .b--light-purple {
  border-color: #a463f2
}

.swagger-ui .b--dark-pink {
  border-color: #d5008f
}

.swagger-ui .b--hot-pink {
  border-color: #ff41b4
}

.swagger-ui .b--pink {
  border-color: #ff80cc
}

.swagger-ui .b--light-pink {
  border-color: #ffa3d7
}

.swagger-ui .b--dark-green {
  border-color: #137752
}

.swagger-ui .b--green {
  border-color: #19a974
}

.swagger-ui .b--light-green {
  border-color: #9eebcf
}

.swagger-ui .b--navy {
  border-color: #001b44
}

.swagger-ui .b--dark-blue {
  border-color: #00449e
}

.swagger-ui .b--blue {
  border-color: #357edd
}

.swagger-ui .b--light-blue {
  border-color: #96ccff
}

.swagger-ui .b--lightest-blue {
  border-color: #cdecff
}

.swagger-ui .b--washed-blue {
  border-color: #f6fffe
}

.swagger-ui .b--washed-green {
  border-color: #e8fdf5
}

.swagger-ui .b--washed-yellow {
  border-color: #fffceb
}

.swagger-ui .b--washed-red {
  border-color: #ffdfdf
}

.swagger-ui .b--transparent {
  border-color: transparent
}

.swagger-ui .b--inherit {
  border-color: inherit
}

.swagger-ui .br0 {
  border-radius: 0
}

.swagger-ui .br1 {
  border-radius: .125rem
}

.swagger-ui .br2 {
  border-radius: .25rem
}

.swagger-ui .br3 {
  border-radius: .5rem
}

.swagger-ui .br4 {
  border-radius: 1rem
}

.swagger-ui .br-100 {
  border-radius: 100%
}

.swagger-ui .br-pill {
  border-radius: 9999px
}

.swagger-ui .br--bottom {
  border-top-left-radius: 0;
  border-top-right-radius: 0
}

.swagger-ui .br--top {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0
}

.swagger-ui .br--right {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.swagger-ui .br--left {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

@media screen and (min-width: 30em) {
  .swagger-ui .br0-ns {
    border-radius: 0
  }

  .swagger-ui .br1-ns {
    border-radius: .125rem
  }

  .swagger-ui .br2-ns {
    border-radius: .25rem
  }

  .swagger-ui .br3-ns {
    border-radius: .5rem
  }

  .swagger-ui .br4-ns {
    border-radius: 1rem
  }

  .swagger-ui .br-100-ns {
    border-radius: 100%
  }

  .swagger-ui .br-pill-ns {
    border-radius: 9999px
  }

  .swagger-ui .br--bottom-ns {
    border-top-left-radius: 0;
    border-top-right-radius: 0
  }

  .swagger-ui .br--top-ns {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
  }

  .swagger-ui .br--right-ns {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
  }

  .swagger-ui .br--left-ns {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .br0-m {
    border-radius: 0
  }

  .swagger-ui .br1-m {
    border-radius: .125rem
  }

  .swagger-ui .br2-m {
    border-radius: .25rem
  }

  .swagger-ui .br3-m {
    border-radius: .5rem
  }

  .swagger-ui .br4-m {
    border-radius: 1rem
  }

  .swagger-ui .br-100-m {
    border-radius: 100%
  }

  .swagger-ui .br-pill-m {
    border-radius: 9999px
  }

  .swagger-ui .br--bottom-m {
    border-top-left-radius: 0;
    border-top-right-radius: 0
  }

  .swagger-ui .br--top-m {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
  }

  .swagger-ui .br--right-m {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
  }

  .swagger-ui .br--left-m {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .br0-l {
    border-radius: 0
  }

  .swagger-ui .br1-l {
    border-radius: .125rem
  }

  .swagger-ui .br2-l {
    border-radius: .25rem
  }

  .swagger-ui .br3-l {
    border-radius: .5rem
  }

  .swagger-ui .br4-l {
    border-radius: 1rem
  }

  .swagger-ui .br-100-l {
    border-radius: 100%
  }

  .swagger-ui .br-pill-l {
    border-radius: 9999px
  }

  .swagger-ui .br--bottom-l {
    border-radius-top-left: 0;
    border-radius-top-right: 0
  }

  .swagger-ui .br--top-l {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
  }

  .swagger-ui .br--right-l {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
  }

  .swagger-ui .br--left-l {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
  }
}

.swagger-ui .b--dotted {
  border-style: dotted
}

.swagger-ui .b--dashed {
  border-style: dashed
}

.swagger-ui .b--solid {
  border-style: solid
}

.swagger-ui .b--none {
  border-style: none
}

@media screen and (min-width: 30em) {
  .swagger-ui .b--dotted-ns {
    border-style: dotted
  }

  .swagger-ui .b--dashed-ns {
    border-style: dashed
  }

  .swagger-ui .b--solid-ns {
    border-style: solid
  }

  .swagger-ui .b--none-ns {
    border-style: none
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .b--dotted-m {
    border-style: dotted
  }

  .swagger-ui .b--dashed-m {
    border-style: dashed
  }

  .swagger-ui .b--solid-m {
    border-style: solid
  }

  .swagger-ui .b--none-m {
    border-style: none
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .b--dotted-l {
    border-style: dotted
  }

  .swagger-ui .b--dashed-l {
    border-style: dashed
  }

  .swagger-ui .b--solid-l {
    border-style: solid
  }

  .swagger-ui .b--none-l {
    border-style: none
  }
}

.swagger-ui .bw0 {
  border-width: 0
}

.swagger-ui .bw1 {
  border-width: .125rem
}

.swagger-ui .bw2 {
  border-width: .25rem
}

.swagger-ui .bw3 {
  border-width: .5rem
}

.swagger-ui .bw4 {
  border-width: 1rem
}

.swagger-ui .bw5 {
  border-width: 2rem
}

.swagger-ui .bt-0 {
  border-top-width: 0
}

.swagger-ui .br-0 {
  border-right-width: 0
}

.swagger-ui .bb-0 {
  border-bottom-width: 0
}

.swagger-ui .bl-0 {
  border-left-width: 0
}

@media screen and (min-width: 30em) {
  .swagger-ui .bw0-ns {
    border-width: 0
  }

  .swagger-ui .bw1-ns {
    border-width: .125rem
  }

  .swagger-ui .bw2-ns {
    border-width: .25rem
  }

  .swagger-ui .bw3-ns {
    border-width: .5rem
  }

  .swagger-ui .bw4-ns {
    border-width: 1rem
  }

  .swagger-ui .bw5-ns {
    border-width: 2rem
  }

  .swagger-ui .bt-0-ns {
    border-top-width: 0
  }

  .swagger-ui .br-0-ns {
    border-right-width: 0
  }

  .swagger-ui .bb-0-ns {
    border-bottom-width: 0
  }

  .swagger-ui .bl-0-ns {
    border-left-width: 0
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .bw0-m {
    border-width: 0
  }

  .swagger-ui .bw1-m {
    border-width: .125rem
  }

  .swagger-ui .bw2-m {
    border-width: .25rem
  }

  .swagger-ui .bw3-m {
    border-width: .5rem
  }

  .swagger-ui .bw4-m {
    border-width: 1rem
  }

  .swagger-ui .bw5-m {
    border-width: 2rem
  }

  .swagger-ui .bt-0-m {
    border-top-width: 0
  }

  .swagger-ui .br-0-m {
    border-right-width: 0
  }

  .swagger-ui .bb-0-m {
    border-bottom-width: 0
  }

  .swagger-ui .bl-0-m {
    border-left-width: 0
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .bw0-l {
    border-width: 0
  }

  .swagger-ui .bw1-l {
    border-width: .125rem
  }

  .swagger-ui .bw2-l {
    border-width: .25rem
  }

  .swagger-ui .bw3-l {
    border-width: .5rem
  }

  .swagger-ui .bw4-l {
    border-width: 1rem
  }

  .swagger-ui .bw5-l {
    border-width: 2rem
  }

  .swagger-ui .bt-0-l {
    border-top-width: 0
  }

  .swagger-ui .br-0-l {
    border-right-width: 0
  }

  .swagger-ui .bb-0-l {
    border-bottom-width: 0
  }

  .swagger-ui .bl-0-l {
    border-left-width: 0
  }
}

.swagger-ui .shadow-1 {
  -webkit-box-shadow: 0 0 4px 2px rgba(0, 0, 0, .2);
  box-shadow: 0 0 4px 2px rgba(0, 0, 0, .2)
}

.swagger-ui .shadow-2 {
  -webkit-box-shadow: 0 0 8px 2px rgba(0, 0, 0, .2);
  box-shadow: 0 0 8px 2px rgba(0, 0, 0, .2)
}

.swagger-ui .shadow-3 {
  -webkit-box-shadow: 2px 2px 4px 2px rgba(0, 0, 0, .2);
  box-shadow: 2px 2px 4px 2px rgba(0, 0, 0, .2)
}

.swagger-ui .shadow-4 {
  -webkit-box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, .2);
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, .2)
}

.swagger-ui .shadow-5 {
  -webkit-box-shadow: 4px 4px 8px 0 rgba(0, 0, 0, .2);
  box-shadow: 4px 4px 8px 0 rgba(0, 0, 0, .2)
}

@media screen and (min-width: 30em) {
  .swagger-ui .shadow-1-ns {
    -webkit-box-shadow: 0 0 4px 2px rgba(0, 0, 0, .2);
    box-shadow: 0 0 4px 2px rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-2-ns {
    -webkit-box-shadow: 0 0 8px 2px rgba(0, 0, 0, .2);
    box-shadow: 0 0 8px 2px rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-3-ns {
    -webkit-box-shadow: 2px 2px 4px 2px rgba(0, 0, 0, .2);
    box-shadow: 2px 2px 4px 2px rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-4-ns {
    -webkit-box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, .2);
    box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-5-ns {
    -webkit-box-shadow: 4px 4px 8px 0 rgba(0, 0, 0, .2);
    box-shadow: 4px 4px 8px 0 rgba(0, 0, 0, .2)
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .shadow-1-m {
    -webkit-box-shadow: 0 0 4px 2px rgba(0, 0, 0, .2);
    box-shadow: 0 0 4px 2px rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-2-m {
    -webkit-box-shadow: 0 0 8px 2px rgba(0, 0, 0, .2);
    box-shadow: 0 0 8px 2px rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-3-m {
    -webkit-box-shadow: 2px 2px 4px 2px rgba(0, 0, 0, .2);
    box-shadow: 2px 2px 4px 2px rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-4-m {
    -webkit-box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, .2);
    box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-5-m {
    -webkit-box-shadow: 4px 4px 8px 0 rgba(0, 0, 0, .2);
    box-shadow: 4px 4px 8px 0 rgba(0, 0, 0, .2)
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .shadow-1-l {
    -webkit-box-shadow: 0 0 4px 2px rgba(0, 0, 0, .2);
    box-shadow: 0 0 4px 2px rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-2-l {
    -webkit-box-shadow: 0 0 8px 2px rgba(0, 0, 0, .2);
    box-shadow: 0 0 8px 2px rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-3-l {
    -webkit-box-shadow: 2px 2px 4px 2px rgba(0, 0, 0, .2);
    box-shadow: 2px 2px 4px 2px rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-4-l {
    -webkit-box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, .2);
    box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, .2)
  }

  .swagger-ui .shadow-5-l {
    -webkit-box-shadow: 4px 4px 8px 0 rgba(0, 0, 0, .2);
    box-shadow: 4px 4px 8px 0 rgba(0, 0, 0, .2)
  }
}

.swagger-ui .pre {
  overflow-x: auto;
  overflow-y: hidden;
  overflow: scroll
}

.swagger-ui .top-0 {
  top: 0
}

.swagger-ui .right-0 {
  right: 0
}

.swagger-ui .bottom-0 {
  bottom: 0
}

.swagger-ui .left-0 {
  left: 0
}

.swagger-ui .top-1 {
  top: 1rem
}

.swagger-ui .right-1 {
  right: 1rem
}

.swagger-ui .bottom-1 {
  bottom: 1rem
}

.swagger-ui .left-1 {
  left: 1rem
}

.swagger-ui .top-2 {
  top: 2rem
}

.swagger-ui .right-2 {
  right: 2rem
}

.swagger-ui .bottom-2 {
  bottom: 2rem
}

.swagger-ui .left-2 {
  left: 2rem
}

.swagger-ui .top--1 {
  top: -1rem
}

.swagger-ui .right--1 {
  right: -1rem
}

.swagger-ui .bottom--1 {
  bottom: -1rem
}

.swagger-ui .left--1 {
  left: -1rem
}

.swagger-ui .top--2 {
  top: -2rem
}

.swagger-ui .right--2 {
  right: -2rem
}

.swagger-ui .bottom--2 {
  bottom: -2rem
}

.swagger-ui .left--2 {
  left: -2rem
}

.swagger-ui .absolute--fill {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0
}

@media screen and (min-width: 30em) {
  .swagger-ui .top-0-ns {
    top: 0
  }

  .swagger-ui .left-0-ns {
    left: 0
  }

  .swagger-ui .right-0-ns {
    right: 0
  }

  .swagger-ui .bottom-0-ns {
    bottom: 0
  }

  .swagger-ui .top-1-ns {
    top: 1rem
  }

  .swagger-ui .left-1-ns {
    left: 1rem
  }

  .swagger-ui .right-1-ns {
    right: 1rem
  }

  .swagger-ui .bottom-1-ns {
    bottom: 1rem
  }

  .swagger-ui .top-2-ns {
    top: 2rem
  }

  .swagger-ui .left-2-ns {
    left: 2rem
  }

  .swagger-ui .right-2-ns {
    right: 2rem
  }

  .swagger-ui .bottom-2-ns {
    bottom: 2rem
  }

  .swagger-ui .top--1-ns {
    top: -1rem
  }

  .swagger-ui .right--1-ns {
    right: -1rem
  }

  .swagger-ui .bottom--1-ns {
    bottom: -1rem
  }

  .swagger-ui .left--1-ns {
    left: -1rem
  }

  .swagger-ui .top--2-ns {
    top: -2rem
  }

  .swagger-ui .right--2-ns {
    right: -2rem
  }

  .swagger-ui .bottom--2-ns {
    bottom: -2rem
  }

  .swagger-ui .left--2-ns {
    left: -2rem
  }

  .swagger-ui .absolute--fill-ns {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .top-0-m {
    top: 0
  }

  .swagger-ui .left-0-m {
    left: 0
  }

  .swagger-ui .right-0-m {
    right: 0
  }

  .swagger-ui .bottom-0-m {
    bottom: 0
  }

  .swagger-ui .top-1-m {
    top: 1rem
  }

  .swagger-ui .left-1-m {
    left: 1rem
  }

  .swagger-ui .right-1-m {
    right: 1rem
  }

  .swagger-ui .bottom-1-m {
    bottom: 1rem
  }

  .swagger-ui .top-2-m {
    top: 2rem
  }

  .swagger-ui .left-2-m {
    left: 2rem
  }

  .swagger-ui .right-2-m {
    right: 2rem
  }

  .swagger-ui .bottom-2-m {
    bottom: 2rem
  }

  .swagger-ui .top--1-m {
    top: -1rem
  }

  .swagger-ui .right--1-m {
    right: -1rem
  }

  .swagger-ui .bottom--1-m {
    bottom: -1rem
  }

  .swagger-ui .left--1-m {
    left: -1rem
  }

  .swagger-ui .top--2-m {
    top: -2rem
  }

  .swagger-ui .right--2-m {
    right: -2rem
  }

  .swagger-ui .bottom--2-m {
    bottom: -2rem
  }

  .swagger-ui .left--2-m {
    left: -2rem
  }

  .swagger-ui .absolute--fill-m {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .top-0-l {
    top: 0
  }

  .swagger-ui .left-0-l {
    left: 0
  }

  .swagger-ui .right-0-l {
    right: 0
  }

  .swagger-ui .bottom-0-l {
    bottom: 0
  }

  .swagger-ui .top-1-l {
    top: 1rem
  }

  .swagger-ui .left-1-l {
    left: 1rem
  }

  .swagger-ui .right-1-l {
    right: 1rem
  }

  .swagger-ui .bottom-1-l {
    bottom: 1rem
  }

  .swagger-ui .top-2-l {
    top: 2rem
  }

  .swagger-ui .left-2-l {
    left: 2rem
  }

  .swagger-ui .right-2-l {
    right: 2rem
  }

  .swagger-ui .bottom-2-l {
    bottom: 2rem
  }

  .swagger-ui .top--1-l {
    top: -1rem
  }

  .swagger-ui .right--1-l {
    right: -1rem
  }

  .swagger-ui .bottom--1-l {
    bottom: -1rem
  }

  .swagger-ui .left--1-l {
    left: -1rem
  }

  .swagger-ui .top--2-l {
    top: -2rem
  }

  .swagger-ui .right--2-l {
    right: -2rem
  }

  .swagger-ui .bottom--2-l {
    bottom: -2rem
  }

  .swagger-ui .left--2-l {
    left: -2rem
  }

  .swagger-ui .absolute--fill-l {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
  }
}

.swagger-ui .cf:after, .swagger-ui .cf:before {
  content: " ";
  display: table
}

.swagger-ui .cf:after {
  clear: both
}

.swagger-ui .cf {
  *zoom: 1
}

.swagger-ui .cl {
  clear: left
}

.swagger-ui .cr {
  clear: right
}

.swagger-ui .cb {
  clear: both
}

.swagger-ui .cn {
  clear: none
}

@media screen and (min-width: 30em) {
  .swagger-ui .cl-ns {
    clear: left
  }

  .swagger-ui .cr-ns {
    clear: right
  }

  .swagger-ui .cb-ns {
    clear: both
  }

  .swagger-ui .cn-ns {
    clear: none
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .cl-m {
    clear: left
  }

  .swagger-ui .cr-m {
    clear: right
  }

  .swagger-ui .cb-m {
    clear: both
  }

  .swagger-ui .cn-m {
    clear: none
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .cl-l {
    clear: left
  }

  .swagger-ui .cr-l {
    clear: right
  }

  .swagger-ui .cb-l {
    clear: both
  }

  .swagger-ui .cn-l {
    clear: none
  }
}

.swagger-ui .dn {
  display: none
}

.swagger-ui .di {
  display: inline
}

.swagger-ui .db {
  display: block
}

.swagger-ui .dib {
  display: inline-block
}

.swagger-ui .dit {
  display: inline-table
}

.swagger-ui .dt {
  display: table
}

.swagger-ui .dtc {
  display: table-cell
}

.swagger-ui .dt-row {
  display: table-row
}

.swagger-ui .dt-row-group {
  display: table-row-group
}

.swagger-ui .dt-column {
  display: table-column
}

.swagger-ui .dt-column-group {
  display: table-column-group
}

.swagger-ui .dt--fixed {
  table-layout: fixed;
  width: 100%
}

@media screen and (min-width: 30em) {
  .swagger-ui .dn-ns {
    display: none
  }

  .swagger-ui .di-ns {
    display: inline
  }

  .swagger-ui .db-ns {
    display: block
  }

  .swagger-ui .dib-ns {
    display: inline-block
  }

  .swagger-ui .dit-ns {
    display: inline-table
  }

  .swagger-ui .dt-ns {
    display: table
  }

  .swagger-ui .dtc-ns {
    display: table-cell
  }

  .swagger-ui .dt-row-ns {
    display: table-row
  }

  .swagger-ui .dt-row-group-ns {
    display: table-row-group
  }

  .swagger-ui .dt-column-ns {
    display: table-column
  }

  .swagger-ui .dt-column-group-ns {
    display: table-column-group
  }

  .swagger-ui .dt--fixed-ns {
    table-layout: fixed;
    width: 100%
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .dn-m {
    display: none
  }

  .swagger-ui .di-m {
    display: inline
  }

  .swagger-ui .db-m {
    display: block
  }

  .swagger-ui .dib-m {
    display: inline-block
  }

  .swagger-ui .dit-m {
    display: inline-table
  }

  .swagger-ui .dt-m {
    display: table
  }

  .swagger-ui .dtc-m {
    display: table-cell
  }

  .swagger-ui .dt-row-m {
    display: table-row
  }

  .swagger-ui .dt-row-group-m {
    display: table-row-group
  }

  .swagger-ui .dt-column-m {
    display: table-column
  }

  .swagger-ui .dt-column-group-m {
    display: table-column-group
  }

  .swagger-ui .dt--fixed-m {
    table-layout: fixed;
    width: 100%
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .dn-l {
    display: none
  }

  .swagger-ui .di-l {
    display: inline
  }

  .swagger-ui .db-l {
    display: block
  }

  .swagger-ui .dib-l {
    display: inline-block
  }

  .swagger-ui .dit-l {
    display: inline-table
  }

  .swagger-ui .dt-l {
    display: table
  }

  .swagger-ui .dtc-l {
    display: table-cell
  }

  .swagger-ui .dt-row-l {
    display: table-row
  }

  .swagger-ui .dt-row-group-l {
    display: table-row-group
  }

  .swagger-ui .dt-column-l {
    display: table-column
  }

  .swagger-ui .dt-column-group-l {
    display: table-column-group
  }

  .swagger-ui .dt--fixed-l {
    table-layout: fixed;
    width: 100%
  }
}

.swagger-ui .flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex
}

.swagger-ui .inline-flex {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex
}

.swagger-ui .flex-auto {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  min-width: 0;
  min-height: 0
}

.swagger-ui .flex-none {
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none
}

.swagger-ui .flex-column {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column
}

.swagger-ui .flex-row {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row
}

.swagger-ui .flex-wrap {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap
}

.swagger-ui .flex-nowrap {
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap
}

.swagger-ui .flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse;
  flex-wrap: wrap-reverse
}

.swagger-ui .flex-column-reverse {
  -webkit-box-orient: vertical;
  -webkit-box-direction: reverse;
  -ms-flex-direction: column-reverse;
  flex-direction: column-reverse
}

.swagger-ui .flex-row-reverse {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse
}

.swagger-ui .items-start {
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start
}

.swagger-ui .items-end {
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end
}

.swagger-ui .items-center {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.swagger-ui .items-baseline {
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline
}

.swagger-ui .items-stretch {
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch
}

.swagger-ui .self-start {
  -ms-flex-item-align: start;
  align-self: flex-start
}

.swagger-ui .self-end {
  -ms-flex-item-align: end;
  align-self: flex-end
}

.swagger-ui .self-center {
  -ms-flex-item-align: center;
  align-self: center
}

.swagger-ui .self-baseline {
  -ms-flex-item-align: baseline;
  align-self: baseline
}

.swagger-ui .self-stretch {
  -ms-flex-item-align: stretch;
  align-self: stretch
}

.swagger-ui .justify-start {
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start
}

.swagger-ui .justify-end {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end
}

.swagger-ui .justify-center {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center
}

.swagger-ui .justify-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between
}

.swagger-ui .justify-around {
  -ms-flex-pack: distribute;
  justify-content: space-around
}

.swagger-ui .content-start {
  -ms-flex-line-pack: start;
  align-content: flex-start
}

.swagger-ui .content-end {
  -ms-flex-line-pack: end;
  align-content: flex-end
}

.swagger-ui .content-center {
  -ms-flex-line-pack: center;
  align-content: center
}

.swagger-ui .content-between {
  -ms-flex-line-pack: justify;
  align-content: space-between
}

.swagger-ui .content-around {
  -ms-flex-line-pack: distribute;
  align-content: space-around
}

.swagger-ui .content-stretch {
  -ms-flex-line-pack: stretch;
  align-content: stretch
}

.swagger-ui .order-0 {
  -webkit-box-ordinal-group: 1;
  -ms-flex-order: 0;
  order: 0
}

.swagger-ui .order-1 {
  -webkit-box-ordinal-group: 2;
  -ms-flex-order: 1;
  order: 1
}

.swagger-ui .order-2 {
  -webkit-box-ordinal-group: 3;
  -ms-flex-order: 2;
  order: 2
}

.swagger-ui .order-3 {
  -webkit-box-ordinal-group: 4;
  -ms-flex-order: 3;
  order: 3
}

.swagger-ui .order-4 {
  -webkit-box-ordinal-group: 5;
  -ms-flex-order: 4;
  order: 4
}

.swagger-ui .order-5 {
  -webkit-box-ordinal-group: 6;
  -ms-flex-order: 5;
  order: 5
}

.swagger-ui .order-6 {
  -webkit-box-ordinal-group: 7;
  -ms-flex-order: 6;
  order: 6
}

.swagger-ui .order-7 {
  -webkit-box-ordinal-group: 8;
  -ms-flex-order: 7;
  order: 7
}

.swagger-ui .order-8 {
  -webkit-box-ordinal-group: 9;
  -ms-flex-order: 8;
  order: 8
}

.swagger-ui .order-last {
  -webkit-box-ordinal-group: 100000;
  -ms-flex-order: 99999;
  order: 99999
}

.swagger-ui .flex-grow-0 {
  -webkit-box-flex: 0;
  -ms-flex-positive: 0;
  flex-grow: 0
}

.swagger-ui .flex-grow-1 {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1
}

.swagger-ui .flex-shrink-0 {
  -ms-flex-negative: 0;
  flex-shrink: 0
}

.swagger-ui .flex-shrink-1 {
  -ms-flex-negative: 1;
  flex-shrink: 1
}

@media screen and (min-width: 30em) {
  .swagger-ui .flex-ns {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
  }

  .swagger-ui .inline-flex-ns {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex
  }

  .swagger-ui .flex-auto-ns {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    min-width: 0;
    min-height: 0
  }

  .swagger-ui .flex-none-ns {
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none
  }

  .swagger-ui .flex-column-ns {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
  }

  .swagger-ui .flex-row-ns {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
  }

  .swagger-ui .flex-wrap-ns {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
  }

  .swagger-ui .flex-nowrap-ns {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap
  }

  .swagger-ui .flex-wrap-reverse-ns {
    -ms-flex-wrap: wrap-reverse;
    flex-wrap: wrap-reverse
  }

  .swagger-ui .flex-column-reverse-ns {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse
  }

  .swagger-ui .flex-row-reverse-ns {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse
  }

  .swagger-ui .items-start-ns {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
  }

  .swagger-ui .items-end-ns {
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end
  }

  .swagger-ui .items-center-ns {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
  }

  .swagger-ui .items-baseline-ns {
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline
  }

  .swagger-ui .items-stretch-ns {
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch
  }

  .swagger-ui .self-start-ns {
    -ms-flex-item-align: start;
    align-self: flex-start
  }

  .swagger-ui .self-end-ns {
    -ms-flex-item-align: end;
    align-self: flex-end
  }

  .swagger-ui .self-center-ns {
    -ms-flex-item-align: center;
    align-self: center
  }

  .swagger-ui .self-baseline-ns {
    -ms-flex-item-align: baseline;
    align-self: baseline
  }

  .swagger-ui .self-stretch-ns {
    -ms-flex-item-align: stretch;
    align-self: stretch
  }

  .swagger-ui .justify-start-ns {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
  }

  .swagger-ui .justify-end-ns {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
  }

  .swagger-ui .justify-center-ns {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
  }

  .swagger-ui .justify-between-ns {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
  }

  .swagger-ui .justify-around-ns {
    -ms-flex-pack: distribute;
    justify-content: space-around
  }

  .swagger-ui .content-start-ns {
    -ms-flex-line-pack: start;
    align-content: flex-start
  }

  .swagger-ui .content-end-ns {
    -ms-flex-line-pack: end;
    align-content: flex-end
  }

  .swagger-ui .content-center-ns {
    -ms-flex-line-pack: center;
    align-content: center
  }

  .swagger-ui .content-between-ns {
    -ms-flex-line-pack: justify;
    align-content: space-between
  }

  .swagger-ui .content-around-ns {
    -ms-flex-line-pack: distribute;
    align-content: space-around
  }

  .swagger-ui .content-stretch-ns {
    -ms-flex-line-pack: stretch;
    align-content: stretch
  }

  .swagger-ui .order-0-ns {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
  }

  .swagger-ui .order-1-ns {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
  }

  .swagger-ui .order-2-ns {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
  }

  .swagger-ui .order-3-ns {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
  }

  .swagger-ui .order-4-ns {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4
  }

  .swagger-ui .order-5-ns {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5
  }

  .swagger-ui .order-6-ns {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6
  }

  .swagger-ui .order-7-ns {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7
  }

  .swagger-ui .order-8-ns {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8
  }

  .swagger-ui .order-last-ns {
    -webkit-box-ordinal-group: 100000;
    -ms-flex-order: 99999;
    order: 99999
  }

  .swagger-ui .flex-grow-0-ns {
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0
  }

  .swagger-ui .flex-grow-1-ns {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1
  }

  .swagger-ui .flex-shrink-0-ns {
    -ms-flex-negative: 0;
    flex-shrink: 0
  }

  .swagger-ui .flex-shrink-1-ns {
    -ms-flex-negative: 1;
    flex-shrink: 1
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .flex-m {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
  }

  .swagger-ui .inline-flex-m {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex
  }

  .swagger-ui .flex-auto-m {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    min-width: 0;
    min-height: 0
  }

  .swagger-ui .flex-none-m {
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none
  }

  .swagger-ui .flex-column-m {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
  }

  .swagger-ui .flex-row-m {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
  }

  .swagger-ui .flex-wrap-m {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
  }

  .swagger-ui .flex-nowrap-m {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap
  }

  .swagger-ui .flex-wrap-reverse-m {
    -ms-flex-wrap: wrap-reverse;
    flex-wrap: wrap-reverse
  }

  .swagger-ui .flex-column-reverse-m {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse
  }

  .swagger-ui .flex-row-reverse-m {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse
  }

  .swagger-ui .items-start-m {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
  }

  .swagger-ui .items-end-m {
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end
  }

  .swagger-ui .items-center-m {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
  }

  .swagger-ui .items-baseline-m {
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline
  }

  .swagger-ui .items-stretch-m {
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch
  }

  .swagger-ui .self-start-m {
    -ms-flex-item-align: start;
    align-self: flex-start
  }

  .swagger-ui .self-end-m {
    -ms-flex-item-align: end;
    align-self: flex-end
  }

  .swagger-ui .self-center-m {
    -ms-flex-item-align: center;
    align-self: center
  }

  .swagger-ui .self-baseline-m {
    -ms-flex-item-align: baseline;
    align-self: baseline
  }

  .swagger-ui .self-stretch-m {
    -ms-flex-item-align: stretch;
    align-self: stretch
  }

  .swagger-ui .justify-start-m {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
  }

  .swagger-ui .justify-end-m {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
  }

  .swagger-ui .justify-center-m {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
  }

  .swagger-ui .justify-between-m {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
  }

  .swagger-ui .justify-around-m {
    -ms-flex-pack: distribute;
    justify-content: space-around
  }

  .swagger-ui .content-start-m {
    -ms-flex-line-pack: start;
    align-content: flex-start
  }

  .swagger-ui .content-end-m {
    -ms-flex-line-pack: end;
    align-content: flex-end
  }

  .swagger-ui .content-center-m {
    -ms-flex-line-pack: center;
    align-content: center
  }

  .swagger-ui .content-between-m {
    -ms-flex-line-pack: justify;
    align-content: space-between
  }

  .swagger-ui .content-around-m {
    -ms-flex-line-pack: distribute;
    align-content: space-around
  }

  .swagger-ui .content-stretch-m {
    -ms-flex-line-pack: stretch;
    align-content: stretch
  }

  .swagger-ui .order-0-m {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
  }

  .swagger-ui .order-1-m {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
  }

  .swagger-ui .order-2-m {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
  }

  .swagger-ui .order-3-m {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
  }

  .swagger-ui .order-4-m {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4
  }

  .swagger-ui .order-5-m {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5
  }

  .swagger-ui .order-6-m {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6
  }

  .swagger-ui .order-7-m {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7
  }

  .swagger-ui .order-8-m {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8
  }

  .swagger-ui .order-last-m {
    -webkit-box-ordinal-group: 100000;
    -ms-flex-order: 99999;
    order: 99999
  }

  .swagger-ui .flex-grow-0-m {
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0
  }

  .swagger-ui .flex-grow-1-m {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1
  }

  .swagger-ui .flex-shrink-0-m {
    -ms-flex-negative: 0;
    flex-shrink: 0
  }

  .swagger-ui .flex-shrink-1-m {
    -ms-flex-negative: 1;
    flex-shrink: 1
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .flex-l {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
  }

  .swagger-ui .inline-flex-l {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex
  }

  .swagger-ui .flex-auto-l {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    min-width: 0;
    min-height: 0
  }

  .swagger-ui .flex-none-l {
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none
  }

  .swagger-ui .flex-column-l {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
  }

  .swagger-ui .flex-row-l {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
  }

  .swagger-ui .flex-wrap-l {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
  }

  .swagger-ui .flex-nowrap-l {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap
  }

  .swagger-ui .flex-wrap-reverse-l {
    -ms-flex-wrap: wrap-reverse;
    flex-wrap: wrap-reverse
  }

  .swagger-ui .flex-column-reverse-l {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse
  }

  .swagger-ui .flex-row-reverse-l {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse
  }

  .swagger-ui .items-start-l {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
  }

  .swagger-ui .items-end-l {
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end
  }

  .swagger-ui .items-center-l {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
  }

  .swagger-ui .items-baseline-l {
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline
  }

  .swagger-ui .items-stretch-l {
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch
  }

  .swagger-ui .self-start-l {
    -ms-flex-item-align: start;
    align-self: flex-start
  }

  .swagger-ui .self-end-l {
    -ms-flex-item-align: end;
    align-self: flex-end
  }

  .swagger-ui .self-center-l {
    -ms-flex-item-align: center;
    align-self: center
  }

  .swagger-ui .self-baseline-l {
    -ms-flex-item-align: baseline;
    align-self: baseline
  }

  .swagger-ui .self-stretch-l {
    -ms-flex-item-align: stretch;
    align-self: stretch
  }

  .swagger-ui .justify-start-l {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
  }

  .swagger-ui .justify-end-l {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
  }

  .swagger-ui .justify-center-l {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
  }

  .swagger-ui .justify-between-l {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
  }

  .swagger-ui .justify-around-l {
    -ms-flex-pack: distribute;
    justify-content: space-around
  }

  .swagger-ui .content-start-l {
    -ms-flex-line-pack: start;
    align-content: flex-start
  }

  .swagger-ui .content-end-l {
    -ms-flex-line-pack: end;
    align-content: flex-end
  }

  .swagger-ui .content-center-l {
    -ms-flex-line-pack: center;
    align-content: center
  }

  .swagger-ui .content-between-l {
    -ms-flex-line-pack: justify;
    align-content: space-between
  }

  .swagger-ui .content-around-l {
    -ms-flex-line-pack: distribute;
    align-content: space-around
  }

  .swagger-ui .content-stretch-l {
    -ms-flex-line-pack: stretch;
    align-content: stretch
  }

  .swagger-ui .order-0-l {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
  }

  .swagger-ui .order-1-l {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
  }

  .swagger-ui .order-2-l {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
  }

  .swagger-ui .order-3-l {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
  }

  .swagger-ui .order-4-l {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4
  }

  .swagger-ui .order-5-l {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5
  }

  .swagger-ui .order-6-l {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6
  }

  .swagger-ui .order-7-l {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7
  }

  .swagger-ui .order-8-l {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8
  }

  .swagger-ui .order-last-l {
    -webkit-box-ordinal-group: 100000;
    -ms-flex-order: 99999;
    order: 99999
  }

  .swagger-ui .flex-grow-0-l {
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0
  }

  .swagger-ui .flex-grow-1-l {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1
  }

  .swagger-ui .flex-shrink-0-l {
    -ms-flex-negative: 0;
    flex-shrink: 0
  }

  .swagger-ui .flex-shrink-1-l {
    -ms-flex-negative: 1;
    flex-shrink: 1
  }
}

.swagger-ui .fl {
  float: left;
  _display: inline
}

.swagger-ui .fr {
  float: right;
  _display: inline
}

.swagger-ui .fn {
  float: none
}

@media screen and (min-width: 30em) {
  .swagger-ui .fl-ns {
    float: left;
    _display: inline
  }

  .swagger-ui .fr-ns {
    float: right;
    _display: inline
  }

  .swagger-ui .fn-ns {
    float: none
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .fl-m {
    float: left;
    _display: inline
  }

  .swagger-ui .fr-m {
    float: right;
    _display: inline
  }

  .swagger-ui .fn-m {
    float: none
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .fl-l {
    float: left;
    _display: inline
  }

  .swagger-ui .fr-l {
    float: right;
    _display: inline
  }

  .swagger-ui .fn-l {
    float: none
  }
}

.swagger-ui .sans-serif {
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, helvetica, helvetica neue, ubuntu, roboto, noto, segoe ui, arial, sans-serif
}

.swagger-ui .serif {
  font-family: georgia, serif
}

.swagger-ui .system-sans-serif {
  font-family: sans-serif
}

.swagger-ui .system-serif {
  font-family: serif
}

.swagger-ui .code, .swagger-ui code {
  font-family: Consolas, monaco, monospace
}

.swagger-ui .courier {
  font-family: Courier Next, courier, monospace
}

.swagger-ui .helvetica {
  font-family: helvetica neue, helvetica, sans-serif
}

.swagger-ui .avenir {
  font-family: avenir next, avenir, sans-serif
}

.swagger-ui .athelas {
  font-family: athelas, georgia, serif
}

.swagger-ui .georgia {
  font-family: georgia, serif
}

.swagger-ui .times {
  font-family: times, serif
}

.swagger-ui .bodoni {
  font-family: Bodoni MT, serif
}

.swagger-ui .calisto {
  font-family: Calisto MT, serif
}

.swagger-ui .garamond {
  font-family: garamond, serif
}

.swagger-ui .baskerville {
  font-family: baskerville, serif
}

.swagger-ui .i {
  font-style: italic
}

.swagger-ui .fs-normal {
  font-style: normal
}

@media screen and (min-width: 30em) {
  .swagger-ui .i-ns {
    font-style: italic
  }

  .swagger-ui .fs-normal-ns {
    font-style: normal
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .i-m {
    font-style: italic
  }

  .swagger-ui .fs-normal-m {
    font-style: normal
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .i-l {
    font-style: italic
  }

  .swagger-ui .fs-normal-l {
    font-style: normal
  }
}

.swagger-ui .normal {
  font-weight: 400
}

.swagger-ui .b {
  font-weight: 700
}

.swagger-ui .fw1 {
  font-weight: 100
}

.swagger-ui .fw2 {
  font-weight: 200
}

.swagger-ui .fw3 {
  font-weight: 300
}

.swagger-ui .fw4 {
  font-weight: 400
}

.swagger-ui .fw5 {
  font-weight: 500
}

.swagger-ui .fw6 {
  font-weight: 600
}

.swagger-ui .fw7 {
  font-weight: 700
}

.swagger-ui .fw8 {
  font-weight: 800
}

.swagger-ui .fw9 {
  font-weight: 900
}

@media screen and (min-width: 30em) {
  .swagger-ui .normal-ns {
    font-weight: 400
  }

  .swagger-ui .b-ns {
    font-weight: 700
  }

  .swagger-ui .fw1-ns {
    font-weight: 100
  }

  .swagger-ui .fw2-ns {
    font-weight: 200
  }

  .swagger-ui .fw3-ns {
    font-weight: 300
  }

  .swagger-ui .fw4-ns {
    font-weight: 400
  }

  .swagger-ui .fw5-ns {
    font-weight: 500
  }

  .swagger-ui .fw6-ns {
    font-weight: 600
  }

  .swagger-ui .fw7-ns {
    font-weight: 700
  }

  .swagger-ui .fw8-ns {
    font-weight: 800
  }

  .swagger-ui .fw9-ns {
    font-weight: 900
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .normal-m {
    font-weight: 400
  }

  .swagger-ui .b-m {
    font-weight: 700
  }

  .swagger-ui .fw1-m {
    font-weight: 100
  }

  .swagger-ui .fw2-m {
    font-weight: 200
  }

  .swagger-ui .fw3-m {
    font-weight: 300
  }

  .swagger-ui .fw4-m {
    font-weight: 400
  }

  .swagger-ui .fw5-m {
    font-weight: 500
  }

  .swagger-ui .fw6-m {
    font-weight: 600
  }

  .swagger-ui .fw7-m {
    font-weight: 700
  }

  .swagger-ui .fw8-m {
    font-weight: 800
  }

  .swagger-ui .fw9-m {
    font-weight: 900
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .normal-l {
    font-weight: 400
  }

  .swagger-ui .b-l {
    font-weight: 700
  }

  .swagger-ui .fw1-l {
    font-weight: 100
  }

  .swagger-ui .fw2-l {
    font-weight: 200
  }

  .swagger-ui .fw3-l {
    font-weight: 300
  }

  .swagger-ui .fw4-l {
    font-weight: 400
  }

  .swagger-ui .fw5-l {
    font-weight: 500
  }

  .swagger-ui .fw6-l {
    font-weight: 600
  }

  .swagger-ui .fw7-l {
    font-weight: 700
  }

  .swagger-ui .fw8-l {
    font-weight: 800
  }

  .swagger-ui .fw9-l {
    font-weight: 900
  }
}

.swagger-ui .input-reset {
  -webkit-appearance: none;
  -moz-appearance: none
}

.swagger-ui .button-reset::-moz-focus-inner, .swagger-ui .input-reset::-moz-focus-inner {
  border: 0;
  padding: 0
}

.swagger-ui .h1 {
  height: 1rem
}

.swagger-ui .h2 {
  height: 2rem
}

.swagger-ui .h3 {
  height: 4rem
}

.swagger-ui .h4 {
  height: 8rem
}

.swagger-ui .h5 {
  height: 16rem
}

.swagger-ui .h-25 {
  height: 25%
}

.swagger-ui .h-50 {
  height: 50%
}

.swagger-ui .h-75 {
  height: 75%
}

.swagger-ui .h-100 {
  height: 100%
}

.swagger-ui .min-h-100 {
  min-height: 100%
}

.swagger-ui .vh-25 {
  height: 25vh
}

.swagger-ui .vh-50 {
  height: 50vh
}

.swagger-ui .vh-75 {
  height: 75vh
}

.swagger-ui .vh-100 {
  height: 100vh
}

.swagger-ui .min-vh-100 {
  min-height: 100vh
}

.swagger-ui .h-auto {
  height: auto
}

.swagger-ui .h-inherit {
  height: inherit
}

@media screen and (min-width: 30em) {
  .swagger-ui .h1-ns {
    height: 1rem
  }

  .swagger-ui .h2-ns {
    height: 2rem
  }

  .swagger-ui .h3-ns {
    height: 4rem
  }

  .swagger-ui .h4-ns {
    height: 8rem
  }

  .swagger-ui .h5-ns {
    height: 16rem
  }

  .swagger-ui .h-25-ns {
    height: 25%
  }

  .swagger-ui .h-50-ns {
    height: 50%
  }

  .swagger-ui .h-75-ns {
    height: 75%
  }

  .swagger-ui .h-100-ns {
    height: 100%
  }

  .swagger-ui .min-h-100-ns {
    min-height: 100%
  }

  .swagger-ui .vh-25-ns {
    height: 25vh
  }

  .swagger-ui .vh-50-ns {
    height: 50vh
  }

  .swagger-ui .vh-75-ns {
    height: 75vh
  }

  .swagger-ui .vh-100-ns {
    height: 100vh
  }

  .swagger-ui .min-vh-100-ns {
    min-height: 100vh
  }

  .swagger-ui .h-auto-ns {
    height: auto
  }

  .swagger-ui .h-inherit-ns {
    height: inherit
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .h1-m {
    height: 1rem
  }

  .swagger-ui .h2-m {
    height: 2rem
  }

  .swagger-ui .h3-m {
    height: 4rem
  }

  .swagger-ui .h4-m {
    height: 8rem
  }

  .swagger-ui .h5-m {
    height: 16rem
  }

  .swagger-ui .h-25-m {
    height: 25%
  }

  .swagger-ui .h-50-m {
    height: 50%
  }

  .swagger-ui .h-75-m {
    height: 75%
  }

  .swagger-ui .h-100-m {
    height: 100%
  }

  .swagger-ui .min-h-100-m {
    min-height: 100%
  }

  .swagger-ui .vh-25-m {
    height: 25vh
  }

  .swagger-ui .vh-50-m {
    height: 50vh
  }

  .swagger-ui .vh-75-m {
    height: 75vh
  }

  .swagger-ui .vh-100-m {
    height: 100vh
  }

  .swagger-ui .min-vh-100-m {
    min-height: 100vh
  }

  .swagger-ui .h-auto-m {
    height: auto
  }

  .swagger-ui .h-inherit-m {
    height: inherit
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .h1-l {
    height: 1rem
  }

  .swagger-ui .h2-l {
    height: 2rem
  }

  .swagger-ui .h3-l {
    height: 4rem
  }

  .swagger-ui .h4-l {
    height: 8rem
  }

  .swagger-ui .h5-l {
    height: 16rem
  }

  .swagger-ui .h-25-l {
    height: 25%
  }

  .swagger-ui .h-50-l {
    height: 50%
  }

  .swagger-ui .h-75-l {
    height: 75%
  }

  .swagger-ui .h-100-l {
    height: 100%
  }

  .swagger-ui .min-h-100-l {
    min-height: 100%
  }

  .swagger-ui .vh-25-l {
    height: 25vh
  }

  .swagger-ui .vh-50-l {
    height: 50vh
  }

  .swagger-ui .vh-75-l {
    height: 75vh
  }

  .swagger-ui .vh-100-l {
    height: 100vh
  }

  .swagger-ui .min-vh-100-l {
    min-height: 100vh
  }

  .swagger-ui .h-auto-l {
    height: auto
  }

  .swagger-ui .h-inherit-l {
    height: inherit
  }
}

.swagger-ui .tracked {
  letter-spacing: .1em
}

.swagger-ui .tracked-tight {
  letter-spacing: -.05em
}

.swagger-ui .tracked-mega {
  letter-spacing: .25em
}

@media screen and (min-width: 30em) {
  .swagger-ui .tracked-ns {
    letter-spacing: .1em
  }

  .swagger-ui .tracked-tight-ns {
    letter-spacing: -.05em
  }

  .swagger-ui .tracked-mega-ns {
    letter-spacing: .25em
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .tracked-m {
    letter-spacing: .1em
  }

  .swagger-ui .tracked-tight-m {
    letter-spacing: -.05em
  }

  .swagger-ui .tracked-mega-m {
    letter-spacing: .25em
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .tracked-l {
    letter-spacing: .1em
  }

  .swagger-ui .tracked-tight-l {
    letter-spacing: -.05em
  }

  .swagger-ui .tracked-mega-l {
    letter-spacing: .25em
  }
}

.swagger-ui .lh-solid {
  line-height: 1
}

.swagger-ui .lh-title {
  line-height: 1.25
}

.swagger-ui .lh-copy {
  line-height: 1.5
}

@media screen and (min-width: 30em) {
  .swagger-ui .lh-solid-ns {
    line-height: 1
  }

  .swagger-ui .lh-title-ns {
    line-height: 1.25
  }

  .swagger-ui .lh-copy-ns {
    line-height: 1.5
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .lh-solid-m {
    line-height: 1
  }

  .swagger-ui .lh-title-m {
    line-height: 1.25
  }

  .swagger-ui .lh-copy-m {
    line-height: 1.5
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .lh-solid-l {
    line-height: 1
  }

  .swagger-ui .lh-title-l {
    line-height: 1.25
  }

  .swagger-ui .lh-copy-l {
    line-height: 1.5
  }
}

.swagger-ui .link {
  text-decoration: none
}

.swagger-ui .link, .swagger-ui .link:active, .swagger-ui .link:focus, .swagger-ui .link:hover, .swagger-ui .link:link, .swagger-ui .link:visited {
  -webkit-transition: color .15s ease-in;
  transition: color .15s ease-in
}

.swagger-ui .link:focus {
  outline: 1px dotted currentColor
}

.swagger-ui .list {
  list-style-type: none
}

.swagger-ui .mw-100 {
  max-width: 100%
}

.swagger-ui .mw1 {
  max-width: 1rem
}

.swagger-ui .mw2 {
  max-width: 2rem
}

.swagger-ui .mw3 {
  max-width: 4rem
}

.swagger-ui .mw4 {
  max-width: 8rem
}

.swagger-ui .mw5 {
  max-width: 16rem
}

.swagger-ui .mw6 {
  max-width: 32rem
}

.swagger-ui .mw7 {
  max-width: 48rem
}

.swagger-ui .mw8 {
  max-width: 64rem
}

.swagger-ui .mw9 {
  max-width: 96rem
}

.swagger-ui .mw-none {
  max-width: none
}

@media screen and (min-width: 30em) {
  .swagger-ui .mw-100-ns {
    max-width: 100%
  }

  .swagger-ui .mw1-ns {
    max-width: 1rem
  }

  .swagger-ui .mw2-ns {
    max-width: 2rem
  }

  .swagger-ui .mw3-ns {
    max-width: 4rem
  }

  .swagger-ui .mw4-ns {
    max-width: 8rem
  }

  .swagger-ui .mw5-ns {
    max-width: 16rem
  }

  .swagger-ui .mw6-ns {
    max-width: 32rem
  }

  .swagger-ui .mw7-ns {
    max-width: 48rem
  }

  .swagger-ui .mw8-ns {
    max-width: 64rem
  }

  .swagger-ui .mw9-ns {
    max-width: 96rem
  }

  .swagger-ui .mw-none-ns {
    max-width: none
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .mw-100-m {
    max-width: 100%
  }

  .swagger-ui .mw1-m {
    max-width: 1rem
  }

  .swagger-ui .mw2-m {
    max-width: 2rem
  }

  .swagger-ui .mw3-m {
    max-width: 4rem
  }

  .swagger-ui .mw4-m {
    max-width: 8rem
  }

  .swagger-ui .mw5-m {
    max-width: 16rem
  }

  .swagger-ui .mw6-m {
    max-width: 32rem
  }

  .swagger-ui .mw7-m {
    max-width: 48rem
  }

  .swagger-ui .mw8-m {
    max-width: 64rem
  }

  .swagger-ui .mw9-m {
    max-width: 96rem
  }

  .swagger-ui .mw-none-m {
    max-width: none
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .mw-100-l {
    max-width: 100%
  }

  .swagger-ui .mw1-l {
    max-width: 1rem
  }

  .swagger-ui .mw2-l {
    max-width: 2rem
  }

  .swagger-ui .mw3-l {
    max-width: 4rem
  }

  .swagger-ui .mw4-l {
    max-width: 8rem
  }

  .swagger-ui .mw5-l {
    max-width: 16rem
  }

  .swagger-ui .mw6-l {
    max-width: 32rem
  }

  .swagger-ui .mw7-l {
    max-width: 48rem
  }

  .swagger-ui .mw8-l {
    max-width: 64rem
  }

  .swagger-ui .mw9-l {
    max-width: 96rem
  }

  .swagger-ui .mw-none-l {
    max-width: none
  }
}

.swagger-ui .w1 {
  width: 1rem
}

.swagger-ui .w2 {
  width: 2rem
}

.swagger-ui .w3 {
  width: 4rem
}

.swagger-ui .w4 {
  width: 8rem
}

.swagger-ui .w5 {
  width: 16rem
}

.swagger-ui .w-10 {
  width: 10%
}

.swagger-ui .w-20 {
  width: 20%
}

.swagger-ui .w-25 {
  width: 25%
}

.swagger-ui .w-30 {
  width: 30%
}

.swagger-ui .w-33 {
  width: 33%
}

.swagger-ui .w-34 {
  width: 34%
}

.swagger-ui .w-40 {
  width: 40%
}

.swagger-ui .w-50 {
  width: 50%
}

.swagger-ui .w-60 {
  width: 60%
}

.swagger-ui .w-70 {
  width: 70%
}

.swagger-ui .w-75 {
  width: 75%
}

.swagger-ui .w-80 {
  width: 80%
}

.swagger-ui .w-90 {
  width: 90%
}

.swagger-ui .w-100 {
  width: 100%
}

.swagger-ui .w-third {
  width: 33.33333%
}

.swagger-ui .w-two-thirds {
  width: 66.66667%
}

.swagger-ui .w-auto {
  width: auto
}

@media screen and (min-width: 30em) {
  .swagger-ui .w1-ns {
    width: 1rem
  }

  .swagger-ui .w2-ns {
    width: 2rem
  }

  .swagger-ui .w3-ns {
    width: 4rem
  }

  .swagger-ui .w4-ns {
    width: 8rem
  }

  .swagger-ui .w5-ns {
    width: 16rem
  }

  .swagger-ui .w-10-ns {
    width: 10%
  }

  .swagger-ui .w-20-ns {
    width: 20%
  }

  .swagger-ui .w-25-ns {
    width: 25%
  }

  .swagger-ui .w-30-ns {
    width: 30%
  }

  .swagger-ui .w-33-ns {
    width: 33%
  }

  .swagger-ui .w-34-ns {
    width: 34%
  }

  .swagger-ui .w-40-ns {
    width: 40%
  }

  .swagger-ui .w-50-ns {
    width: 50%
  }

  .swagger-ui .w-60-ns {
    width: 60%
  }

  .swagger-ui .w-70-ns {
    width: 70%
  }

  .swagger-ui .w-75-ns {
    width: 75%
  }

  .swagger-ui .w-80-ns {
    width: 80%
  }

  .swagger-ui .w-90-ns {
    width: 90%
  }

  .swagger-ui .w-100-ns {
    width: 100%
  }

  .swagger-ui .w-third-ns {
    width: 33.33333%
  }

  .swagger-ui .w-two-thirds-ns {
    width: 66.66667%
  }

  .swagger-ui .w-auto-ns {
    width: auto
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .w1-m {
    width: 1rem
  }

  .swagger-ui .w2-m {
    width: 2rem
  }

  .swagger-ui .w3-m {
    width: 4rem
  }

  .swagger-ui .w4-m {
    width: 8rem
  }

  .swagger-ui .w5-m {
    width: 16rem
  }

  .swagger-ui .w-10-m {
    width: 10%
  }

  .swagger-ui .w-20-m {
    width: 20%
  }

  .swagger-ui .w-25-m {
    width: 25%
  }

  .swagger-ui .w-30-m {
    width: 30%
  }

  .swagger-ui .w-33-m {
    width: 33%
  }

  .swagger-ui .w-34-m {
    width: 34%
  }

  .swagger-ui .w-40-m {
    width: 40%
  }

  .swagger-ui .w-50-m {
    width: 50%
  }

  .swagger-ui .w-60-m {
    width: 60%
  }

  .swagger-ui .w-70-m {
    width: 70%
  }

  .swagger-ui .w-75-m {
    width: 75%
  }

  .swagger-ui .w-80-m {
    width: 80%
  }

  .swagger-ui .w-90-m {
    width: 90%
  }

  .swagger-ui .w-100-m {
    width: 100%
  }

  .swagger-ui .w-third-m {
    width: 33.33333%
  }

  .swagger-ui .w-two-thirds-m {
    width: 66.66667%
  }

  .swagger-ui .w-auto-m {
    width: auto
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .w1-l {
    width: 1rem
  }

  .swagger-ui .w2-l {
    width: 2rem
  }

  .swagger-ui .w3-l {
    width: 4rem
  }

  .swagger-ui .w4-l {
    width: 8rem
  }

  .swagger-ui .w5-l {
    width: 16rem
  }

  .swagger-ui .w-10-l {
    width: 10%
  }

  .swagger-ui .w-20-l {
    width: 20%
  }

  .swagger-ui .w-25-l {
    width: 25%
  }

  .swagger-ui .w-30-l {
    width: 30%
  }

  .swagger-ui .w-33-l {
    width: 33%
  }

  .swagger-ui .w-34-l {
    width: 34%
  }

  .swagger-ui .w-40-l {
    width: 40%
  }

  .swagger-ui .w-50-l {
    width: 50%
  }

  .swagger-ui .w-60-l {
    width: 60%
  }

  .swagger-ui .w-70-l {
    width: 70%
  }

  .swagger-ui .w-75-l {
    width: 75%
  }

  .swagger-ui .w-80-l {
    width: 80%
  }

  .swagger-ui .w-90-l {
    width: 90%
  }

  .swagger-ui .w-100-l {
    width: 100%
  }

  .swagger-ui .w-third-l {
    width: 33.33333%
  }

  .swagger-ui .w-two-thirds-l {
    width: 66.66667%
  }

  .swagger-ui .w-auto-l {
    width: auto
  }
}

.swagger-ui .overflow-visible {
  overflow: visible
}

.swagger-ui .overflow-hidden {
  overflow: hidden
}

.swagger-ui .overflow-scroll {
  overflow: scroll
}

.swagger-ui .overflow-auto {
  overflow: auto
}

.swagger-ui .overflow-x-visible {
  overflow-x: visible
}

.swagger-ui .overflow-x-hidden {
  overflow-x: hidden
}

.swagger-ui .overflow-x-scroll {
  overflow-x: scroll
}

.swagger-ui .overflow-x-auto {
  overflow-x: auto
}

.swagger-ui .overflow-y-visible {
  overflow-y: visible
}

.swagger-ui .overflow-y-hidden {
  overflow-y: hidden
}

.swagger-ui .overflow-y-scroll {
  overflow-y: scroll
}

.swagger-ui .overflow-y-auto {
  overflow-y: auto
}

@media screen and (min-width: 30em) {
  .swagger-ui .overflow-visible-ns {
    overflow: visible
  }

  .swagger-ui .overflow-hidden-ns {
    overflow: hidden
  }

  .swagger-ui .overflow-scroll-ns {
    overflow: scroll
  }

  .swagger-ui .overflow-auto-ns {
    overflow: auto
  }

  .swagger-ui .overflow-x-visible-ns {
    overflow-x: visible
  }

  .swagger-ui .overflow-x-hidden-ns {
    overflow-x: hidden
  }

  .swagger-ui .overflow-x-scroll-ns {
    overflow-x: scroll
  }

  .swagger-ui .overflow-x-auto-ns {
    overflow-x: auto
  }

  .swagger-ui .overflow-y-visible-ns {
    overflow-y: visible
  }

  .swagger-ui .overflow-y-hidden-ns {
    overflow-y: hidden
  }

  .swagger-ui .overflow-y-scroll-ns {
    overflow-y: scroll
  }

  .swagger-ui .overflow-y-auto-ns {
    overflow-y: auto
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .overflow-visible-m {
    overflow: visible
  }

  .swagger-ui .overflow-hidden-m {
    overflow: hidden
  }

  .swagger-ui .overflow-scroll-m {
    overflow: scroll
  }

  .swagger-ui .overflow-auto-m {
    overflow: auto
  }

  .swagger-ui .overflow-x-visible-m {
    overflow-x: visible
  }

  .swagger-ui .overflow-x-hidden-m {
    overflow-x: hidden
  }

  .swagger-ui .overflow-x-scroll-m {
    overflow-x: scroll
  }

  .swagger-ui .overflow-x-auto-m {
    overflow-x: auto
  }

  .swagger-ui .overflow-y-visible-m {
    overflow-y: visible
  }

  .swagger-ui .overflow-y-hidden-m {
    overflow-y: hidden
  }

  .swagger-ui .overflow-y-scroll-m {
    overflow-y: scroll
  }

  .swagger-ui .overflow-y-auto-m {
    overflow-y: auto
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .overflow-visible-l {
    overflow: visible
  }

  .swagger-ui .overflow-hidden-l {
    overflow: hidden
  }

  .swagger-ui .overflow-scroll-l {
    overflow: scroll
  }

  .swagger-ui .overflow-auto-l {
    overflow: auto
  }

  .swagger-ui .overflow-x-visible-l {
    overflow-x: visible
  }

  .swagger-ui .overflow-x-hidden-l {
    overflow-x: hidden
  }

  .swagger-ui .overflow-x-scroll-l {
    overflow-x: scroll
  }

  .swagger-ui .overflow-x-auto-l {
    overflow-x: auto
  }

  .swagger-ui .overflow-y-visible-l {
    overflow-y: visible
  }

  .swagger-ui .overflow-y-hidden-l {
    overflow-y: hidden
  }

  .swagger-ui .overflow-y-scroll-l {
    overflow-y: scroll
  }

  .swagger-ui .overflow-y-auto-l {
    overflow-y: auto
  }
}

.swagger-ui .static {
  position: static
}

.swagger-ui .relative {
  position: relative
}

.swagger-ui .absolute {
  position: absolute
}

.swagger-ui .fixed {
  position: fixed
}

@media screen and (min-width: 30em) {
  .swagger-ui .static-ns {
    position: static
  }

  .swagger-ui .relative-ns {
    position: relative
  }

  .swagger-ui .absolute-ns {
    position: absolute
  }

  .swagger-ui .fixed-ns {
    position: fixed
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .static-m {
    position: static
  }

  .swagger-ui .relative-m {
    position: relative
  }

  .swagger-ui .absolute-m {
    position: absolute
  }

  .swagger-ui .fixed-m {
    position: fixed
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .static-l {
    position: static
  }

  .swagger-ui .relative-l {
    position: relative
  }

  .swagger-ui .absolute-l {
    position: absolute
  }

  .swagger-ui .fixed-l {
    position: fixed
  }
}

.swagger-ui .o-100 {
  opacity: 1
}

.swagger-ui .o-90 {
  opacity: .9
}

.swagger-ui .o-80 {
  opacity: .8
}

.swagger-ui .o-70 {
  opacity: .7
}

.swagger-ui .o-60 {
  opacity: .6
}

.swagger-ui .o-50 {
  opacity: .5
}

.swagger-ui .o-40 {
  opacity: .4
}

.swagger-ui .o-30 {
  opacity: .3
}

.swagger-ui .o-20 {
  opacity: .2
}

.swagger-ui .o-10 {
  opacity: .1
}

.swagger-ui .o-05 {
  opacity: .05
}

.swagger-ui .o-025 {
  opacity: .025
}

.swagger-ui .o-0 {
  opacity: 0
}

.swagger-ui .rotate-45 {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg)
}

.swagger-ui .rotate-90 {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg)
}

.swagger-ui .rotate-135 {
  -webkit-transform: rotate(135deg);
  transform: rotate(135deg)
}

.swagger-ui .rotate-180 {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg)
}

.swagger-ui .rotate-225 {
  -webkit-transform: rotate(225deg);
  transform: rotate(225deg)
}

.swagger-ui .rotate-270 {
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg)
}

.swagger-ui .rotate-315 {
  -webkit-transform: rotate(315deg);
  transform: rotate(315deg)
}

@media screen and (min-width: 30em) {
  .swagger-ui .rotate-45-ns {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
  }

  .swagger-ui .rotate-90-ns {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
  }

  .swagger-ui .rotate-135-ns {
    -webkit-transform: rotate(135deg);
    transform: rotate(135deg)
  }

  .swagger-ui .rotate-180-ns {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
  }

  .swagger-ui .rotate-225-ns {
    -webkit-transform: rotate(225deg);
    transform: rotate(225deg)
  }

  .swagger-ui .rotate-270-ns {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg)
  }

  .swagger-ui .rotate-315-ns {
    -webkit-transform: rotate(315deg);
    transform: rotate(315deg)
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .rotate-45-m {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
  }

  .swagger-ui .rotate-90-m {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
  }

  .swagger-ui .rotate-135-m {
    -webkit-transform: rotate(135deg);
    transform: rotate(135deg)
  }

  .swagger-ui .rotate-180-m {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
  }

  .swagger-ui .rotate-225-m {
    -webkit-transform: rotate(225deg);
    transform: rotate(225deg)
  }

  .swagger-ui .rotate-270-m {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg)
  }

  .swagger-ui .rotate-315-m {
    -webkit-transform: rotate(315deg);
    transform: rotate(315deg)
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .rotate-45-l {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
  }

  .swagger-ui .rotate-90-l {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
  }

  .swagger-ui .rotate-135-l {
    -webkit-transform: rotate(135deg);
    transform: rotate(135deg)
  }

  .swagger-ui .rotate-180-l {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
  }

  .swagger-ui .rotate-225-l {
    -webkit-transform: rotate(225deg);
    transform: rotate(225deg)
  }

  .swagger-ui .rotate-270-l {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg)
  }

  .swagger-ui .rotate-315-l {
    -webkit-transform: rotate(315deg);
    transform: rotate(315deg)
  }
}

.swagger-ui .black-90 {
  color: rgba(0, 0, 0, .9)
}

.swagger-ui .black-80 {
  color: rgba(0, 0, 0, .8)
}

.swagger-ui .black-70 {
  color: rgba(0, 0, 0, .7)
}

.swagger-ui .black-60 {
  color: rgba(0, 0, 0, .6)
}

.swagger-ui .black-50 {
  color: rgba(0, 0, 0, .5)
}

.swagger-ui .black-40 {
  color: rgba(0, 0, 0, .4)
}

.swagger-ui .black-30 {
  color: rgba(0, 0, 0, .3)
}

.swagger-ui .black-20 {
  color: rgba(0, 0, 0, .2)
}

.swagger-ui .black-10 {
  color: rgba(0, 0, 0, .1)
}

.swagger-ui .black-05 {
  color: rgba(0, 0, 0, .05)
}

.swagger-ui .white-90 {
  color: hsla(0, 0%, 100%, .9)
}

.swagger-ui .white-80 {
  color: hsla(0, 0%, 100%, .8)
}

.swagger-ui .white-70 {
  color: hsla(0, 0%, 100%, .7)
}

.swagger-ui .white-60 {
  color: hsla(0, 0%, 100%, .6)
}

.swagger-ui .white-50 {
  color: hsla(0, 0%, 100%, .5)
}

.swagger-ui .white-40 {
  color: hsla(0, 0%, 100%, .4)
}

.swagger-ui .white-30 {
  color: hsla(0, 0%, 100%, .3)
}

.swagger-ui .white-20 {
  color: hsla(0, 0%, 100%, .2)
}

.swagger-ui .white-10 {
  color: hsla(0, 0%, 100%, .1)
}

.swagger-ui .black {
  color: #000
}

.swagger-ui .near-black {
  color: #111
}

.swagger-ui .dark-gray {
  color: #333
}

.swagger-ui .mid-gray {
  color: #555
}

.swagger-ui .gray {
  color: #777
}

.swagger-ui .silver {
  color: #999
}

.swagger-ui .light-silver {
  color: #aaa
}

.swagger-ui .moon-gray {
  color: #ccc
}

.swagger-ui .light-gray {
  color: #eee
}

.swagger-ui .near-white {
  color: #f4f4f4
}

.swagger-ui .white {
  color: #fff
}

.swagger-ui .dark-red {
  color: #e7040f
}

.swagger-ui .red {
  color: #ff4136
}

.swagger-ui .light-red {
  color: #ff725c
}

.swagger-ui .orange {
  color: #ff6300
}

.swagger-ui .gold {
  color: #ffb700
}

.swagger-ui .yellow {
  color: gold
}

.swagger-ui .light-yellow {
  color: #fbf1a9
}

.swagger-ui .purple {
  color: #5e2ca5
}

.swagger-ui .light-purple {
  color: #a463f2
}

.swagger-ui .dark-pink {
  color: #d5008f
}

.swagger-ui .hot-pink {
  color: #ff41b4
}

.swagger-ui .pink {
  color: #ff80cc
}

.swagger-ui .light-pink {
  color: #ffa3d7
}

.swagger-ui .dark-green {
  color: #137752
}

.swagger-ui .green {
  color: #19a974
}

.swagger-ui .light-green {
  color: #9eebcf
}

.swagger-ui .navy {
  color: #001b44
}

.swagger-ui .dark-blue {
  color: #00449e
}

.swagger-ui .blue {
  color: #357edd
}

.swagger-ui .light-blue {
  color: #96ccff
}

.swagger-ui .lightest-blue {
  color: #cdecff
}

.swagger-ui .washed-blue {
  color: #f6fffe
}

.swagger-ui .washed-green {
  color: #e8fdf5
}

.swagger-ui .washed-yellow {
  color: #fffceb
}

.swagger-ui .washed-red {
  color: #ffdfdf
}

.swagger-ui .color-inherit {
  color: inherit
}

.swagger-ui .bg-black-90 {
  background-color: rgba(0, 0, 0, .9)
}

.swagger-ui .bg-black-80 {
  background-color: rgba(0, 0, 0, .8)
}

.swagger-ui .bg-black-70 {
  background-color: rgba(0, 0, 0, .7)
}

.swagger-ui .bg-black-60 {
  background-color: rgba(0, 0, 0, .6)
}

.swagger-ui .bg-black-50 {
  background-color: rgba(0, 0, 0, .5)
}

.swagger-ui .bg-black-40 {
  background-color: rgba(0, 0, 0, .4)
}

.swagger-ui .bg-black-30 {
  background-color: rgba(0, 0, 0, .3)
}

.swagger-ui .bg-black-20 {
  background-color: rgba(0, 0, 0, .2)
}

.swagger-ui .bg-black-10 {
  background-color: rgba(0, 0, 0, .1)
}

.swagger-ui .bg-black-05 {
  background-color: rgba(0, 0, 0, .05)
}

.swagger-ui .bg-white-90 {
  background-color: hsla(0, 0%, 100%, .9)
}

.swagger-ui .bg-white-80 {
  background-color: hsla(0, 0%, 100%, .8)
}

.swagger-ui .bg-white-70 {
  background-color: hsla(0, 0%, 100%, .7)
}

.swagger-ui .bg-white-60 {
  background-color: hsla(0, 0%, 100%, .6)
}

.swagger-ui .bg-white-50 {
  background-color: hsla(0, 0%, 100%, .5)
}

.swagger-ui .bg-white-40 {
  background-color: hsla(0, 0%, 100%, .4)
}

.swagger-ui .bg-white-30 {
  background-color: hsla(0, 0%, 100%, .3)
}

.swagger-ui .bg-white-20 {
  background-color: hsla(0, 0%, 100%, .2)
}

.swagger-ui .bg-white-10 {
  background-color: hsla(0, 0%, 100%, .1)
}

.swagger-ui .bg-black {
  background-color: #000
}

.swagger-ui .bg-near-black {
  background-color: #111
}

.swagger-ui .bg-dark-gray {
  background-color: #333
}

.swagger-ui .bg-mid-gray {
  background-color: #555
}

.swagger-ui .bg-gray {
  background-color: #777
}

.swagger-ui .bg-silver {
  background-color: #999
}

.swagger-ui .bg-light-silver {
  background-color: #aaa
}

.swagger-ui .bg-moon-gray {
  background-color: #ccc
}

.swagger-ui .bg-light-gray {
  background-color: #eee
}

.swagger-ui .bg-near-white {
  background-color: #f4f4f4
}

.swagger-ui .bg-white {
  background-color: #fff
}

.swagger-ui .bg-transparent {
  background-color: transparent
}

.swagger-ui .bg-dark-red {
  background-color: #e7040f
}

.swagger-ui .bg-red {
  background-color: #ff4136
}

.swagger-ui .bg-light-red {
  background-color: #ff725c
}

.swagger-ui .bg-orange {
  background-color: #ff6300
}

.swagger-ui .bg-gold {
  background-color: #ffb700
}

.swagger-ui .bg-yellow {
  background-color: gold
}

.swagger-ui .bg-light-yellow {
  background-color: #fbf1a9
}

.swagger-ui .bg-purple {
  background-color: #5e2ca5
}

.swagger-ui .bg-light-purple {
  background-color: #a463f2
}

.swagger-ui .bg-dark-pink {
  background-color: #d5008f
}

.swagger-ui .bg-hot-pink {
  background-color: #ff41b4
}

.swagger-ui .bg-pink {
  background-color: #ff80cc
}

.swagger-ui .bg-light-pink {
  background-color: #ffa3d7
}

.swagger-ui .bg-dark-green {
  background-color: #137752
}

.swagger-ui .bg-green {
  background-color: #19a974
}

.swagger-ui .bg-light-green {
  background-color: #9eebcf
}

.swagger-ui .bg-navy {
  background-color: #001b44
}

.swagger-ui .bg-dark-blue {
  background-color: #00449e
}

.swagger-ui .bg-blue {
  background-color: #357edd
}

.swagger-ui .bg-light-blue {
  background-color: #96ccff
}

.swagger-ui .bg-lightest-blue {
  background-color: #cdecff
}

.swagger-ui .bg-washed-blue {
  background-color: #f6fffe
}

.swagger-ui .bg-washed-green {
  background-color: #e8fdf5
}

.swagger-ui .bg-washed-yellow {
  background-color: #fffceb
}

.swagger-ui .bg-washed-red {
  background-color: #ffdfdf
}

.swagger-ui .bg-inherit {
  background-color: inherit
}

.swagger-ui .hover-black:focus, .swagger-ui .hover-black:hover {
  color: #000
}

.swagger-ui .hover-near-black:focus, .swagger-ui .hover-near-black:hover {
  color: #111
}

.swagger-ui .hover-dark-gray:focus, .swagger-ui .hover-dark-gray:hover {
  color: #333
}

.swagger-ui .hover-mid-gray:focus, .swagger-ui .hover-mid-gray:hover {
  color: #555
}

.swagger-ui .hover-gray:focus, .swagger-ui .hover-gray:hover {
  color: #777
}

.swagger-ui .hover-silver:focus, .swagger-ui .hover-silver:hover {
  color: #999
}

.swagger-ui .hover-light-silver:focus, .swagger-ui .hover-light-silver:hover {
  color: #aaa
}

.swagger-ui .hover-moon-gray:focus, .swagger-ui .hover-moon-gray:hover {
  color: #ccc
}

.swagger-ui .hover-light-gray:focus, .swagger-ui .hover-light-gray:hover {
  color: #eee
}

.swagger-ui .hover-near-white:focus, .swagger-ui .hover-near-white:hover {
  color: #f4f4f4
}

.swagger-ui .hover-white:focus, .swagger-ui .hover-white:hover {
  color: #fff
}

.swagger-ui .hover-black-90:focus, .swagger-ui .hover-black-90:hover {
  color: rgba(0, 0, 0, .9)
}

.swagger-ui .hover-black-80:focus, .swagger-ui .hover-black-80:hover {
  color: rgba(0, 0, 0, .8)
}

.swagger-ui .hover-black-70:focus, .swagger-ui .hover-black-70:hover {
  color: rgba(0, 0, 0, .7)
}

.swagger-ui .hover-black-60:focus, .swagger-ui .hover-black-60:hover {
  color: rgba(0, 0, 0, .6)
}

.swagger-ui .hover-black-50:focus, .swagger-ui .hover-black-50:hover {
  color: rgba(0, 0, 0, .5)
}

.swagger-ui .hover-black-40:focus, .swagger-ui .hover-black-40:hover {
  color: rgba(0, 0, 0, .4)
}

.swagger-ui .hover-black-30:focus, .swagger-ui .hover-black-30:hover {
  color: rgba(0, 0, 0, .3)
}

.swagger-ui .hover-black-20:focus, .swagger-ui .hover-black-20:hover {
  color: rgba(0, 0, 0, .2)
}

.swagger-ui .hover-black-10:focus, .swagger-ui .hover-black-10:hover {
  color: rgba(0, 0, 0, .1)
}

.swagger-ui .hover-white-90:focus, .swagger-ui .hover-white-90:hover {
  color: hsla(0, 0%, 100%, .9)
}

.swagger-ui .hover-white-80:focus, .swagger-ui .hover-white-80:hover {
  color: hsla(0, 0%, 100%, .8)
}

.swagger-ui .hover-white-70:focus, .swagger-ui .hover-white-70:hover {
  color: hsla(0, 0%, 100%, .7)
}

.swagger-ui .hover-white-60:focus, .swagger-ui .hover-white-60:hover {
  color: hsla(0, 0%, 100%, .6)
}

.swagger-ui .hover-white-50:focus, .swagger-ui .hover-white-50:hover {
  color: hsla(0, 0%, 100%, .5)
}

.swagger-ui .hover-white-40:focus, .swagger-ui .hover-white-40:hover {
  color: hsla(0, 0%, 100%, .4)
}

.swagger-ui .hover-white-30:focus, .swagger-ui .hover-white-30:hover {
  color: hsla(0, 0%, 100%, .3)
}

.swagger-ui .hover-white-20:focus, .swagger-ui .hover-white-20:hover {
  color: hsla(0, 0%, 100%, .2)
}

.swagger-ui .hover-white-10:focus, .swagger-ui .hover-white-10:hover {
  color: hsla(0, 0%, 100%, .1)
}

.swagger-ui .hover-inherit:focus, .swagger-ui .hover-inherit:hover {
  color: inherit
}

.swagger-ui .hover-bg-black:focus, .swagger-ui .hover-bg-black:hover {
  background-color: #000
}

.swagger-ui .hover-bg-near-black:focus, .swagger-ui .hover-bg-near-black:hover {
  background-color: #111
}

.swagger-ui .hover-bg-dark-gray:focus, .swagger-ui .hover-bg-dark-gray:hover {
  background-color: #333
}

.swagger-ui .hover-bg-mid-gray:focus, .swagger-ui .hover-bg-mid-gray:hover {
  background-color: #555
}

.swagger-ui .hover-bg-gray:focus, .swagger-ui .hover-bg-gray:hover {
  background-color: #777
}

.swagger-ui .hover-bg-silver:focus, .swagger-ui .hover-bg-silver:hover {
  background-color: #999
}

.swagger-ui .hover-bg-light-silver:focus, .swagger-ui .hover-bg-light-silver:hover {
  background-color: #aaa
}

.swagger-ui .hover-bg-moon-gray:focus, .swagger-ui .hover-bg-moon-gray:hover {
  background-color: #ccc
}

.swagger-ui .hover-bg-light-gray:focus, .swagger-ui .hover-bg-light-gray:hover {
  background-color: #eee
}

.swagger-ui .hover-bg-near-white:focus, .swagger-ui .hover-bg-near-white:hover {
  background-color: #f4f4f4
}

.swagger-ui .hover-bg-white:focus, .swagger-ui .hover-bg-white:hover {
  background-color: #fff
}

.swagger-ui .hover-bg-transparent:focus, .swagger-ui .hover-bg-transparent:hover {
  background-color: transparent
}

.swagger-ui .hover-bg-black-90:focus, .swagger-ui .hover-bg-black-90:hover {
  background-color: rgba(0, 0, 0, .9)
}

.swagger-ui .hover-bg-black-80:focus, .swagger-ui .hover-bg-black-80:hover {
  background-color: rgba(0, 0, 0, .8)
}

.swagger-ui .hover-bg-black-70:focus, .swagger-ui .hover-bg-black-70:hover {
  background-color: rgba(0, 0, 0, .7)
}

.swagger-ui .hover-bg-black-60:focus, .swagger-ui .hover-bg-black-60:hover {
  background-color: rgba(0, 0, 0, .6)
}

.swagger-ui .hover-bg-black-50:focus, .swagger-ui .hover-bg-black-50:hover {
  background-color: rgba(0, 0, 0, .5)
}

.swagger-ui .hover-bg-black-40:focus, .swagger-ui .hover-bg-black-40:hover {
  background-color: rgba(0, 0, 0, .4)
}

.swagger-ui .hover-bg-black-30:focus, .swagger-ui .hover-bg-black-30:hover {
  background-color: rgba(0, 0, 0, .3)
}

.swagger-ui .hover-bg-black-20:focus, .swagger-ui .hover-bg-black-20:hover {
  background-color: rgba(0, 0, 0, .2)
}

.swagger-ui .hover-bg-black-10:focus, .swagger-ui .hover-bg-black-10:hover {
  background-color: rgba(0, 0, 0, .1)
}

.swagger-ui .hover-bg-white-90:focus, .swagger-ui .hover-bg-white-90:hover {
  background-color: hsla(0, 0%, 100%, .9)
}

.swagger-ui .hover-bg-white-80:focus, .swagger-ui .hover-bg-white-80:hover {
  background-color: hsla(0, 0%, 100%, .8)
}

.swagger-ui .hover-bg-white-70:focus, .swagger-ui .hover-bg-white-70:hover {
  background-color: hsla(0, 0%, 100%, .7)
}

.swagger-ui .hover-bg-white-60:focus, .swagger-ui .hover-bg-white-60:hover {
  background-color: hsla(0, 0%, 100%, .6)
}

.swagger-ui .hover-bg-white-50:focus, .swagger-ui .hover-bg-white-50:hover {
  background-color: hsla(0, 0%, 100%, .5)
}

.swagger-ui .hover-bg-white-40:focus, .swagger-ui .hover-bg-white-40:hover {
  background-color: hsla(0, 0%, 100%, .4)
}

.swagger-ui .hover-bg-white-30:focus, .swagger-ui .hover-bg-white-30:hover {
  background-color: hsla(0, 0%, 100%, .3)
}

.swagger-ui .hover-bg-white-20:focus, .swagger-ui .hover-bg-white-20:hover {
  background-color: hsla(0, 0%, 100%, .2)
}

.swagger-ui .hover-bg-white-10:focus, .swagger-ui .hover-bg-white-10:hover {
  background-color: hsla(0, 0%, 100%, .1)
}

.swagger-ui .hover-dark-red:focus, .swagger-ui .hover-dark-red:hover {
  color: #e7040f
}

.swagger-ui .hover-red:focus, .swagger-ui .hover-red:hover {
  color: #ff4136
}

.swagger-ui .hover-light-red:focus, .swagger-ui .hover-light-red:hover {
  color: #ff725c
}

.swagger-ui .hover-orange:focus, .swagger-ui .hover-orange:hover {
  color: #ff6300
}

.swagger-ui .hover-gold:focus, .swagger-ui .hover-gold:hover {
  color: #ffb700
}

.swagger-ui .hover-yellow:focus, .swagger-ui .hover-yellow:hover {
  color: gold
}

.swagger-ui .hover-light-yellow:focus, .swagger-ui .hover-light-yellow:hover {
  color: #fbf1a9
}

.swagger-ui .hover-purple:focus, .swagger-ui .hover-purple:hover {
  color: #5e2ca5
}

.swagger-ui .hover-light-purple:focus, .swagger-ui .hover-light-purple:hover {
  color: #a463f2
}

.swagger-ui .hover-dark-pink:focus, .swagger-ui .hover-dark-pink:hover {
  color: #d5008f
}

.swagger-ui .hover-hot-pink:focus, .swagger-ui .hover-hot-pink:hover {
  color: #ff41b4
}

.swagger-ui .hover-pink:focus, .swagger-ui .hover-pink:hover {
  color: #ff80cc
}

.swagger-ui .hover-light-pink:focus, .swagger-ui .hover-light-pink:hover {
  color: #ffa3d7
}

.swagger-ui .hover-dark-green:focus, .swagger-ui .hover-dark-green:hover {
  color: #137752
}

.swagger-ui .hover-green:focus, .swagger-ui .hover-green:hover {
  color: #19a974
}

.swagger-ui .hover-light-green:focus, .swagger-ui .hover-light-green:hover {
  color: #9eebcf
}

.swagger-ui .hover-navy:focus, .swagger-ui .hover-navy:hover {
  color: #001b44
}

.swagger-ui .hover-dark-blue:focus, .swagger-ui .hover-dark-blue:hover {
  color: #00449e
}

.swagger-ui .hover-blue:focus, .swagger-ui .hover-blue:hover {
  color: #357edd
}

.swagger-ui .hover-light-blue:focus, .swagger-ui .hover-light-blue:hover {
  color: #96ccff
}

.swagger-ui .hover-lightest-blue:focus, .swagger-ui .hover-lightest-blue:hover {
  color: #cdecff
}

.swagger-ui .hover-washed-blue:focus, .swagger-ui .hover-washed-blue:hover {
  color: #f6fffe
}

.swagger-ui .hover-washed-green:focus, .swagger-ui .hover-washed-green:hover {
  color: #e8fdf5
}

.swagger-ui .hover-washed-yellow:focus, .swagger-ui .hover-washed-yellow:hover {
  color: #fffceb
}

.swagger-ui .hover-washed-red:focus, .swagger-ui .hover-washed-red:hover {
  color: #ffdfdf
}

.swagger-ui .hover-bg-dark-red:focus, .swagger-ui .hover-bg-dark-red:hover {
  background-color: #e7040f
}

.swagger-ui .hover-bg-red:focus, .swagger-ui .hover-bg-red:hover {
  background-color: #ff4136
}

.swagger-ui .hover-bg-light-red:focus, .swagger-ui .hover-bg-light-red:hover {
  background-color: #ff725c
}

.swagger-ui .hover-bg-orange:focus, .swagger-ui .hover-bg-orange:hover {
  background-color: #ff6300
}

.swagger-ui .hover-bg-gold:focus, .swagger-ui .hover-bg-gold:hover {
  background-color: #ffb700
}

.swagger-ui .hover-bg-yellow:focus, .swagger-ui .hover-bg-yellow:hover {
  background-color: gold
}

.swagger-ui .hover-bg-light-yellow:focus, .swagger-ui .hover-bg-light-yellow:hover {
  background-color: #fbf1a9
}

.swagger-ui .hover-bg-purple:focus, .swagger-ui .hover-bg-purple:hover {
  background-color: #5e2ca5
}

.swagger-ui .hover-bg-light-purple:focus, .swagger-ui .hover-bg-light-purple:hover {
  background-color: #a463f2
}

.swagger-ui .hover-bg-dark-pink:focus, .swagger-ui .hover-bg-dark-pink:hover {
  background-color: #d5008f
}

.swagger-ui .hover-bg-hot-pink:focus, .swagger-ui .hover-bg-hot-pink:hover {
  background-color: #ff41b4
}

.swagger-ui .hover-bg-pink:focus, .swagger-ui .hover-bg-pink:hover {
  background-color: #ff80cc
}

.swagger-ui .hover-bg-light-pink:focus, .swagger-ui .hover-bg-light-pink:hover {
  background-color: #ffa3d7
}

.swagger-ui .hover-bg-dark-green:focus, .swagger-ui .hover-bg-dark-green:hover {
  background-color: #137752
}

.swagger-ui .hover-bg-green:focus, .swagger-ui .hover-bg-green:hover {
  background-color: #19a974
}

.swagger-ui .hover-bg-light-green:focus, .swagger-ui .hover-bg-light-green:hover {
  background-color: #9eebcf
}

.swagger-ui .hover-bg-navy:focus, .swagger-ui .hover-bg-navy:hover {
  background-color: #001b44
}

.swagger-ui .hover-bg-dark-blue:focus, .swagger-ui .hover-bg-dark-blue:hover {
  background-color: #00449e
}

.swagger-ui .hover-bg-blue:focus, .swagger-ui .hover-bg-blue:hover {
  background-color: #357edd
}

.swagger-ui .hover-bg-light-blue:focus, .swagger-ui .hover-bg-light-blue:hover {
  background-color: #96ccff
}

.swagger-ui .hover-bg-lightest-blue:focus, .swagger-ui .hover-bg-lightest-blue:hover {
  background-color: #cdecff
}

.swagger-ui .hover-bg-washed-blue:focus, .swagger-ui .hover-bg-washed-blue:hover {
  background-color: #f6fffe
}

.swagger-ui .hover-bg-washed-green:focus, .swagger-ui .hover-bg-washed-green:hover {
  background-color: #e8fdf5
}

.swagger-ui .hover-bg-washed-yellow:focus, .swagger-ui .hover-bg-washed-yellow:hover {
  background-color: #fffceb
}

.swagger-ui .hover-bg-washed-red:focus, .swagger-ui .hover-bg-washed-red:hover {
  background-color: #ffdfdf
}

.swagger-ui .hover-bg-inherit:focus, .swagger-ui .hover-bg-inherit:hover {
  background-color: inherit
}

.swagger-ui .pa0 {
  padding: 0
}

.swagger-ui .pa1 {
  padding: .25rem
}

.swagger-ui .pa2 {
  padding: .5rem
}

.swagger-ui .pa3 {
  padding: 1rem
}

.swagger-ui .pa4 {
  padding: 2rem
}

.swagger-ui .pa5 {
  padding: 4rem
}

.swagger-ui .pa6 {
  padding: 8rem
}

.swagger-ui .pa7 {
  padding: 16rem
}

.swagger-ui .pl0 {
  padding-left: 0
}

.swagger-ui .pl1 {
  padding-left: .25rem
}

.swagger-ui .pl2 {
  padding-left: .5rem
}

.swagger-ui .pl3 {
  padding-left: 1rem
}

.swagger-ui .pl4 {
  padding-left: 2rem
}

.swagger-ui .pl5 {
  padding-left: 4rem
}

.swagger-ui .pl6 {
  padding-left: 8rem
}

.swagger-ui .pl7 {
  padding-left: 16rem
}

.swagger-ui .pr0 {
  padding-right: 0
}

.swagger-ui .pr1 {
  padding-right: .25rem
}

.swagger-ui .pr2 {
  padding-right: .5rem
}

.swagger-ui .pr3 {
  padding-right: 1rem
}

.swagger-ui .pr4 {
  padding-right: 2rem
}

.swagger-ui .pr5 {
  padding-right: 4rem
}

.swagger-ui .pr6 {
  padding-right: 8rem
}

.swagger-ui .pr7 {
  padding-right: 16rem
}

.swagger-ui .pb0 {
  padding-bottom: 0
}

.swagger-ui .pb1 {
  padding-bottom: .25rem
}

.swagger-ui .pb2 {
  padding-bottom: .5rem
}

.swagger-ui .pb3 {
  padding-bottom: 1rem
}

.swagger-ui .pb4 {
  padding-bottom: 2rem
}

.swagger-ui .pb5 {
  padding-bottom: 4rem
}

.swagger-ui .pb6 {
  padding-bottom: 8rem
}

.swagger-ui .pb7 {
  padding-bottom: 16rem
}

.swagger-ui .pt0 {
  padding-top: 0
}

.swagger-ui .pt1 {
  padding-top: .25rem
}

.swagger-ui .pt2 {
  padding-top: .5rem
}

.swagger-ui .pt3 {
  padding-top: 1rem
}

.swagger-ui .pt4 {
  padding-top: 2rem
}

.swagger-ui .pt5 {
  padding-top: 4rem
}

.swagger-ui .pt6 {
  padding-top: 8rem
}

.swagger-ui .pt7 {
  padding-top: 16rem
}

.swagger-ui .pv0 {
  padding-top: 0;
  padding-bottom: 0
}

.swagger-ui .pv1 {
  padding-top: .25rem;
  padding-bottom: .25rem
}

.swagger-ui .pv2 {
  padding-top: .5rem;
  padding-bottom: .5rem
}

.swagger-ui .pv3 {
  padding-top: 1rem;
  padding-bottom: 1rem
}

.swagger-ui .pv4 {
  padding-top: 2rem;
  padding-bottom: 2rem
}

.swagger-ui .pv5 {
  padding-top: 4rem;
  padding-bottom: 4rem
}

.swagger-ui .pv6 {
  padding-top: 8rem;
  padding-bottom: 8rem
}

.swagger-ui .pv7 {
  padding-top: 16rem;
  padding-bottom: 16rem
}

.swagger-ui .ph0 {
  padding-left: 0;
  padding-right: 0
}

.swagger-ui .ph1 {
  padding-left: .25rem;
  padding-right: .25rem
}

.swagger-ui .ph2 {
  padding-left: .5rem;
  padding-right: .5rem
}

.swagger-ui .ph3 {
  padding-left: 1rem;
  padding-right: 1rem
}

.swagger-ui .ph4 {
  padding-left: 2rem;
  padding-right: 2rem
}

.swagger-ui .ph5 {
  padding-left: 4rem;
  padding-right: 4rem
}

.swagger-ui .ph6 {
  padding-left: 8rem;
  padding-right: 8rem
}

.swagger-ui .ph7 {
  padding-left: 16rem;
  padding-right: 16rem
}

.swagger-ui .ma0 {
  margin: 0
}

.swagger-ui .ma1 {
  margin: .25rem
}

.swagger-ui .ma2 {
  margin: .5rem
}

.swagger-ui .ma3 {
  margin: 1rem
}

.swagger-ui .ma4 {
  margin: 2rem
}

.swagger-ui .ma5 {
  margin: 4rem
}

.swagger-ui .ma6 {
  margin: 8rem
}

.swagger-ui .ma7 {
  margin: 16rem
}

.swagger-ui .ml0 {
  margin-left: 0
}

.swagger-ui .ml1 {
  margin-left: .25rem
}

.swagger-ui .ml2 {
  margin-left: .5rem
}

.swagger-ui .ml3 {
  margin-left: 1rem
}

.swagger-ui .ml4 {
  margin-left: 2rem
}

.swagger-ui .ml5 {
  margin-left: 4rem
}

.swagger-ui .ml6 {
  margin-left: 8rem
}

.swagger-ui .ml7 {
  margin-left: 16rem
}

.swagger-ui .mr0 {
  margin-right: 0
}

.swagger-ui .mr1 {
  margin-right: .25rem
}

.swagger-ui .mr2 {
  margin-right: .5rem
}

.swagger-ui .mr3 {
  margin-right: 1rem
}

.swagger-ui .mr4 {
  margin-right: 2rem
}

.swagger-ui .mr5 {
  margin-right: 4rem
}

.swagger-ui .mr6 {
  margin-right: 8rem
}

.swagger-ui .mr7 {
  margin-right: 16rem
}

.swagger-ui .mb0 {
  margin-bottom: 0
}

.swagger-ui .mb1 {
  margin-bottom: .25rem
}

.swagger-ui .mb2 {
  margin-bottom: .5rem
}

.swagger-ui .mb3 {
  margin-bottom: 1rem
}

.swagger-ui .mb4 {
  margin-bottom: 2rem
}

.swagger-ui .mb5 {
  margin-bottom: 4rem
}

.swagger-ui .mb6 {
  margin-bottom: 8rem
}

.swagger-ui .mb7 {
  margin-bottom: 16rem
}

.swagger-ui .mt0 {
  margin-top: 0
}

.swagger-ui .mt1 {
  margin-top: .25rem
}

.swagger-ui .mt2 {
  margin-top: .5rem
}

.swagger-ui .mt3 {
  margin-top: 1rem
}

.swagger-ui .mt4 {
  margin-top: 2rem
}

.swagger-ui .mt5 {
  margin-top: 4rem
}

.swagger-ui .mt6 {
  margin-top: 8rem
}

.swagger-ui .mt7 {
  margin-top: 16rem
}

.swagger-ui .mv0 {
  margin-top: 0;
  margin-bottom: 0
}

.swagger-ui .mv1 {
  margin-top: .25rem;
  margin-bottom: .25rem
}

.swagger-ui .mv2 {
  margin-top: .5rem;
  margin-bottom: .5rem
}

.swagger-ui .mv3 {
  margin-top: 1rem;
  margin-bottom: 1rem
}

.swagger-ui .mv4 {
  margin-top: 2rem;
  margin-bottom: 2rem
}

.swagger-ui .mv5 {
  margin-top: 4rem;
  margin-bottom: 4rem
}

.swagger-ui .mv6 {
  margin-top: 8rem;
  margin-bottom: 8rem
}

.swagger-ui .mv7 {
  margin-top: 16rem;
  margin-bottom: 16rem
}

.swagger-ui .mh0 {
  margin-left: 0;
  margin-right: 0
}

.swagger-ui .mh1 {
  margin-left: .25rem;
  margin-right: .25rem
}

.swagger-ui .mh2 {
  margin-left: .5rem;
  margin-right: .5rem
}

.swagger-ui .mh3 {
  margin-left: 1rem;
  margin-right: 1rem
}

.swagger-ui .mh4 {
  margin-left: 2rem;
  margin-right: 2rem
}

.swagger-ui .mh5 {
  margin-left: 4rem;
  margin-right: 4rem
}

.swagger-ui .mh6 {
  margin-left: 8rem;
  margin-right: 8rem
}

.swagger-ui .mh7 {
  margin-left: 16rem;
  margin-right: 16rem
}

@media screen and (min-width: 30em) {
  .swagger-ui .pa0-ns {
    padding: 0
  }

  .swagger-ui .pa1-ns {
    padding: .25rem
  }

  .swagger-ui .pa2-ns {
    padding: .5rem
  }

  .swagger-ui .pa3-ns {
    padding: 1rem
  }

  .swagger-ui .pa4-ns {
    padding: 2rem
  }

  .swagger-ui .pa5-ns {
    padding: 4rem
  }

  .swagger-ui .pa6-ns {
    padding: 8rem
  }

  .swagger-ui .pa7-ns {
    padding: 16rem
  }

  .swagger-ui .pl0-ns {
    padding-left: 0
  }

  .swagger-ui .pl1-ns {
    padding-left: .25rem
  }

  .swagger-ui .pl2-ns {
    padding-left: .5rem
  }

  .swagger-ui .pl3-ns {
    padding-left: 1rem
  }

  .swagger-ui .pl4-ns {
    padding-left: 2rem
  }

  .swagger-ui .pl5-ns {
    padding-left: 4rem
  }

  .swagger-ui .pl6-ns {
    padding-left: 8rem
  }

  .swagger-ui .pl7-ns {
    padding-left: 16rem
  }

  .swagger-ui .pr0-ns {
    padding-right: 0
  }

  .swagger-ui .pr1-ns {
    padding-right: .25rem
  }

  .swagger-ui .pr2-ns {
    padding-right: .5rem
  }

  .swagger-ui .pr3-ns {
    padding-right: 1rem
  }

  .swagger-ui .pr4-ns {
    padding-right: 2rem
  }

  .swagger-ui .pr5-ns {
    padding-right: 4rem
  }

  .swagger-ui .pr6-ns {
    padding-right: 8rem
  }

  .swagger-ui .pr7-ns {
    padding-right: 16rem
  }

  .swagger-ui .pb0-ns {
    padding-bottom: 0
  }

  .swagger-ui .pb1-ns {
    padding-bottom: .25rem
  }

  .swagger-ui .pb2-ns {
    padding-bottom: .5rem
  }

  .swagger-ui .pb3-ns {
    padding-bottom: 1rem
  }

  .swagger-ui .pb4-ns {
    padding-bottom: 2rem
  }

  .swagger-ui .pb5-ns {
    padding-bottom: 4rem
  }

  .swagger-ui .pb6-ns {
    padding-bottom: 8rem
  }

  .swagger-ui .pb7-ns {
    padding-bottom: 16rem
  }

  .swagger-ui .pt0-ns {
    padding-top: 0
  }

  .swagger-ui .pt1-ns {
    padding-top: .25rem
  }

  .swagger-ui .pt2-ns {
    padding-top: .5rem
  }

  .swagger-ui .pt3-ns {
    padding-top: 1rem
  }

  .swagger-ui .pt4-ns {
    padding-top: 2rem
  }

  .swagger-ui .pt5-ns {
    padding-top: 4rem
  }

  .swagger-ui .pt6-ns {
    padding-top: 8rem
  }

  .swagger-ui .pt7-ns {
    padding-top: 16rem
  }

  .swagger-ui .pv0-ns {
    padding-top: 0;
    padding-bottom: 0
  }

  .swagger-ui .pv1-ns {
    padding-top: .25rem;
    padding-bottom: .25rem
  }

  .swagger-ui .pv2-ns {
    padding-top: .5rem;
    padding-bottom: .5rem
  }

  .swagger-ui .pv3-ns {
    padding-top: 1rem;
    padding-bottom: 1rem
  }

  .swagger-ui .pv4-ns {
    padding-top: 2rem;
    padding-bottom: 2rem
  }

  .swagger-ui .pv5-ns {
    padding-top: 4rem;
    padding-bottom: 4rem
  }

  .swagger-ui .pv6-ns {
    padding-top: 8rem;
    padding-bottom: 8rem
  }

  .swagger-ui .pv7-ns {
    padding-top: 16rem;
    padding-bottom: 16rem
  }

  .swagger-ui .ph0-ns {
    padding-left: 0;
    padding-right: 0
  }

  .swagger-ui .ph1-ns {
    padding-left: .25rem;
    padding-right: .25rem
  }

  .swagger-ui .ph2-ns {
    padding-left: .5rem;
    padding-right: .5rem
  }

  .swagger-ui .ph3-ns {
    padding-left: 1rem;
    padding-right: 1rem
  }

  .swagger-ui .ph4-ns {
    padding-left: 2rem;
    padding-right: 2rem
  }

  .swagger-ui .ph5-ns {
    padding-left: 4rem;
    padding-right: 4rem
  }

  .swagger-ui .ph6-ns {
    padding-left: 8rem;
    padding-right: 8rem
  }

  .swagger-ui .ph7-ns {
    padding-left: 16rem;
    padding-right: 16rem
  }

  .swagger-ui .ma0-ns {
    margin: 0
  }

  .swagger-ui .ma1-ns {
    margin: .25rem
  }

  .swagger-ui .ma2-ns {
    margin: .5rem
  }

  .swagger-ui .ma3-ns {
    margin: 1rem
  }

  .swagger-ui .ma4-ns {
    margin: 2rem
  }

  .swagger-ui .ma5-ns {
    margin: 4rem
  }

  .swagger-ui .ma6-ns {
    margin: 8rem
  }

  .swagger-ui .ma7-ns {
    margin: 16rem
  }

  .swagger-ui .ml0-ns {
    margin-left: 0
  }

  .swagger-ui .ml1-ns {
    margin-left: .25rem
  }

  .swagger-ui .ml2-ns {
    margin-left: .5rem
  }

  .swagger-ui .ml3-ns {
    margin-left: 1rem
  }

  .swagger-ui .ml4-ns {
    margin-left: 2rem
  }

  .swagger-ui .ml5-ns {
    margin-left: 4rem
  }

  .swagger-ui .ml6-ns {
    margin-left: 8rem
  }

  .swagger-ui .ml7-ns {
    margin-left: 16rem
  }

  .swagger-ui .mr0-ns {
    margin-right: 0
  }

  .swagger-ui .mr1-ns {
    margin-right: .25rem
  }

  .swagger-ui .mr2-ns {
    margin-right: .5rem
  }

  .swagger-ui .mr3-ns {
    margin-right: 1rem
  }

  .swagger-ui .mr4-ns {
    margin-right: 2rem
  }

  .swagger-ui .mr5-ns {
    margin-right: 4rem
  }

  .swagger-ui .mr6-ns {
    margin-right: 8rem
  }

  .swagger-ui .mr7-ns {
    margin-right: 16rem
  }

  .swagger-ui .mb0-ns {
    margin-bottom: 0
  }

  .swagger-ui .mb1-ns {
    margin-bottom: .25rem
  }

  .swagger-ui .mb2-ns {
    margin-bottom: .5rem
  }

  .swagger-ui .mb3-ns {
    margin-bottom: 1rem
  }

  .swagger-ui .mb4-ns {
    margin-bottom: 2rem
  }

  .swagger-ui .mb5-ns {
    margin-bottom: 4rem
  }

  .swagger-ui .mb6-ns {
    margin-bottom: 8rem
  }

  .swagger-ui .mb7-ns {
    margin-bottom: 16rem
  }

  .swagger-ui .mt0-ns {
    margin-top: 0
  }

  .swagger-ui .mt1-ns {
    margin-top: .25rem
  }

  .swagger-ui .mt2-ns {
    margin-top: .5rem
  }

  .swagger-ui .mt3-ns {
    margin-top: 1rem
  }

  .swagger-ui .mt4-ns {
    margin-top: 2rem
  }

  .swagger-ui .mt5-ns {
    margin-top: 4rem
  }

  .swagger-ui .mt6-ns {
    margin-top: 8rem
  }

  .swagger-ui .mt7-ns {
    margin-top: 16rem
  }

  .swagger-ui .mv0-ns {
    margin-top: 0;
    margin-bottom: 0
  }

  .swagger-ui .mv1-ns {
    margin-top: .25rem;
    margin-bottom: .25rem
  }

  .swagger-ui .mv2-ns {
    margin-top: .5rem;
    margin-bottom: .5rem
  }

  .swagger-ui .mv3-ns {
    margin-top: 1rem;
    margin-bottom: 1rem
  }

  .swagger-ui .mv4-ns {
    margin-top: 2rem;
    margin-bottom: 2rem
  }

  .swagger-ui .mv5-ns {
    margin-top: 4rem;
    margin-bottom: 4rem
  }

  .swagger-ui .mv6-ns {
    margin-top: 8rem;
    margin-bottom: 8rem
  }

  .swagger-ui .mv7-ns {
    margin-top: 16rem;
    margin-bottom: 16rem
  }

  .swagger-ui .mh0-ns {
    margin-left: 0;
    margin-right: 0
  }

  .swagger-ui .mh1-ns {
    margin-left: .25rem;
    margin-right: .25rem
  }

  .swagger-ui .mh2-ns {
    margin-left: .5rem;
    margin-right: .5rem
  }

  .swagger-ui .mh3-ns {
    margin-left: 1rem;
    margin-right: 1rem
  }

  .swagger-ui .mh4-ns {
    margin-left: 2rem;
    margin-right: 2rem
  }

  .swagger-ui .mh5-ns {
    margin-left: 4rem;
    margin-right: 4rem
  }

  .swagger-ui .mh6-ns {
    margin-left: 8rem;
    margin-right: 8rem
  }

  .swagger-ui .mh7-ns {
    margin-left: 16rem;
    margin-right: 16rem
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .pa0-m {
    padding: 0
  }

  .swagger-ui .pa1-m {
    padding: .25rem
  }

  .swagger-ui .pa2-m {
    padding: .5rem
  }

  .swagger-ui .pa3-m {
    padding: 1rem
  }

  .swagger-ui .pa4-m {
    padding: 2rem
  }

  .swagger-ui .pa5-m {
    padding: 4rem
  }

  .swagger-ui .pa6-m {
    padding: 8rem
  }

  .swagger-ui .pa7-m {
    padding: 16rem
  }

  .swagger-ui .pl0-m {
    padding-left: 0
  }

  .swagger-ui .pl1-m {
    padding-left: .25rem
  }

  .swagger-ui .pl2-m {
    padding-left: .5rem
  }

  .swagger-ui .pl3-m {
    padding-left: 1rem
  }

  .swagger-ui .pl4-m {
    padding-left: 2rem
  }

  .swagger-ui .pl5-m {
    padding-left: 4rem
  }

  .swagger-ui .pl6-m {
    padding-left: 8rem
  }

  .swagger-ui .pl7-m {
    padding-left: 16rem
  }

  .swagger-ui .pr0-m {
    padding-right: 0
  }

  .swagger-ui .pr1-m {
    padding-right: .25rem
  }

  .swagger-ui .pr2-m {
    padding-right: .5rem
  }

  .swagger-ui .pr3-m {
    padding-right: 1rem
  }

  .swagger-ui .pr4-m {
    padding-right: 2rem
  }

  .swagger-ui .pr5-m {
    padding-right: 4rem
  }

  .swagger-ui .pr6-m {
    padding-right: 8rem
  }

  .swagger-ui .pr7-m {
    padding-right: 16rem
  }

  .swagger-ui .pb0-m {
    padding-bottom: 0
  }

  .swagger-ui .pb1-m {
    padding-bottom: .25rem
  }

  .swagger-ui .pb2-m {
    padding-bottom: .5rem
  }

  .swagger-ui .pb3-m {
    padding-bottom: 1rem
  }

  .swagger-ui .pb4-m {
    padding-bottom: 2rem
  }

  .swagger-ui .pb5-m {
    padding-bottom: 4rem
  }

  .swagger-ui .pb6-m {
    padding-bottom: 8rem
  }

  .swagger-ui .pb7-m {
    padding-bottom: 16rem
  }

  .swagger-ui .pt0-m {
    padding-top: 0
  }

  .swagger-ui .pt1-m {
    padding-top: .25rem
  }

  .swagger-ui .pt2-m {
    padding-top: .5rem
  }

  .swagger-ui .pt3-m {
    padding-top: 1rem
  }

  .swagger-ui .pt4-m {
    padding-top: 2rem
  }

  .swagger-ui .pt5-m {
    padding-top: 4rem
  }

  .swagger-ui .pt6-m {
    padding-top: 8rem
  }

  .swagger-ui .pt7-m {
    padding-top: 16rem
  }

  .swagger-ui .pv0-m {
    padding-top: 0;
    padding-bottom: 0
  }

  .swagger-ui .pv1-m {
    padding-top: .25rem;
    padding-bottom: .25rem
  }

  .swagger-ui .pv2-m {
    padding-top: .5rem;
    padding-bottom: .5rem
  }

  .swagger-ui .pv3-m {
    padding-top: 1rem;
    padding-bottom: 1rem
  }

  .swagger-ui .pv4-m {
    padding-top: 2rem;
    padding-bottom: 2rem
  }

  .swagger-ui .pv5-m {
    padding-top: 4rem;
    padding-bottom: 4rem
  }

  .swagger-ui .pv6-m {
    padding-top: 8rem;
    padding-bottom: 8rem
  }

  .swagger-ui .pv7-m {
    padding-top: 16rem;
    padding-bottom: 16rem
  }

  .swagger-ui .ph0-m {
    padding-left: 0;
    padding-right: 0
  }

  .swagger-ui .ph1-m {
    padding-left: .25rem;
    padding-right: .25rem
  }

  .swagger-ui .ph2-m {
    padding-left: .5rem;
    padding-right: .5rem
  }

  .swagger-ui .ph3-m {
    padding-left: 1rem;
    padding-right: 1rem
  }

  .swagger-ui .ph4-m {
    padding-left: 2rem;
    padding-right: 2rem
  }

  .swagger-ui .ph5-m {
    padding-left: 4rem;
    padding-right: 4rem
  }

  .swagger-ui .ph6-m {
    padding-left: 8rem;
    padding-right: 8rem
  }

  .swagger-ui .ph7-m {
    padding-left: 16rem;
    padding-right: 16rem
  }

  .swagger-ui .ma0-m {
    margin: 0
  }

  .swagger-ui .ma1-m {
    margin: .25rem
  }

  .swagger-ui .ma2-m {
    margin: .5rem
  }

  .swagger-ui .ma3-m {
    margin: 1rem
  }

  .swagger-ui .ma4-m {
    margin: 2rem
  }

  .swagger-ui .ma5-m {
    margin: 4rem
  }

  .swagger-ui .ma6-m {
    margin: 8rem
  }

  .swagger-ui .ma7-m {
    margin: 16rem
  }

  .swagger-ui .ml0-m {
    margin-left: 0
  }

  .swagger-ui .ml1-m {
    margin-left: .25rem
  }

  .swagger-ui .ml2-m {
    margin-left: .5rem
  }

  .swagger-ui .ml3-m {
    margin-left: 1rem
  }

  .swagger-ui .ml4-m {
    margin-left: 2rem
  }

  .swagger-ui .ml5-m {
    margin-left: 4rem
  }

  .swagger-ui .ml6-m {
    margin-left: 8rem
  }

  .swagger-ui .ml7-m {
    margin-left: 16rem
  }

  .swagger-ui .mr0-m {
    margin-right: 0
  }

  .swagger-ui .mr1-m {
    margin-right: .25rem
  }

  .swagger-ui .mr2-m {
    margin-right: .5rem
  }

  .swagger-ui .mr3-m {
    margin-right: 1rem
  }

  .swagger-ui .mr4-m {
    margin-right: 2rem
  }

  .swagger-ui .mr5-m {
    margin-right: 4rem
  }

  .swagger-ui .mr6-m {
    margin-right: 8rem
  }

  .swagger-ui .mr7-m {
    margin-right: 16rem
  }

  .swagger-ui .mb0-m {
    margin-bottom: 0
  }

  .swagger-ui .mb1-m {
    margin-bottom: .25rem
  }

  .swagger-ui .mb2-m {
    margin-bottom: .5rem
  }

  .swagger-ui .mb3-m {
    margin-bottom: 1rem
  }

  .swagger-ui .mb4-m {
    margin-bottom: 2rem
  }

  .swagger-ui .mb5-m {
    margin-bottom: 4rem
  }

  .swagger-ui .mb6-m {
    margin-bottom: 8rem
  }

  .swagger-ui .mb7-m {
    margin-bottom: 16rem
  }

  .swagger-ui .mt0-m {
    margin-top: 0
  }

  .swagger-ui .mt1-m {
    margin-top: .25rem
  }

  .swagger-ui .mt2-m {
    margin-top: .5rem
  }

  .swagger-ui .mt3-m {
    margin-top: 1rem
  }

  .swagger-ui .mt4-m {
    margin-top: 2rem
  }

  .swagger-ui .mt5-m {
    margin-top: 4rem
  }

  .swagger-ui .mt6-m {
    margin-top: 8rem
  }

  .swagger-ui .mt7-m {
    margin-top: 16rem
  }

  .swagger-ui .mv0-m {
    margin-top: 0;
    margin-bottom: 0
  }

  .swagger-ui .mv1-m {
    margin-top: .25rem;
    margin-bottom: .25rem
  }

  .swagger-ui .mv2-m {
    margin-top: .5rem;
    margin-bottom: .5rem
  }

  .swagger-ui .mv3-m {
    margin-top: 1rem;
    margin-bottom: 1rem
  }

  .swagger-ui .mv4-m {
    margin-top: 2rem;
    margin-bottom: 2rem
  }

  .swagger-ui .mv5-m {
    margin-top: 4rem;
    margin-bottom: 4rem
  }

  .swagger-ui .mv6-m {
    margin-top: 8rem;
    margin-bottom: 8rem
  }

  .swagger-ui .mv7-m {
    margin-top: 16rem;
    margin-bottom: 16rem
  }

  .swagger-ui .mh0-m {
    margin-left: 0;
    margin-right: 0
  }

  .swagger-ui .mh1-m {
    margin-left: .25rem;
    margin-right: .25rem
  }

  .swagger-ui .mh2-m {
    margin-left: .5rem;
    margin-right: .5rem
  }

  .swagger-ui .mh3-m {
    margin-left: 1rem;
    margin-right: 1rem
  }

  .swagger-ui .mh4-m {
    margin-left: 2rem;
    margin-right: 2rem
  }

  .swagger-ui .mh5-m {
    margin-left: 4rem;
    margin-right: 4rem
  }

  .swagger-ui .mh6-m {
    margin-left: 8rem;
    margin-right: 8rem
  }

  .swagger-ui .mh7-m {
    margin-left: 16rem;
    margin-right: 16rem
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .pa0-l {
    padding: 0
  }

  .swagger-ui .pa1-l {
    padding: .25rem
  }

  .swagger-ui .pa2-l {
    padding: .5rem
  }

  .swagger-ui .pa3-l {
    padding: 1rem
  }

  .swagger-ui .pa4-l {
    padding: 2rem
  }

  .swagger-ui .pa5-l {
    padding: 4rem
  }

  .swagger-ui .pa6-l {
    padding: 8rem
  }

  .swagger-ui .pa7-l {
    padding: 16rem
  }

  .swagger-ui .pl0-l {
    padding-left: 0
  }

  .swagger-ui .pl1-l {
    padding-left: .25rem
  }

  .swagger-ui .pl2-l {
    padding-left: .5rem
  }

  .swagger-ui .pl3-l {
    padding-left: 1rem
  }

  .swagger-ui .pl4-l {
    padding-left: 2rem
  }

  .swagger-ui .pl5-l {
    padding-left: 4rem
  }

  .swagger-ui .pl6-l {
    padding-left: 8rem
  }

  .swagger-ui .pl7-l {
    padding-left: 16rem
  }

  .swagger-ui .pr0-l {
    padding-right: 0
  }

  .swagger-ui .pr1-l {
    padding-right: .25rem
  }

  .swagger-ui .pr2-l {
    padding-right: .5rem
  }

  .swagger-ui .pr3-l {
    padding-right: 1rem
  }

  .swagger-ui .pr4-l {
    padding-right: 2rem
  }

  .swagger-ui .pr5-l {
    padding-right: 4rem
  }

  .swagger-ui .pr6-l {
    padding-right: 8rem
  }

  .swagger-ui .pr7-l {
    padding-right: 16rem
  }

  .swagger-ui .pb0-l {
    padding-bottom: 0
  }

  .swagger-ui .pb1-l {
    padding-bottom: .25rem
  }

  .swagger-ui .pb2-l {
    padding-bottom: .5rem
  }

  .swagger-ui .pb3-l {
    padding-bottom: 1rem
  }

  .swagger-ui .pb4-l {
    padding-bottom: 2rem
  }

  .swagger-ui .pb5-l {
    padding-bottom: 4rem
  }

  .swagger-ui .pb6-l {
    padding-bottom: 8rem
  }

  .swagger-ui .pb7-l {
    padding-bottom: 16rem
  }

  .swagger-ui .pt0-l {
    padding-top: 0
  }

  .swagger-ui .pt1-l {
    padding-top: .25rem
  }

  .swagger-ui .pt2-l {
    padding-top: .5rem
  }

  .swagger-ui .pt3-l {
    padding-top: 1rem
  }

  .swagger-ui .pt4-l {
    padding-top: 2rem
  }

  .swagger-ui .pt5-l {
    padding-top: 4rem
  }

  .swagger-ui .pt6-l {
    padding-top: 8rem
  }

  .swagger-ui .pt7-l {
    padding-top: 16rem
  }

  .swagger-ui .pv0-l {
    padding-top: 0;
    padding-bottom: 0
  }

  .swagger-ui .pv1-l {
    padding-top: .25rem;
    padding-bottom: .25rem
  }

  .swagger-ui .pv2-l {
    padding-top: .5rem;
    padding-bottom: .5rem
  }

  .swagger-ui .pv3-l {
    padding-top: 1rem;
    padding-bottom: 1rem
  }

  .swagger-ui .pv4-l {
    padding-top: 2rem;
    padding-bottom: 2rem
  }

  .swagger-ui .pv5-l {
    padding-top: 4rem;
    padding-bottom: 4rem
  }

  .swagger-ui .pv6-l {
    padding-top: 8rem;
    padding-bottom: 8rem
  }

  .swagger-ui .pv7-l {
    padding-top: 16rem;
    padding-bottom: 16rem
  }

  .swagger-ui .ph0-l {
    padding-left: 0;
    padding-right: 0
  }

  .swagger-ui .ph1-l {
    padding-left: .25rem;
    padding-right: .25rem
  }

  .swagger-ui .ph2-l {
    padding-left: .5rem;
    padding-right: .5rem
  }

  .swagger-ui .ph3-l {
    padding-left: 1rem;
    padding-right: 1rem
  }

  .swagger-ui .ph4-l {
    padding-left: 2rem;
    padding-right: 2rem
  }

  .swagger-ui .ph5-l {
    padding-left: 4rem;
    padding-right: 4rem
  }

  .swagger-ui .ph6-l {
    padding-left: 8rem;
    padding-right: 8rem
  }

  .swagger-ui .ph7-l {
    padding-left: 16rem;
    padding-right: 16rem
  }

  .swagger-ui .ma0-l {
    margin: 0
  }

  .swagger-ui .ma1-l {
    margin: .25rem
  }

  .swagger-ui .ma2-l {
    margin: .5rem
  }

  .swagger-ui .ma3-l {
    margin: 1rem
  }

  .swagger-ui .ma4-l {
    margin: 2rem
  }

  .swagger-ui .ma5-l {
    margin: 4rem
  }

  .swagger-ui .ma6-l {
    margin: 8rem
  }

  .swagger-ui .ma7-l {
    margin: 16rem
  }

  .swagger-ui .ml0-l {
    margin-left: 0
  }

  .swagger-ui .ml1-l {
    margin-left: .25rem
  }

  .swagger-ui .ml2-l {
    margin-left: .5rem
  }

  .swagger-ui .ml3-l {
    margin-left: 1rem
  }

  .swagger-ui .ml4-l {
    margin-left: 2rem
  }

  .swagger-ui .ml5-l {
    margin-left: 4rem
  }

  .swagger-ui .ml6-l {
    margin-left: 8rem
  }

  .swagger-ui .ml7-l {
    margin-left: 16rem
  }

  .swagger-ui .mr0-l {
    margin-right: 0
  }

  .swagger-ui .mr1-l {
    margin-right: .25rem
  }

  .swagger-ui .mr2-l {
    margin-right: .5rem
  }

  .swagger-ui .mr3-l {
    margin-right: 1rem
  }

  .swagger-ui .mr4-l {
    margin-right: 2rem
  }

  .swagger-ui .mr5-l {
    margin-right: 4rem
  }

  .swagger-ui .mr6-l {
    margin-right: 8rem
  }

  .swagger-ui .mr7-l {
    margin-right: 16rem
  }

  .swagger-ui .mb0-l {
    margin-bottom: 0
  }

  .swagger-ui .mb1-l {
    margin-bottom: .25rem
  }

  .swagger-ui .mb2-l {
    margin-bottom: .5rem
  }

  .swagger-ui .mb3-l {
    margin-bottom: 1rem
  }

  .swagger-ui .mb4-l {
    margin-bottom: 2rem
  }

  .swagger-ui .mb5-l {
    margin-bottom: 4rem
  }

  .swagger-ui .mb6-l {
    margin-bottom: 8rem
  }

  .swagger-ui .mb7-l {
    margin-bottom: 16rem
  }

  .swagger-ui .mt0-l {
    margin-top: 0
  }

  .swagger-ui .mt1-l {
    margin-top: .25rem
  }

  .swagger-ui .mt2-l {
    margin-top: .5rem
  }

  .swagger-ui .mt3-l {
    margin-top: 1rem
  }

  .swagger-ui .mt4-l {
    margin-top: 2rem
  }

  .swagger-ui .mt5-l {
    margin-top: 4rem
  }

  .swagger-ui .mt6-l {
    margin-top: 8rem
  }

  .swagger-ui .mt7-l {
    margin-top: 16rem
  }

  .swagger-ui .mv0-l {
    margin-top: 0;
    margin-bottom: 0
  }

  .swagger-ui .mv1-l {
    margin-top: .25rem;
    margin-bottom: .25rem
  }

  .swagger-ui .mv2-l {
    margin-top: .5rem;
    margin-bottom: .5rem
  }

  .swagger-ui .mv3-l {
    margin-top: 1rem;
    margin-bottom: 1rem
  }

  .swagger-ui .mv4-l {
    margin-top: 2rem;
    margin-bottom: 2rem
  }

  .swagger-ui .mv5-l {
    margin-top: 4rem;
    margin-bottom: 4rem
  }

  .swagger-ui .mv6-l {
    margin-top: 8rem;
    margin-bottom: 8rem
  }

  .swagger-ui .mv7-l {
    margin-top: 16rem;
    margin-bottom: 16rem
  }

  .swagger-ui .mh0-l {
    margin-left: 0;
    margin-right: 0
  }

  .swagger-ui .mh1-l {
    margin-left: .25rem;
    margin-right: .25rem
  }

  .swagger-ui .mh2-l {
    margin-left: .5rem;
    margin-right: .5rem
  }

  .swagger-ui .mh3-l {
    margin-left: 1rem;
    margin-right: 1rem
  }

  .swagger-ui .mh4-l {
    margin-left: 2rem;
    margin-right: 2rem
  }

  .swagger-ui .mh5-l {
    margin-left: 4rem;
    margin-right: 4rem
  }

  .swagger-ui .mh6-l {
    margin-left: 8rem;
    margin-right: 8rem
  }

  .swagger-ui .mh7-l {
    margin-left: 16rem;
    margin-right: 16rem
  }
}

.swagger-ui .na1 {
  margin: -.25rem
}

.swagger-ui .na2 {
  margin: -.5rem
}

.swagger-ui .na3 {
  margin: -1rem
}

.swagger-ui .na4 {
  margin: -2rem
}

.swagger-ui .na5 {
  margin: -4rem
}

.swagger-ui .na6 {
  margin: -8rem
}

.swagger-ui .na7 {
  margin: -16rem
}

.swagger-ui .nl1 {
  margin-left: -.25rem
}

.swagger-ui .nl2 {
  margin-left: -.5rem
}

.swagger-ui .nl3 {
  margin-left: -1rem
}

.swagger-ui .nl4 {
  margin-left: -2rem
}

.swagger-ui .nl5 {
  margin-left: -4rem
}

.swagger-ui .nl6 {
  margin-left: -8rem
}

.swagger-ui .nl7 {
  margin-left: -16rem
}

.swagger-ui .nr1 {
  margin-right: -.25rem
}

.swagger-ui .nr2 {
  margin-right: -.5rem
}

.swagger-ui .nr3 {
  margin-right: -1rem
}

.swagger-ui .nr4 {
  margin-right: -2rem
}

.swagger-ui .nr5 {
  margin-right: -4rem
}

.swagger-ui .nr6 {
  margin-right: -8rem
}

.swagger-ui .nr7 {
  margin-right: -16rem
}

.swagger-ui .nb1 {
  margin-bottom: -.25rem
}

.swagger-ui .nb2 {
  margin-bottom: -.5rem
}

.swagger-ui .nb3 {
  margin-bottom: -1rem
}

.swagger-ui .nb4 {
  margin-bottom: -2rem
}

.swagger-ui .nb5 {
  margin-bottom: -4rem
}

.swagger-ui .nb6 {
  margin-bottom: -8rem
}

.swagger-ui .nb7 {
  margin-bottom: -16rem
}

.swagger-ui .nt1 {
  margin-top: -.25rem
}

.swagger-ui .nt2 {
  margin-top: -.5rem
}

.swagger-ui .nt3 {
  margin-top: -1rem
}

.swagger-ui .nt4 {
  margin-top: -2rem
}

.swagger-ui .nt5 {
  margin-top: -4rem
}

.swagger-ui .nt6 {
  margin-top: -8rem
}

.swagger-ui .nt7 {
  margin-top: -16rem
}

@media screen and (min-width: 30em) {
  .swagger-ui .na1-ns {
    margin: -.25rem
  }

  .swagger-ui .na2-ns {
    margin: -.5rem
  }

  .swagger-ui .na3-ns {
    margin: -1rem
  }

  .swagger-ui .na4-ns {
    margin: -2rem
  }

  .swagger-ui .na5-ns {
    margin: -4rem
  }

  .swagger-ui .na6-ns {
    margin: -8rem
  }

  .swagger-ui .na7-ns {
    margin: -16rem
  }

  .swagger-ui .nl1-ns {
    margin-left: -.25rem
  }

  .swagger-ui .nl2-ns {
    margin-left: -.5rem
  }

  .swagger-ui .nl3-ns {
    margin-left: -1rem
  }

  .swagger-ui .nl4-ns {
    margin-left: -2rem
  }

  .swagger-ui .nl5-ns {
    margin-left: -4rem
  }

  .swagger-ui .nl6-ns {
    margin-left: -8rem
  }

  .swagger-ui .nl7-ns {
    margin-left: -16rem
  }

  .swagger-ui .nr1-ns {
    margin-right: -.25rem
  }

  .swagger-ui .nr2-ns {
    margin-right: -.5rem
  }

  .swagger-ui .nr3-ns {
    margin-right: -1rem
  }

  .swagger-ui .nr4-ns {
    margin-right: -2rem
  }

  .swagger-ui .nr5-ns {
    margin-right: -4rem
  }

  .swagger-ui .nr6-ns {
    margin-right: -8rem
  }

  .swagger-ui .nr7-ns {
    margin-right: -16rem
  }

  .swagger-ui .nb1-ns {
    margin-bottom: -.25rem
  }

  .swagger-ui .nb2-ns {
    margin-bottom: -.5rem
  }

  .swagger-ui .nb3-ns {
    margin-bottom: -1rem
  }

  .swagger-ui .nb4-ns {
    margin-bottom: -2rem
  }

  .swagger-ui .nb5-ns {
    margin-bottom: -4rem
  }

  .swagger-ui .nb6-ns {
    margin-bottom: -8rem
  }

  .swagger-ui .nb7-ns {
    margin-bottom: -16rem
  }

  .swagger-ui .nt1-ns {
    margin-top: -.25rem
  }

  .swagger-ui .nt2-ns {
    margin-top: -.5rem
  }

  .swagger-ui .nt3-ns {
    margin-top: -1rem
  }

  .swagger-ui .nt4-ns {
    margin-top: -2rem
  }

  .swagger-ui .nt5-ns {
    margin-top: -4rem
  }

  .swagger-ui .nt6-ns {
    margin-top: -8rem
  }

  .swagger-ui .nt7-ns {
    margin-top: -16rem
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .na1-m {
    margin: -.25rem
  }

  .swagger-ui .na2-m {
    margin: -.5rem
  }

  .swagger-ui .na3-m {
    margin: -1rem
  }

  .swagger-ui .na4-m {
    margin: -2rem
  }

  .swagger-ui .na5-m {
    margin: -4rem
  }

  .swagger-ui .na6-m {
    margin: -8rem
  }

  .swagger-ui .na7-m {
    margin: -16rem
  }

  .swagger-ui .nl1-m {
    margin-left: -.25rem
  }

  .swagger-ui .nl2-m {
    margin-left: -.5rem
  }

  .swagger-ui .nl3-m {
    margin-left: -1rem
  }

  .swagger-ui .nl4-m {
    margin-left: -2rem
  }

  .swagger-ui .nl5-m {
    margin-left: -4rem
  }

  .swagger-ui .nl6-m {
    margin-left: -8rem
  }

  .swagger-ui .nl7-m {
    margin-left: -16rem
  }

  .swagger-ui .nr1-m {
    margin-right: -.25rem
  }

  .swagger-ui .nr2-m {
    margin-right: -.5rem
  }

  .swagger-ui .nr3-m {
    margin-right: -1rem
  }

  .swagger-ui .nr4-m {
    margin-right: -2rem
  }

  .swagger-ui .nr5-m {
    margin-right: -4rem
  }

  .swagger-ui .nr6-m {
    margin-right: -8rem
  }

  .swagger-ui .nr7-m {
    margin-right: -16rem
  }

  .swagger-ui .nb1-m {
    margin-bottom: -.25rem
  }

  .swagger-ui .nb2-m {
    margin-bottom: -.5rem
  }

  .swagger-ui .nb3-m {
    margin-bottom: -1rem
  }

  .swagger-ui .nb4-m {
    margin-bottom: -2rem
  }

  .swagger-ui .nb5-m {
    margin-bottom: -4rem
  }

  .swagger-ui .nb6-m {
    margin-bottom: -8rem
  }

  .swagger-ui .nb7-m {
    margin-bottom: -16rem
  }

  .swagger-ui .nt1-m {
    margin-top: -.25rem
  }

  .swagger-ui .nt2-m {
    margin-top: -.5rem
  }

  .swagger-ui .nt3-m {
    margin-top: -1rem
  }

  .swagger-ui .nt4-m {
    margin-top: -2rem
  }

  .swagger-ui .nt5-m {
    margin-top: -4rem
  }

  .swagger-ui .nt6-m {
    margin-top: -8rem
  }

  .swagger-ui .nt7-m {
    margin-top: -16rem
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .na1-l {
    margin: -.25rem
  }

  .swagger-ui .na2-l {
    margin: -.5rem
  }

  .swagger-ui .na3-l {
    margin: -1rem
  }

  .swagger-ui .na4-l {
    margin: -2rem
  }

  .swagger-ui .na5-l {
    margin: -4rem
  }

  .swagger-ui .na6-l {
    margin: -8rem
  }

  .swagger-ui .na7-l {
    margin: -16rem
  }

  .swagger-ui .nl1-l {
    margin-left: -.25rem
  }

  .swagger-ui .nl2-l {
    margin-left: -.5rem
  }

  .swagger-ui .nl3-l {
    margin-left: -1rem
  }

  .swagger-ui .nl4-l {
    margin-left: -2rem
  }

  .swagger-ui .nl5-l {
    margin-left: -4rem
  }

  .swagger-ui .nl6-l {
    margin-left: -8rem
  }

  .swagger-ui .nl7-l {
    margin-left: -16rem
  }

  .swagger-ui .nr1-l {
    margin-right: -.25rem
  }

  .swagger-ui .nr2-l {
    margin-right: -.5rem
  }

  .swagger-ui .nr3-l {
    margin-right: -1rem
  }

  .swagger-ui .nr4-l {
    margin-right: -2rem
  }

  .swagger-ui .nr5-l {
    margin-right: -4rem
  }

  .swagger-ui .nr6-l {
    margin-right: -8rem
  }

  .swagger-ui .nr7-l {
    margin-right: -16rem
  }

  .swagger-ui .nb1-l {
    margin-bottom: -.25rem
  }

  .swagger-ui .nb2-l {
    margin-bottom: -.5rem
  }

  .swagger-ui .nb3-l {
    margin-bottom: -1rem
  }

  .swagger-ui .nb4-l {
    margin-bottom: -2rem
  }

  .swagger-ui .nb5-l {
    margin-bottom: -4rem
  }

  .swagger-ui .nb6-l {
    margin-bottom: -8rem
  }

  .swagger-ui .nb7-l {
    margin-bottom: -16rem
  }

  .swagger-ui .nt1-l {
    margin-top: -.25rem
  }

  .swagger-ui .nt2-l {
    margin-top: -.5rem
  }

  .swagger-ui .nt3-l {
    margin-top: -1rem
  }

  .swagger-ui .nt4-l {
    margin-top: -2rem
  }

  .swagger-ui .nt5-l {
    margin-top: -4rem
  }

  .swagger-ui .nt6-l {
    margin-top: -8rem
  }

  .swagger-ui .nt7-l {
    margin-top: -16rem
  }
}

.swagger-ui .collapse {
  border-collapse: collapse;
  border-spacing: 0
}

.swagger-ui .striped--light-silver:nth-child(odd) {
  background-color: #aaa
}

.swagger-ui .striped--moon-gray:nth-child(odd) {
  background-color: #ccc
}

.swagger-ui .striped--light-gray:nth-child(odd) {
  background-color: #eee
}

.swagger-ui .striped--near-white:nth-child(odd) {
  background-color: #f4f4f4
}

.swagger-ui .stripe-light:nth-child(odd) {
  background-color: hsla(0, 0%, 100%, .1)
}

.swagger-ui .stripe-dark:nth-child(odd) {
  background-color: rgba(0, 0, 0, .1)
}

.swagger-ui .strike {
  text-decoration: line-through
}

.swagger-ui .underline {
  text-decoration: underline
}

.swagger-ui .no-underline {
  text-decoration: none
}

@media screen and (min-width: 30em) {
  .swagger-ui .strike-ns {
    text-decoration: line-through
  }

  .swagger-ui .underline-ns {
    text-decoration: underline
  }

  .swagger-ui .no-underline-ns {
    text-decoration: none
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .strike-m {
    text-decoration: line-through
  }

  .swagger-ui .underline-m {
    text-decoration: underline
  }

  .swagger-ui .no-underline-m {
    text-decoration: none
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .strike-l {
    text-decoration: line-through
  }

  .swagger-ui .underline-l {
    text-decoration: underline
  }

  .swagger-ui .no-underline-l {
    text-decoration: none
  }
}

.swagger-ui .tl {
  text-align: left
}

.swagger-ui .tr {
  text-align: right
}

.swagger-ui .tc {
  text-align: center
}

@media screen and (min-width: 30em) {
  .swagger-ui .tl-ns {
    text-align: left
  }

  .swagger-ui .tr-ns {
    text-align: right
  }

  .swagger-ui .tc-ns {
    text-align: center
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .tl-m {
    text-align: left
  }

  .swagger-ui .tr-m {
    text-align: right
  }

  .swagger-ui .tc-m {
    text-align: center
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .tl-l {
    text-align: left
  }

  .swagger-ui .tr-l {
    text-align: right
  }

  .swagger-ui .tc-l {
    text-align: center
  }
}

.swagger-ui .ttc {
  text-transform: capitalize
}

.swagger-ui .ttl {
  text-transform: lowercase
}

.swagger-ui .ttu {
  text-transform: uppercase
}

.swagger-ui .ttn {
  text-transform: none
}

@media screen and (min-width: 30em) {
  .swagger-ui .ttc-ns {
    text-transform: capitalize
  }

  .swagger-ui .ttl-ns {
    text-transform: lowercase
  }

  .swagger-ui .ttu-ns {
    text-transform: uppercase
  }

  .swagger-ui .ttn-ns {
    text-transform: none
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .ttc-m {
    text-transform: capitalize
  }

  .swagger-ui .ttl-m {
    text-transform: lowercase
  }

  .swagger-ui .ttu-m {
    text-transform: uppercase
  }

  .swagger-ui .ttn-m {
    text-transform: none
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .ttc-l {
    text-transform: capitalize
  }

  .swagger-ui .ttl-l {
    text-transform: lowercase
  }

  .swagger-ui .ttu-l {
    text-transform: uppercase
  }

  .swagger-ui .ttn-l {
    text-transform: none
  }
}

.swagger-ui .f-6, .swagger-ui .f-headline {
  font-size: 6rem
}

.swagger-ui .f-5, .swagger-ui .f-subheadline {
  font-size: 5rem
}

.swagger-ui .f1 {
  font-size: 3rem
}

.swagger-ui .f2 {
  font-size: 2.25rem
}

.swagger-ui .f3 {
  font-size: 1.5rem
}

.swagger-ui .f4 {
  font-size: 1.25rem
}

.swagger-ui .f5 {
  font-size: 1rem
}

.swagger-ui .f6 {
  font-size: .875rem
}

.swagger-ui .f7 {
  font-size: .75rem
}

@media screen and (min-width: 30em) {
  .swagger-ui .f-6-ns, .swagger-ui .f-headline-ns {
    font-size: 6rem
  }

  .swagger-ui .f-5-ns, .swagger-ui .f-subheadline-ns {
    font-size: 5rem
  }

  .swagger-ui .f1-ns {
    font-size: 3rem
  }

  .swagger-ui .f2-ns {
    font-size: 2.25rem
  }

  .swagger-ui .f3-ns {
    font-size: 1.5rem
  }

  .swagger-ui .f4-ns {
    font-size: 1.25rem
  }

  .swagger-ui .f5-ns {
    font-size: 1rem
  }

  .swagger-ui .f6-ns {
    font-size: .875rem
  }

  .swagger-ui .f7-ns {
    font-size: .75rem
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .f-6-m, .swagger-ui .f-headline-m {
    font-size: 6rem
  }

  .swagger-ui .f-5-m, .swagger-ui .f-subheadline-m {
    font-size: 5rem
  }

  .swagger-ui .f1-m {
    font-size: 3rem
  }

  .swagger-ui .f2-m {
    font-size: 2.25rem
  }

  .swagger-ui .f3-m {
    font-size: 1.5rem
  }

  .swagger-ui .f4-m {
    font-size: 1.25rem
  }

  .swagger-ui .f5-m {
    font-size: 1rem
  }

  .swagger-ui .f6-m {
    font-size: .875rem
  }

  .swagger-ui .f7-m {
    font-size: .75rem
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .f-6-l, .swagger-ui .f-headline-l {
    font-size: 6rem
  }

  .swagger-ui .f-5-l, .swagger-ui .f-subheadline-l {
    font-size: 5rem
  }

  .swagger-ui .f1-l {
    font-size: 3rem
  }

  .swagger-ui .f2-l {
    font-size: 2.25rem
  }

  .swagger-ui .f3-l {
    font-size: 1.5rem
  }

  .swagger-ui .f4-l {
    font-size: 1.25rem
  }

  .swagger-ui .f5-l {
    font-size: 1rem
  }

  .swagger-ui .f6-l {
    font-size: .875rem
  }

  .swagger-ui .f7-l {
    font-size: .75rem
  }
}

.swagger-ui .measure {
  max-width: 30em
}

.swagger-ui .measure-wide {
  max-width: 34em
}

.swagger-ui .measure-narrow {
  max-width: 20em
}

.swagger-ui .indent {
  text-indent: 1em;
  margin-top: 0;
  margin-bottom: 0
}

.swagger-ui .small-caps {
  font-variant: small-caps
}

.swagger-ui .truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

@media screen and (min-width: 30em) {
  .swagger-ui .measure-ns {
    max-width: 30em
  }

  .swagger-ui .measure-wide-ns {
    max-width: 34em
  }

  .swagger-ui .measure-narrow-ns {
    max-width: 20em
  }

  .swagger-ui .indent-ns {
    text-indent: 1em;
    margin-top: 0;
    margin-bottom: 0
  }

  .swagger-ui .small-caps-ns {
    font-variant: small-caps
  }

  .swagger-ui .truncate-ns {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .measure-m {
    max-width: 30em
  }

  .swagger-ui .measure-wide-m {
    max-width: 34em
  }

  .swagger-ui .measure-narrow-m {
    max-width: 20em
  }

  .swagger-ui .indent-m {
    text-indent: 1em;
    margin-top: 0;
    margin-bottom: 0
  }

  .swagger-ui .small-caps-m {
    font-variant: small-caps
  }

  .swagger-ui .truncate-m {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .measure-l {
    max-width: 30em
  }

  .swagger-ui .measure-wide-l {
    max-width: 34em
  }

  .swagger-ui .measure-narrow-l {
    max-width: 20em
  }

  .swagger-ui .indent-l {
    text-indent: 1em;
    margin-top: 0;
    margin-bottom: 0
  }

  .swagger-ui .small-caps-l {
    font-variant: small-caps
  }

  .swagger-ui .truncate-l {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
  }
}

.swagger-ui .overflow-container {
  overflow-y: scroll
}

.swagger-ui .center {
  margin-right: auto;
  margin-left: auto
}

.swagger-ui .mr-auto {
  margin-right: auto
}

.swagger-ui .ml-auto {
  margin-left: auto
}

@media screen and (min-width: 30em) {
  .swagger-ui .center-ns {
    margin-right: auto;
    margin-left: auto
  }

  .swagger-ui .mr-auto-ns {
    margin-right: auto
  }

  .swagger-ui .ml-auto-ns {
    margin-left: auto
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .center-m {
    margin-right: auto;
    margin-left: auto
  }

  .swagger-ui .mr-auto-m {
    margin-right: auto
  }

  .swagger-ui .ml-auto-m {
    margin-left: auto
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .center-l {
    margin-right: auto;
    margin-left: auto
  }

  .swagger-ui .mr-auto-l {
    margin-right: auto
  }

  .swagger-ui .ml-auto-l {
    margin-left: auto
  }
}

.swagger-ui .clip {
  position: fixed !important;
  _position: absolute !important;
  clip: rect(1px 1px 1px 1px);
  clip: rect(1px, 1px, 1px, 1px)
}

@media screen and (min-width: 30em) {
  .swagger-ui .clip-ns {
    position: fixed !important;
    _position: absolute !important;
    clip: rect(1px 1px 1px 1px);
    clip: rect(1px, 1px, 1px, 1px)
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .clip-m {
    position: fixed !important;
    _position: absolute !important;
    clip: rect(1px 1px 1px 1px);
    clip: rect(1px, 1px, 1px, 1px)
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .clip-l {
    position: fixed !important;
    _position: absolute !important;
    clip: rect(1px 1px 1px 1px);
    clip: rect(1px, 1px, 1px, 1px)
  }
}

.swagger-ui .ws-normal {
  white-space: normal
}

.swagger-ui .nowrap {
  white-space: nowrap
}

.swagger-ui .pre {
  white-space: pre
}

@media screen and (min-width: 30em) {
  .swagger-ui .ws-normal-ns {
    white-space: normal
  }

  .swagger-ui .nowrap-ns {
    white-space: nowrap
  }

  .swagger-ui .pre-ns {
    white-space: pre
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .ws-normal-m {
    white-space: normal
  }

  .swagger-ui .nowrap-m {
    white-space: nowrap
  }

  .swagger-ui .pre-m {
    white-space: pre
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .ws-normal-l {
    white-space: normal
  }

  .swagger-ui .nowrap-l {
    white-space: nowrap
  }

  .swagger-ui .pre-l {
    white-space: pre
  }
}

.swagger-ui .v-base {
  vertical-align: baseline
}

.swagger-ui .v-mid {
  vertical-align: middle
}

.swagger-ui .v-top {
  vertical-align: top
}

.swagger-ui .v-btm {
  vertical-align: bottom
}

@media screen and (min-width: 30em) {
  .swagger-ui .v-base-ns {
    vertical-align: baseline
  }

  .swagger-ui .v-mid-ns {
    vertical-align: middle
  }

  .swagger-ui .v-top-ns {
    vertical-align: top
  }

  .swagger-ui .v-btm-ns {
    vertical-align: bottom
  }
}

@media screen and (min-width: 30em) and (max-width: 60em) {
  .swagger-ui .v-base-m {
    vertical-align: baseline
  }

  .swagger-ui .v-mid-m {
    vertical-align: middle
  }

  .swagger-ui .v-top-m {
    vertical-align: top
  }

  .swagger-ui .v-btm-m {
    vertical-align: bottom
  }
}

@media screen and (min-width: 60em) {
  .swagger-ui .v-base-l {
    vertical-align: baseline
  }

  .swagger-ui .v-mid-l {
    vertical-align: middle
  }

  .swagger-ui .v-top-l {
    vertical-align: top
  }

  .swagger-ui .v-btm-l {
    vertical-align: bottom
  }
}

.swagger-ui .dim {
  opacity: 1
}

.swagger-ui .dim, .swagger-ui .dim:focus, .swagger-ui .dim:hover {
  -webkit-transition: opacity .15s ease-in;
  transition: opacity .15s ease-in
}

.swagger-ui .dim:focus, .swagger-ui .dim:hover {
  opacity: .5
}

.swagger-ui .dim:active {
  opacity: .8;
  -webkit-transition: opacity .15s ease-out;
  transition: opacity .15s ease-out
}

.swagger-ui .glow, .swagger-ui .glow:focus, .swagger-ui .glow:hover {
  -webkit-transition: opacity .15s ease-in;
  transition: opacity .15s ease-in
}

.swagger-ui .glow:focus, .swagger-ui .glow:hover {
  opacity: 1
}

.swagger-ui .hide-child .child {
  opacity: 0;
  -webkit-transition: opacity .15s ease-in;
  transition: opacity .15s ease-in
}

.swagger-ui .hide-child:active .child, .swagger-ui .hide-child:focus .child, .swagger-ui .hide-child:hover .child {
  opacity: 1;
  -webkit-transition: opacity .15s ease-in;
  transition: opacity .15s ease-in
}

.swagger-ui .underline-hover:focus, .swagger-ui .underline-hover:hover {
  text-decoration: underline
}

.swagger-ui .grow {
  -moz-osx-font-smoothing: grayscale;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-transition: -webkit-transform .25s ease-out;
  transition: -webkit-transform .25s ease-out;
  transition: transform .25s ease-out;
  transition: transform .25s ease-out, -webkit-transform .25s ease-out
}

.swagger-ui .grow:focus, .swagger-ui .grow:hover {
  -webkit-transform: scale(1.05);
  transform: scale(1.05)
}

.swagger-ui .grow:active {
  -webkit-transform: scale(.9);
  transform: scale(.9)
}

.swagger-ui .grow-large {
  -moz-osx-font-smoothing: grayscale;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-transition: -webkit-transform .25s ease-in-out;
  transition: -webkit-transform .25s ease-in-out;
  transition: transform .25s ease-in-out;
  transition: transform .25s ease-in-out, -webkit-transform .25s ease-in-out
}

.swagger-ui .grow-large:focus, .swagger-ui .grow-large:hover {
  -webkit-transform: scale(1.2);
  transform: scale(1.2)
}

.swagger-ui .grow-large:active {
  -webkit-transform: scale(.95);
  transform: scale(.95)
}

.swagger-ui .pointer:hover {
  cursor: pointer
}

.swagger-ui .shadow-hover {
  cursor: pointer;
  position: relative;
  -webkit-transition: all .5s cubic-bezier(.165, .84, .44, 1);
  transition: all .5s cubic-bezier(.165, .84, .44, 1)
}

.swagger-ui .shadow-hover:after {
  content: "";
  -webkit-box-shadow: 0 0 16px 2px rgba(0, 0, 0, .2);
  box-shadow: 0 0 16px 2px rgba(0, 0, 0, .2);
  border-radius: inherit;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  -webkit-transition: opacity .5s cubic-bezier(.165, .84, .44, 1);
  transition: opacity .5s cubic-bezier(.165, .84, .44, 1)
}

.swagger-ui .shadow-hover:focus:after, .swagger-ui .shadow-hover:hover:after {
  opacity: 1
}

.swagger-ui .bg-animate, .swagger-ui .bg-animate:focus, .swagger-ui .bg-animate:hover {
  -webkit-transition: background-color .15s ease-in-out;
  transition: background-color .15s ease-in-out
}

.swagger-ui .z-0 {
  z-index: 0
}

.swagger-ui .z-1 {
  z-index: 1
}

.swagger-ui .z-2 {
  z-index: 2
}

.swagger-ui .z-3 {
  z-index: 3
}

.swagger-ui .z-4 {
  z-index: 4
}

.swagger-ui .z-5 {
  z-index: 5
}

.swagger-ui .z-999 {
  z-index: 999
}

.swagger-ui .z-9999 {
  z-index: 9999
}

.swagger-ui .z-max {
  z-index: 2147483647
}

.swagger-ui .z-inherit {
  z-index: inherit
}

.swagger-ui .z-initial {
  z-index: auto
}

.swagger-ui .z-unset {
  z-index: unset
}

.swagger-ui .nested-copy-line-height ol, .swagger-ui .nested-copy-line-height p, .swagger-ui .nested-copy-line-height ul {
  line-height: 1.5
}

.swagger-ui .nested-headline-line-height h1, .swagger-ui .nested-headline-line-height h2, .swagger-ui .nested-headline-line-height h3, .swagger-ui .nested-headline-line-height h4, .swagger-ui .nested-headline-line-height h5, .swagger-ui .nested-headline-line-height h6 {
  line-height: 1.25rem
}

.swagger-ui .nested-list-reset ol, .swagger-ui .nested-list-reset ul {
  padding-left: 0;
  margin-left: 0;
  list-style-type: none
}

.swagger-ui .nested-copy-indent p + p {
  text-indent: .1em;
  margin-top: 0;
  margin-bottom: 0
}

.swagger-ui .nested-copy-seperator p + p {
  margin-top: 1.5em
}

.swagger-ui .nested-img img {
  width: 100%;
  max-width: 100%;
  display: block
}

.swagger-ui .nested-links a {
  color: #357edd;
  -webkit-transition: color .15s ease-in;
  transition: color .15s ease-in
}

.swagger-ui .nested-links a:focus, .swagger-ui .nested-links a:hover {
  color: #96ccff;
  -webkit-transition: color .15s ease-in;
  transition: color .15s ease-in
}

.swagger-ui .wrapper {
  width: 100%;
  max-width: 1460px;
  margin: 0 auto;
  padding: 0 20px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

.swagger-ui .opblock-tag-section {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column
}

.swagger-ui .opblock-tag {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 20px 10px 10px;
  cursor: pointer;
  -webkit-transition: all .2s;
  transition: all .2s;
  border-bottom: 1px solid rgba(59, 65, 81, .3)
}

.swagger-ui .opblock-tag:hover {
  background: rgba(0, 0, 0, .02)
}

.swagger-ui .opblock-tag {
  font-size: 24px;
  margin: 0 0 5px;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .opblock-tag.no-desc span {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1
}

.swagger-ui .opblock-tag svg {
  -webkit-transition: all .4s;
  transition: all .4s
}

.swagger-ui .opblock-tag small {
  font-size: 14px;
  font-weight: 400;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding: 0 10px;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .parameter__type {
  font-size: 12px;
  padding: 5px 0;
  font-family: monospace;
  font-weight: 600;
  color: #3b4151
}

.swagger-ui .view-line-link {
  position: relative;
  top: 3px;
  width: 20px;
  margin: 0 5px;
  cursor: pointer;
  -webkit-transition: all .5s;
  transition: all .5s
}

.swagger-ui .opblock {
  margin: 0 0 15px;
  border: 1px solid #000;
  border-radius: 4px;
  -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, .19);
  box-shadow: 0 0 3px rgba(0, 0, 0, .19)
}

.swagger-ui .opblock .tab-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1
}

.swagger-ui .opblock .tab-header .tab-item {
  padding: 0 40px;
  cursor: pointer
}

.swagger-ui .opblock .tab-header .tab-item:first-of-type {
  padding: 0 40px 0 0
}

.swagger-ui .opblock .tab-header .tab-item.active h4 span {
  position: relative
}

.swagger-ui .opblock .tab-header .tab-item.active h4 span:after {
  position: absolute;
  bottom: -15px;
  left: 50%;
  width: 120%;
  height: 4px;
  content: "";
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  background: gray
}

.swagger-ui .opblock.is-open .opblock-summary {
  border-bottom: 1px solid #000
}

.swagger-ui .opblock .opblock-section-header {
  padding: 8px 20px;
  min-height: 50px;
  background: hsla(0, 0%, 100%, .8);
  -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, .1)
}

.swagger-ui .opblock .opblock-section-header, .swagger-ui .opblock .opblock-section-header label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.swagger-ui .opblock .opblock-section-header label {
  font-size: 12px;
  font-weight: 700;
  margin: 0;
  margin-left: auto;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .opblock .opblock-section-header label span {
  padding: 0 10px 0 0
}

.swagger-ui .opblock .opblock-section-header h4 {
  font-size: 14px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin: 0;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .opblock .opblock-summary-method {
  font-size: 14px;
  font-weight: 700;
  min-width: 80px;
  padding: 6px 15px;
  text-align: center;
  border-radius: 3px;
  background: #000;
  text-shadow: 0 1px 0 rgba(0, 0, 0, .1);
  font-family: sans-serif;
  color: #fff
}

.swagger-ui .opblock .opblock-summary-operation-id, .swagger-ui .opblock .opblock-summary-path, .swagger-ui .opblock .opblock-summary-path__deprecated {
  font-size: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
  -ms-flex: 0 3 auto;
  flex: 0 3 auto;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  word-break: break-all;
  padding: 0 10px;
  font-family: monospace;
  font-weight: 600;
  color: #3b4151
}

@media (max-width: 768px) {
  .swagger-ui .opblock .opblock-summary-operation-id, .swagger-ui .opblock .opblock-summary-path, .swagger-ui .opblock .opblock-summary-path__deprecated {
    font-size: 12px
  }
}

.swagger-ui .opblock .opblock-summary-path__deprecated {
  text-decoration: line-through
}

.swagger-ui .opblock .opblock-summary-operation-id {
  font-size: 14px
}

.swagger-ui .opblock .opblock-summary-description {
  font-size: 13px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .opblock .opblock-summary {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 5px;
  cursor: pointer
}

.swagger-ui .opblock .opblock-summary .view-line-link {
  position: relative;
  top: 2px;
  width: 0;
  margin: 0;
  cursor: pointer;
  -webkit-transition: all .5s;
  transition: all .5s
}

.swagger-ui .opblock .opblock-summary:hover .view-line-link {
  width: 18px;
  margin: 0 5px
}

.swagger-ui .opblock.opblock-post {
  border-color: #49cc90;
  background: rgba(73, 204, 144, .1)
}

.swagger-ui .opblock.opblock-post .opblock-summary-method {
  background: #49cc90
}

.swagger-ui .opblock.opblock-post .opblock-summary {
  border-color: #49cc90
}

.swagger-ui .opblock.opblock-post .tab-header .tab-item.active h4 span:after {
  background: #49cc90
}

.swagger-ui .opblock.opblock-put {
  border-color: #fca130;
  background: rgba(252, 161, 48, .1)
}

.swagger-ui .opblock.opblock-put .opblock-summary-method {
  background: #fca130
}

.swagger-ui .opblock.opblock-put .opblock-summary {
  border-color: #fca130
}

.swagger-ui .opblock.opblock-put .tab-header .tab-item.active h4 span:after {
  background: #fca130
}

.swagger-ui .opblock.opblock-delete {
  border-color: #f93e3e;
  background: rgba(249, 62, 62, .1)
}

.swagger-ui .opblock.opblock-delete .opblock-summary-method {
  background: #f93e3e
}

.swagger-ui .opblock.opblock-delete .opblock-summary {
  border-color: #f93e3e
}

.swagger-ui .opblock.opblock-delete .tab-header .tab-item.active h4 span:after {
  background: #f93e3e
}

.swagger-ui .opblock.opblock-get {
  border-color: #61affe;
  background: rgba(97, 175, 254, .1)
}

.swagger-ui .opblock.opblock-get .opblock-summary-method {
  background: #61affe
}

.swagger-ui .opblock.opblock-get .opblock-summary {
  border-color: #61affe
}

.swagger-ui .opblock.opblock-get .tab-header .tab-item.active h4 span:after {
  background: #61affe
}

.swagger-ui .opblock.opblock-patch {
  border-color: #50e3c2;
  background: rgba(80, 227, 194, .1)
}

.swagger-ui .opblock.opblock-patch .opblock-summary-method {
  background: #50e3c2
}

.swagger-ui .opblock.opblock-patch .opblock-summary {
  border-color: #50e3c2
}

.swagger-ui .opblock.opblock-patch .tab-header .tab-item.active h4 span:after {
  background: #50e3c2
}

.swagger-ui .opblock.opblock-head {
  border-color: #9012fe;
  background: rgba(144, 18, 254, .1)
}

.swagger-ui .opblock.opblock-head .opblock-summary-method {
  background: #9012fe
}

.swagger-ui .opblock.opblock-head .opblock-summary {
  border-color: #9012fe
}

.swagger-ui .opblock.opblock-head .tab-header .tab-item.active h4 span:after {
  background: #9012fe
}

.swagger-ui .opblock.opblock-options {
  border-color: #0d5aa7;
  background: rgba(13, 90, 167, .1)
}

.swagger-ui .opblock.opblock-options .opblock-summary-method {
  background: #0d5aa7
}

.swagger-ui .opblock.opblock-options .opblock-summary {
  border-color: #0d5aa7
}

.swagger-ui .opblock.opblock-options .tab-header .tab-item.active h4 span:after {
  background: #0d5aa7
}

.swagger-ui .opblock.opblock-deprecated {
  opacity: .6;
  border-color: #ebebeb;
  background: hsla(0, 0%, 92%, .1)
}

.swagger-ui .opblock.opblock-deprecated .opblock-summary-method {
  background: #ebebeb
}

.swagger-ui .opblock.opblock-deprecated .opblock-summary {
  border-color: #ebebeb
}

.swagger-ui .opblock.opblock-deprecated .tab-header .tab-item.active h4 span:after {
  background: #ebebeb
}

.swagger-ui .opblock .opblock-schemes {
  padding: 8px 20px
}

.swagger-ui .opblock .opblock-schemes .schemes-title {
  padding: 0 10px 0 0
}

.swagger-ui .filter .operation-filter-input {
  width: 100%;
  margin: 20px 0;
  padding: 10px;
  border: 2px solid #d8dde7
}

.swagger-ui .tab {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 20px 0 10px;
  padding: 0;
  list-style: none
}

.swagger-ui .tab li {
  font-size: 12px;
  min-width: 60px;
  padding: 0;
  cursor: pointer;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .tab li:first-of-type {
  position: relative;
  padding-left: 0;
  padding-right: 12px
}

.swagger-ui .tab li:first-of-type:after {
  position: absolute;
  top: 0;
  right: 6px;
  width: 1px;
  height: 100%;
  content: "";
  background: rgba(0, 0, 0, .2)
}

.swagger-ui .tab li.active {
  font-weight: 700
}

.swagger-ui .opblock-description-wrapper, .swagger-ui .opblock-external-docs-wrapper, .swagger-ui .opblock-title_normal {
  font-size: 12px;
  margin: 0 0 5px;
  padding: 15px 20px;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .opblock-description-wrapper h4, .swagger-ui .opblock-external-docs-wrapper h4, .swagger-ui .opblock-title_normal h4 {
  font-size: 12px;
  margin: 0 0 5px;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .opblock-description-wrapper p, .swagger-ui .opblock-external-docs-wrapper p, .swagger-ui .opblock-title_normal p {
  font-size: 14px;
  margin: 0;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .opblock-external-docs-wrapper h4 {
  padding-left: 0
}

.swagger-ui .execute-wrapper {
  padding: 20px;
  text-align: right
}

.swagger-ui .execute-wrapper .btn {
  width: 100%;
  padding: 8px 40px
}

.swagger-ui .body-param-options {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column
}

.swagger-ui .body-param-options .body-param-edit {
  padding: 10px 0
}

.swagger-ui .body-param-options label {
  padding: 8px 0
}

.swagger-ui .body-param-options label select {
  margin: 3px 0 0
}

.swagger-ui .responses-inner {
  padding: 20px
}

.swagger-ui .responses-inner h4, .swagger-ui .responses-inner h5 {
  font-size: 12px;
  margin: 10px 0 5px;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .response-col_status {
  font-size: 14px;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .response-col_status .response-undocumented {
  font-size: 11px;
  font-family: monospace;
  font-weight: 600;
  color: #909090
}

.swagger-ui .response-col_links {
  padding-left: 2em;
  max-width: 40em;
  font-size: 14px;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .response-col_links .response-undocumented {
  font-size: 11px;
  font-family: monospace;
  font-weight: 600;
  color: #909090
}

.swagger-ui .response-col_description__inner div.markdown, .swagger-ui .response-col_description__inner div.renderedMarkdown {
  font-size: 12px;
  font-style: italic;
  display: block;
  margin: 0;
  padding: 10px;
  border-radius: 4px;
  background: #41444e;
  font-family: monospace;
  font-weight: 600;
  color: #fff
}

.swagger-ui .response-col_description__inner div.markdown p, .swagger-ui .response-col_description__inner div.renderedMarkdown p {
  margin: 0;
  font-family: monospace;
  font-weight: 600;
  color: #fff
}

.swagger-ui .response-col_description__inner div.markdown a, .swagger-ui .response-col_description__inner div.renderedMarkdown a {
  font-family: monospace;
  font-weight: 600;
  color: #89bf04;
  text-decoration: underline
}

.swagger-ui .response-col_description__inner div.markdown a:hover, .swagger-ui .response-col_description__inner div.renderedMarkdown a:hover {
  color: #81b10c
}

.swagger-ui .response-col_description__inner div.markdown th, .swagger-ui .response-col_description__inner div.renderedMarkdown th {
  font-family: monospace;
  font-weight: 600;
  color: #fff;
  border-bottom: 1px solid #fff
}

.swagger-ui .opblock-body .opblock-loading-animation {
  display: block;
  margin: 3em;
  margin-left: auto;
  margin-right: auto
}

.swagger-ui .opblock-body pre {
  font-size: 12px;
  margin: 0;
  padding: 10px;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  word-break: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  hyphens: auto;
  border-radius: 4px;
  background: #41444e;
  overflow-wrap: break-word;
  font-family: monospace;
  font-weight: 600;
  color: #fff
}

.swagger-ui .opblock-body pre span {
  color: #fff !important
}

.swagger-ui .opblock-body pre .headerline {
  display: block
}

.swagger-ui .highlight-code {
  position: relative
}

.swagger-ui .highlight-code > .microlight {
  overflow-y: auto;
  max-height: 400px;
  min-height: 6em
}

.swagger-ui .download-contents {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: pointer;
  background: #7d8293;
  text-align: center;
  padding: 5px;
  border-radius: 4px;
  font-family: Titillium Web, sans-serif;
  font-weight: 600;
  color: #fff;
  font-size: 14px;
  height: 30px;
  width: 75px
}

.swagger-ui .scheme-container {
  margin: 0 0 20px;
  padding: 30px 0;
  background: #fff;
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .15);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .15)
}

.swagger-ui .scheme-container .schemes {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.swagger-ui .scheme-container .schemes > label {
  font-size: 12px;
  font-weight: 700;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: -20px 15px 0 0;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .scheme-container .schemes > label select {
  min-width: 130px;
  text-transform: uppercase
}

.swagger-ui .loading-container {
  padding: 40px 0 60px;
  margin-top: 1em;
  min-height: 1px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column
}

.swagger-ui .loading-container .loading {
  position: relative
}

.swagger-ui .loading-container .loading:after {
  font-size: 10px;
  font-weight: 700;
  position: absolute;
  top: 50%;
  left: 50%;
  content: "loading";
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-transform: uppercase;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .loading-container .loading:before {
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  width: 60px;
  height: 60px;
  margin: -30px;
  content: "";
  -webkit-animation: rotation 1s infinite linear, opacity .5s;
  animation: rotation 1s infinite linear, opacity .5s;
  opacity: 1;
  border: 2px solid rgba(85, 85, 85, .1);
  border-top-color: rgba(0, 0, 0, .6);
  border-radius: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden
}

@-webkit-keyframes rotation {
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn)
  }
}

@keyframes rotation {
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn)
  }
}

.swagger-ui .response-content-type {
  padding-top: 1em
}

.swagger-ui .response-content-type.controls-accept-header select {
  border-color: green
}

.swagger-ui .response-content-type.controls-accept-header small {
  color: green;
  font-size: .7em
}

@-webkit-keyframes blinker {
  50% {
    opacity: 0
  }
}

@keyframes blinker {
  50% {
    opacity: 0
  }
}

.swagger-ui section h3 {
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui a.nostyle {
  display: inline
}

.swagger-ui a.nostyle, .swagger-ui a.nostyle:visited {
  text-decoration: inherit;
  color: inherit;
  cursor: pointer
}

.swagger-ui .version-pragma {
  height: 100%;
  padding: 5em 0
}

.swagger-ui .version-pragma__message {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100%;
  font-size: 1.2em;
  text-align: center;
  line-height: 1.5em;
  padding: 0 .6em
}

.swagger-ui .version-pragma__message > div {
  max-width: 55ch;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1
}

.swagger-ui .version-pragma__message code {
  background-color: #dedede;
  padding: 4px 4px 2px;
  white-space: pre
}

.swagger-ui .btn {
  font-size: 14px;
  font-weight: 700;
  padding: 5px 23px;
  -webkit-transition: all .3s;
  transition: all .3s;
  border: 2px solid gray;
  border-radius: 4px;
  background: transparent;
  -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, .1);
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .btn.btn-sm {
  font-size: 12px;
  padding: 4px 23px
}

.swagger-ui .btn[disabled] {
  cursor: not-allowed;
  opacity: .3
}

.swagger-ui .btn:hover {
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3);
  box-shadow: 0 0 5px rgba(0, 0, 0, .3)
}

.swagger-ui .btn.cancel {
  border-color: #ff6060;
  background-color: transparent;
  font-family: sans-serif;
  color: #ff6060
}

.swagger-ui .btn.authorize {
  line-height: 1;
  display: inline;
  color: #49cc90;
  border-color: #49cc90;
  background-color: transparent
}

.swagger-ui .btn.authorize span {
  float: left;
  padding: 4px 20px 0 0
}

.swagger-ui .btn.authorize svg {
  fill: #49cc90
}

.swagger-ui .btn.execute {
  background-color: #4990e2;
  color: #fff;
  border-color: #4990e2
}

.swagger-ui .btn-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 30px
}

.swagger-ui .btn-group .btn {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1
}

.swagger-ui .btn-group .btn:first-child {
  border-radius: 4px 0 0 4px
}

.swagger-ui .btn-group .btn:last-child {
  border-radius: 0 4px 4px 0
}

.swagger-ui .authorization__btn {
  padding: 0 10px;
  border: none;
  background: none
}

.swagger-ui .authorization__btn.locked {
  opacity: 1
}

.swagger-ui .authorization__btn.unlocked {
  opacity: .4
}

.swagger-ui .expand-methods, .swagger-ui .expand-operation {
  border: none;
  background: none
}

.swagger-ui .expand-methods svg, .swagger-ui .expand-operation svg {
  width: 20px;
  height: 20px
}

.swagger-ui .expand-methods {
  padding: 0 10px
}

.swagger-ui .expand-methods:hover svg {
  fill: #404040
}

.swagger-ui .expand-methods svg {
  -webkit-transition: all .3s;
  transition: all .3s;
  fill: #707070
}

.swagger-ui button {
  cursor: pointer;
  outline: none
}

.swagger-ui button.invalid {
  -webkit-animation: shake .4s 1;
  animation: shake .4s 1;
  border-color: #f93e3e;
  background: #feebeb
}

.swagger-ui select {
  font-size: 14px;
  font-weight: 700;
  padding: 5px 40px 5px 10px;
  border: 2px solid #41444e;
  border-radius: 4px;
  background: #f7f7f7 url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAyMCI+ICAgIDxwYXRoIGQ9Ik0xMy40MTggNy44NTljLjI3MS0uMjY4LjcwOS0uMjY4Ljk3OCAwIC4yNy4yNjguMjcyLjcwMSAwIC45NjlsLTMuOTA4IDMuODNjLS4yNy4yNjgtLjcwNy4yNjgtLjk3OSAwbC0zLjkwOC0zLjgzYy0uMjctLjI2Ny0uMjctLjcwMSAwLS45NjkuMjcxLS4yNjguNzA5LS4yNjguOTc4IDBMMTAgMTFsMy40MTgtMy4xNDF6Ii8+PC9zdmc+) right 10px center no-repeat;
  background-size: 20px;
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .25);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .25);
  font-family: sans-serif;
  color: #3b4151;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none
}

.swagger-ui select[multiple] {
  margin: 5px 0;
  padding: 5px;
  background: #f7f7f7
}

.swagger-ui select.invalid {
  -webkit-animation: shake .4s 1;
  animation: shake .4s 1;
  border-color: #f93e3e;
  background: #feebeb
}

.swagger-ui .opblock-body select {
  min-width: 230px
}

@media (max-width: 768px) {
  .swagger-ui .opblock-body select {
    min-width: 180px
  }
}

.swagger-ui label {
  font-size: 12px;
  font-weight: 700;
  margin: 0 0 5px;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui input[type=email], .swagger-ui input[type=file], .swagger-ui input[type=password], .swagger-ui input[type=search], .swagger-ui input[type=text], .swagger-ui textarea {
  min-width: 100px;
  margin: 5px 0;
  padding: 8px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff
}

@media (max-width: 768px) {
  .swagger-ui input[type=email], .swagger-ui input[type=file], .swagger-ui input[type=password], .swagger-ui input[type=search], .swagger-ui input[type=text], .swagger-ui textarea {
    max-width: 175px
  }
}

.swagger-ui input[type=email].invalid, .swagger-ui input[type=file].invalid, .swagger-ui input[type=password].invalid, .swagger-ui input[type=search].invalid, .swagger-ui input[type=text].invalid, .swagger-ui textarea.invalid {
  -webkit-animation: shake .4s 1;
  animation: shake .4s 1;
  border-color: #f93e3e;
  background: #feebeb
}

@-webkit-keyframes shake {
  10%, 90% {
    -webkit-transform: translate3d(-1px, 0, 0);
    transform: translate3d(-1px, 0, 0)
  }
  20%, 80% {
    -webkit-transform: translate3d(2px, 0, 0);
    transform: translate3d(2px, 0, 0)
  }
  30%, 50%, 70% {
    -webkit-transform: translate3d(-4px, 0, 0);
    transform: translate3d(-4px, 0, 0)
  }
  40%, 60% {
    -webkit-transform: translate3d(4px, 0, 0);
    transform: translate3d(4px, 0, 0)
  }
}

@keyframes shake {
  10%, 90% {
    -webkit-transform: translate3d(-1px, 0, 0);
    transform: translate3d(-1px, 0, 0)
  }
  20%, 80% {
    -webkit-transform: translate3d(2px, 0, 0);
    transform: translate3d(2px, 0, 0)
  }
  30%, 50%, 70% {
    -webkit-transform: translate3d(-4px, 0, 0);
    transform: translate3d(-4px, 0, 0)
  }
  40%, 60% {
    -webkit-transform: translate3d(4px, 0, 0);
    transform: translate3d(4px, 0, 0)
  }
}

.swagger-ui textarea {
  font-size: 12px;
  width: 100%;
  min-height: 280px;
  padding: 10px;
  border: none;
  border-radius: 4px;
  outline: none;
  background: hsla(0, 0%, 100%, .8);
  font-family: monospace;
  font-weight: 600;
  color: #3b4151
}

.swagger-ui textarea:focus {
  border: 2px solid #61affe
}

.swagger-ui textarea.curl {
  font-size: 12px;
  min-height: 100px;
  margin: 0;
  padding: 10px;
  resize: none;
  border-radius: 4px;
  background: #41444e;
  font-family: monospace;
  font-weight: 600;
  color: #fff
}

.swagger-ui .checkbox {
  padding: 5px 0 10px;
  -webkit-transition: opacity .5s;
  transition: opacity .5s;
  color: #303030
}

.swagger-ui .checkbox label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex
}

.swagger-ui .checkbox p {
  font-weight: 400 !important;
  font-style: italic;
  margin: 0 !important;
  font-family: monospace;
  font-weight: 600;
  color: #3b4151
}

.swagger-ui .checkbox input[type=checkbox] {
  display: none
}

.swagger-ui .checkbox input[type=checkbox] + label > .item {
  position: relative;
  top: 3px;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin: 0 8px 0 0;
  padding: 5px;
  cursor: pointer;
  border-radius: 1px;
  background: #e8e8e8;
  -webkit-box-shadow: 0 0 0 2px #e8e8e8;
  box-shadow: 0 0 0 2px #e8e8e8;
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none
}

.swagger-ui .checkbox input[type=checkbox] + label > .item:active {
  -webkit-transform: scale(.9);
  transform: scale(.9)
}

.swagger-ui .checkbox input[type=checkbox]:checked + label > .item {
  background: #e8e8e8 url("data:image/svg+xml;charset=utf-8,%3Csvg width='10' height='8' viewBox='3 7 10 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%2341474E' fill-rule='evenodd' d='M6.333 15L3 11.667l1.333-1.334 2 2L11.667 7 13 8.333z'/%3E%3C/svg%3E") 50% no-repeat
}

.swagger-ui .dialog-ux {
  position: fixed;
  z-index: 9999;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0
}

.swagger-ui .dialog-ux .backdrop-ux {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, .8)
}

.swagger-ui .dialog-ux .modal-ux {
  position: absolute;
  z-index: 9999;
  top: 50%;
  left: 50%;
  width: 100%;
  min-width: 300px;
  max-width: 650px;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  border: 1px solid #ebebeb;
  border-radius: 4px;
  background: #fff;
  -webkit-box-shadow: 0 10px 30px 0 rgba(0, 0, 0, .2);
  box-shadow: 0 10px 30px 0 rgba(0, 0, 0, .2)
}

.swagger-ui .dialog-ux .modal-ux-content {
  overflow-y: auto;
  max-height: 540px;
  padding: 20px
}

.swagger-ui .dialog-ux .modal-ux-content p {
  font-size: 12px;
  margin: 0 0 5px;
  color: #41444e;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .dialog-ux .modal-ux-content h4 {
  font-size: 18px;
  font-weight: 600;
  margin: 15px 0 0;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .dialog-ux .modal-ux-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #ebebeb;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.swagger-ui .dialog-ux .modal-ux-header .close-modal {
  padding: 0 10px;
  border: none;
  background: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none
}

.swagger-ui .dialog-ux .modal-ux-header h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  padding: 0 20px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .model {
  font-size: 12px;
  font-weight: 300;
  font-family: monospace;
  font-weight: 600;
  color: #3b4151
}

.swagger-ui .model .deprecated span, .swagger-ui .model .deprecated td {
  color: #a0a0a0 !important
}

.swagger-ui .model .deprecated > td:first-of-type {
  text-decoration: line-through
}

.swagger-ui .model-toggle {
  font-size: 10px;
  position: relative;
  top: 6px;
  display: inline-block;
  margin: auto .3em;
  cursor: pointer;
  -webkit-transition: -webkit-transform .15s ease-in;
  transition: -webkit-transform .15s ease-in;
  transition: transform .15s ease-in;
  transition: transform .15s ease-in, -webkit-transform .15s ease-in;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
  -webkit-transform-origin: 50% 50%;
  transform-origin: 50% 50%
}

.swagger-ui .model-toggle.collapsed {
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg)
}

.swagger-ui .model-toggle:after {
  display: block;
  width: 20px;
  height: 20px;
  content: "";
  background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z'/%3E%3C/svg%3E") 50% no-repeat;
  background-size: 100%
}

.swagger-ui .model-jump-to-path {
  position: relative;
  cursor: pointer
}

.swagger-ui .model-jump-to-path .view-line-link {
  position: absolute;
  top: -.4em;
  cursor: pointer
}

.swagger-ui .model-title {
  position: relative
}

.swagger-ui .model-title:hover .model-hint {
  visibility: visible
}

.swagger-ui .model-hint {
  position: absolute;
  top: -1.8em;
  visibility: hidden;
  padding: .1em .5em;
  white-space: nowrap;
  color: #ebebeb;
  border-radius: 4px;
  background: rgba(0, 0, 0, .7)
}

.swagger-ui .model p {
  margin: 0 0 1em
}

.swagger-ui section.models {
  margin: 30px 0;
  border: 1px solid rgba(59, 65, 81, .3);
  border-radius: 4px
}

.swagger-ui section.models.is-open {
  padding: 0 0 20px
}

.swagger-ui section.models.is-open h4 {
  margin: 0 0 5px;
  border-bottom: 1px solid rgba(59, 65, 81, .3)
}

.swagger-ui section.models h4 {
  font-size: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0;
  padding: 10px 20px 10px 10px;
  cursor: pointer;
  -webkit-transition: all .2s;
  transition: all .2s;
  font-family: sans-serif;
  color: #606060
}

.swagger-ui section.models h4 svg {
  -webkit-transition: all .4s;
  transition: all .4s
}

.swagger-ui section.models h4 span {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1
}

.swagger-ui section.models h4:hover {
  background: rgba(0, 0, 0, .02)
}

.swagger-ui section.models h5 {
  font-size: 16px;
  margin: 0 0 10px;
  font-family: sans-serif;
  color: #707070
}

.swagger-ui section.models .model-jump-to-path {
  position: relative;
  top: 5px
}

.swagger-ui section.models .model-container {
  margin: 0 20px 15px;
  position: relative;
  -webkit-transition: all .5s;
  transition: all .5s;
  border-radius: 4px;
  background: rgba(0, 0, 0, .05)
}

.swagger-ui section.models .model-container:hover {
  background: rgba(0, 0, 0, .07)
}

.swagger-ui section.models .model-container:first-of-type {
  margin: 20px
}

.swagger-ui section.models .model-container:last-of-type {
  margin: 0 20px
}

.swagger-ui section.models .model-container .models-jump-to-path {
  position: absolute;
  top: 8px;
  right: 5px;
  opacity: .65
}

.swagger-ui section.models .model-box {
  background: none
}

.swagger-ui .model-box {
  padding: 10px;
  display: inline-block;
  border-radius: 4px;
  background: rgba(0, 0, 0, .1)
}

.swagger-ui .model-box .model-jump-to-path {
  position: relative;
  top: 4px
}

.swagger-ui .model-box.deprecated {
  opacity: .5
}

.swagger-ui .model-title {
  font-size: 16px;
  font-family: sans-serif;
  color: #505050
}

.swagger-ui .model-deprecated-warning {
  font-size: 16px;
  font-weight: 600;
  margin-right: 1em;
  font-family: sans-serif;
  color: #f93e3e
}

.swagger-ui span > span.model .brace-close {
  padding: 0 0 0 10px
}

.swagger-ui .prop-name {
  display: inline-block;
  margin-right: 1em
}

.swagger-ui .prop-type {
  color: #55a
}

.swagger-ui .prop-enum {
  display: block
}

.swagger-ui .prop-format {
  color: #606060
}

.swagger-ui .servers > label {
  font-size: 12px;
  margin: -20px 15px 0 0;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .servers > label select {
  min-width: 130px;
  max-width: 100%
}

.swagger-ui .servers h4.message {
  padding-bottom: 2em
}

.swagger-ui .servers table tr {
  width: 30em
}

.swagger-ui .servers table td {
  display: inline-block;
  max-width: 15em;
  vertical-align: middle;
  padding-top: 10px;
  padding-bottom: 10px
}

.swagger-ui .servers table td:first-of-type {
  padding-right: 2em
}

.swagger-ui .servers table td input {
  width: 100%;
  height: 100%
}

.swagger-ui .servers .computed-url {
  margin: 2em 0
}

.swagger-ui .servers .computed-url code {
  display: inline-block;
  padding: 4px;
  font-size: 16px;
  margin: 0 1em
}

.swagger-ui .global-server-container {
  margin: 0 0 20px;
  padding: 30px 0;
  background: #fff;
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .15);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .15)
}

.swagger-ui .global-server-container .servers-title {
  line-height: 2em;
  font-weight: 700
}

.swagger-ui .operation-servers h4.message {
  margin-bottom: 2em
}

.swagger-ui table {
  width: 100%;
  padding: 0 10px;
  border-collapse: collapse
}

.swagger-ui table.model tbody tr td {
  padding: 0;
  vertical-align: top
}

.swagger-ui table.model tbody tr td:first-of-type {
  width: 174px;
  padding: 0 0 0 2em
}

.swagger-ui table.headers td {
  font-size: 12px;
  font-weight: 300;
  vertical-align: middle;
  font-family: monospace;
  font-weight: 600;
  color: #3b4151
}

.swagger-ui table tbody tr td {
  padding: 10px 0 0;
  vertical-align: top
}

.swagger-ui table tbody tr td:first-of-type {
  max-width: 20%;
  min-width: 6em;
  padding: 10px 0
}

.swagger-ui table thead tr td, .swagger-ui table thead tr th {
  font-size: 12px;
  font-weight: 700;
  padding: 12px 0;
  text-align: left;
  border-bottom: 1px solid rgba(59, 65, 81, .2);
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .parameters-col_description input[type=text] {
  width: 100%;
  max-width: 340px
}

.swagger-ui .parameters-col_description select {
  border-width: 1px
}

.swagger-ui .parameter__name {
  font-size: 16px;
  font-weight: 400;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .parameter__name.required {
  font-weight: 700
}

.swagger-ui .parameter__name.required:after {
  font-size: 10px;
  position: relative;
  top: -6px;
  padding: 5px;
  content: "required";
  color: rgba(255, 0, 0, .6)
}

.swagger-ui .parameter__extension, .swagger-ui .parameter__in {
  font-size: 12px;
  font-style: italic;
  font-family: monospace;
  font-weight: 600;
  color: gray
}

.swagger-ui .parameter__deprecated {
  font-size: 12px;
  font-style: italic;
  font-family: monospace;
  font-weight: 600;
  color: red
}

.swagger-ui .parameter__empty_value_toggle {
  font-size: 13px;
  padding-top: 5px;
  padding-bottom: 12px
}

.swagger-ui .parameter__empty_value_toggle input {
  margin-right: 7px
}

.swagger-ui .parameter__empty_value_toggle.disabled {
  opacity: .7
}

.swagger-ui .table-container {
  padding: 20px
}

.swagger-ui .topbar {
  padding: 8px 0;
  background-color: #89bf04
}

.swagger-ui .topbar .topbar-wrapper, .swagger-ui .topbar a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.swagger-ui .topbar a {
  font-size: 1.5em;
  font-weight: 700;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  max-width: 300px;
  text-decoration: none;
  font-family: sans-serif;
  color: #fff
}

.swagger-ui .topbar a span {
  margin: 0;
  padding: 0 10px
}

.swagger-ui .topbar .download-url-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 3;
  -ms-flex: 3;
  flex: 3;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end
}

.swagger-ui .topbar .download-url-wrapper input[type=text] {
  width: 100%;
  margin: 0;
  border: 2px solid #547f00;
  border-radius: 4px 0 0 4px;
  outline: none
}

.swagger-ui .topbar .download-url-wrapper .select-label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  max-width: 600px;
  margin: 0
}

.swagger-ui .topbar .download-url-wrapper .select-label span {
  font-size: 16px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding: 0 10px 0 0;
  text-align: right
}

.swagger-ui .topbar .download-url-wrapper .select-label select {
  -webkit-box-flex: 2;
  -ms-flex: 2;
  flex: 2;
  width: 100%;
  border: 2px solid #547f00;
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none
}

.swagger-ui .topbar .download-url-wrapper .download-url-button {
  font-size: 16px;
  font-weight: 700;
  padding: 4px 30px;
  border: none;
  border-radius: 0 4px 4px 0;
  background: #547f00;
  font-family: sans-serif;
  color: #fff
}

.swagger-ui .info {
  margin: 50px 0
}

.swagger-ui .info hgroup.main {
  margin: 0 0 20px
}

.swagger-ui .info hgroup.main a {
  font-size: 12px
}

.swagger-ui .info li, .swagger-ui .info p, .swagger-ui .info table {
  font-size: 14px;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .info h1, .swagger-ui .info h2, .swagger-ui .info h3, .swagger-ui .info h4, .swagger-ui .info h5 {
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .info code {
  padding: 3px 5px;
  border-radius: 4px;
  background: rgba(0, 0, 0, .05);
  font-family: monospace;
  font-weight: 600;
  color: #9012fe
}

.swagger-ui .info a {
  font-size: 14px;
  -webkit-transition: all .4s;
  transition: all .4s;
  font-family: sans-serif;
  color: #4990e2
}

.swagger-ui .info a:hover {
  color: #1f69c0
}

.swagger-ui .info > div {
  margin: 0 0 5px
}

.swagger-ui .info .base-url {
  font-size: 12px;
  font-weight: 300 !important;
  margin: 0;
  font-family: monospace;
  font-weight: 600;
  color: #3b4151
}

.swagger-ui .info .title {
  font-size: 36px;
  margin: 0;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .info .title small {
  font-size: 10px;
  position: relative;
  top: -5px;
  display: inline-block;
  margin: 0 0 0 5px;
  padding: 2px 4px;
  vertical-align: super;
  border-radius: 57px;
  background: #7d8492
}

.swagger-ui .info .title small pre {
  margin: 0;
  font-family: sans-serif;
  color: #fff
}

.swagger-ui .auth-btn-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 0;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center
}

.swagger-ui .auth-btn-wrapper .btn-done {
  margin-right: 1em
}

.swagger-ui .auth-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end
}

.swagger-ui .auth-wrapper .authorize {
  padding-right: 20px;
  margin-right: 10px
}

.swagger-ui .auth-container {
  margin: 0 0 10px;
  padding: 10px 20px;
  border-bottom: 1px solid #ebebeb
}

.swagger-ui .auth-container:last-of-type {
  margin: 0;
  padding: 10px 20px;
  border: 0
}

.swagger-ui .auth-container h4 {
  margin: 5px 0 15px !important
}

.swagger-ui .auth-container .wrapper {
  margin: 0;
  padding: 0
}

.swagger-ui .auth-container input[type=password], .swagger-ui .auth-container input[type=text] {
  min-width: 230px
}

.swagger-ui .auth-container .errors {
  font-size: 12px;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-weight: 600;
  color: #3b4151
}

.swagger-ui .scopes h2 {
  font-size: 14px;
  font-family: sans-serif;
  color: #3b4151
}

.swagger-ui .scope-def {
  padding: 0 0 20px
}

.swagger-ui .errors-wrapper {
  margin: 20px;
  padding: 10px 20px;
  -webkit-animation: scaleUp .5s;
  animation: scaleUp .5s;
  border: 2px solid #f93e3e;
  border-radius: 4px;
  background: rgba(249, 62, 62, .1)
}

.swagger-ui .errors-wrapper .error-wrapper {
  margin: 0 0 10px
}

.swagger-ui .errors-wrapper .errors h4 {
  font-size: 14px;
  margin: 0;
  font-family: monospace;
  font-weight: 600;
  color: #3b4151
}

.swagger-ui .errors-wrapper .errors small {
  color: #606060
}

.swagger-ui .errors-wrapper hgroup {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.swagger-ui .errors-wrapper hgroup h4 {
  font-size: 20px;
  margin: 0;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-family: sans-serif;
  color: #3b4151
}

@-webkit-keyframes scaleUp {
  0% {
    -webkit-transform: scale(.8);
    transform: scale(.8);
    opacity: 0
  }
  to {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1
  }
}

@keyframes scaleUp {
  0% {
    -webkit-transform: scale(.8);
    transform: scale(.8);
    opacity: 0
  }
  to {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1
  }
}

.swagger-ui .Resizer.vertical.disabled {
  display: none
}

/*# sourceMappingURL=swagger-ui.css.map*/