package com.facishare.er.biz.outrest.controller;

import com.facishare.enterprise.common.annotation.ControllerInfo;
import com.facishare.enterprise.common.annotation.OldController;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.er.biz.outrest.arg.BatchGetWxUrlByCustomerAppIdsOutArg;
import com.facishare.er.biz.outrest.arg.EnterpriseAccountOutArg;
import com.facishare.er.biz.outrest.arg.ListCustomerAppByEaAndWxServiceIdOutArg;
import com.facishare.linkapp.api.service.HttpLinkAppService;
import com.fxiaoke.enterpriserelation.result.CustomerAppWxServiceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/outapi/enterpriseCustomerAppRelation")
@Api(tags = {"互联应用"})
public class EnterpriseCustomerAppRelationOutController {
    @Autowired
    private HttpLinkAppService httpLinkAppService;

    @ApiOperation("一个企业购买客户互联时，在这里执行应用初始化逻辑")
    @RequestMapping(value = "/handleBoughtCustomerLinkEvent", method = RequestMethod.POST)
    public Result<Void> handleBoughtCustomerLinkEvent(@RequestBody EnterpriseAccountOutArg enterpriseAccountOutArg) {
        return Result.newSuccess();
    }

    @ControllerInfo(test = true)
    @ApiOperation("根据客户互联应用id获取相应的微信跳转地址")
    @OldController
    @RequestMapping(value = "/batchGetWxUrlByCustomerAppIds", method = RequestMethod.POST)
    public Result<Map<String, String>> batchGetWxUrlByCustomerAppIds(@RequestBody BatchGetWxUrlByCustomerAppIdsOutArg arg) {
        String ea = arg.getEa();
        Map<String, String> map = new HashMap<>();
        if (arg.getCustomerAppIds() != null) {
            for (String customerAppId : arg.getCustomerAppIds()) {
                String url = httpLinkAppService.getWechatServiceAppUrlByLinkAppIdAndUpstreamEa(customerAppId, ea).getData();
                if (url != null) {
                    map.put(customerAppId, url);
                }
            }
        }
        return Result.newSuccess(map);
    }

    @ControllerInfo(test = true)
    @ApiOperation("获取某个企业的某个服务号下所有打开的客户互联应用信息")
    @OldController
    @RequestMapping(value = "/listCustomerAppByEaAndWxServiceId", method = RequestMethod.POST)
    public Result<List<CustomerAppWxServiceVO>> listCustomerAppByEaAndWxServiceId(@RequestBody ListCustomerAppByEaAndWxServiceIdOutArg arg) {
        return Result.newError(ResultCode.NOT_SUPPORT_SPECIAL);
    }
}
