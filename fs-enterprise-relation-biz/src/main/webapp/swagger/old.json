{"swagger": "2.0", "paths": {"/enterpriseCard/updateEnterpriseCardProfileImage": {"post": {"tags": ["EnterpriseCardController"], "operationId": "updateEnterpriseCardProfileImage", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/TempProfileImageArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}, "/enterpriseCard/getEnterpriseCard": {"post": {"tags": ["EnterpriseCardController"], "operationId": "getEnterpriseCard", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultEnterpriseCard"}, "headers": {}}}}}, "/EnterpriseBizAdmin/IsAdministrator": {"post": {"tags": ["EnterpriseBizAdminAction"], "operationId": "isAdministrator", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/VerifyERAdminArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/VerifyERAdminResult"}, "headers": {}}}, "deprecated": true}}, "/upstreamService/listEnterpriseLinkApps": {"post": {"tags": ["UpstreamController"], "operationId": "listEnterpriseLinkApps", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BaseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListLinkAppAuthVo"}, "headers": {}}}}}, "/upstreamService/createEmployeeLinkAppRoles": {"post": {"tags": ["UpstreamController"], "operationId": "createEmployeeLinkAppRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpstreamEmployeeAppRelationsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListString"}, "headers": {}}}}}, "/upstreamService/listAdminsByLinkAppId": {"post": {"tags": ["UpstreamController"], "operationId": "listAdminsByLinkAppId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/LinkAppIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListUpstreamEmployeeVo"}, "headers": {}}}}}, "/upstreamService/listEmployeesByLinkAppIdAndRole": {"post": {"tags": ["UpstreamController"], "operationId": "listEmployeesByLinkAppIdAndRole", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/LinkAppIdAndRoleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListUpstreamEmployeeVo"}, "headers": {}}}}}, "/upstreamService/updateEmployeeLinkAppRoles": {"post": {"tags": ["UpstreamController"], "operationId": "updateEmployeeLinkAppRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpstreamEmployeeAppRelationsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}, "/upstreamService/deleteEmployeeLinkAppRoles": {"post": {"tags": ["UpstreamController"], "operationId": "deleteEmployeeLinkAppRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpstreamEmployeeAppRelationsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}, "/upstreamService/updateLinkAppAuthType": {"post": {"tags": ["UpstreamController"], "operationId": "updateLinkAppAuthType", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateLinkAppAuthTypeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultBoolean"}, "headers": {}}}}}, "/upstreamService/getLinkAppDataAuthPolicys": {"post": {"tags": ["UpstreamController"], "operationId": "getLinkAppDataAuthPolicys", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetLinkAppDataAuthPolicysArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListLinkAppDataAuthPolicyVo"}, "headers": {}}}}}, "/upstreamService/removeLinkAppDataAuthPolicy": {"post": {"tags": ["UpstreamController"], "operationId": "removeLinkAppDataAuthPolicy", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/RemoveLinkAppDataAuthPolicyArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultBoolean"}, "headers": {}}}}}, "/upstreamService/mergeLinkAppDataAuthPolicy": {"post": {"tags": ["UpstreamController"], "operationId": "mergeLinkAppDataAuthPolicy", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/MergeLinkAppDataAuthPolicyArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultBoolean"}, "headers": {}}}}}, "/upstreamService/updateEnterpriseLinkAppRelations": {"post": {"tags": ["UpstreamController"], "operationId": "updateEnterpriseLinkAppRelationStatus", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpstreamEnterpriseAppRelationsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}, "/unionAccountController/batchGetUnionAccountByOuterUIds": {"post": {"tags": ["UnionAccountController"], "operationId": "batchGetUnionAccountByOuterUIds", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchGetUnionAccountArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListUnionAccountVo"}, "headers": {}}}}}, "/customerAction/checkCustomerByName": {"post": {"tags": ["CustomerController"], "operationId": "checkCustomerByName", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CheckCustomerByNameArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultCustomerVo"}, "headers": {}}}}}, "/customerAction/listAllConnectedCustomerId": {"post": {"tags": ["CustomerController"], "operationId": "listAllConnectedCustomerId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultSetString"}, "headers": {}}}}}, "/customerAction/getAllCustomerUserDefinedField": {"post": {"tags": ["CustomerController"], "operationId": "getAllCustomerUserDefinedField", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListUserDefineDescriptionVo"}, "headers": {}}}}}, "/appRole/listAssigns": {"post": {"tags": ["AppInnerRoleController"], "operationId": "listRoleInnerAssigns", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListAppRoleAssignsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListInteger"}, "headers": {}}}}}, "/appRole/listAppRoles": {"post": {"tags": ["AppInnerRoleController"], "operationId": "listAppInnerRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppBaseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListRoleVo"}, "headers": {}}}}}, "/appRole/updateAssigns": {"post": {"tags": ["AppInnerRoleController"], "operationId": "batchUpdateRoleInnerAssigns", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchUpdateRoleInnerAssignsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/EmployeeCard/GetFriendEmployeeCard": {"post": {"tags": ["EmployeeCardAction"], "operationId": "getFriendEmployeeCard", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetEmployeeCardOfFriendArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/GetEmployeeCardOfFriendResult"}, "headers": {}}}}}, "/EmployeeCard/GetEmployeeCard": {"post": {"tags": ["EmployeeCardAction"], "operationId": "getEmployeeCard", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetEmployeeCardArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/GetEmployeeCardResult"}, "headers": {}}}}}, "/EmployeeCard/EditEmployeeCard": {"post": {"tags": ["EmployeeCardAction"], "operationId": "editEmployeeCard", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EditEmployeeCardArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object"}, "headers": {}}}}}, "/customerAppController/listSystemCustomerApps": {"post": {"tags": ["CustomerAppController"], "operationId": "listSystemCustomerApps", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListCustomerAppVO"}, "headers": {}}}}}, "/PublicEmployee/QueryPublicEmployeeOfSelf": {"post": {"tags": ["PublicEmployeeAction"], "operationId": "queryPublicEmployeeOfSelf", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/QueryPublicEmployeeOfSelfArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/QueryPublicEmployeeOfSelfResult"}, "headers": {}}}}}, "/PublicEmployee/QueryPublicEmployeeWithEnterprise": {"post": {"tags": ["PublicEmployeeAction"], "operationId": "queryPublicEmployeeWithEnterprise", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/QueryPublicEmployeeWithEnterpriseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/QueryPublicEmployeeWithEnterpriseResult"}, "headers": {}}}}}, "/PublicEmployee/GetPublicEmployeeFriendEnterpriseOfSelf": {"post": {"tags": ["PublicEmployeeAction"], "operationId": "getPublicEmployeeFriendEnterpriseOfSelf", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetPublicEmployeeFriendEnterpriseOfSelfArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/GetPublicEmployeeFriendEnterpriseOfSelfResult"}, "headers": {}}}}}, "/enterpriseRelation/batchUpdateRelationTags": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "batchUpdateRelationTags", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchUpdateRelationTagArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/enterpriseRelation/deleteFailedRelation": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "deleteFailedRelation", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DeleteFailedRelationArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/enterpriseRelation/listFriendEnterprises": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "listFriendEnterprises", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListEnterpriseSimpleVo"}, "headers": {}}}}}, "/enterpriseRelation/listUpstreamEnterprises": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "listUpstreamEnterprises", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListEnterpriseSimpleVo"}, "headers": {}}}}}, "/enterpriseRelation/batchDeleteRelations": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "batchDeleteRelations", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EnterpriseAccountListArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListString"}, "headers": {}}}}}, "/enterpriseRelation/getEnterpriseRelationIntroduction": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "getEnterpriseRelationIntroduce", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultEnterpriseRelationIntroduceVo"}, "headers": {}}}}}, "/enterpriseRelation/getEnterpriseRelationDetail": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "getEnterpriseRelationDetail", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultEnterpriseRelationDetailVo"}, "headers": {}}}}}, "/enterpriseRelation/getRelationManagementInfo": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "getRelationManagementInfo", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListRelationManagermentVo"}, "headers": {}}}}}, "/enterpriseRelation/getEnterpriseDataByName": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "getEnterpriseDataByName", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EnterpriseNameArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultObject"}, "headers": {}}}}}, "/enterpriseRelation/examAndGetContactsByCrmObjectIds": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "examAndGetContactsByCrmObjectIds", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ExamAndGetContactsByCrmObjectIdsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultCrmObjectVoList"}, "headers": {}}}}}, "/enterpriseRelation/batchImportCrmObjects": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "batchImportCrmObjects", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchImportCrmObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListCrmObjectWithContactsVo"}, "headers": {}}}}}, "/enterpriseRelation/getExpiredByUpstreamEaList": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "getExpiredByUpstreamEaList", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultExpiredByUpstreamEaVo"}, "headers": {}}}}}, "/enterpriseRelation/isShortVacant": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "isShortNameVacant", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/IsShortNameVacantArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultBoolean"}, "headers": {}}}}}, "/enterpriseRelation/getDownstreamSimpleInfo": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "getDownstreamSimpleInfo", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetDownStreamSimpleInfoArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultSimpleDownstreamVo"}, "headers": {}}}}}, "/enterpriseRelation/listRelations": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "listRelations", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListRelationArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultRelationRelatePageVo"}, "headers": {}}}}}, "/enterpriseRelation/createCrmBind": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "createCrmBind", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CreateNewCrmBindArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}, "/enterpriseRelation/updateRelation": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "updateRelation", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateRelationArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}, "/enterpriseRelation/queryCustomers": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "queryCustomers", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/QueryCustomerArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultDuplicateSearchCustomerVo"}, "headers": {}}}}}, "/enterpriseRelation/isQuotaExpired": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "isQuotaExpired", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpstreamAndLinkAppArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultBoolean"}, "headers": {}}}}}, "/enterpriseRelation/addPartnerEnterpriseWithInviteCode": {"post": {"tags": ["EnterpriseRelationController"], "operationId": "addPartnerEnterpriseWithInviteCode", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AddPartnerEnterpriseWithInviteCodeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/enterpriseCustomerAppRelationController/listCustomerAppByEa": {"post": {"tags": ["EnterpriseCustomerAppRelationController"], "operationId": "listCustomerAppByEa", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListCustomerAppVO"}, "headers": {}}}}}, "/enterpriseCustomerAppRelationController/updateEnterpriseCustomerAppOpenStatus": {"post": {"tags": ["EnterpriseCustomerAppRelationController"], "operationId": "updateEnterpriseCustomerAppOpenStatus", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateEnterpriseCustomerAppOpenStatusArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}, "/enterpriseCustomerAppRelationController/listEnabledCustomerAppByEaAndWxServiceId": {"post": {"tags": ["EnterpriseCustomerAppRelationController"], "operationId": "listEnabledCustomerAppByEaAndWxServiceId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/WxServiceIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListCustomerAppWxServiceVO"}, "headers": {}}}, "deprecated": true}}, "/publicEmployee/batchCreatePublicEmployees": {"post": {"tags": ["PublicEmployeeController"], "operationId": "batchCreatePublicEmployees", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchCreatePublicEmployeeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultObject"}, "headers": {}}}}}, "/publicEmployee/createFriendEmployee": {"post": {"tags": ["PublicEmployeeController"], "operationId": "createFriendEmployee", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FriendEmployeeCreateInfo"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/publicEmployee/batchSetPublicEmployeesRange": {"post": {"tags": ["PublicEmployeeController"], "operationId": "batchSetPublicEmployeesRange", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchSetPublicEmployeeRangeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultObject"}, "headers": {}}}}}, "/publicEmployee/queryPublicEmployees": {"post": {"tags": ["PublicEmployeeController"], "operationId": "queryPublicEmployees", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EmployeeQueryCondition"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultPublicEmployeePageVo"}, "headers": {}}}}}, "/publicEmployee/batchUpdatePublicEmployeesStatus": {"post": {"tags": ["PublicEmployeeController"], "operationId": "batchUpdatePublicEmployeesStatus", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchUpdatePublicEmployeeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultObject"}, "headers": {}}}}}, "/publicEmployee/batchUpdateFriendEmployeesStatus": {"post": {"tags": ["PublicEmployeeController"], "operationId": "batchUpdateFriendEmployeesStatus", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchUpdatePublicEmployeeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultObject"}, "headers": {}}}}}, "/publicEmployee/batchUpdateFriendRoles": {"post": {"tags": ["PublicEmployeeController"], "operationId": "batchUpdateFriendEmployeeRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchUpdateFriendEmployeeRoleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/publicEmployee/queryFriendEmployeeByAppId": {"post": {"tags": ["PublicEmployeeController"], "operationId": "queryFriendEmployeeByAppId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/QueryFriendEmployeeByAppIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListEaEmpView"}, "headers": {}}}}}, "/publicEmployee/deletePublicEmployee": {"post": {"tags": ["PublicEmployeeController"], "operationId": "deletePublicEmployee", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DeletePublicEmployeesArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/publicEmployee/listAllFirendEnterprises": {"post": {"tags": ["PublicEmployeeController"], "operationId": "listAllFirendEnterprises", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListIdNameVoInteger"}, "headers": {}}}}}, "/publicEmployee/createGlobalPublicEmployees": {"post": {"tags": ["PublicEmployeeController"], "operationId": "createGlobalPublicEmployees", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CreateOrRemoveGlobalEmployeeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/publicEmployee/listGlobalPublicEmployees": {"post": {"tags": ["PublicEmployeeController"], "operationId": "listGlobalPublicEmployees", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/PageArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultPublicEmployeePageVo"}, "headers": {}}}}}, "/publicEmployee/listSrcPub": {"post": {"tags": ["PublicEmployeeController"], "operationId": "listSrcPublicEmployeeIds", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListInteger"}, "headers": {}}}}}, "/publicEmployee/updateRelationOwner": {"post": {"tags": ["PublicEmployeeController"], "operationId": "updateRelationOwner", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateRelationOwnerArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/publicEmployee/listDownstreamPublicEmployeesWithUpstream": {"post": {"tags": ["PublicEmployeeController"], "operationId": "listDownstreamPublicEmployeesWithUpstream", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultMapStringListPublicEmployeeVo"}, "headers": {}}}}}, "/publicEmployee/queryPublicEmployeeWithUpstreamEnterprise": {"post": {"tags": ["PublicEmployeeController"], "operationId": "queryPublicEmployeeWithUpstreamEnterprise", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/PageArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultPublicEmployeePageVo"}, "headers": {}}}}}, "/publicEmployee/countUnboundWxServicePublicEmployees": {"post": {"tags": ["PublicEmployeeController"], "operationId": "countUnboundWxServicePublicEmployees", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultUnboundWxServiceEmpCountVo"}, "headers": {}}}}}, "/publicEmployee/sendMessageToUnboundPublicEmployees": {"post": {"tags": ["PublicEmployeeController"], "operationId": "sendMessageToUnboundPublicEmployees", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/SendMessageToUnboundEmpArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/friendEmployee/getRLevelAllDataByBizAdminAppIdFilter": {"post": {"tags": ["RLevelDataServiceController"], "operationId": "getRLevelAllDataByBizAdminAppIdFilter", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetRLevelAllDataByBizAdminAppIDFilterArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultRLevelAllDataByBizAdminAppIDFilterResult"}, "headers": {}}}}}, "/enterpriseRelationCombine/listEnterpriseApps": {"post": {"tags": ["EnterpriseRelationCombineController"], "operationId": "listEnterpriseApps", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListEnterpriseAppsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultEnterpriseAppsVo"}, "headers": {}}}}}, "/enterpriseRelationCombine/createDownPubEmpAppRoles": {"post": {"tags": ["EnterpriseRelationCombineController"], "operationId": "createDownPublicEmployeeAppRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DownPublicEmployeeAppRolesCreateArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/enterpriseRelationCombine/getRelationBench": {"post": {"tags": ["EnterpriseRelationCombineController"], "operationId": "getRelationBench", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultRelationBenchVo"}, "headers": {}}}}}, "/enterpriseRelationCombine/listDownAuthByUp": {"post": {"tags": ["EnterpriseRelationCombineController"], "operationId": "listDownAuthedAppAndCustomerServiceByUp", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListDownAuthByUpArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultDownAuthVo"}, "headers": {}}}}}, "/upstreamEmployeeCustomerAppRoleController/listEnabledCustomerAppByUser": {"post": {"tags": ["UpstreamEmployeeCustomerAppRoleController"], "operationId": "listEnabledCustomerAppByUser", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListCustomerAppWxServiceVO"}, "headers": {}}}}}, "/upstreamEmployeeCustomerAppRoleController/batchMergeUpstreamEmployeeCustomerAppRoles": {"post": {"tags": ["UpstreamEmployeeCustomerAppRoleController"], "operationId": "batchMergeUpstreamEmployeeCustomerAppRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchMergeUpstreamEmployeeCustomerAppRoleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/upstreamEmployeeCustomerAppRoleController/listUpstreamEmployeeIdByCustomerAppIdAndRole": {"post": {"tags": ["UpstreamEmployeeCustomerAppRoleController"], "operationId": "listUpstreamEmployeeIdByCustomerAppIdAndRole", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListUpstreamEmployeeIdByCustomerAppIdAndRoleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListInteger"}, "headers": {}}}}}, "/EnterpriseRelation/QueryRelation": {"post": {"tags": ["EnterpriseRelationAction"], "operationId": "queryRelation", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/QueryRelationArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/QueryRelationResult"}, "headers": {}}}}}, "/enterpriseMetaData/createTag": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "createTag", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/TagArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultTagCreateResult"}, "headers": {}}}}}, "/enterpriseMetaData/deleteRole": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "deleteRole", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/RoleVo"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListRoleVo"}, "headers": {}}}}}, "/enterpriseMetaData/createRole": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "createRole", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/RoleVo"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultRoleCreateResult"}, "headers": {}}}}}, "/enterpriseMetaData/listRoles": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "listRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListRoleVo"}, "headers": {}}}}}, "/enterpriseMetaData/getDownApplySetting": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "getDownApplySetting", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultEnterpriseDownApplySettingVo"}, "headers": {}}}}}, "/enterpriseMetaData/updateDownApplySetting": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "updateDownApplySetting", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/SettingArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/enterpriseMetaData/getEnterpriseRelationStatistics": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "getEnterpriseRelationStatistics", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultEnterpriseRelationStatisticVo"}, "headers": {}}}}}, "/enterpriseMetaData/getEnterpriseByInviteCode": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "getEnterpriseByInviteCode", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/InviteCodeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultEnterpriseCard"}, "headers": {}}}}}, "/enterpriseMetaData/listAllCircleWithEmployee": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "listAllCircleWithEmployee", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultGetAllCircleVo"}, "headers": {}}}}}, "/enterpriseMetaData/updateAccountManageMode": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "updateAccountManageMode", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateAccountManageModeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/enterpriseMetaData/getAccountManageMode": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "getAccountManageMode", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultInteger"}, "headers": {}}}}}, "/enterpriseMetaData/listTags": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "listTags", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListTagVo"}, "headers": {}}}}}, "/enterpriseMetaData/deleteTag": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "deleteTag", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/TagArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListTagVo"}, "headers": {}}}}}, "/enterpriseMetaData/updateTag": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "updateTag", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/TagArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListTagVo"}, "headers": {}}}}}, "/enterpriseMetaData/updateRole": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "updateRole", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/RoleVo"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListRoleVo"}, "headers": {}}}}}, "/enterpriseMetaData/getInviteCode": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "getInviteCode", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetInviteCodeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultEnterpriseInviteCodeVo"}, "headers": {}}}}}, "/enterpriseMetaData/isGrayEa63": {"post": {"tags": ["EnterpriseMetaDataController"], "operationId": "isGrayEa63", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultBoolean"}, "headers": {}}}}}, "/approval/listApprovals": {"post": {"tags": ["RelationRelateApprovalController"], "operationId": "listApprovalsByEnterpriseAccount", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListApprovalsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultRelationRelateApprovalPageVo"}, "headers": {}}}}}, "/approval/getApprovalDetail": {"post": {"tags": ["RelationRelateApprovalController"], "operationId": "getApprovalDetail", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetApprovalDetailArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultGetApprovalDetailResult"}, "headers": {}}}}}, "/approval/getMessageApproval": {"post": {"tags": ["RelationRelateApprovalController"], "operationId": "getMessageApproval", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetApprovalDetailArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultGetApprovalDetailResult"}, "headers": {}}}}}, "/approval/applyEmployeeAppRoles": {"post": {"tags": ["RelationRelateApprovalController"], "operationId": "applyEmployeeAppRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DownPublicEmployeeAppRolesCreateArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}, "deprecated": true}}, "/approval/operateMessageApproval": {"post": {"tags": ["RelationRelateApprovalController"], "operationId": "operateMessageApproval", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DoApprovalArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/approval/operate": {"post": {"tags": ["RelationRelateApprovalController"], "operationId": "operateApproval", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DoApprovalArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/FriendEmployee/GetRLevelOffsetData": {"post": {"tags": ["RLevelDataServiceAction"], "operationId": "getRLevelOffsetData", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetRLevelOffsetDataArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/GetRLevelOffsetDataResult"}, "headers": {}}}}}, "/FriendEmployee/GetEmployeePublicData": {"post": {"tags": ["RLevelDataServiceAction"], "operationId": "getEmployeePublicData", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetEmployeePublicDataArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/GetEmployeePublicDataResult"}, "headers": {}}}}}, "/FriendEmployee/BatchGetFriendEnterpriseData": {"post": {"tags": ["RLevelDataServiceAction"], "operationId": "batchGetFriendEnterpriseData", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchGetFriendEnterpriseDataArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BatchGetFriendEnterpriseDataResult"}, "headers": {}}}}}, "/FriendEmployee/GetRLevelAllData": {"post": {"tags": ["RLevelDataServiceAction"], "operationId": "getRLevelAllData", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetRLevelAllDataArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/GetRLevelAllDataResult"}, "headers": {}}}}}, "/FriendEmployee/GetEmployeeData": {"post": {"tags": ["RLevelDataServiceAction"], "operationId": "getEmployeeData", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetEmployeeDataArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/GetEmployeeDataResult"}, "headers": {}}}}}, "/linkApp/listEnabledCustomerAppByUser": {"post": {"tags": ["CombineController"], "operationId": "listEnabledCustomerAppByUser", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListCustomerAppWxServiceVO"}, "headers": {}}}}}, "/linkApp/clientEntry": {"post": {"tags": ["CombineController"], "operationId": "clientEntry", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultClientEntryVo"}, "headers": {}}}}}, "/downstreamService/createEmployeeLinkAppRoles": {"post": {"tags": ["DownstreamController"], "operationId": "createEmployeeLinkAppRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DownstreamEmployeeRelationArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}, "deprecated": true}}, "/downstreamService/listAppRelationsWithUpAndDownEa": {"post": {"tags": ["DownstreamController"], "operationId": "listAppRelationsWithUpAndDownEa", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EnterpriseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListLinkAppVo"}, "headers": {}}}}}, "/downstreamService/updateLinkAppEmployeeRelations": {"post": {"tags": ["DownstreamController"], "operationId": "updateLinkAppEmployeeRelations", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DownstreamEmployeeAppRelationsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}, "/downstreamService/updateLinkAppAuth": {"post": {"tags": ["DownstreamController"], "operationId": "updateLinkAppAuth", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateDownstreamEmployeeAuthArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}, "/downstreamService/listEmployeeByAppId": {"post": {"tags": ["DownstreamController"], "operationId": "listEmployeeByAppId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EmployeeByAppIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListEmployeeListVo"}, "headers": {}}}}}, "/downstreamService/deleteEmployeeRoles": {"post": {"tags": ["DownstreamController"], "operationId": "deleteEmployeeRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DownstreamDeleteArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}, "/downstreamService/updateLinkAppEnterpriseRelations": {"post": {"tags": ["DownstreamController"], "operationId": "updateLinkAppEnterpriseRelations", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/LinkAppDownstreamRelationsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/downstreamService/deleteByIds": {"post": {"tags": ["DownstreamController"], "operationId": "deleteByIds", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/IdsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}, "/downstreamService/listDownstreamEnterprisesByLinkAppId": {"post": {"tags": ["DownstreamController"], "operationId": "listDownstreamEnterprisesByLinkAppId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/LinkAppIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListDownstreamEnterpriseVo"}, "headers": {}}}}}, "/downstreamService/listAuthDownstreamEnterprisesByLinkAppId": {"post": {"tags": ["DownstreamController"], "operationId": "listAuthDownstreamEnterprisesByLinkAppId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/LinkAppIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListDownstreamEnterpriseVo"}, "headers": {}}}}}, "/downstreamService/listAllUpstreamEnterpriseAppsByDownstream": {"post": {"tags": ["DownstreamController"], "operationId": "listAllUpstreamEnterpriseAppsByDownstream", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListUpstreamEnterpriseAppsVo"}, "headers": {}}}}}, "/downstreamService/listCurrentEnterpriseAppRelationsByEa": {"post": {"tags": ["DownstreamController"], "operationId": "listCurrentEnterpriseAppRelationsByEa", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EnterpriseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListLinkAppVo"}, "headers": {}}}}}, "/customerServiceControl/listDownAuth": {"post": {"tags": ["CustomerServiceController"], "operationId": "listServiceDownAuth", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CustomerServiceBaseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListEnterpriseSimpleVo"}, "headers": {}}}}}, "/customerServiceControl/listDownEnterprises": {"post": {"tags": ["CustomerServiceController"], "operationId": "listDownEnterprises", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListEaEmpView"}, "headers": {}}}}}, "/customerServiceControl/updateDownAuth": {"post": {"tags": ["CustomerServiceController"], "operationId": "updateDownCustomerServiceAuth", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateDownCustomerServiceAuthArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/enterpriseRelationLog/queryRelationLogByPage": {"post": {"tags": ["EnterpriseRelationLogController"], "operationId": "queryRelationLogByPage", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/PageArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultRelationLogPageVo"}, "headers": {}}}}}, "/enterpriseRelationLog/queryRelationLogFriendEnterprise": {"post": {"tags": ["EnterpriseRelationLogController"], "operationId": "queryRelationLogFriendEnterprise", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/QueryRelationLogQFriendEnterpriseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListEnterpriseSimpleData"}, "headers": {}}}}}, "/file/createNFileFromAFile": {"post": {"tags": ["FileController"], "operationId": "createNFileFromAFile", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/CreateNFileFromAFileResult"}, "headers": {}}}}}, "/linkWxService/listBindGuideState": {"post": {"tags": ["LinkWxServiceController"], "operationId": "listBindGuideState", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultMapStringBoolean"}, "headers": {}}}}}, "/linkWxService/updateOpenGuideByAppId": {"post": {"tags": ["LinkWxServiceController"], "operationId": "updateOpenGuideByAppId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BindGuideArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/linkWxService/listBindServices": {"post": {"tags": ["LinkWxServiceController"], "operationId": "listBindServices", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListWXServiceInfoVO"}, "headers": {}}}}}, "/employeeCard/queryFsEmployeeInfo": {"post": {"tags": ["EmployeeCardController"], "operationId": "queryFsEmployeeInfo", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EmployeeInfoQueryArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultEmployeeInfo"}, "headers": {}}}}}, "/employeeCard/getEmployeeCard": {"post": {"tags": ["EmployeeCardController"], "operationId": "getEmployeeCard", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EmployeeInfoQueryArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultEmployeeCard"}, "headers": {}}}}}, "/customerAppDataAuth/updateAppDataAuth": {"post": {"tags": ["UpstreamCustomerAppDataAuthController"], "operationId": "updateAppDataAuth", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CustomerAppDataAuthBatchView"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/customerAppDataAuth/listAppDataAuth": {"post": {"tags": ["UpstreamCustomerAppDataAuthController"], "operationId": "listAppDataAuth", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppBaseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListCustomerAppDataAuth"}, "headers": {}}}}}, "/unionAppController/listAppsByWxServiceId": {"post": {"tags": ["UnionAppController"], "operationId": "listAppsByWxServiceId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/WxServiceIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListAppsResult"}, "headers": {}}}}}, "/customerAppFunctionPermission/listAllocatedLayoutByObject": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "listAllocatedLayoutByObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListAllocatedLayoutByObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultAllocatedLayoutVO"}, "headers": {}}}}}, "/customerAppFunctionPermission/allocateLayoutByObject": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "allocateLayoutByObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AllocateLayoutByObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/customerAppFunctionPermission/listAllocatedRecordTypeByObject": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "listAllocatedRecordTypeByObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListAllocatedRecordTypeByObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultAllocatedRecordTypeVO"}, "headers": {}}}}}, "/customerAppFunctionPermission/allocateRecordTypeByObject": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "allocateRecordTypeByObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AllocateRecordTypeByObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/customerAppFunctionPermission/listAllowedAddObjects": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "listAllowedAddObjects", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppBaseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListObjectSimpleVo"}, "headers": {}}}}}, "/customerAppFunctionPermission/listAssociationObjects": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "listAssociationObjects", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListAssociationObjectsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListCustomerAppAssociationObjectVO"}, "headers": {}}}}}, "/customerAppFunctionPermission/batchAddAssociationObjects": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "batchAddAssociationObjects", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchAddAssociationObjectsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListCustomerAppAssociationObjectVO"}, "headers": {}}}}}, "/customerAppFunctionPermission/removeAssociationObject": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "removeAssociationObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/RemoveAssociationObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListCustomerAppAssociationObjectVO"}, "headers": {}}}}}, "/customerAppFunctionPermission/getRoleFunctionPermission": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "getAppRoleFunctionPermission", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppRoleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListObjectFunctionInfo"}, "headers": {}}}}}, "/customerAppFunctionPermission/updateRoleFunctionPermission": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "updateAppRoleFunctionPermission", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateAppRolePermissionArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/customerAppFunctionPermission/getRoleFieldPermission": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "getAppRoleFieldPermission", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppRoleFieldPermissionArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultAppRoleFieldPermission"}, "headers": {}}}}}, "/customerAppFunctionPermission/updateRoleFieldPermission": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "updateAppRoleFieldPermission", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppRoleFieldPermissionArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/customerAppFunctionPermission/listAppRoles": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "listAppRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppBaseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListRoleVo"}, "headers": {}}}}}, "/customerAppFunctionPermission/getAppRoleView": {"post": {"tags": ["CustomerAppFunctionPermissionController"], "operationId": "getAppRoleView", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListAppRolesArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultAppRoleView"}, "headers": {}}}}}, "/crmMapperController/getAndUpdateCrmBindStatus": {"post": {"tags": ["CrmMapperController"], "operationId": "getAndUpdateCrmBindStatus", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetCrmBindArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultCrmObjectSimpleVo"}, "headers": {}}}}}, "/crmMapperController/updateBindCrmObject": {"post": {"tags": ["CrmMapperController"], "operationId": "updateBindCrmObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateCrmBindArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/crmMapperController/pagedSimpleCrmObject": {"post": {"tags": ["CrmMapperController"], "operationId": "pagedSimpleCrmObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/PagedQueryCrmObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultPagedCrmObjectSimpleVo"}, "headers": {}}}}}, "/crmMapperController/listAllMappedCrmObjectIds": {"post": {"tags": ["CrmMapperController"], "operationId": "listAllMappedCrmObjectIds", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CrmObjectApiNameArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultSetString"}, "headers": {}}}}}, "/relationPattern/listAllRelationPatterns": {"post": {"tags": ["RelationPatternController"], "operationId": "listAllRelationPatterns", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListRelationPatternVo"}, "headers": {}}}}}, "/relationPattern/listRelationPatternsByEa": {"post": {"tags": ["RelationPatternController"], "operationId": "listRelationPatternsByEa", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListRelationPatternVo"}, "headers": {}}}}}, "/relationPattern/getRecordTypeByEnterpriseType": {"post": {"tags": ["RelationPatternController"], "operationId": "getRecordTypeByEnterpriseType", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/RelationEnterpriseTypeVo"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultRelationRecordTypeResult"}, "headers": {}}}}}, "/relationPattern/updateRelationPatternConfig": {"post": {"tags": ["RelationPatternController"], "operationId": "updateRelationPatternConfig", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/RelationPatternVo"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/relationPattern/removeRelationPatternConfig": {"post": {"tags": ["RelationPatternController"], "operationId": "removeRelationPatternConfig", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/RemoveRelationPatternConfigArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/linkAppFunctionPermission/listAllocatedLayoutByObject": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "listAllocatedLayoutByObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListAllocatedLayoutByObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultAllocatedLayoutVO"}, "headers": {}}}}}, "/linkAppFunctionPermission/allocateLayoutByObject": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "allocateLayoutByObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AllocateLayoutByObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/linkAppFunctionPermission/listAllocatedRecordTypeByObject": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "listAllocatedRecordTypeByObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListAllocatedRecordTypeByObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultAllocatedRecordTypeVO"}, "headers": {}}}}}, "/linkAppFunctionPermission/allocateRecordTypeByObject": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "allocateRecordTypeByObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AllocateRecordTypeByObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/linkAppFunctionPermission/listAllowedAddObjects": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "listAllowedAddObjects", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppBaseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListObjectSimpleVo"}, "headers": {}}}}}, "/linkAppFunctionPermission/listAssociationObjects": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "listAssociationObjects", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListAssociationObjectsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListLinkAppAssociationObjectVO"}, "headers": {}}}}}, "/linkAppFunctionPermission/batchAddAssociationObjects": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "batchAddAssociationObjects", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchAddAssociationObjectsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListLinkAppAssociationObjectVO"}, "headers": {}}}}}, "/linkAppFunctionPermission/removeAssociationObject": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "removeAssociationObject", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/RemoveAssociationObjectArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListLinkAppAssociationObjectVO"}, "headers": {}}}}}, "/linkAppFunctionPermission/getRoleFunctionPermission": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "getAppRoleFunctionPermission", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppRoleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListObjectFunctionInfo"}, "headers": {}}}}}, "/linkAppFunctionPermission/updateRoleFunctionPermission": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "updateAppRoleFunctionPermission", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateAppRolePermissionArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/linkAppFunctionPermission/getRoleFieldPermission": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "getAppRoleFieldPermission", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppRoleFieldPermissionArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultAppRoleFieldPermission"}, "headers": {}}}}}, "/linkAppFunctionPermission/updateRoleFieldPermission": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "updateAppRoleFieldPermission", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppRoleFieldPermissionArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/linkAppFunctionPermission/listAssigns": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "listAppRoleAssigns", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListAppRoleAssignsArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListAppRoleAssignVo"}, "headers": {}}}}}, "/linkAppFunctionPermission/batchAddAppRoleAssigns": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "batchAddAppRoleAssigns", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppRoleOuterAssignArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/linkAppFunctionPermission/batchUpdateMainAssigns": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "batchUpdateMainAssigns", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppRoleOuterAssignArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/linkAppFunctionPermission/batchUpdateDiffMainAssigns": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "batchUpdateDiffMainAssigns", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchUpdateDiffMainAssignArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultVoid"}, "headers": {}}}}}, "/linkAppFunctionPermission/batchDeleteAppRoleAssigns": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "batchDeleteAppRoleAssigns", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppRoleOuterAssignArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultObject"}, "headers": {}}}}}, "/linkAppFunctionPermission/listAppRoles": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "listAppRoles", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppBaseArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultListRoleVo"}, "headers": {}}}}}, "/linkAppFunctionPermission/getAppRoleView": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "getAppRoleView", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ListAppRolesArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultAppRoleView"}, "headers": {}}}}}, "/linkAppFunctionPermission/deleteAppRole": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "deleteAppRole", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AppRoleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultObject"}, "headers": {}}}}}, "/linkAppFunctionPermission/addAppRole": {"post": {"tags": ["LinkAppFunctionPermissionController"], "operationId": "addAppRole", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AddAppRoleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ResultString"}, "headers": {}}}}}}, "definitions": {"Result": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ResultString": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "TempProfileImageArg": {"type": "object", "properties": {"profileImage": {"type": "string"}, "ext": {"type": "string"}}}, "EnterpriseCard": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "enterpriseName": {"type": "string"}, "categoryDesc": {"type": "string"}, "description": {"type": "string"}, "profileImage": {"type": "string"}, "enterpriseNameSpell": {"type": "string"}, "updateTime": {"type": "integer", "format": "int64"}, "profileImageFullPath": {"type": "string"}}}, "ResultEnterpriseCard": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/EnterpriseCard"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "VerifyERAdminResult": {"type": "object", "properties": {"resultCode": {"type": "integer", "format": "int32"}, "admin": {"type": "boolean"}}}, "VerifyERAdminArg": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "employeeId": {"type": "integer", "format": "int32"}, "bizCategory": {"type": "integer", "format": "int32"}}}, "LinkAppAuthVo": {"type": "object", "properties": {"linkApp": {"$ref": "#/definitions/LinkAppVo"}, "authedDownNum": {"type": "integer", "format": "int32"}, "authedDownEmployeeNum": {"type": "integer", "format": "int32"}}}, "LinkAppVo": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "nameI18nKey": {"type": "string"}, "icon": {"type": "string"}, "managerIcon": {"type": "string"}, "introduction": {"type": "string"}, "introductionI18nKey": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "webManagerUrl": {"type": "string"}, "appManagerUrl": {"type": "string"}, "webUrl": {"type": "string"}, "appUrl": {"type": "string"}, "supportSetManager": {"type": "boolean"}, "unableSetManagerText": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "internalVisibleRangePrompt": {"type": "string"}, "enablePrompt": {"type": "string"}, "disablePrompt": {"type": "string"}, "enabledByDefault": {"type": "integer", "format": "int32"}, "addManagerTipsUrl": {"type": "string"}, "downstreamMenuJson": {"type": "string"}, "isDefaultSelect": {"type": "boolean"}, "isNew": {"type": "integer", "format": "int32"}, "createTime": {"type": "integer", "format": "int64"}, "updateTime": {"type": "integer", "format": "int64"}, "sessionId": {"type": "string"}, "unVisibleInManager": {"type": "boolean"}, "authType": {"type": "integer", "format": "int32"}, "authByEa": {"type": "boolean"}, "supportRoleExt": {"type": "boolean"}, "enterpriseTypes": {"type": "array", "uniqueItems": true, "items": {"type": "integer", "format": "int32"}}, "dataAuthType": {"type": "integer", "format": "int32"}, "supportExtendObject": {"type": "boolean"}, "associationCrmObject": {"type": "boolean"}, "openValidatePostUrl": {"type": "string"}, "wappId": {"type": "string"}}}, "ResultListLinkAppAuthVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/LinkAppAuthVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "AuthContext": {"type": "object", "properties": {"ea": {"type": "string"}, "fsUserId": {"type": "integer", "format": "int32"}}}, "BaseArg": {"type": "object", "properties": {"authContext": {"$ref": "#/definitions/AuthContext"}, "onlyOpen": {"type": "boolean"}}}, "ResultListString": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "string"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "UpstreamEmployeeAppRelationsArg": {"type": "object", "properties": {"authContext": {"$ref": "#/definitions/AuthContext"}, "onlyOpen": {"type": "boolean"}, "fsUserIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "linkAppIds": {"type": "array", "items": {"type": "string"}}, "role": {"type": "integer", "format": "int32"}, "migrate": {"type": "boolean"}}}, "ResultListUpstreamEmployeeVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/UpstreamEmployeeVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "UpstreamEmployeeVo": {"type": "object", "properties": {"ea": {"type": "string"}, "fsUserId": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "nameSpell": {"type": "string"}, "departmentList": {"type": "array", "items": {"type": "string"}}, "position": {"type": "string"}, "linkAppOpen": {"type": "boolean"}}}, "LinkAppIdArg": {"type": "object", "properties": {"authContext": {"$ref": "#/definitions/AuthContext"}, "onlyOpen": {"type": "boolean"}, "linkAppId": {"type": "string"}, "checkQuotaExpire": {"type": "boolean"}}}, "LinkAppIdAndRoleArg": {"type": "object", "properties": {"authContext": {"$ref": "#/definitions/AuthContext"}, "onlyOpen": {"type": "boolean"}, "linkAppId": {"type": "string"}, "role": {"type": "integer", "format": "int32"}}}, "ResultBoolean": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "UpdateLinkAppAuthTypeArg": {"type": "object", "properties": {"authContext": {"$ref": "#/definitions/AuthContext"}, "onlyOpen": {"type": "boolean"}, "linkAppId": {"type": "string"}, "authType": {"type": "integer", "format": "int32"}, "checkQuotaExpire": {"type": "boolean"}}}, "CrmObjectAuthRangeVo": {"type": "object", "properties": {"objectApiName": {"type": "string"}, "downstreamAuthRange": {"type": "integer", "format": "int32"}, "objectLabel": {"type": "string"}, "allowedAuthRanges": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "LinkAppDataAuthPolicyVo": {"type": "object", "properties": {"policyId": {"type": "string"}, "policyName": {"type": "string"}, "isDefault": {"type": "boolean"}, "downstreamEas": {"type": "array", "items": {"$ref": "#/definitions/SimpleEnterpriseVo"}}, "crmObjectAuthRanges": {"type": "array", "items": {"$ref": "#/definitions/CrmObjectAuthRangeVo"}}}}, "ResultListLinkAppDataAuthPolicyVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/LinkAppDataAuthPolicyVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "SimpleEnterpriseVo": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "enterpriseName": {"type": "string"}}}, "GetLinkAppDataAuthPolicysArg": {"type": "object", "properties": {"authContext": {"$ref": "#/definitions/AuthContext"}, "onlyOpen": {"type": "boolean"}, "linkAppId": {"type": "string"}}}, "RemoveLinkAppDataAuthPolicyArg": {"type": "object", "properties": {"authContext": {"$ref": "#/definitions/AuthContext"}, "onlyOpen": {"type": "boolean"}, "linkAppId": {"type": "string"}, "policyId": {"type": "string"}}}, "MergeLinkAppDataAuthPolicyArg": {"type": "object", "properties": {"authContext": {"$ref": "#/definitions/AuthContext"}, "onlyOpen": {"type": "boolean"}, "linkAppId": {"type": "string"}, "policyId": {"type": "string"}, "policyName": {"type": "string"}, "downstreamEas": {"type": "array", "items": {"type": "string"}}, "crmObjectAuthRanges": {"type": "array", "items": {"$ref": "#/definitions/CrmObjectAuthRangeVo"}}}}, "UpstreamEnterpriseAppRelationsArg": {"type": "object", "properties": {"authContext": {"$ref": "#/definitions/AuthContext"}, "onlyOpen": {"type": "boolean"}, "status": {"type": "integer", "format": "int32"}, "linkAppIds": {"type": "array", "items": {"type": "string"}}}}, "ResultListUnionAccountVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/UnionAccountVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "UnionAccountVo": {"type": "object", "properties": {"accountType": {"type": "integer", "format": "int32"}, "employeeCardId": {"type": "integer", "format": "int64"}, "employeeName": {"type": "string"}, "profileImageFullPath": {"type": "string"}, "outerEi": {"type": "integer", "format": "int32"}, "enterpriseAccount": {"type": "string"}, "enterpriseName": {"type": "string"}, "enterpriseShortName": {"type": "string"}, "employeeId": {"type": "integer", "format": "int32"}, "profileImage": {"type": "string"}, "nameSpell": {"type": "string"}, "gender": {"type": "string"}, "enterpriseShortNameSpell": {"type": "string"}, "profileImagePath": {"type": "string"}, "nameOrder": {"type": "string"}, "mail": {"type": "string"}, "jobIntroduce": {"type": "string"}, "phone": {"type": "string"}, "customerId": {"type": "string"}, "customerName": {"type": "string"}, "contactId": {"type": "string"}, "contactName": {"type": "string"}}}, "BatchGetUnionAccountArg": {"type": "object", "properties": {"outerIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "CustomerVo": {"type": "object", "properties": {"customerID": {"type": "string"}, "name": {"type": "string"}, "level": {"type": "string"}, "areaFullName": {"type": "string"}, "houseNo": {"type": "string"}, "tel": {"type": "string"}, "postCode": {"type": "string"}, "fax": {"type": "string"}, "ownerID": {"type": "integer", "format": "int32"}, "ownerName": {"type": "string"}, "customerType": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "createTime": {"type": "integer", "format": "int32"}, "updateTime": {"type": "integer", "format": "int32"}, "invalid": {"type": "boolean"}}}, "ResultCustomerVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/CustomerVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "CheckCustomerByNameArg": {"type": "object", "properties": {"customerName": {"type": "string"}, "unCheckTenantName": {"type": "boolean"}, "customerId": {"type": "string"}}}, "ResultSetString": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "EnumDetail": {"type": "object", "properties": {"enumMainID": {"type": "string"}, "enumName": {"type": "string"}, "enumDetailID": {"type": "string"}, "itemCode": {"type": "string"}, "itemName": {"type": "string"}, "itemOrder": {"type": "integer", "format": "int32"}, "children": {"type": "array", "items": {"$ref": "#/definitions/EnumDetail"}}, "parentID": {"type": "string"}, "isSysItem": {"type": "boolean"}}}, "ResultListUserDefineDescriptionVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/UserDefineDescriptionVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "UserDefineDescriptionVo": {"type": "object", "properties": {"group": {"type": "string", "enum": ["DEFAULT", "TAG", "ROLE", "APP", "CRM"]}, "userDefinedFieldID": {"type": "string"}, "fieldProperty": {"type": "integer", "format": "int32"}, "fieldName": {"type": "string"}, "fieldCaption": {"type": "string"}, "fieldType": {"type": "integer", "format": "int32"}, "fieldLength": {"type": "integer", "format": "int32"}, "isNotNull": {"type": "boolean"}, "isCodeField": {"type": "boolean"}, "isVisible": {"type": "boolean"}, "enumName": {"type": "string"}, "isAllowEditNotNull": {"type": "boolean"}, "fieldOrder": {"type": "integer", "format": "int32"}, "enumDetails": {"type": "array", "items": {"$ref": "#/definitions/EnumDetail"}}, "selectType": {"type": "boolean"}}}, "ResultListInteger": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ListAppRoleAssignsArg": {"type": "object", "properties": {"appId": {"type": "string"}, "roleId": {"type": "string"}, "upstreamEa": {"type": "string"}}}, "ResultListRoleVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/RoleVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "RoleVo": {"type": "object", "properties": {"roleId": {"type": "string"}, "roleName": {"type": "string"}, "description": {"type": "string"}, "roleType": {"type": "integer", "format": "int32"}, "isModifiable": {"type": "boolean"}, "isAssignable": {"type": "boolean"}, "isDeletable": {"type": "boolean"}}}, "AppBaseArg": {"type": "object", "properties": {"appId": {"type": "string"}}}, "ResultVoid": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/Void"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "Void": {"type": "object"}, "BatchUpdateRoleInnerAssignsArg": {"type": "object", "properties": {"appId": {"type": "string"}, "roleId": {"type": "string"}, "employeeIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "EmployeeCard": {"type": "object", "properties": {"employeeId": {"type": "integer", "format": "int32"}, "employeeName": {"type": "string"}, "mobile": {"type": "string"}, "mail": {"type": "string"}, "post": {"type": "string"}, "profileImage": {"type": "string"}, "gender": {"type": "string"}, "jobIntroduce": {"type": "string"}, "department": {"type": "string"}, "nameSpell": {"type": "string"}, "nameOrder": {"type": "string"}, "profileImageToken": {"type": "string"}, "employeeCardId": {"type": "integer", "format": "int64"}, "enterpriseAccount": {"type": "string"}, "updateTime": {"type": "integer", "format": "int64"}, "type": {"type": "integer", "format": "int32"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/RoleVo"}}, "profileImageFullPath": {"type": "string"}, "qrCodeUrl": {"type": "string"}}}, "GetEmployeeCardOfFriendResult": {"type": "object", "properties": {"employeeCard": {"$ref": "#/definitions/EmployeeCard"}}}, "GetEmployeeCardOfFriendArg": {"type": "object", "properties": {"destEnterpriseAccount": {"type": "string"}, "destEmployeeId": {"type": "integer", "format": "int32"}}}, "GetEmployeeCardResult": {"type": "object", "properties": {"employeeCard": {"$ref": "#/definitions/EmployeeCard"}}}, "GetEmployeeCardArg": {"type": "object"}, "EditEmployeeCardArg": {"type": "object", "properties": {"mobile": {"type": "string"}, "mail": {"type": "string"}, "post": {"type": "string"}, "department": {"type": "string"}, "jobIntroduce": {"type": "string"}}}, "CustomerAppVO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "introduction": {"type": "string"}, "icon": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "webManagerUrl": {"type": "string"}, "wechatServiceUrl": {"type": "string"}, "openStatus": {"type": "boolean"}, "wxServiceId": {"type": "string"}, "wxServiceName": {"type": "string"}, "supportBizRole": {"type": "boolean"}, "supportExtendObject": {"type": "boolean"}, "associationCrmObject": {"type": "boolean"}}}, "ResultListCustomerAppVO": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CustomerAppVO"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "QueryPublicEmployeeOfSelfResult": {"type": "object", "properties": {"publicEmployeeIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "QueryPublicEmployeeOfSelfArg": {"type": "object"}, "EmployeeIdsWithFriendEnterprise": {"type": "object", "properties": {"employeeId": {"type": "integer", "format": "int32"}, "enterprises": {"type": "array", "items": {"$ref": "#/definitions/EnterpriseSimpleData"}}}}, "EnterpriseSimpleData": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "enterpriseName": {"type": "string"}, "destShortName": {"type": "string"}}}, "QueryPublicEmployeeWithEnterpriseResult": {"type": "object", "properties": {"publicEmployeeIds": {"type": "array", "items": {"$ref": "#/definitions/EmployeeIdsWithFriendEnterprise"}}}}, "QueryPublicEmployeeWithEnterpriseArg": {"type": "object"}, "GetPublicEmployeeFriendEnterpriseOfSelfResult": {"type": "object", "properties": {"enterprises": {"type": "array", "items": {"$ref": "#/definitions/EnterpriseSimpleData"}}}}, "GetPublicEmployeeFriendEnterpriseOfSelfArg": {"type": "object"}, "BatchUpdateRelationTagArg": {"type": "object", "properties": {"destEnterpriseAccounts": {"type": "array", "items": {"type": "string"}}, "tagIds": {"type": "array", "items": {"type": "string"}}, "append": {"type": "integer", "format": "int32"}}}, "DeleteFailedRelationArg": {"type": "object", "properties": {"destEnterpriseAccount": {"type": "string"}}}, "EnterpriseSimpleVo": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "enterpriseName": {"type": "string"}, "enterpriseShortName": {"type": "string"}, "enterpriseShortNameSpell": {"type": "string"}}}, "ResultListEnterpriseSimpleVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/EnterpriseSimpleVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "EnterpriseAccountListArg": {"type": "object", "properties": {"enterpriseAccounts": {"type": "array", "items": {"type": "string"}}}}, "EnterpriseRelationIntroduceVo": {"type": "object", "properties": {"title": {"type": "string"}, "subTitle": {"type": "string"}, "urls": {"type": "array", "items": {"$ref": "#/definitions/UrlVo"}}}}, "ResultEnterpriseRelationIntroduceVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/EnterpriseRelationIntroduceVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "UrlVo": {"type": "object", "properties": {"url": {"type": "string"}, "description": {"type": "string"}}}, "EnterpriseRelationDetailVo": {"type": "object", "properties": {"upstreams": {"type": "array", "items": {"$ref": "#/definitions/EnterpriseSimpleVo"}}, "downstreams": {"type": "array", "items": {"$ref": "#/definitions/EnterpriseSimpleVo"}}}}, "ResultEnterpriseRelationDetailVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/EnterpriseRelationDetailVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "RelationManagermentVo": {"type": "object", "properties": {"linkAppVos": {"type": "array", "items": {"$ref": "#/definitions/LinkAppVo"}}, "groupName": {"type": "string"}}}, "ResultListRelationManagermentVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/RelationManagermentVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ResultObject": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "EnterpriseNameArg": {"type": "object", "properties": {"enterpriseName": {"type": "string"}}}, "CircleVo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "spell": {"type": "string"}, "circlePath": {"type": "string"}, "parentId": {"type": "integer", "format": "int32"}}}, "ContactVo": {"type": "object", "properties": {"name": {"type": "string"}, "mobile": {"type": "string"}, "post": {"type": "string"}, "isSystemManager": {"type": "boolean"}, "isPublicEmployee": {"type": "boolean"}}}, "CrmObjectVoList": {"type": "object", "properties": {"crmObjectWithContactsVos": {"type": "array", "items": {"$ref": "#/definitions/CrmObjectWithContactsVo"}}, "leftQuota": {"type": "integer", "format": "int32"}, "allCircleVo": {"$ref": "#/definitions/GetAllCircleVo"}, "typeLabels": {"type": "array", "items": {"type": "string"}}, "levelLabels": {"type": "array", "items": {"type": "string"}}}}, "CrmObjectWithContactsVo": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "level": {"type": "string"}, "type": {"type": "string"}, "ownerId": {"type": "integer", "format": "int32"}, "ownerName": {"type": "string"}, "linkAppIds": {"type": "array", "items": {"type": "string"}}, "wxServiceIds": {"type": "array", "items": {"type": "string"}}, "contactVos": {"type": "array", "items": {"$ref": "#/definitions/ContactVo"}}, "opResult": {"$ref": "#/definitions/ResultString"}}}, "EmployeeVo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "spell": {"type": "string"}, "profileImage": {"type": "string"}, "groupIDs": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "GetAllCircleVo": {"type": "object", "properties": {"groups": {"type": "array", "items": {"$ref": "#/definitions/CircleVo"}}, "members": {"type": "array", "items": {"$ref": "#/definitions/EmployeeVo"}}}}, "ResultCrmObjectVoList": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/CrmObjectVoList"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ExamAndGetContactsByCrmObjectIdsArg": {"type": "object", "properties": {"downstreamType": {"type": "integer", "format": "int32"}, "crmObjects": {"type": "array", "items": {"$ref": "#/definitions/CrmObjectWithContactsVo"}}}}, "ResultListCrmObjectWithContactsVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CrmObjectWithContactsVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "BatchImportCrmObjectArg": {"type": "object", "properties": {"downstreamType": {"type": "integer", "format": "int32"}, "crmObjectWithContactsVos": {"type": "array", "items": {"$ref": "#/definitions/CrmObjectWithContactsVo"}}, "onlyRightDataImport": {"type": "boolean"}}}, "ExpiredByUpstreamEaVo": {"type": "object", "properties": {"upstreamEaList": {"type": "array", "items": {"type": "string"}}, "quotaExpireHintMsg": {"type": "string"}}}, "ResultExpiredByUpstreamEaVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/ExpiredByUpstreamEaVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "IsShortNameVacantArg": {"type": "object", "properties": {"shortName": {"type": "string"}}}, "ResultSimpleDownstreamVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/SimpleDownstreamVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "SimpleDownstreamVo": {"type": "object", "properties": {"downstreamType": {"type": "integer", "format": "int32"}, "accountManageMode": {"type": "integer", "format": "int32"}}}, "GetDownStreamSimpleInfoArg": {"type": "object", "properties": {"downstreamEnterpriseAccount": {"type": "string"}}}, "EmployeeSimpleVo": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "enterpriseName": {"type": "string"}, "employeeId": {"type": "string"}, "employeeName": {"type": "string"}, "nameSpell": {"type": "string"}, "mobile": {"type": "string"}, "email": {"type": "string"}, "post": {"type": "string"}, "department": {"type": "string"}, "jobIntroduce": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "profileImage": {"type": "string"}, "profileImageToken": {"type": "string"}, "profileImageFullPath": {"type": "string"}, "enterpriseSimpleVos": {"type": "array", "items": {"$ref": "#/definitions/EnterpriseSimpleVo"}}, "employeeCardId": {"type": "integer", "format": "int64"}, "sourceType": {"type": "integer", "format": "int32"}, "publicType": {"type": "integer", "format": "int32"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/RoleVo"}}, "relationOwner": {"type": "boolean"}, "activeState": {"type": "integer", "format": "int32"}, "bindingsWeChatAppName": {"type": "string"}}}, "FriendEnterpriseVo": {"type": "object", "properties": {"enterpriseName": {"type": "string"}, "enterpriseAccount": {"type": "string"}, "enterpriseShortName": {"type": "string"}, "tags": {"type": "array", "items": {"$ref": "#/definitions/TagVo"}}, "category": {"type": "integer", "format": "int32"}, "categoryDesc": {"type": "string"}, "profileImage": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string"}, "downstreamType": {"type": "integer", "format": "int32"}, "mapperApiName": {"type": "string"}, "manageMode": {"type": "integer", "format": "int32"}}}, "RelationRelatePageVo": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int64"}, "relationRelates": {"type": "array", "items": {"$ref": "#/definitions/RelationRelateVo"}}}}, "RelationRelateVo": {"type": "object", "properties": {"sourceType": {"type": "integer", "format": "int32"}, "publicEmployees": {"type": "array", "items": {"$ref": "#/definitions/EmployeeSimpleVo"}}, "publieEmployeeTotalCount": {"type": "integer", "format": "int32"}, "friendEnterprise": {"$ref": "#/definitions/FriendEnterpriseVo"}, "friendEmployees": {"type": "array", "items": {"$ref": "#/definitions/EmployeeSimpleVo"}}, "friendEmployeeTotalCount": {"type": "integer", "format": "int32"}, "createTime": {"type": "integer", "format": "int64"}}}, "ResultRelationRelatePageVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/RelationRelatePageVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "TagVo": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "childTags": {"type": "array", "items": {"$ref": "#/definitions/TagVo"}}, "nameSpell": {"type": "string"}, "attachCount": {"type": "integer", "format": "int32"}}}, "ListRelationArg": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "searchText": {"type": "string"}, "tagIds": {"type": "array", "items": {"type": "string"}}, "sortField": {"type": "string"}, "sortOrder": {"type": "integer", "format": "int32"}, "destEnterpriseAccounts": {"type": "array", "items": {"type": "string"}}, "tagsMap": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "offset": {"type": "integer", "format": "int32"}, "limit": {"type": "integer", "format": "int32"}}}, "CreateNewCrmBindArg": {"type": "object", "properties": {"srcEnterpriseAccount": {"type": "string"}, "operatorId": {"type": "integer", "format": "int32"}, "destEnterpriseAccount": {"type": "string"}, "customerName": {"type": "string"}, "get_userDefinedData": {"type": "object", "additionalProperties": {"type": "object"}}, "ownerID": {"type": "integer", "format": "int32"}}}, "UpdateRelationArg": {"type": "object", "properties": {"destEnterpriseAccount": {"type": "string"}, "shortName": {"type": "string"}, "tagIds": {"type": "array", "items": {"type": "string"}}}}, "Customer": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "customerType": {"type": "string"}, "ownerName": {"type": "string"}, "level": {"type": "string"}, "email": {"type": "string"}, "area": {"type": "string"}, "source": {"type": "string"}, "industry": {"type": "string"}, "status": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}}}, "DuplicateSearchCustomerVo": {"type": "object", "properties": {"duplicateMode": {"type": "integer", "format": "int32"}, "objects": {"type": "array", "items": {"$ref": "#/definitions/Customer"}}}}, "ResultDuplicateSearchCustomerVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/DuplicateSearchCustomerVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "QueryCustomerArg": {"type": "object", "properties": {"params": {"type": "object", "additionalProperties": {"type": "object"}}, "name": {"type": "string"}, "recordTypeId": {"type": "string"}}}, "UpstreamAndLinkAppArg": {"type": "object", "properties": {"upstreamEa": {"type": "string"}, "linkAppId": {"type": "string"}}}, "AddPartnerEnterpriseWithInviteCodeArg": {"type": "object", "properties": {"authInfo": {"$ref": "#/definitions/AuthInfo"}, "inviteCode": {"type": "string"}}}, "AuthInfo": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "enterpriseId": {"type": "integer", "format": "int64"}, "employeeId": {"type": "integer", "format": "int32"}, "employeeFullId": {"$ref": "#/definitions/EmployeeFullId"}, "epTag": {"type": "string"}, "postId": {"type": "string"}, "clientInfo": {"type": "string"}, "traceId": {"type": "string"}, "traceColor": {"type": "integer", "format": "int32"}, "rpcId": {"type": "string"}, "clientAbility": {"type": "integer", "format": "int64"}, "source": {"type": "string"}, "statisticExt": {"type": "integer", "format": "int64"}, "locale": {"$ref": "#/definitions/Locale"}}}, "EmployeeFullId": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "employeeId": {"type": "integer", "format": "int32"}, "employeeFullId": {"type": "string"}}}, "Locale": {"type": "object", "properties": {"language": {"type": "string"}, "country": {"type": "string"}, "displayCountry": {"type": "string"}, "displayLanguage": {"type": "string"}, "displayName": {"type": "string"}, "displayScript": {"type": "string"}, "displayVariant": {"type": "string"}, "extensionKeys": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}, "iso3Country": {"type": "string"}, "iso3Language": {"type": "string"}, "script": {"type": "string"}, "unicodeLocaleAttributes": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}, "unicodeLocaleKeys": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}, "variant": {"type": "string"}}}, "UpdateEnterpriseCustomerAppOpenStatusArg": {"type": "object", "properties": {"customerAppId": {"type": "string"}, "openStatus": {"type": "boolean"}}}, "CustomerAppWxServiceVO": {"type": "object", "properties": {"appId": {"type": "string"}, "name": {"type": "string"}, "icon": {"type": "string"}, "introduction": {"type": "string"}, "wechatServiceUrl": {"type": "string"}, "webManagerUrl": {"type": "string"}, "appManagerUrl": {"type": "string"}}}, "ResultListCustomerAppWxServiceVO": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CustomerAppWxServiceVO"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "WxServiceIdArg": {"type": "object", "properties": {"wxServiceId": {"type": "string"}}}, "BatchCreatePublicEmployeeArg": {"type": "object", "properties": {"destEnterpriseAccounts": {"type": "array", "items": {"type": "string"}}, "employeeIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "appIds": {"type": "array", "items": {"type": "string"}}, "remark": {"type": "string"}, "append": {"type": "integer", "format": "int32"}, "callerType": {"type": "integer", "format": "int32"}}}, "FriendEmployeeCreateInfo": {"type": "object", "properties": {"destEnterpriseAccount": {"type": "string"}, "employeeName": {"type": "string"}, "mobile": {"type": "string"}, "appIds": {"type": "array", "items": {"type": "string"}}, "roleIds": {"type": "array", "items": {"type": "string"}}}}, "BatchSetPublicEmployeeRangeArg": {"type": "object", "properties": {"destEnterpriseAccounts": {"type": "array", "items": {"type": "string"}}, "employeeIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "append": {"type": "integer", "format": "int32"}, "callerType": {"type": "integer", "format": "int32"}}}, "PublicEmployeePageVo": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int64"}, "employeeSimpleVos": {"type": "array", "items": {"$ref": "#/definitions/EmployeeSimpleVo"}}}}, "ResultPublicEmployeePageVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/PublicEmployeePageVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "EmployeeQueryCondition": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "searchText": {"type": "string"}, "needDestRange": {"type": "integer", "format": "int32"}, "employeeId": {"type": "integer", "format": "int32"}, "destEnterpriseAccount": {"type": "string"}, "queryType": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}, "limit": {"type": "integer", "format": "int32"}}}, "BatchUpdatePublicEmployeeArg": {"type": "object", "properties": {"updatePublicEmployeeArgs": {"type": "array", "items": {"$ref": "#/definitions/UpdatePublicEmployeeArg"}}}}, "UpdatePublicEmployeeArg": {"type": "object", "properties": {"employeeId": {"type": "integer", "format": "int32"}, "targetEnterpriseAccount": {"type": "string"}, "employeeName": {"type": "string"}}}, "BatchUpdateFriendEmployeeRoleArg": {"type": "object", "properties": {"friendEmployees": {"type": "array", "items": {"$ref": "#/definitions/UpdatePublicEmployeeArg"}}, "roleIds": {"type": "array", "items": {"type": "string"}}, "isAppend": {"type": "integer", "format": "int32"}}}, "EaEmpView": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "enterpriseName": {"type": "string"}, "employeeCardList": {"type": "array", "items": {"$ref": "#/definitions/EmployeeCard"}}, "tagList": {"type": "array", "items": {"$ref": "#/definitions/TagVo"}}, "enterpriseShortName": {"type": "string"}, "enterpriseShortNameSpell": {"type": "string"}, "manageMode": {"type": "integer", "format": "int32"}}}, "ResultListEaEmpView": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/EaEmpView"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "QueryFriendEmployeeByAppIdArg": {"type": "object", "properties": {"appId": {"type": "string"}, "appType": {"type": "integer", "format": "int32"}, "upstreamEa": {"type": "string"}}}, "DeletePublicEmployeesArg": {"type": "object", "properties": {"destEnterpriseAccount": {"type": "string"}, "employeeId": {"type": "integer", "format": "int32"}}}, "IdNameVo": {"type": "object", "properties": {"id": {"type": "object"}, "name": {"type": "string"}}}, "IdNameVoInteger": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}}}, "ResultListIdNameVoInteger": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/IdNameVoInteger"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "CreateOrRemoveGlobalEmployeeArg": {"type": "object", "properties": {"employeeIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "PageArg": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}, "limit": {"type": "integer", "format": "int32"}}}, "UpdateRelationOwnerArg": {"type": "object", "properties": {"destEnterpriseAccount": {"type": "string"}, "employeeId": {"type": "integer", "format": "int32"}, "updateType": {"type": "integer", "format": "int32"}}}, "PublicEmployeeVo": {"type": "object", "properties": {"employeeId": {"type": "integer", "format": "int32"}, "type": {"type": "integer", "format": "int32"}, "destEnterpriseAccount": {"type": "string"}, "sourceEnterpriseAccount": {"type": "string"}, "publicTags": {"type": "array", "items": {"type": "string"}}}}, "ResultMapStringListPublicEmployeeVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/PublicEmployeeVo"}}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ResultUnboundWxServiceEmpCountVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/UnboundWxServiceEmpCountVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "UnboundWxServiceEmpCountVo": {"type": "object", "properties": {"unboundNumber": {"type": "integer", "format": "int32"}, "allUnboundDownstreamEmp": {"type": "array", "items": {"$ref": "#/definitions/UnboundWxServiceEmpVo"}}}}, "UnboundWxServiceEmpVo": {"type": "object", "properties": {"emplpyeeId": {"type": "integer", "format": "int32"}, "emplpyeeCardId": {"type": "integer", "format": "int64"}, "emplpyeeName": {"type": "string"}, "enterpriseAccount": {"type": "string"}, "enterpriseName": {"type": "string"}, "mobilePhone": {"type": "string"}}}, "SendMessageToUnboundEmpArg": {"type": "object", "properties": {"employeeCardIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "FriendEnterpriseDataAtList": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "enterpriseName": {"type": "string"}, "enterpriseShortName": {"type": "string"}, "profileImage": {"type": "string"}, "enterpriseShortNameSpell": {"type": "string"}, "profileImageFullPath": {"type": "string"}}}, "FriendEnterpriseEmployeeDataAtList": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "employeeId": {"type": "integer", "format": "int32"}, "employeeName": {"type": "string"}, "profileImage": {"type": "string"}, "nameSpell": {"type": "string"}, "nameOrder": {"type": "string"}, "post": {"type": "string"}, "department": {"type": "string"}, "profileImagePath": {"type": "string"}, "employeeCardId": {"type": "integer", "format": "int32"}, "profileImageFullPath": {"type": "string"}, "mail": {"type": "string"}, "jobIntroduce": {"type": "string"}, "enterpriseName": {"type": "string"}}}, "RLevelAllDataByBizAdminAppIDFilterResult": {"type": "object", "properties": {"enterpriseList": {"type": "array", "items": {"$ref": "#/definitions/FriendEnterpriseDataAtList"}}, "employeeList": {"type": "array", "items": {"$ref": "#/definitions/FriendEnterpriseEmployeeDataAtList"}}}}, "ResultRLevelAllDataByBizAdminAppIDFilterResult": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/RLevelAllDataByBizAdminAppIDFilterResult"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "GetRLevelAllDataByBizAdminAppIDFilterArg": {"type": "object", "properties": {"appId": {"type": "string"}}}, "CustomerServiceVo": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "icon": {"type": "string"}, "description": {"type": "string"}}}, "EnterpriseAppsVo": {"type": "object", "properties": {"linApps": {"type": "array", "items": {"$ref": "#/definitions/LinkAppVo"}}, "customServices": {"type": "array", "items": {"$ref": "#/definitions/CustomerServiceVo"}}}}, "ResultEnterpriseAppsVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/EnterpriseAppsVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ListEnterpriseAppsArg": {"type": "object", "properties": {"authInfo": {"$ref": "#/definitions/AuthInfo"}, "relationEnterpriseType": {"type": "integer", "format": "int32"}}}, "DownPublicEmployeeAppRolesCreateArg": {"type": "object", "properties": {"authInfo": {"$ref": "#/definitions/AuthInfo"}, "destEnterpriseAccount": {"type": "string"}, "employeeIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "appIds": {"type": "array", "items": {"type": "string"}}, "remark": {"type": "string"}}}, "DownstreamLinkAppsVo": {"type": "object", "properties": {"ea": {"type": "string"}, "enterpriseName": {"type": "string"}, "linkApps": {"type": "array", "items": {"$ref": "#/definitions/LinkAppVo"}}, "shortName": {"type": "string"}}}, "RelationBenchVo": {"type": "object", "properties": {"linkApps": {"type": "array", "items": {"$ref": "#/definitions/LinkAppVo"}}, "downstreamLinkApps": {"type": "array", "items": {"$ref": "#/definitions/DownstreamLinkAppsVo"}}, "customServices": {"type": "array", "items": {"$ref": "#/definitions/CustomerServiceVo"}}}}, "ResultRelationBenchVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/RelationBenchVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "DownAuthVo": {"type": "object", "properties": {"linkApps": {"type": "array", "items": {"$ref": "#/definitions/LinkAppVo"}}, "customerServices": {"type": "array", "items": {"$ref": "#/definitions/CustomerServiceVo"}}}}, "ResultDownAuthVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/DownAuthVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ListDownAuthByUpArg": {"type": "object", "properties": {"destEnterpriseAccount": {"type": "string"}}}, "BatchMergeUpstreamEmployeeCustomerAppRoleArg": {"type": "object", "properties": {"upstreamEa": {"type": "string"}, "fsUserIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "customerAppId": {"type": "string"}, "role": {"type": "integer", "format": "int32"}}}, "ListUpstreamEmployeeIdByCustomerAppIdAndRoleArg": {"type": "object", "properties": {"customerAppId": {"type": "string"}, "role": {"type": "integer", "format": "int32"}}}, "EmployeeInfo": {"type": "object", "properties": {"employeeId": {"type": "integer", "format": "int32"}, "employeeName": {"type": "string"}, "department": {"type": "string"}, "post": {"type": "string"}, "gender": {"type": "string"}, "profileImage": {"type": "string"}, "mobile": {"type": "string"}, "email": {"type": "string"}, "leaderID": {"type": "integer", "format": "int32"}, "nameSpell": {"type": "string"}, "nameOrder": {"type": "string"}, "stop": {"type": "boolean"}}}, "EnterpriseRelation": {"type": "object", "properties": {"sourceEnterpriseAccount": {"type": "string"}, "destEnterpriseAccount": {"type": "string"}, "relationType": {"type": "integer", "format": "int32"}, "groupId": {"type": "integer", "format": "int32"}, "createTime": {"type": "integer", "format": "int64"}, "updateTime": {"type": "integer", "format": "int64"}, "destShortName": {"type": "string"}, "destEnterpriseName": {"type": "string"}, "destEnterpriseShortNameSpell": {"type": "string"}, "tagIds": {"type": "array", "items": {"type": "string"}}, "sourceType": {"type": "integer", "format": "int32"}, "remark": {"type": "string"}, "quotaExpired": {"type": "boolean"}, "downstreamType": {"type": "integer", "format": "int32"}, "mapperApiName": {"type": "string"}}}, "FriendEnterpriseData": {"type": "object", "properties": {"enterpriseCard": {"$ref": "#/definitions/EnterpriseCard"}, "enterpriseRelation": {"$ref": "#/definitions/EnterpriseRelation"}, "publicEmployees": {"type": "array", "items": {"$ref": "#/definitions/EmployeeInfo"}}, "totalPublicEmployeesCount": {"type": "integer", "format": "int32"}}}, "QueryRelationResult": {"type": "object", "properties": {"friendEnterpriseDatas": {"type": "array", "items": {"$ref": "#/definitions/FriendEnterpriseData"}}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}}}, "QueryRelationArg": {"type": "object", "properties": {"authInfo": {"$ref": "#/definitions/AuthInfo"}, "enterpriseName": {"type": "string"}, "groupId": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}}, "ResultTagCreateResult": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/TagCreateResult"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "TagCreateResult": {"type": "object", "properties": {"curTags": {"type": "array", "items": {"$ref": "#/definitions/TagVo"}}, "tagIdCreated": {"type": "string"}}}, "TagArg": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "parentId": {"type": "string"}}}, "ResultRoleCreateResult": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/RoleCreateResult"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "RoleCreateResult": {"type": "object", "properties": {"roles": {"type": "array", "items": {"$ref": "#/definitions/RoleVo"}}, "roleIdCreated": {"type": "string"}}}, "EnterpriseDownApplySettingVo": {"type": "object", "properties": {"downEmployeeApply": {"type": "boolean"}, "downAppRoleApply": {"type": "boolean"}}}, "ResultEnterpriseDownApplySettingVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/EnterpriseDownApplySettingVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "SettingArg": {"type": "object", "properties": {"type": {"type": "integer", "format": "int32"}, "needApply": {"type": "boolean"}}}, "EnterpriseQuotaVo": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "maxQuota": {"type": "integer", "format": "int32"}, "usedQuota": {"type": "integer", "format": "int32"}, "hintMsgList": {"type": "array", "items": {"type": "string"}}}}, "EnterpriseRelationStatisticVo": {"type": "object", "properties": {"quota": {"$ref": "#/definitions/EnterpriseQuotaVo"}, "weChatNormal": {"type": "boolean"}}}, "ResultEnterpriseRelationStatisticVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/EnterpriseRelationStatisticVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "InviteCodeArg": {"type": "object", "properties": {"inviteCode": {"type": "string"}}}, "ResultGetAllCircleVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/GetAllCircleVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "UpdateAccountManageModeArg": {"type": "object", "properties": {"accountManageMode": {"type": "integer", "format": "int32"}}}, "ResultInteger": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ResultListTagVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/TagVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "EnterpriseInviteCodeVo": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "enterpriseName": {"type": "string"}, "inviteCode": {"type": "string"}, "inviteCodeExpireTime": {"type": "integer", "format": "int64"}, "leftTime": {"type": "integer", "format": "int64"}}}, "ResultEnterpriseInviteCodeVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/EnterpriseInviteCodeVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "GetInviteCodeArg": {"type": "object", "properties": {"authInfo": {"$ref": "#/definitions/AuthInfo"}, "refreshCode": {"type": "boolean"}}}, "AppVo": {"type": "object", "properties": {"appId": {"type": "string"}, "appName": {"type": "string"}, "appLogo": {"type": "string"}}}, "ApplicantVo": {"type": "object", "properties": {"applicantId": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "mobile": {"type": "string"}, "profileImage": {"type": "string"}}}, "RelationRelateApprovalPageVo": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "approvals": {"type": "array", "items": {"$ref": "#/definitions/RelationRelateApprovalVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int64"}}}, "RelationRelateApprovalRecordVo": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "operatorId": {"type": "integer", "format": "int32"}, "operatorName": {"type": "string"}, "operateType": {"type": "integer", "format": "int32"}, "remark": {"type": "string"}, "operateTime": {"type": "integer", "format": "int64"}}}, "RelationRelateApprovalVo": {"type": "object", "properties": {"id": {"type": "string"}, "sourceEnterpriseAccount": {"type": "string"}, "sourceEnterpriseNameOrShortName": {"type": "string"}, "destEnterpriseAccount": {"type": "string"}, "destEnterpriseNameOrShortName": {"type": "string"}, "applicant": {"$ref": "#/definitions/ApplicantVo"}, "applyTime": {"type": "integer", "format": "int64"}, "approveType": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "friendEmployees": {"type": "array", "items": {"$ref": "#/definitions/EmployeeCard"}}, "friendEmployeeName": {"type": "string"}, "friendEmployeeMobile": {"type": "string"}, "apps": {"type": "array", "items": {"$ref": "#/definitions/AppVo"}}, "approvalRecords": {"type": "array", "items": {"$ref": "#/definitions/RelationRelateApprovalRecordVo"}}, "remark": {"type": "string"}, "expire": {"type": "integer", "format": "int64"}}}, "ResultRelationRelateApprovalPageVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/RelationRelateApprovalPageVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ListApprovalsArg": {"type": "object", "properties": {"authInfo": {"$ref": "#/definitions/AuthInfo"}, "type": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "status": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}}}, "GetApprovalDetailResult": {"type": "object", "properties": {"approval": {"$ref": "#/definitions/RelationRelateApprovalVo"}, "operateStyle": {"type": "integer", "format": "int32"}, "employeeExisted": {"type": "boolean"}, "isSrc": {"type": "boolean"}}}, "ResultGetApprovalDetailResult": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/GetApprovalDetailResult"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "GetApprovalDetailArg": {"type": "object", "properties": {"ea": {"type": "string"}, "eid": {"type": "integer", "format": "int32"}, "id": {"type": "string"}}}, "DoApprovalArg": {"type": "object", "properties": {"id": {"type": "string"}, "ea": {"type": "string"}, "eid": {"type": "integer", "format": "int32"}, "apps": {"type": "array", "items": {"type": "string"}}, "services": {"type": "array", "items": {"type": "string"}}, "type": {"type": "integer", "format": "int32"}, "remark": {"type": "string"}, "tagIds": {"type": "array", "items": {"type": "string"}}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "customerId": {"type": "string"}, "customerName": {"type": "string"}, "enterpriseShortName": {"type": "string"}, "crmObjectId": {"type": "string"}, "downstreamType": {"type": "integer", "format": "int32"}, "get_userDefinedData": {"type": "object", "additionalProperties": {"type": "object"}}, "reuseEmployee": {"type": "boolean"}}}, "EmployeeKey": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "employeeId": {"type": "integer", "format": "int32"}}}, "GetRLevelOffsetDataResult": {"type": "object", "properties": {"changedEnterpriseList": {"type": "array", "items": {"$ref": "#/definitions/FriendEnterpriseDataAtList"}}, "removedEnterpriseAccountList": {"type": "array", "items": {"type": "string"}}, "changedEmployeeList": {"type": "array", "items": {"$ref": "#/definitions/FriendEnterpriseEmployeeDataAtList"}}, "removedEmployeeList": {"type": "array", "items": {"$ref": "#/definitions/EmployeeKey"}}, "rlevelTimeStamp": {"type": "integer", "format": "int64"}}}, "GetRLevelOffsetDataArg": {"type": "object", "properties": {"localRLevelTimeStamp": {"type": "integer", "format": "int64"}}}, "EmployeePublicData": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "enterpriseName": {"type": "string"}, "enterpriseShortName": {"type": "string"}, "employeeId": {"type": "integer", "format": "int32"}, "employeeName": {"type": "string"}, "profileImage": {"type": "string"}, "nameSpell": {"type": "string"}, "gender": {"type": "string"}, "enterpriseShortNameSpell": {"type": "string"}, "profileImagePath": {"type": "string"}, "employeeCardId": {"type": "integer", "format": "int32"}, "nameOrder": {"type": "string"}, "profileImageFullPath": {"type": "string"}}}, "GetEmployeePublicDataResult": {"type": "object", "properties": {"employeePublicDatas": {"type": "array", "items": {"$ref": "#/definitions/EmployeePublicData"}}}}, "GetEmployeePublicDataArg": {"type": "object", "properties": {"employeeKeys": {"type": "array", "items": {"$ref": "#/definitions/EmployeeKey"}}}}, "BatchGetFriendEnterpriseDataResult": {"type": "object", "properties": {"enterpriseList": {"type": "array", "items": {"$ref": "#/definitions/FriendEnterpriseDataAtList"}}}}, "BatchGetFriendEnterpriseDataArg": {"type": "object", "properties": {"enterpriseAccounts": {"type": "array", "items": {"type": "string"}}}}, "GetRLevelAllDataResult": {"type": "object", "properties": {"enterpriseList": {"type": "array", "items": {"$ref": "#/definitions/FriendEnterpriseDataAtList"}}, "employeeList": {"type": "array", "items": {"$ref": "#/definitions/FriendEnterpriseEmployeeDataAtList"}}, "lastUpdateRLevelTimeStamp": {"type": "integer", "format": "int64"}}}, "GetRLevelAllDataArg": {"type": "object"}, "GetEmployeeDataResult": {"type": "object", "properties": {"enterpriseList": {"type": "array", "items": {"$ref": "#/definitions/FriendEnterpriseDataAtList"}}, "employeeList": {"type": "array", "items": {"$ref": "#/definitions/FriendEnterpriseEmployeeDataAtList"}}, "employeePublicDatas": {"type": "array", "items": {"$ref": "#/definitions/EmployeePublicData"}}}}, "GetEmployeeDataArg": {"type": "object", "properties": {"employeeKeys": {"type": "array", "items": {"$ref": "#/definitions/EmployeeKey"}}}}, "ClientEntryVo": {"type": "object", "properties": {"type": {"type": "integer", "format": "int32"}, "url": {"type": "string"}, "entryData": {"$ref": "#/definitions/EntryDataVo"}}}, "EntryDataVo": {"type": "object", "properties": {"isSystemManager": {"type": "boolean"}, "isUpstreamEnterprise": {"type": "boolean"}, "downstreamLinkApps": {"type": "array", "items": {"$ref": "#/definitions/DownstreamLinkAppsVo"}}, "upstreamLinkApps": {"type": "array", "items": {"$ref": "#/definitions/LinkAppVo"}}}}, "ResultClientEntryVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/ClientEntryVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "DownstreamEmployeeRelationArg": {"type": "object", "properties": {"upstreamEa": {"type": "string"}, "downstreamEa": {"type": "string"}, "fsUsers": {"type": "array", "items": {"$ref": "#/definitions/FsUser"}}, "linkAppIds": {"type": "array", "items": {"type": "string"}}}}, "FsUser": {"type": "object", "properties": {"role": {"type": "integer", "format": "int32"}, "fsUserId": {"type": "integer", "format": "int32"}}}, "ResultListLinkAppVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/LinkAppVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "EnterpriseArg": {"type": "object", "properties": {"ea": {"type": "string"}, "upstreamEa": {"type": "string"}, "downstreamEa": {"type": "string"}}}, "DownstreamEmployeeAppRelationsArg": {"type": "object", "properties": {"upstreamEa": {"type": "string"}, "adminId": {"type": "integer", "format": "int32"}, "downstreamEmployeeIdsArg": {"type": "array", "items": {"$ref": "#/definitions/DownstreamEmployeeIdsArg"}}, "linkAppId": {"type": "string"}, "authWithEa": {"type": "integer", "format": "int32"}}}, "DownstreamEmployeeIdsArg": {"type": "object", "properties": {"downstreamEa": {"type": "string"}, "fsUserIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "UpdateDownstreamEmployeeAuthArg": {"type": "object", "properties": {"linkAppId": {"type": "string"}, "authType": {"type": "integer", "format": "int32"}, "downstreamEas": {"type": "array", "items": {"type": "string"}}, "downstreamEaWithEmployees": {"type": "array", "items": {"$ref": "#/definitions/DownstreamEmployeeIdsArg"}}}}, "EmployeeListVo": {"type": "object", "properties": {"employeeVos": {"type": "array", "items": {"$ref": "#/definitions/UpstreamEmployeeVo"}}, "downstreamEa": {"type": "string"}}}, "ResultListEmployeeListVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/EmployeeListVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "EmployeeByAppIdArg": {"type": "object", "properties": {"linkAppId": {"type": "string"}, "upstreamEa": {"type": "string"}, "downstreamEa": {"type": "array", "items": {"type": "string"}}}}, "DownstreamDeleteArg": {"type": "object", "properties": {"upstreamEa": {"type": "string"}, "downstreamEa": {"type": "string"}, "downstreamFsUserId": {"type": "integer", "format": "int32"}, "linkAppId": {"type": "string"}, "role": {"type": "integer", "format": "int32"}}}, "LinkAppDownstreamRelationsArg": {"type": "object", "properties": {"downstreamEa": {"type": "string"}, "linkAppIds": {"type": "array", "items": {"type": "string"}}}}, "IdsArg": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}}}, "DownstreamEmployeeVo": {"type": "object", "properties": {"ea": {"type": "string"}, "fsUserId": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "authorized": {"type": "boolean"}, "nameSpell": {"type": "string"}}}, "DownstreamEnterpriseVo": {"type": "object", "properties": {"downstreamEa": {"type": "string"}, "name": {"type": "string"}, "shortName": {"type": "string"}, "authorized": {"type": "boolean"}, "downstreamEmployees": {"type": "array", "items": {"$ref": "#/definitions/DownstreamEmployeeVo"}}, "linkAppOpen": {"type": "boolean"}, "shortNameSpell": {"type": "string"}, "nameSpell": {"type": "string"}}}, "ResultListDownstreamEnterpriseVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/DownstreamEnterpriseVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ResultListUpstreamEnterpriseAppsVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/UpstreamEnterpriseAppsVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "UpstreamEnterpriseAppsVo": {"type": "object", "properties": {"upstreamEa": {"type": "string"}, "name": {"type": "string"}, "shortName": {"type": "string"}, "linkAppVos": {"type": "array", "items": {"$ref": "#/definitions/LinkAppVo"}}}}, "CustomerServiceBaseArg": {"type": "object", "properties": {"serviceId": {"type": "string"}}}, "UpdateDownCustomerServiceAuthArg": {"type": "object", "properties": {"destEnterpriseAccount": {"type": "string"}, "customServiceIds": {"type": "array", "items": {"type": "string"}}}}, "RelationLogPageVo": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int64"}, "logs": {"type": "array", "items": {"$ref": "#/definitions/RelationLogVo"}}}}, "RelationLogVo": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "content": {"type": "string"}, "detail": {"type": "string"}, "operateTime": {"type": "integer", "format": "int64"}}}, "ResultRelationLogPageVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/RelationLogPageVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ResultListEnterpriseSimpleData": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/EnterpriseSimpleData"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "QueryRelationLogQFriendEnterpriseArg": {"type": "object", "properties": {"recordId": {"type": "string"}}}, "CreateNFileFromAFileResult": {"type": "object", "properties": {"getnPath": {"type": "string"}}}, "ResultMapStringBoolean": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "boolean"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "BindGuideArg": {"type": "object", "properties": {"appId": {"type": "string"}, "openGuide": {"type": "boolean"}}}, "ResultListWXServiceInfoVO": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/WXServiceInfoVO"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "WXServiceInfoVO": {"type": "object", "properties": {"appId": {"type": "string"}, "appName": {"type": "string"}, "appLogoUrl": {"type": "string"}, "wxAppId": {"type": "string"}, "wxAppName": {"type": "string"}, "wxFansNumber": {"type": "integer", "format": "int32"}, "appDesc": {"type": "string"}, "wechatServiceType": {"type": "string"}, "openGuide": {"type": "boolean"}}}, "ResultEmployeeInfo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/EmployeeInfo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "EmployeeInfoQueryArg": {"type": "object", "properties": {"enterpriseAccount": {"type": "string"}, "employeeId": {"type": "integer", "format": "int32"}}}, "ResultEmployeeCard": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/EmployeeCard"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "CustomerAppDataAuthBatchView": {"type": "object", "properties": {"appId": {"type": "string"}, "objectRange": {"type": "array", "items": {"$ref": "#/definitions/ObjectAuthRangeVo"}}}}, "ObjectAuthRangeVo": {"type": "object", "properties": {"objectApiName": {"type": "string"}, "range": {"type": "integer", "format": "int32"}}}, "CustomerAppDataAuth": {"type": "object", "properties": {"objectApiName": {"type": "string"}, "range": {"type": "integer", "format": "int32"}, "objectLabel": {"type": "string"}, "allowedAuthRanges": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "ResultListCustomerAppDataAuth": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CustomerAppDataAuth"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ListAppsResult": {"type": "object", "properties": {"isCustomerLinkEnabled": {"type": "boolean"}, "isEnterpriseLinkEnabled": {"type": "boolean"}, "recommendApps": {"type": "array", "items": {"$ref": "#/definitions/RecommendApp"}}, "enabledApps": {"type": "array", "items": {"$ref": "#/definitions/UnionApp"}}}}, "RecommendApp": {"type": "object", "properties": {"appId": {"type": "string"}, "name": {"type": "string"}, "introduction": {"type": "string"}, "icon": {"type": "string"}, "detailUrl": {"type": "string"}}}, "ResultListAppsResult": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/ListAppsResult"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "UnionApp": {"type": "object", "properties": {"appId": {"type": "string"}, "name": {"type": "string"}, "icon": {"type": "string"}, "introduction": {"type": "string"}, "webManagerUrl": {"type": "string"}, "isConfigToMenu": {"type": "boolean"}, "ready": {"type": "boolean"}}}, "AllocatedLayoutVO": {"type": "object", "properties": {"recordTypeList": {"type": "array", "items": {"$ref": "#/definitions/SimpleRecordTypeVO"}}, "layoutList": {"type": "array", "items": {"$ref": "#/definitions/SimpleObjectLayoutVO"}}, "roleList": {"type": "array", "items": {"$ref": "#/definitions/RoleAssociationLayoutVO"}}}}, "RecordTypeAssociationLayout": {"type": "object", "properties": {"recordTypeApiName": {"type": "string"}, "layoutApiName": {"type": "string"}}}, "ResultAllocatedLayoutVO": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/AllocatedLayoutVO"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "RoleAssociationLayoutVO": {"type": "object", "properties": {"roleCode": {"type": "string"}, "roleName": {"type": "string"}, "recordTypeAssociationLayoutList": {"type": "array", "items": {"$ref": "#/definitions/RecordTypeAssociationLayout"}}}}, "SimpleObjectLayoutVO": {"type": "object", "properties": {"api_name": {"type": "string"}, "label": {"type": "string"}, "is_default": {"type": "boolean"}}}, "SimpleRecordTypeVO": {"type": "object", "properties": {"api_name": {"type": "string"}, "label": {"type": "string"}}}, "ListAllocatedLayoutByObjectArg": {"type": "object", "properties": {"appId": {"type": "string"}, "objectApiName": {"type": "string"}}}, "AddRoleLayoutVO": {"type": "object", "properties": {"roleCode": {"type": "string"}, "recordTypeApiName": {"type": "string"}, "layoutApiName": {"type": "string"}}}, "AllocateLayoutByObjectArg": {"type": "object", "properties": {"appId": {"type": "string"}, "objectApiName": {"type": "string"}, "roleList": {"type": "array", "items": {"$ref": "#/definitions/AddRoleLayoutVO"}}}}, "AllocatedRecordTypeVO": {"type": "object", "properties": {"recordTypeList": {"type": "array", "items": {"$ref": "#/definitions/SimpleRecordTypeVO"}}, "roleList": {"type": "array", "items": {"$ref": "#/definitions/RoleAssociationRecordTypeVO"}}}}, "ResultAllocatedRecordTypeVO": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/AllocatedRecordTypeVO"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "RoleAssociationRecordTypeVO": {"type": "object", "properties": {"roleCode": {"type": "string"}, "roleName": {"type": "string"}, "recordTypeApiNames": {"type": "array", "items": {"type": "string"}}}}, "ListAllocatedRecordTypeByObjectArg": {"type": "object", "properties": {"appId": {"type": "string"}, "objectApiName": {"type": "string"}}}, "AddRoleRecordTypeVO": {"type": "object", "properties": {"roleCode": {"type": "string"}, "recordTypeApiNames": {"type": "array", "items": {"type": "string"}}}}, "AllocateRecordTypeByObjectArg": {"type": "object", "properties": {"appId": {"type": "string"}, "objectApiName": {"type": "string"}, "roleList": {"type": "array", "items": {"$ref": "#/definitions/AddRoleRecordTypeVO"}}}}, "ObjectSimpleVo": {"type": "object", "properties": {"apiName": {"type": "string"}, "label": {"type": "string"}}}, "ResultListObjectSimpleVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ObjectSimpleVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "CustomerAppAssociationObjectVO": {"type": "object", "properties": {"customerAppId": {"type": "string"}, "objectApiName": {"type": "string"}, "objectLabel": {"type": "string"}, "inLay": {"type": "boolean"}, "needAllocate": {"type": "boolean"}, "allowRemove": {"type": "boolean"}}}, "ResultListCustomerAppAssociationObjectVO": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CustomerAppAssociationObjectVO"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ListAssociationObjectsArg": {"type": "object", "properties": {"appId": {"type": "string"}}}, "BatchAddAssociationObjectsArg": {"type": "object", "properties": {"appId": {"type": "string"}, "objectApiNames": {"type": "array", "items": {"type": "string"}}, "allowRemove": {"type": "boolean"}}}, "RemoveAssociationObjectArg": {"type": "object", "properties": {"appId": {"type": "string"}, "objectApiName": {"type": "string"}}}, "FunctionInfo": {"type": "object", "properties": {"functionCode": {"type": "string"}, "displayName": {"type": "string"}, "isModifiable": {"type": "boolean"}, "isEnabled": {"type": "boolean"}, "defaultStatus": {"type": "boolean"}, "isFiledReadOnlyRequired": {"type": "boolean"}, "isAdd": {"type": "boolean"}, "isIntelligentForm": {"type": "boolean"}}}, "ObjectFunctionInfo": {"type": "object", "properties": {"apiName": {"type": "string"}, "displayName": {"type": "string"}, "isHaveFieldPrivilege": {"type": "boolean"}, "viewListFuncCode": {"type": "string"}, "isEditable": {"type": "boolean"}, "isHaveViewListPermiss": {"type": "boolean"}, "isBIObject": {"type": "boolean"}, "functionInfoList": {"type": "array", "items": {"$ref": "#/definitions/FunctionInfo"}}}}, "ResultListObjectFunctionInfo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ObjectFunctionInfo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "AppRoleArg": {"type": "object", "properties": {"appId": {"type": "string"}, "roleId": {"type": "string"}}}, "UpdateAppRolePermissionArg": {"type": "object", "properties": {"appId": {"type": "string"}, "roleId": {"type": "string"}, "permission": {"type": "object", "additionalProperties": {"type": "boolean"}}}}, "AppRoleFieldPermission": {"type": "object", "properties": {"permissionScope": {"type": "array", "items": {"$ref": "#/definitions/Option"}}, "fieldPermission": {"type": "array", "items": {"$ref": "#/definitions/FieldPermission"}}}}, "FieldPermission": {"type": "object", "properties": {"fieldApiName": {"type": "string"}, "fieldCaption": {"type": "string"}, "isRequired": {"type": "boolean"}, "isModifiable": {"type": "boolean"}, "permission": {"type": "integer", "format": "int32"}, "isEditableOfInvisible": {"type": "boolean"}, "isEditableOfReadOnly": {"type": "boolean"}}}, "Option": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"type": "object"}, "children": {"type": "array", "items": {"$ref": "#/definitions/Option"}}}}, "ResultAppRoleFieldPermission": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/AppRoleFieldPermission"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "AppRoleFieldPermissionArg": {"type": "object", "properties": {"appId": {"type": "string"}, "roleId": {"type": "string"}, "objectApiName": {"type": "string"}, "permission": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}}}, "AppRoleView": {"type": "object", "properties": {"roles": {"type": "array", "items": {"$ref": "#/definitions/RoleVo"}}, "roleCount": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}, "supportRoleExt": {"type": "boolean"}}}, "ResultAppRoleView": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/AppRoleView"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "ListAppRolesArg": {"type": "object", "properties": {"appId": {"type": "string"}, "upstreamEa": {"type": "string"}}}, "CrmObjectSimpleVo": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "ownerId": {"type": "integer", "format": "int32"}, "ownerName": {"type": "string"}, "bindStatus": {"type": "integer", "format": "int32"}}}, "ResultCrmObjectSimpleVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/CrmObjectSimpleVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "GetCrmBindArg": {"type": "object", "properties": {"destEnterpriseAccount": {"type": "string"}, "objectApiName": {"type": "string"}}}, "UpdateCrmBindArg": {"type": "object", "properties": {"destEnterpriseAccount": {"type": "string"}, "customerID": {"type": "string"}, "objectApiName": {"type": "string"}, "crmObjectId": {"type": "string"}, "ownerId": {"type": "integer", "format": "int32"}}}, "PagedCrmObjectSimpleVo": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int64"}, "crmObjects": {"type": "array", "items": {"$ref": "#/definitions/CrmObjectSimpleVo"}}}}, "ResultPagedCrmObjectSimpleVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/PagedCrmObjectSimpleVo"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "PagedQueryCrmObjectArg": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "objectApiName": {"type": "string"}, "name": {"type": "string"}, "recordTypeApiName": {"type": "string"}, "offset": {"type": "integer", "format": "int32"}, "limit": {"type": "integer", "format": "int32"}}}, "CrmObjectApiNameArg": {"type": "object", "properties": {"objectApiName": {"type": "string"}}}, "RelationEnterpriseType": {"type": "object", "properties": {"relationEnterpriseType": {"type": "integer", "format": "int32"}, "recordTypeApiName": {"type": "string"}, "recordTypeName": {"type": "string"}}}, "RelationPatternVo": {"type": "object", "properties": {"patternType": {"type": "integer", "format": "int32"}, "mapperApiName": {"type": "string"}, "accountManageMode": {"type": "integer", "format": "int32"}, "relationEnterpriseTypes": {"type": "array", "items": {"$ref": "#/definitions/RelationEnterpriseType"}}, "relationedDownstreamNum": {"type": "integer", "format": "int64"}}}, "ResultListRelationPatternVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/RelationPatternVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "RelationRecordTypeResult": {"type": "object", "properties": {"recordTypeApiName": {"type": "string"}, "recordTypeName": {"type": "string"}}}, "ResultRelationRecordTypeResult": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/RelationRecordTypeResult"}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "RelationEnterpriseTypeVo": {"type": "object", "properties": {"relationEnterpriseType": {"type": "integer", "format": "int32"}}}, "RemoveRelationPatternConfigArg": {"type": "object", "properties": {"patternType": {"type": "integer", "format": "int32"}}}, "LinkAppAssociationObjectVO": {"type": "object", "properties": {"linkAppId": {"type": "string"}, "objectApiName": {"type": "string"}, "objectLabel": {"type": "string"}, "inLay": {"type": "boolean"}, "needAllocate": {"type": "boolean"}, "allowRemove": {"type": "boolean"}}}, "ResultListLinkAppAssociationObjectVO": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/LinkAppAssociationObjectVO"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "AppRoleAssignVo": {"type": "object", "properties": {"employee": {"type": "object"}, "publicTags": {"type": "array", "items": {"type": "string"}}, "enterpriseName": {"type": "string"}, "isCurrentMain": {"type": "boolean"}, "isModifiable": {"type": "boolean"}}}, "ResultListAppRoleAssignVo": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/AppRoleAssignVo"}}, "success": {"type": "boolean"}, "errDescription": {"type": "string"}, "errMessage": {"type": "string"}}}, "AppRoleOuterAssignArg": {"type": "object", "properties": {"appId": {"type": "string"}, "roleId": {"type": "string"}, "upstreamEa": {"type": "string"}, "outerUIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "BatchUpdateDiffMainAssignArg": {"type": "object", "properties": {"appId": {"type": "string"}, "upstreamEa": {"type": "string"}, "mainAssigns": {"type": "object", "additionalProperties": {"type": "string"}}}}, "AddAppRoleArg": {"type": "object", "properties": {"appId": {"type": "string"}, "roleName": {"type": "string"}, "roleDesc": {"type": "string"}}}}}