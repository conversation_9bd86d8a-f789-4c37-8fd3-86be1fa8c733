package com.facishare.linkapp.api.result;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 策略对对象访问的权限关系表 Created by huanghp on 2018/6/22.
 */
@Data
public class UpstreamPolicyDataAuthRangeData implements Serializable {
    private Long id;
    /**
     * 方案Id
     */
    private String policyId;
    /**
     * 对象apiName
     */
    private String objectApiName;
    /**
     * 下游访问范围
     *
     * @see com.facishare.enterprise.common.constant.LinkAppDataAuthRange
     */
    private Integer downstreamAuthRange;
    private Date createTime;
    private Date updateTime;
}
