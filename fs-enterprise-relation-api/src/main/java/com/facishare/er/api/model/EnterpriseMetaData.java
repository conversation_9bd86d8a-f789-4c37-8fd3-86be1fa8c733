package com.facishare.er.api.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.er.api.model.vo.RoleVo;
import com.facishare.er.api.model.vo.TagVo;
import io.protostuff.Tag;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * Created by wa<PERSON><PERSON> on 16/5/3.
 */
@Data
public class EnterpriseMetaData implements Serializable {
    private static final long serialVersionUID = -1414939094636778708L;
    @Tag(1)
    @JSONField(name = "M1")
    private String enterpriseAccount;
    @Tag(2)
    @JSONField(name = "M2")
    private int maxRelationNumber;
    //剩余可用配额
    private int leftRelationNumber;
    //实际使用的配额数
    private int usedRelationNumber;
    private String crmVersion;
    @Tag(3)
    @JSONField(name = "M3")
    private String inviteCode;
    @Tag(4)
    @JSONField(name = "M4")
    private long inviteCodeExpireTime;
    @Tag(6)
    @JSONField(name = "M6")
    private long updateTime;
    @Tag(7)
    @JSONField(name = "M7")
    private List<TagVo> tags;
    @Tag(8)
    @JSONField(name = "M8")
    private List<RoleVo> roles;
    /**
     * 下游伙伴添加我方对接人是否需要审批，true：需要，false：不需要
     */
    @Tag(9)
    @JSONField(name = "M9")
    private Boolean downEmployeeApply;
    /**
     * 下游伙伴设置我方对接人应用权限是否需要审批，true：需要，false：不需要
     */
    @Tag(10)
    @JSONField(name = "M10")
    private Boolean downAppRoleApply;
    /**
     * 是否开启CRM数据同步
     */
    @Tag(11)
    @JSONField(name = "M11")
    private Boolean crmRelationInfoSyncSwitch;
    /**
     * 是否开启关联失效，系统自动删除客户，联系人
     */
    @Tag(12)
    @JSONField(name = "M12")
    private Boolean crmRelationOperationSwitch;
}
