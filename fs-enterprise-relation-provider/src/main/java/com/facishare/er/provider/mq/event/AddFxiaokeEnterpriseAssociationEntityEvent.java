package com.facishare.er.provider.mq.event;

import com.facishare.common.fsi.ProtoBase;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 新增FxiaokeEnterpriseAssociationEntity事件
 */
@Getter
@Setter
@ToString
public class AddFxiaokeEnterpriseAssociationEntityEvent extends ProtoBase {
    public static final Integer TENANT_BIND_FLAG = 9001;
    private String ea;
    private Long outerTenantId;

    private AddFxiaokeEnterpriseAssociationEntityEvent(String ea, Long outerTenantId) {
        this.ea = ea;
        this.outerTenantId = outerTenantId;
    }

    public static AddFxiaokeEnterpriseAssociationEntityEvent getInstance(String ea, Long outerTenantId) {
        return new AddFxiaokeEnterpriseAssociationEntityEvent(ea, outerTenantId);
    }
}
