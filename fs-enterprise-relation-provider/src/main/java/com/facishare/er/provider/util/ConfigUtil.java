package com.facishare.er.provider.util;

import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.facishare.enterprise.common.util.I18nUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.util.List;
import java.util.Map;

/**
 * Created by z<PERSON>j<PERSON> on 2017/4/18.
 */
public class ConfigUtil {
    public static String crmUrl;
    public static String layoutPaasUrl;
    public static Map<String, String> customerPresetFieldsMapped;
    public static String crmAppId = "FSAID_5f5e248"; //crm应用ID，默认ceshi113
    public static String manageToken = "enterpriseRelation2017";
    public static String avatarUrlPrefix; //头像获取方式前缀
    public static String fscEMFileUrlPath; // Oss文件访问地址
    public static String fsInnerImageUrlPath; // 内部存储图片地址
    // 企业类型文件访问方式,比如企业logo
    // http://wiki.firstshare.cn/pages/viewpage.action?pageId=105581865
    public static String erCookieNpathUrlFormat;
    public static String defaultEnterpriseLogo;
    public static String createPublicEmployeeUrl;
    public static String publicEmployeeIntroduceUrl;
    public static long inviteCodeEffectiveTime = 604800L * 1000; //邀请码有效时间，单位：秒
    public static String templateMsgTitleColor = "#234587"; //模版消息标题颜色
    public static String templateInfoValueColor = "#234587"; //模版消息内容颜色
    public static String approvalDetailUrl = ""; //审批详情跳转url
    public static String grayEas63 = "";
    public static String templateButtonColor = "#234587"; //模版消息链接颜色
    public static int maxFriendEmployeeShowNum = 3; //开通对接人互联业务审批结果通告中展示的最大对接人数
    public static int accountObjHasIsErEnterprise = 0;
    public static String upstreamRemoveDownstreamPublicEmployeeNotifyDownAdmin = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_38);
    public static String upstreamRemoveDownstreamPublicEmployeeNotifyDownEmployee = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_39);
    public static String removeSelfPublicEmployeeNotifySelfEmployee = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_40);
    public static String removeSelfPublicEmployeeNotifyDestAdmin = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_41);
    public static String upstreamCreatePublicEmployeeNotifyDownstreamAdmin = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_42);
    public static String publicEmployeeRemindOTTMsgBody = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_43);
    public static String deleteRelationNotifySrcAdmin = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_44);
    public static String deleteRelationNotifySrcPublicEmployee = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_45);
    public static String deleteRelationNotifyDestAdmin = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_46);
    public static String deleteRelationNotifyDestPublicEmployee = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_47);
    public static String upAddDownNoExistEmployeeNotifyDownAdmin = I18nUtil.get(I18nKeyEnum.EPI_UTIL_CONFIGUTIL_49);
    public static String upAddDownExistedEmployeeNotifyDownAdmin = I18nUtil.get(I18nKeyEnum.EPI_UTIL_CONFIGUTIL_51);
    public static String upAddDownExistedEmployeeApprovalUrl = "";
    public static String upAddDownNoExistEmployeeApprovalContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_53);
    public static String upAddDownExistedEmployeeApprovalContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_54);
    public static String upAddDownEmployeeApprovalDefaultRemark = I18nUtil.get(I18nKeyEnum.EPI_UTIL_CONFIGUTIL_56);
    public static String upAddDownEmployeeResultNotifyUpApplicantTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_56);
    public static String upAddDownEmployeeTimeOutNotifyUpApplicantTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_57);
    public static String upAddDownEmployeeApplyNotifyUpApplicantTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_58);
    public static String upAddDownEmployeeApplyNotifyUpApplicantFirstContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_59);
    public static String upAddDownEmployeeAgreeNotifyUpApplicantFirstContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_60);
    public static String upAddDownEmployeeAgreeNotifyUpApplicantRemark = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_61);
    public static String upAddDownEmployeeRejectNotifyUpApplicantFirstContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_62);
    public static String upAddDownEmployeeTimeOutNotifyUpApplicantFirstContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_63);
    public static String upAddDownEmployeeType = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_64);
    public static long upAddDownApprovalExpireInterval = 172800;//上游添加下游对接人审批过期间隔，单位：秒
    public static String upAddDownMobileExistedRemind = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_67);
    public static String newCreatedEmployeeInviteSmsContent = I18nUtil.get(I18nKeyEnum.EPI_UTIL_CONFIGUTIL_69);
    public static String downAddEmployeeApplyNotifyUpAdminTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_70);
    public static String downAddEmployeeAgreeNotifyDownApplicantTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_71);
    public static String downAddEmployeeRejectNotifyDownApplicantTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_72);
    public static String downAddEmployeeType = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_73);
    public static String relationApplyNotifyReceiverAdminTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_74);
    public static String relationApplyAgreeNotifyApplicantTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_75);
    public static String relationApplyRejectNotifyApplicantTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_76);
    public static String relationApplyType = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_77);
    public static String employeeBusinessNotifyReceiverAdminTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_78);
    public static String employeeBusinessAgreeNotifyApplicantTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_79);
    public static String employeeBusinessRejectNotifyApplicantTitleContent = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_80);
    public static String employeeBusinessType = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_81);
    public static String quotaExpireSettingHint = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_82);
    public static String quotaExpireRelationHint = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_83);
    public static String updateAccountManagerMode2AssistManage = I18nUtil.get(I18nKeyEnum.EPI_UTIL_CONFIGUTIL_85);
    public static String noticeAppId;
    public static Integer saleOrderEi;
    //UDInt12__c
    public static String accountEiFieldName;
    public static String networkDiskAppId;
    public static String asyncMigration;
    //互联应用使用分析重复操作生效的间隔（redis超时时间）
    public static Integer usageAnalysisExpire;
    public static int DEFAULT_CREATOR = 1000;
    public static List<String> notCheckQuotaEas = Lists.newArrayList();
    /************
     * 互联log start
     ***********/
    public static String downApplyRelationLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_101);
    public static String srcReleaseRelationWithDestLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_102);
    public static String srcRelationReleasedByDestLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_103);
    public static String srcRelationCreationBySrcLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_104);
    public static String createFsAccountOpenForNoTenantLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_105);
    public static String selfRegisterRelationCreationBySrcLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_106);
    public static String srcRelationCreationByDestLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_107);
    public static String srcAddSelfPublicEmployeeLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_108);
    public static String srcRemoveSelfPublicEmployeeLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_109);
    public static String srcChangePublicEmployeeScopeLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_110);
    public static String upAddDownPublicEmployeeLog4Up = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_111);
    public static String upRemoveDownPublicEmployeeLog4Up = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_112);
    public static String srcCreateGlobalPublicEmployeeLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_113);
    public static String appStatusChangeLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_114);
    public static String appStatusOpenLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_115);
    public static String appStatusCloseLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_116);
    public static String appAdminChangeLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_117);
    public static String appDownRoleChangeLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_118);
    public static String accountManagerModeChangeLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_119);
    public static String enterpriseNameChangeLog = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_CONFIGUTIL_120);
    public static String sendCodeWebUrl = "";
    public static String sendCodeAppUrl = "";
    public static String applyrElationWebUrl = "";
    public static String applyrElationAppUrl = "";
    public static String createPublicEmployeeWebUrl = "";
    public static String createPublicEmployeeAppUrl = "";
    public static String stopPublicEmployeeWebUrl = "";
    public static String stopPublicEmployeeAppUrl = "";
    public static String crossHelperAppIdKey = "FSAID_11490cb0";
    public static String pushPublicEmployeeOTMessageUrl = "";
    //    public static String prmObjectDetailUrl = "www.ceshi113.com/hcrm/prm/#/crm/detail/${apiname}/${dataid}";
//    public static String objectDetailUrl = "https://www.ceshi113.com/ec/pages/gray/#/meta/views?objectApiName=${objectApiName}&objectId=${objectId}&fsAppId=${fsAppId}";
    public static String objectDetailUrl = "";
    public static String bindGuideLog;
    /************
     * 互联log end
     ***********/

    //微联服务号start
    public static String msgContentTemplate;
    public static String invitionSmsContentTemplate;
    public static String openErAccountSmsContentTemplate = I18nUtil.get(I18nKeyEnum.EPI_UTIL_CONFIGUTIL_135);
    public static String inviteSmsContentTemplate = I18nUtil.get(I18nKeyEnum.EPI_UTIL_CONFIGUTIL_136);
    public static Gson gson = new Gson();

    static {
        ConfigFactory.getInstance().getConfig("fs-er-provider-application", config -> {
            crmUrl = config.get("crm_url");
            layoutPaasUrl = config.get("inner_v1_url");
            String customerPresetFeileStr = config.get("customer_preset_field");
            if (!Strings.isNullOrEmpty(customerPresetFeileStr)) {
                try {
                    customerPresetFieldsMapped = gson.fromJson(customerPresetFeileStr, new TypeToken<Map<String, String>>() {
                    }.getType());
                } catch (Exception e) {
                }
            }

            String notCheckQuotaEasStr = config.get("not_check_quota_eas");
            if (!Strings.isNullOrEmpty(notCheckQuotaEasStr)) {
                try {
                    notCheckQuotaEas = gson.fromJson(notCheckQuotaEasStr, new TypeToken<List<String>>() {
                    }.getType());
                } catch (Exception e) {
                }
            }
        });

        ConfigFactory.getConfig("fs-er-provider-log", config -> {
            downApplyRelationLog = config.get("downApplyRelationLog", downApplyRelationLog);
            srcReleaseRelationWithDestLog = config.get("srcReleaseRelationWithDestLog", srcReleaseRelationWithDestLog);
            srcRelationReleasedByDestLog = config.get("srcRelationReleasedByDestLog", srcRelationReleasedByDestLog);
            createFsAccountOpenForNoTenantLog = config.get("createFsAccountOpenForNoTenantLog", srcRelationCreationBySrcLog);
            srcRelationCreationBySrcLog = config.get("srcRelationCreationBySrcLog", srcRelationCreationBySrcLog);
            srcRelationCreationByDestLog = config.get("srcRelationCreationByDestLog", srcRelationCreationByDestLog);
            srcAddSelfPublicEmployeeLog = config.get("srcAddSelfPublicEmployeeLog", srcAddSelfPublicEmployeeLog);
            srcRemoveSelfPublicEmployeeLog = config.get("srcRemoveSelfPublicEmployeeLog", srcRemoveSelfPublicEmployeeLog);
            srcChangePublicEmployeeScopeLog = config.get("srcChangePublicEmployeeScopeLog", srcChangePublicEmployeeScopeLog);
            upAddDownPublicEmployeeLog4Up = config.get("upAddDownPublicEmployeeLog4Up", upAddDownPublicEmployeeLog4Up);
            upRemoveDownPublicEmployeeLog4Up = config.get("upRemoveDownPublicEmployeeLog4Up", upRemoveDownPublicEmployeeLog4Up);
            srcCreateGlobalPublicEmployeeLog = config.get("srcCreateGlobalPublicEmployeeLog", srcCreateGlobalPublicEmployeeLog);
            appStatusChangeLog = config.get("appStatusChangeLog", appStatusChangeLog);
            appStatusOpenLog = config.get("appStatusOpenLog");
            appStatusCloseLog = config.get("appStatusCloseLog");
            appAdminChangeLog = config.get("appAdminChangeLog", appAdminChangeLog);
            appDownRoleChangeLog = config.get("appDownRoleChangeLog", appDownRoleChangeLog);
            bindGuideLog = config.get("bindGuideLog");
        });

        ConfigFactory.getConfig("fs-er-properties", config -> {
            usageAnalysisExpire = config.getInt("usageAnalysisExpire");
            asyncMigration = config.get("asyncMigration");
            noticeAppId = config.get("noticeAppId");
            saleOrderEi = config.getInt("saleOrderEi", 1);
            accountEiFieldName = config.get("accountEiFieldName", "UDInt12__c");
            networkDiskAppId = config.get("networkDiskAppId");
            crmAppId = config.get("crmAppId", crmAppId);
            manageToken = config.get("manageToken", manageToken);
            templateMsgTitleColor = config.get("title.color", templateMsgTitleColor);
            templateInfoValueColor = config.get("info.value.color", templateInfoValueColor);
            approvalDetailUrl = config.get("approval.detail.url", approvalDetailUrl);
            grayEas63 = config.get("gray.eas.63", grayEas63);
            templateButtonColor = config.get("button.color", templateButtonColor);
            maxFriendEmployeeShowNum = config.getInt("maxFriendEmployeeShowNum", maxFriendEmployeeShowNum);
            accountObjHasIsErEnterprise = config.getInt("accountObjHasIsErEnterprise", accountObjHasIsErEnterprise);
            avatarUrlPrefix = config.get("avatarUrlPrefix");
            fscEMFileUrlPath = config.get("fscEMFileUrlPath");
            fsInnerImageUrlPath = config.get("fsInnerImageUrlPath");
            erCookieNpathUrlFormat = config.get("erCookieNpathUrlFormat");
            defaultEnterpriseLogo = config.get("default_enterprise_logo");
            publicEmployeeIntroduceUrl = config.get("public_employee_introduce_url");
            createPublicEmployeeUrl = config.get("create_public_employee_url");
            inviteCodeEffectiveTime = config.getLong("effectiveTime", 604800) * 1000;
            upstreamRemoveDownstreamPublicEmployeeNotifyDownAdmin = config.get("upRemoveDownEmployee.notifyDownAdmin", upstreamRemoveDownstreamPublicEmployeeNotifyDownAdmin);
            upstreamRemoveDownstreamPublicEmployeeNotifyDownEmployee = config.get("upRemoveDownEmployee.notifyDownRemovedEmployee", upstreamRemoveDownstreamPublicEmployeeNotifyDownEmployee);
            removeSelfPublicEmployeeNotifySelfEmployee = config.get("removeSelfPublicEmployee.notifySelfEmployee", removeSelfPublicEmployeeNotifySelfEmployee);
            removeSelfPublicEmployeeNotifyDestAdmin = config.get("removeSelfPublicEmployee.notifyDestAdmin", removeSelfPublicEmployeeNotifyDestAdmin);
            upstreamCreatePublicEmployeeNotifyDownstreamAdmin = config.get("upstreamCreatePublicEmployee.notifyDownAdmin", upstreamCreatePublicEmployeeNotifyDownstreamAdmin);
            publicEmployeeRemindOTTMsgBody = config.get("publicEmployeeRemindOTTMsgBody", publicEmployeeRemindOTTMsgBody);
            deleteRelationNotifySrcAdmin = config.get("deleteRelation.notifySrcAdmin", deleteRelationNotifySrcAdmin);
            deleteRelationNotifySrcPublicEmployee = config.get("deleteRelation.notifySrcPublicEmployee", deleteRelationNotifySrcPublicEmployee);
            deleteRelationNotifyDestAdmin = config.get("deleteRelation.notifyDestAdmin", deleteRelationNotifyDestAdmin);
            deleteRelationNotifyDestPublicEmployee = config.get("deleteRelation.notifyDestPublicEmployee", deleteRelationNotifyDestPublicEmployee);

            /**5.7.1*/
            upAddDownNoExistEmployeeNotifyDownAdmin = config.get("upAddDownNoExistEmployeeNotifyDownAdmin", upAddDownNoExistEmployeeNotifyDownAdmin);
            upAddDownExistedEmployeeNotifyDownAdmin = config.get("upAddDownExistedEmployeeNotifyDownAdmin", upAddDownExistedEmployeeNotifyDownAdmin);
            upAddDownNoExistEmployeeApprovalContent = config.get("upAddDownNoExistEmployeeApprovalContent", upAddDownNoExistEmployeeApprovalContent);
            upAddDownExistedEmployeeApprovalContent = config.get("upAddDownExistedEmployeeApprovalContent", upAddDownExistedEmployeeApprovalContent);
            upAddDownExistedEmployeeApprovalUrl = config.get("upAddDownExistedEmployeeApprovalUrl", upAddDownExistedEmployeeApprovalUrl);
            upAddDownEmployeeApprovalDefaultRemark = config.get("upAddDownEmployeeApprovalDefaultRemark", upAddDownEmployeeApprovalDefaultRemark);
            upAddDownEmployeeApplyNotifyUpApplicantFirstContent = config.get("upAddDownEmployeeApplyNotifyUpApplicantFirstContent", upAddDownEmployeeApplyNotifyUpApplicantFirstContent);
            upAddDownEmployeeAgreeNotifyUpApplicantFirstContent = config.get("upAddDownEmployeeAgreeNotifyUpApplicantFirstContent", upAddDownEmployeeAgreeNotifyUpApplicantFirstContent);
            upAddDownEmployeeAgreeNotifyUpApplicantRemark = config.get("upAddDownEmployeeAgreeNotifyUpApplicantRemark", upAddDownEmployeeAgreeNotifyUpApplicantRemark);
            upAddDownEmployeeRejectNotifyUpApplicantFirstContent = config.get("upAddDownEmployeeRejectNotifyUpApplicantFirstContent", upAddDownEmployeeRejectNotifyUpApplicantFirstContent);
            upAddDownEmployeeTimeOutNotifyUpApplicantFirstContent = config.get("upAddDownEmployeeTimeOutNotifyUpApplicantFirstContent", upAddDownEmployeeTimeOutNotifyUpApplicantFirstContent);
            upAddDownEmployeeResultNotifyUpApplicantTitleContent = config.get("upAddDownEmployeeResultNotifyUpApplicantTitleContent", upAddDownEmployeeResultNotifyUpApplicantTitleContent);
            upAddDownEmployeeTimeOutNotifyUpApplicantTitleContent = config.get("upAddDownEmployeeTimeOutNotifyUpApplicantTitleContent", upAddDownEmployeeTimeOutNotifyUpApplicantTitleContent);
            upAddDownEmployeeApplyNotifyUpApplicantTitleContent = config.get("upAddDownEmployeeApplyNotifyUpApplicantTitleContent", upAddDownEmployeeApplyNotifyUpApplicantTitleContent);
            upAddDownEmployeeType = config.get("upAddDownEmployeeType", upAddDownEmployeeType);
            upAddDownApprovalExpireInterval = config.getLong("upAddDownApprovalExpireInterval", upAddDownApprovalExpireInterval) * 1000;
            upAddDownMobileExistedRemind = config.get("upAddDownMobileExistedRemind", upAddDownMobileExistedRemind);
            newCreatedEmployeeInviteSmsContent = config.get("newCreatedEmployeeInviteSmsContent", newCreatedEmployeeInviteSmsContent);

            downAddEmployeeApplyNotifyUpAdminTitleContent = config.get("downAddEmployeeApplyNotifyUpAdminTitleContent", downAddEmployeeApplyNotifyUpAdminTitleContent);
            downAddEmployeeAgreeNotifyDownApplicantTitleContent = config.get("downAddEmployeeAgreeNotifyDownApplicantTitleContent", downAddEmployeeAgreeNotifyDownApplicantTitleContent);
            downAddEmployeeRejectNotifyDownApplicantTitleContent = config.get("downAddEmployeeRejectNotifyDownApplicantTitleContent", downAddEmployeeRejectNotifyDownApplicantTitleContent);
            downAddEmployeeType = config.get("downAddEmployeeType", downAddEmployeeType);

            employeeBusinessNotifyReceiverAdminTitleContent = config.get("employeeBusinessNotifyReceiverAdminTitleContent", employeeBusinessNotifyReceiverAdminTitleContent);
            employeeBusinessAgreeNotifyApplicantTitleContent = config.get("employeeBusinessAgreeNotifyApplicantTitleContent", employeeBusinessAgreeNotifyApplicantTitleContent);
            employeeBusinessRejectNotifyApplicantTitleContent = config.get("employeeBusinessRejectNotifyApplicantTitleContent", employeeBusinessRejectNotifyApplicantTitleContent);
            employeeBusinessType = config.get("employeeBusinessType", employeeBusinessType);

            relationApplyNotifyReceiverAdminTitleContent = config.get("relationApplyNotifyReceiverAdminTitleContent", relationApplyNotifyReceiverAdminTitleContent);
            relationApplyAgreeNotifyApplicantTitleContent = config.get("relationApplyAgreeNotifyApplicantTitleContent", relationApplyAgreeNotifyApplicantTitleContent);
            relationApplyRejectNotifyApplicantTitleContent = config.get("relationApplyRejectNotifyApplicantTitleContent", relationApplyRejectNotifyApplicantTitleContent);
            relationApplyType = config.get("relationApplyType", relationApplyType);
            quotaExpireSettingHint = config.get("quotaExpireSettingHint", quotaExpireSettingHint);

            msgContentTemplate = config.get("msg_content_template");
            invitionSmsContentTemplate = config.get("invition_sms_content_template");
        });

//        public static String prmObjectDetailUrl = "www.ceshi113.com/hcrm/prm/#/crm/detail/${apiname}/${dataid}";
        ConfigFactory.getInstance().getConfig("fs-er-provider-properties", config -> {
            sendCodeWebUrl = config.get("managerment.sendcode.weburl", sendCodeWebUrl);
            sendCodeAppUrl = config.get("managerment.sendcode.appurl", sendCodeAppUrl);
            applyrElationWebUrl = config.get("managerment.applyrelation.weburl", applyrElationWebUrl);
            applyrElationAppUrl = config.get("managerment.applyrelation.appurl", applyrElationAppUrl);
            createPublicEmployeeWebUrl = config.get("managerment.createpublicemployee.weburl", createPublicEmployeeWebUrl);
            createPublicEmployeeAppUrl = config.get("managerment.createpublicemployee.appurl", createPublicEmployeeAppUrl);
            stopPublicEmployeeWebUrl = config.get("managerment.stoppublicemployee.weburl", stopPublicEmployeeWebUrl);
            stopPublicEmployeeAppUrl = config.get("managerment.stoppublicemployee.appurl", stopPublicEmployeeAppUrl);
            crossHelperAppIdKey = config.get("cosshelper.appId", crossHelperAppIdKey);
            objectDetailUrl = config.get("objectDetailUrl", objectDetailUrl);
            pushPublicEmployeeOTMessageUrl = config.get("pushPublicEmployeeOTMessageUrl", pushPublicEmployeeOTMessageUrl);
        });
    }

    public static String getUpstreamRemoveDownstreamPublicEmployeeNotifyDownAdmin() {
        return I18nUtil.get("fs-er-properties.upremovedownemployee.notifydownadmin", upstreamRemoveDownstreamPublicEmployeeNotifyDownAdmin);
    }

    public static String getUpstreamRemoveDownstreamPublicEmployeeNotifyDownEmployee() {
        return I18nUtil.get("fs-er-properties.upremovedownemployee.notifydownremovedemployee", upstreamRemoveDownstreamPublicEmployeeNotifyDownEmployee);
    }

    public static String getRemoveSelfPublicEmployeeNotifySelfEmployee() {
        return I18nUtil.get("fs-er-properties.removeselfpublicemployee.notifyselfemployee", removeSelfPublicEmployeeNotifySelfEmployee);
    }

    public static String getRemoveSelfPublicEmployeeNotifyDestAdmin() {
        return I18nUtil.get("fs-er-properties.removeselfpublicemployee.notifydestadmin", removeSelfPublicEmployeeNotifyDestAdmin);
    }

    public static String getUpstreamCreatePublicEmployeeNotifyDownstreamAdmin() {
        return I18nUtil.get("fs-er-properties.upstreamcreatepublicemployee.notifydownadmin", upstreamCreatePublicEmployeeNotifyDownstreamAdmin);
    }

    public static String getPublicEmployeeRemindOTTMsgBody() {
        return I18nUtil.get("fs-er-properties.publicemployeeremindottmsgbody", publicEmployeeRemindOTTMsgBody);
    }

    public static String getDeleteRelationNotifySrcAdmin() {
        return I18nUtil.get("fs-er-properties.deleterelation.notifysrcadmin", deleteRelationNotifySrcAdmin);
    }

    public static String getDeleteRelationNotifySrcPublicEmployee() {
        return I18nUtil.get("fs-er-properties.deleterelation.notifysrcpublicemployee", deleteRelationNotifySrcPublicEmployee);
    }

    public static String getDeleteRelationNotifyDestAdmin() {
        return I18nUtil.get("fs-er-properties.deleterelation.notifydestadmin", deleteRelationNotifyDestAdmin);
    }

    public static String getDeleteRelationNotifyDestPublicEmployee() {
        return I18nUtil.get("fs-er-properties.deleterelation.notifydestpublicemployee", deleteRelationNotifyDestPublicEmployee);
    }

    public static String getUpAddDownNoExistEmployeeNotifyDownAdmin() {
        return I18nUtil.get("fs-er-properties.upadddownnoexistemployeenotifydownadmin", upAddDownNoExistEmployeeNotifyDownAdmin);
    }

    public static String getUpAddDownExistedEmployeeNotifyDownAdmin() {
        return I18nUtil.get("fs-er-properties.upadddownexistedemployeenotifydownadmin", upAddDownExistedEmployeeNotifyDownAdmin);
    }

    public static String getUpAddDownNoExistEmployeeApprovalContent() {
        return I18nUtil.get("fs-er-properties.upadddownnoexistemployeeapprovalcontent", upAddDownNoExistEmployeeApprovalContent);
    }

    public static String getUpAddDownExistedEmployeeApprovalContent() {
        return I18nUtil.get("fs-er-properties.upadddownexistedemployeeapprovalcontent", upAddDownExistedEmployeeApprovalContent);
    }

    public static String getUpAddDownEmployeeApprovalDefaultRemark() {
        return I18nUtil.get("fs-er-properties.upadddownemployeeapprovaldefaultremark", upAddDownEmployeeApprovalDefaultRemark);
    }

    public static String getUpAddDownEmployeeResultNotifyUpApplicantTitleContent() {
        return I18nUtil.get("fs-er-properties.upadddownemployeeresultnotifyupapplicanttitlecontent", upAddDownEmployeeResultNotifyUpApplicantTitleContent);
    }

    public static String getUpAddDownEmployeeTimeOutNotifyUpApplicantTitleContent() {
        return I18nUtil.get("fs-er-properties.upadddownemployeetimeoutnotifyupapplicanttitlecontent", upAddDownEmployeeTimeOutNotifyUpApplicantTitleContent);
    }

    public static String getUpAddDownEmployeeApplyNotifyUpApplicantTitleContent() {
        return I18nUtil.get("fs-er-properties.upadddownemployeeapplynotifyupapplicanttitlecontent", upAddDownEmployeeApplyNotifyUpApplicantTitleContent);
    }

    public static String getUpAddDownEmployeeApplyNotifyUpApplicantFirstContent() {
        return I18nUtil.get("fs-er-properties.upadddownemployeeapplynotifyupapplicantfirstcontent", upAddDownEmployeeApplyNotifyUpApplicantFirstContent);
    }

    public static String getUpAddDownEmployeeAgreeNotifyUpApplicantFirstContent() {
        return I18nUtil.get("fs-er-properties.upadddownemployeeagreenotifyupapplicantfirstcontent", upAddDownEmployeeAgreeNotifyUpApplicantFirstContent);
    }

    public static String getUpAddDownEmployeeAgreeNotifyUpApplicantRemark() {
        return I18nUtil.get("fs-er-properties.upadddownemployeeagreenotifyupapplicantremark", upAddDownEmployeeAgreeNotifyUpApplicantRemark);
    }

    public static String getUpAddDownEmployeeRejectNotifyUpApplicantFirstContent() {
        return I18nUtil.get("fs-er-properties.upadddownemployeerejectnotifyupapplicantfirstcontent", upAddDownEmployeeRejectNotifyUpApplicantFirstContent);
    }

    public static String getUpAddDownEmployeeTimeOutNotifyUpApplicantFirstContent() {
        return I18nUtil.get("fs-er-properties.upadddownemployeetimeoutnotifyupapplicantfirstcontent", upAddDownEmployeeTimeOutNotifyUpApplicantFirstContent);
    }

    public static String getUpAddDownEmployeeType() {
        return I18nUtil.get("fs-er-properties.upadddownemployeetype", upAddDownEmployeeType);
    }

    public static String getUpAddDownMobileExistedRemind() {
        return I18nUtil.get("fs-er-properties.upadddownmobileexistedremind", upAddDownMobileExistedRemind);
    }

    public static String getNewCreatedEmployeeInviteSmsContent() {
        return I18nUtil.get("fs-er-properties.newcreatedemployeeinvitesmscontent", newCreatedEmployeeInviteSmsContent);
    }

    public static String getDownAddEmployeeApplyNotifyUpAdminTitleContent() {
        return I18nUtil.get("fs-er-properties.downaddemployeeapplynotifyupadmintitlecontent", downAddEmployeeApplyNotifyUpAdminTitleContent);
    }

    public static String getDownAddEmployeeAgreeNotifyDownApplicantTitleContent() {
        return I18nUtil.get("fs-er-properties.downaddemployeeagreenotifydownapplicanttitlecontent", downAddEmployeeAgreeNotifyDownApplicantTitleContent);
    }

    public static String getDownAddEmployeeRejectNotifyDownApplicantTitleContent() {
        return I18nUtil.get("fs-er-properties.downaddemployeerejectnotifydownapplicanttitlecontent", downAddEmployeeRejectNotifyDownApplicantTitleContent);
    }

    public static String getDownAddEmployeeType() {
        return I18nUtil.get("fs-er-properties.downaddemployeetype", downAddEmployeeType);
    }

    public static String getRelationApplyNotifyReceiverAdminTitleContent() {
        return I18nUtil.get("fs-er-properties.relationapplynotifyreceiveradmintitlecontent", relationApplyNotifyReceiverAdminTitleContent);
    }

    public static String getRelationApplyAgreeNotifyApplicantTitleContent() {
        return I18nUtil.get("fs-er-properties.relationapplyagreenotifyapplicanttitlecontent", relationApplyAgreeNotifyApplicantTitleContent);
    }

    public static String getRelationApplyRejectNotifyApplicantTitleContent() {
        return I18nUtil.get("fs-er-properties.relationapplyrejectnotifyapplicanttitlecontent", relationApplyRejectNotifyApplicantTitleContent);
    }

    public static String getRelationApplyType() {
        return I18nUtil.get("fs-er-properties.relationapplytype", relationApplyType);
    }

    public static String getEmployeeBusinessNotifyReceiverAdminTitleContent() {
        return I18nUtil.get("fs-er-properties.employeebusinessnotifyreceiveradmintitlecontent", employeeBusinessNotifyReceiverAdminTitleContent);
    }

    public static String getEmployeeBusinessAgreeNotifyApplicantTitleContent() {
        return I18nUtil.get("fs-er-properties.employeebusinessagreenotifyapplicanttitlecontent", employeeBusinessAgreeNotifyApplicantTitleContent);
    }

    public static String getEmployeeBusinessRejectNotifyApplicantTitleContent() {
        return I18nUtil.get("fs-er-properties.employeebusinessrejectnotifyapplicanttitlecontent", employeeBusinessRejectNotifyApplicantTitleContent);
    }

    public static String getEmployeeBusinessType() {
        return I18nUtil.get("fs-er-properties.employeebusinesstype", employeeBusinessType);
    }

    public static String getQuotaExpireSettingHint() {
        return I18nUtil.get("fs-er-properties.quotaexpiresettinghint", quotaExpireSettingHint);
    }

    public static String getQuotaExpireRelationHint() {
        return I18nUtil.get("fs-er-properties.quotaexpirerelationhint", quotaExpireRelationHint);
    }

    public static String getDownApplyRelationLog() {
        return I18nUtil.get("fs-er-provider-log.downApplyRelationLog", downApplyRelationLog);
    }

    public static String getSrcReleaseRelationWithDestLog() {
        return I18nUtil.get("fs-er-provider-log.srcreleaserelationwithdestlog", srcReleaseRelationWithDestLog);
    }

    public static String getSrcRelationReleasedByDestLog() {
        return I18nUtil.get("fs-er-provider-log.srcrelationreleasedbydestlog", srcRelationReleasedByDestLog);
    }

    public static String getSrcRelationCreationBySrcLog() {
        return I18nUtil.get("fs-er-provider-log.srcrelationcreationbysrclog", srcRelationCreationBySrcLog);
    }

    public static String getSrcRelationCreationByDestLog() {
        return I18nUtil.get("fs-er-provider-log.srcrelationcreationbydestlog", srcRelationCreationByDestLog);
    }

    public static String getSrcAddSelfPublicEmployeeLog() {
        return I18nUtil.get("fs-er-provider-log.srcAddSelfPublicEmployeeLog", srcAddSelfPublicEmployeeLog);
    }

    public static String getSrcRemoveSelfPublicEmployeeLog() {
        return I18nUtil.get("fs-er-provider-log.srcremoveselfpublicemployeelog", srcRemoveSelfPublicEmployeeLog);
    }

    public static String getSrcChangePublicEmployeeScopeLog() {
        return I18nUtil.get("fs-er-provider-log.srcchangepublicemployeescopelog", srcChangePublicEmployeeScopeLog);
    }

    public static String getUpAddDownPublicEmployeeLog4Up() {
        return I18nUtil.get("fs-er-provider-log.upadddownpublicemployeelog4up", upAddDownPublicEmployeeLog4Up);
    }

    public static String getUpRemoveDownPublicEmployeeLog4Up() {
        return I18nUtil.get("fs-er-provider-log.upremovedownpublicemployeelog4up", upRemoveDownPublicEmployeeLog4Up);
    }

    public static String getSrcCreateGlobalPublicEmployeeLog() {
        return I18nUtil.get("fs-er-provider-log.srccreateglobalpublicemployeelog", srcCreateGlobalPublicEmployeeLog);
    }

    public static String getAppStatusChangeLog() {
        return I18nUtil.get("fs-er-provider-log.appstatuschangelog", appStatusChangeLog);
    }

    public static String getAppStatusOpenLog() {
        return I18nUtil.get("fs-er-provider-log.appstatusopenlog", appStatusOpenLog);
    }

    public static String getAppStatusCloseLog() {
        return I18nUtil.get("fs-er-provider-log.appstatuscloselog", appStatusCloseLog);
    }

    public static String getAppAdminChangeLog() {
        return I18nUtil.get("fs-er-provider-log.appadminchangelog", appAdminChangeLog);
    }

    public static String getAppDownRoleChangeLog() {
        return I18nUtil.get("fs-er-provider-log.appdownrolechangelog", appDownRoleChangeLog);
    }

    public static String getInvitionSmsContentTemplate() {
        return I18nUtil.get("eip.invitionSmsContentTemplate", invitionSmsContentTemplate);
    }

    /**
     * 开通互联账号发送短信模板
     */
    public static String getOpenErAccountSmsContentTemplate() {
        return I18nUtil.get("eip.openErAccountSmsContentTemplate", openErAccountSmsContentTemplate);
    }

    /**
     * 点击邀请绑定发送短信模板
     */
    public static String getInviteSmsContentTemplate() {
        return I18nUtil.get("eip.inviteSmsContentTemplate", inviteSmsContentTemplate);
    }
}