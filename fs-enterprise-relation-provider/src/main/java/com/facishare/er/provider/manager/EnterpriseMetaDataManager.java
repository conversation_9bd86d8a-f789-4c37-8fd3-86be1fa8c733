package com.facishare.er.provider.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.AccountManageMode;
import com.facishare.enterprise.common.constant.AppRoleType;
import com.facishare.enterprise.common.constant.GlobalRoleConstants;
import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.facishare.enterprise.common.model.CloudConfigVo;
import com.facishare.enterprise.common.model.OuterRoleVo;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.util.CloudUtil;
import com.facishare.enterprise.common.util.DistributeLock;
import com.facishare.enterprise.common.util.FsGrayReleaseUtil;
import com.facishare.enterprise.common.util.I18nUtil;
import com.facishare.er.api.model.EnterpriseMetaData;
import com.facishare.er.api.model.enums.EnterpriseNameDisplayModeEnum;
import com.facishare.er.api.model.enums.WeChatAccountModeEnum;
import com.facishare.er.api.model.vo.EnterpriseQuotaVo;
import com.facishare.er.api.model.vo.RoleVo;
import com.facishare.er.api.service.EnterpriseMessageService;
import com.facishare.er.api.service.FxiaokeAccountService;
import com.facishare.er.api.util.BeanUtil;
import com.facishare.er.provider.remote.CrmManager;
import com.facishare.er.provider.remote.OuterRoleRemoteManager;
import com.facishare.er.provider.remote.PaasLicenseManager;
import com.facishare.er.provider.util.ConfigUtil;
import com.facishare.global.rest.dao.mongo.EnterpriseMetaDataEntityDao;
import com.facishare.global.rest.dao.mongo.FxiaokeEnterpriseAssociationEntityDao;
import com.facishare.global.rest.dao.pg.EnterpriseCardEntityDao;
import com.facishare.global.rest.model.entity.EnterpriseCardEntity;
import com.facishare.global.rest.model.entity.EnterpriseErLoginWelcomePageConfig;
import com.facishare.global.rest.model.entity.EnterpriseInfoDisplayConfig;
import com.facishare.global.rest.model.entity.EnterpriseMetaDataEntity;
import com.facishare.global.rest.model.entity.Role;
import com.facishare.linkapp.api.service.HttpUpstreamService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.enterpriserelation.objrest.arg.UpdateEnterpriseAccountManageModeArg;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.fxiaoke.model.InternationalItem;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EnterpriseMetaDataManager {
    @Autowired
    private EnterpriseMetaDataEntityDao enterpriseMetaDataEntityDao;
    @Autowired
    private PaasLicenseManager paasLicenseManager;
    @Autowired
    private DistributeLock distributeLock;
    @Autowired
    private HttpUpstreamService httpUpstreamService;
    @Autowired
    private CrmManager crmManager;
    @Autowired
    private CloudUtil cloudUtil;
    @Autowired
    private EnterpriseMessageService enterpriseMessageService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterRoleRemoteManager outerRoleRemoteManager;
    @Autowired
    private EnterpriseRelationObjService enterpriseRelationObjService;
    @Autowired
    private FxiaokeAccountService fxiaokeAccountService;
    @Autowired
    private FxiaokeEnterpriseAssociationEntityDao fxiaokeEnterpriseAssociationEntityDao;
    @Autowired
    private EnterpriseCardEntityDao enterpriseCardEntityDao;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    private EnterpriseMetaData createEnterpriseMetaData(EnterpriseMetaDataEntity entity) {
        if (entity == null) {
            return null;
        }

        EnterpriseMetaData enterpriseMetaData = new EnterpriseMetaData();
        Date updateTime = entity.getUpdateTime();
        enterpriseMetaData.setUpdateTime(updateTime == null ? 0L : updateTime.getTime());
        enterpriseMetaData.setEnterpriseAccount(entity.getEnterpriseAccount());
        enterpriseMetaData.setUsedRelationNumber(entity.getUsedRelationNumber() != null ? entity.getUsedRelationNumber() : 0);
        Integer quota = 0;
        if (!FsGrayReleaseUtil.isAllowInUseOldLicenseQuote(enterpriseMetaData.getEnterpriseAccount())) {
            quota = paasLicenseManager.getEnterpriseQuotaFromPaasNew840(enterpriseMetaData.getEnterpriseAccount());
        } else {
            quota = paasLicenseManager.getEnterpriseDownstreamFirmLimit(enterpriseMetaData.getEnterpriseAccount());
        }
        enterpriseMetaData.setMaxRelationNumber(quota);
        enterpriseMetaData.setLeftRelationNumber(
            enterpriseMetaData.getMaxRelationNumber() >= enterpriseMetaData.getUsedRelationNumber() ? enterpriseMetaData.getMaxRelationNumber() - enterpriseMetaData.getUsedRelationNumber() : 0);
        enterpriseMetaData.setCrmVersion(entity.getCrmVersion());
        enterpriseMetaData.setDownEmployeeApply(entity.getDownEmployeeApply());
        enterpriseMetaData.setDownAppRoleApply(entity.getDownAppRoleApply());

        enterpriseMetaData.setInviteCode(entity.getInviteCode());
        Date inviteCodeExpireTime = entity.getInviteCodeExpireTime();
        enterpriseMetaData.setInviteCodeExpireTime(Objects.isNull(inviteCodeExpireTime) ? 0 : inviteCodeExpireTime.getTime());

        List<Role> roles = entity.getRoles();
        List<RoleVo> roleVos;
        if (Objects.isNull(roles)) {
            roleVos = new ArrayList<>(0);
        } else {
            roleVos = roles.stream().map(role -> BeanUtil.copy(role, RoleVo.class)).collect(Collectors.toList());
            Collections.reverse(roleVos);
        }
        enterpriseMetaData.setRoles(roleVos);
        enterpriseMetaData.setTags(new ArrayList<>(0));
        return enterpriseMetaData;
    }


    public void initRelationUsedQuota(String enterpriseAccount, int usedQuota) {
        enterpriseMetaDataEntityDao.initRelationUsedQuota(enterpriseAccount, usedQuota);
    }

    /**
     * 更新标签
     *
     * @param enterpriseAccount 企业帐号
     * @param roleId 角色ID
     * @param roleName 角色名称
     * @return 更新后的元记录
     */


    /**
     * 查询企业元数据，不存在时默认初始化默认数据
     */
    public EnterpriseMetaData getMetaByAccountWithInit(String enterpriseAccount) {
        EnterpriseMetaDataEntity entity = enterpriseMetaDataEntityDao.getMetaWithInitDefault(enterpriseAccount);
        return createEnterpriseMetaData(entity);
    }



    /**
     * 获取企业端口配额统计 最大可用配额在paas中，实际使用配额在我们自己管理的的mongo中。
     *
     * @param enterpriseAccount 企业帐号
     * @return 配额数据
     */
    public EnterpriseQuotaVo getEnterpriseQuota(String enterpriseAccount) {
        Integer enterpriseId = eieaConverter.enterpriseAccountToId(enterpriseAccount);
        EnterpriseMetaDataEntity quotaFromDB = enterpriseMetaDataEntityDao.find(enterpriseAccount);
        Integer usedQuotaFromDBNum = paasLicenseManager.getEnterpriseUsedQuotaFromObjectApi(enterpriseId);
        Integer maxQuotaFromPaasNum = 0;
        if (!FsGrayReleaseUtil.isAllowInUseOldLicenseQuote(enterpriseAccount)) {
            maxQuotaFromPaasNum = paasLicenseManager.getEnterpriseQuotaFromPaasNew840(enterpriseAccount);
        } else {
            maxQuotaFromPaasNum = paasLicenseManager.getEnterpriseDownstreamFirmLimit(enterpriseAccount);
        }
        //延迟初始化互联平台配置
        if (quotaFromDB == null && maxQuotaFromPaasNum != null && maxQuotaFromPaasNum > 0) {
            initUpstreamEnterprise(enterpriseAccount);
        }
        return new EnterpriseQuotaVo(enterpriseAccount, maxQuotaFromPaasNum, usedQuotaFromDBNum);
    }

    public void initUpstreamEnterprise(String upstreamEa) {
        String key = "initUpstreamEnterprise_" + upstreamEa;
        Boolean quotaEverExists = validateQuotaEverExist(upstreamEa);
        if (!quotaEverExists) {
            distributeLock.executeIfLockWeakly(key, new Runnable() {
                @Override
                public void run() {
                    EnterpriseMetaDataEntity quotaFromDB = enterpriseMetaDataEntityDao.find(upstreamEa);
                    if (quotaFromDB == null || quotaFromDB.getManageMode() == null) {
                        //设置为自主管理账号
                        updateAccountManageMode(upstreamEa, null, AccountManageMode.SELF_MANAGE);
                    }
                    //如果初次使用
                    crmManager.openIsErEnterpriseByCusttomer(upstreamEa);
                    initRelationUsedQuota(upstreamEa, 0); //初始化该企业已经使用配额数
                    log.info("initUpstreamEnterprise success, upstreamEa={} ", upstreamEa);
                    CloudConfigVo cloudConfigVo = cloudUtil.getCloudConfig(upstreamEa).getData();
                    if (!cloudConfigVo.isCloud()) {
                        Result<String> initAppsResult = httpUpstreamService.createEnterpriseDefaultAppRelations(upstreamEa, 1000);//开通默认的应用
                        if (!initAppsResult.isSuccess()) {
                            log.warn("createEnterpriseDefaultAppRelations fail, upEa:{}, errcode:{}, errmsg:{}", upstreamEa, initAppsResult.getErrCode(), initAppsResult.getErrDescription());
                        }
                    }
                    createPersonalEnterpriseRole(upstreamEa);
                    ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(eieaConverter.enterpriseAccountToId(upstreamEa) + "", SuperUserConstants.USER_ID + "");
                    enterpriseRelationObjService.initErObjectDescribe(serviceContext, false);
                }
            });
        }
    }

    public int createPersonalEnterpriseRole(String ea) {
        log.info("initPersonalEnterpriseRole ea:{}", ea);
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        boolean enterpriseRoleInited = false;
        boolean personRoleInited = false;
        int result = 0;
        try {
            List<OuterRoleVo> list = outerRoleRemoteManager.batchGetRoleInfos(ei, Lists.newArrayList(GlobalRoleConstants.ENTERPRISE_ROLE_CODE, GlobalRoleConstants.PERSONAL_ROLE_CODE));
            for (OuterRoleVo outerRoleVo : list) {
                if (outerRoleVo.getRoleId().equals(GlobalRoleConstants.ENTERPRISE_ROLE_CODE)) {
                    enterpriseRoleInited = true;
                } else if (outerRoleVo.getRoleId().equals(GlobalRoleConstants.PERSONAL_ROLE_CODE)) {
                    personRoleInited = true;
                }
            }
        } catch (Exception ex) {
            log.info("batchGetRoleInfos ,ex:{}", ex);
        }
        if (!enterpriseRoleInited) {
            try {
                outerRoleRemoteManager.addRole(ei, GlobalRoleConstants.ENTERPRISE_ROLE_CODE, GlobalRoleConstants.ENTERPRISE_ROLE_NAME, GlobalRoleConstants.ENTERPRISE_ROLE_DESC,
                    AppRoleType.OUTER_PRESET_ROLE);
                result++;
            } catch (Exception ex) {
                log.info("createPersonalEnterpriseRole ,ex:{}", ex);
            }
        }
        if (!personRoleInited) {
            try {
                outerRoleRemoteManager.addRole(ei, GlobalRoleConstants.PERSONAL_ROLE_CODE, GlobalRoleConstants.PERSONAL_ROLE_NAME, GlobalRoleConstants.PERSONAL_ROLE_DESC,
                    AppRoleType.OUTER_PRESET_ROLE);
                result++;
            } catch (Exception ex) {
                log.info("createPersonalEnterpriseRole ,ex:{}", ex);
            }
        }
        return result;
    }

    /**
     * 查询曾经购买过配额的企业列表
     */
    public Boolean validateQuotaEverExist(String enterpriseAccount) {
        List<EnterpriseMetaDataEntity> entityList = enterpriseMetaDataEntityDao.listByQuotaEverExist(enterpriseAccount);
        return CollectionUtils.isNotEmpty(entityList);
    }

    /**
     * @param ea: 企业ea
     */
    public Integer getAccountManageMode(String ea) {
        EnterpriseMetaDataEntity entity = enterpriseMetaDataEntityDao.find(ea);
        if (entity == null || entity.getManageMode() == null) {
            return AccountManageMode.SELF_MANAGE.getType();
        }
        return entity.getManageMode();
    }

    public Map<String, Integer> batchGetAccountManageMode(List<String> eas) {
        List<EnterpriseMetaDataEntity> entitys = enterpriseMetaDataEntityDao.listMetasByAccounts(eas);

        Map<String, Integer> map = new HashMap<>();
        for (EnterpriseMetaDataEntity entity : entitys) {
            if (entity == null || entity.getManageMode() == null) {
                map.put(entity.getEnterpriseAccount(), AccountManageMode.SELF_MANAGE.getType());
            } else {
                map.put(entity.getEnterpriseAccount(), entity.getManageMode());
            }
        }

        return map;
    }


    public void updateAccountManageMode(String ea, String accountManageUpstreamEa, AccountManageMode newManageMode) {
        // 企业代管添加发送互联服务号的企信消息说明代管风险
        if (AccountManageMode.ASSIST_MANAGE.equals(newManageMode)) {
            List<Integer> sendEmployees = new ArrayList<>();
            Result<List<Integer>> relationAdmins = fxiaokeAccountService.listRelationAdmins(ea);
            Result<List<Integer>> systemAdmins = fxiaokeAccountService.listSystemAdmins(ea);
            if (relationAdmins.isSuccess() && relationAdmins.getData() != null) {
                sendEmployees.addAll(relationAdmins.getData());
            }
            if (systemAdmins.isSuccess() && systemAdmins.getData() != null) {
                sendEmployees.addAll(systemAdmins.getData());
            }

            enterpriseMessageService.sendCrossHelpTextMessage(ea, sendEmployees, I18nKeyEnum.EPI_UTIL_CONFIGUTIL_85.getDefaultValue(), new InternationalItem(I18nKeyEnum.EPI_UTIL_CONFIGUTIL_85.getKey()));
        }
        int ei = eieaConverter.enterpriseAccountToId(ea);
        ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext( ei+ "", SuperUserConstants.USER_ID + "");
        enterpriseRelationObjService.updateEnterpriseAccountManageMode(serviceContext, UpdateEnterpriseAccountManageModeArg.newInstance(accountManageUpstreamEa, newManageMode.getType()));
    }


    public EnterpriseMetaDataEntity getEnterpriseMetaData(String ea) {
        EnterpriseMetaDataEntity entity = enterpriseMetaDataEntityDao.find(ea);
        if (entity == null) {
            entity = new EnterpriseMetaDataEntity();
            entity.setUpstreamEnterpriseMenuVisibleSwitch(false);
            entity.setWeChatAccountMode(WeChatAccountModeEnum.PART.getState());
            entity.setIsOpenERVisitor(false);
            entity.setAutoSendSMSWhenAccountCreate(false);
            // entity.setUsedRelationNumber(checkEnterpriseRelationUsedQuota(ea,0)); 840版本废除
            EnterpriseErLoginWelcomePageConfig config = new EnterpriseErLoginWelcomePageConfig();
            config.setErLoginWelcomePageEnterpriseName("");
            config.setErLoginWelcomePageEnterpriseLogoNpath("");
            config.setErLoginWelcomePageEnterpriseLogoShowSwitch(false);
            config.setErLoginWelcomePageTitle("");
            config.setErLoginWelcomePageContent("");
            entity.setEnterpriseErLoginWelcomePageConfig(config);
        } else {
            if (entity.getUpstreamEnterpriseMenuVisibleSwitch() == null) {
                entity.setUpstreamEnterpriseMenuVisibleSwitch(false);
            }
            if (entity.getWeChatAccountMode() == null) {
                entity.setWeChatAccountMode(WeChatAccountModeEnum.PART.getState());
            }
            if (entity.getIsOpenERVisitor() == null) {
                entity.setIsOpenERVisitor(false);
            }
            if (entity.getEnterpriseErLoginWelcomePageConfig() == null) {
                EnterpriseErLoginWelcomePageConfig config = new EnterpriseErLoginWelcomePageConfig();
                config.setErLoginWelcomePageEnterpriseName("");
                config.setErLoginWelcomePageEnterpriseLogoNpath("");
                config.setErLoginWelcomePageEnterpriseLogoShowSwitch(false);
                config.setErLoginWelcomePageTitle("");
                config.setErLoginWelcomePageContent("");
                entity.setEnterpriseErLoginWelcomePageConfig(config);
            }
            if (entity.getAutoSendSMSWhenAccountCreate() == null) {
                entity.setAutoSendSMSWhenAccountCreate(true);
            }
            if (entity.getAccountObjOpenErDefaultOuterRoleIds() == null) {
                entity.setAccountObjOpenErDefaultOuterRoleIds(new ArrayList<>());
            }
            if (entity.getPartnerObjOpenErDefaultOuterRoleIds() == null) {
                entity.setPartnerObjOpenErDefaultOuterRoleIds(new ArrayList<>());
            }
            if (StringUtils.isEmpty(entity.getEnterpriseName())) {
                entity.setEnterpriseName(initEnterpriseName(ea));
            }
            if (entity.getEnterpriseInfoDisplayConfig() == null) {
                // 默认显示企业名称，不显示logo
                EnterpriseInfoDisplayConfig config = new EnterpriseInfoDisplayConfig();
                config.setIsShowLogo(false);
                config.setEnterpriseNameDisplayMode(EnterpriseNameDisplayModeEnum.DISPLAY_ENTERPRISE_NAME.getType());
                entity.setEnterpriseInfoDisplayConfig(config);
            }
            // 840版本废除
            /**
             if (entity.getUsedRelationNumber() != null) {
             entity.setUsedRelationNumber(checkEnterpriseRelationUsedQuota(ea, entity.getUsedRelationNumber()));
             }**/
        }
        return entity;
    }

    public String initEnterpriseName(String upstreamEa) {
        String enterpriseName = "";
        Long outerTenantId = fxiaokeEnterpriseAssociationEntityDao.getOuterTenantIdByEa(upstreamEa);
        EnterpriseCardEntity enterpriseCardEntity = enterpriseCardEntityDao.getByOutTenantId(outerTenantId);
        if (enterpriseCardEntity != null && !StringUtils.isEmpty(enterpriseCardEntity.getEnterpriseName())) {
            enterpriseName = enterpriseCardEntity.getEnterpriseName();
        }
        if (StringUtils.isEmpty(enterpriseName)) {
            GetSimpleEnterpriseDataArg getSimpleEnterpriseDataArg = new GetSimpleEnterpriseDataArg();
            getSimpleEnterpriseDataArg.setEnterpriseAccount(upstreamEa);
            GetSimpleEnterpriseDataResult simpleEnterpriseData = enterpriseEditionService.getSimpleEnterpriseData(getSimpleEnterpriseDataArg);
            if (simpleEnterpriseData != null && simpleEnterpriseData.getEnterpriseData() != null) {
                enterpriseName = simpleEnterpriseData.getEnterpriseData().getEnterpriseName();
            }
        }
        EnterpriseMetaDataEntity entity = new EnterpriseMetaDataEntity();
        entity.setEnterpriseName(enterpriseName);
        entity.setEnterpriseAccount(upstreamEa);
        enterpriseMetaDataEntityDao.findAndUpdate(entity);
        return enterpriseName;
    }



    public Boolean getUseOtherSmsServiceSwitch(String enterpriseAccount) {
        EnterpriseMetaDataEntity entity = enterpriseMetaDataEntityDao.find(enterpriseAccount);
        if (entity == null) {
            return false;
        }
        Boolean useOtherSmsServiceSwitch = entity.getUseOtherSmsServiceSwitch();
        if (useOtherSmsServiceSwitch == null) {
            return false;
        } else {
            return useOtherSmsServiceSwitch;
        }
    }


    public Boolean getUpstreamEnterpriseMessageSmsChannelSwitch(String enterpriseAccount) {
        EnterpriseMetaDataEntity enterpriseMetaDataEntity = enterpriseMetaDataEntityDao.find(enterpriseAccount);
        if (enterpriseMetaDataEntity != null) {
            return enterpriseMetaDataEntity.getMessageSmsChannelSwitch() == null ? false : enterpriseMetaDataEntity.getMessageSmsChannelSwitch();
        }
        return false;
    }

    private interface LockType {
        int role = 0;
        int tagGroup = 1;
        int metaTag = 2;
    }
}
