package com.facishare.global.rest.service.impl;

import static com.facishare.enterprise.common.constant.PublicEmployeeTypeEnum.TYPE_IS_PUBLIC_EMPLOYEE;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.enums.LoginAccountTypeEnum;
import com.facishare.enterprise.common.model.LoginAccountData;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.util.IdUtil;
import com.facishare.enterprise.common.util.ListUtil;
import com.facishare.er.cross.cloud.mq.syncdata.SyncCrossCloudDataManager;
import com.facishare.global.rest.arg.BatchUpsertUpstreamPublicEmployeeArg;
import com.facishare.global.rest.arg.GetUserInfoSimpleInfoArg;
import com.facishare.global.rest.arg.HasUpstreamPublicEmployeeArg;
import com.facishare.global.rest.arg.ListUpstreamEnterpriseRelationsArg;
import com.facishare.global.rest.arg.ListUpstreamPublicEmployeesArg;
import com.facishare.global.rest.arg.SearchUpstreamPublicEmployeesByNameArg;
import com.facishare.global.rest.dao.mongo.FxiaokeEnterpriseAssociationEntityDao;
import com.facishare.global.rest.dao.pg.EmployeeCardDao;
import com.facishare.global.rest.dao.pg.EmployeeCardEntityDao;
import com.facishare.global.rest.dao.pg.EnterpriseCardDao;
import com.facishare.global.rest.dao.pg.GlobalEnterpriseRelationDao;
import com.facishare.global.rest.dao.pg.GlobalPublicEmployeeRelationDao;
import com.facishare.global.rest.dao.pg.UpstreamPublicEmployeeDao;
import com.facishare.global.rest.data.GlobalPublicEmployeeData;
import com.facishare.global.rest.data.UpstreamEnterpriseRelationVo;
import com.facishare.global.rest.data.UpstreamPublicEmployeeVos;
import com.facishare.global.rest.model.arg.DeletePublicEmployeeArg;
import com.facishare.global.rest.model.data.EiUerIdData;
import com.facishare.global.rest.model.data.OuterUserIdData;
import com.facishare.global.rest.model.entity.EnterpriseCardEntity;
import com.facishare.global.rest.model.entity.GlobalPublicEmployeeEntity;
import com.facishare.global.rest.model.entity.UpstreamPublicEmployeeEntity;
import com.facishare.global.rest.result.ListUpstreamEnterpriseRelationsResult;
import com.facishare.global.rest.result.ListUpstreamPublicEmployeesResult;
import com.facishare.global.rest.result.UserInfoSimpleInfoData;
import com.facishare.global.rest.service.GlobalPublicEmployeeService;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 互联用户关系
 *
 * <AUTHOR>
 * @since 2022/06/22
 */
@Service
@Slf4j
public class GlobalPublicEmployeeServiceImpl implements GlobalPublicEmployeeService {
    @Autowired
    private EIEAConverter eIEAConverter;
    @Autowired
    private GlobalPublicEmployeeRelationDao globalPublicEmployeeRelationDao;
    @Autowired
    private GlobalEnterpriseRelationDao globalEnterpriseRelationDao;
    @Autowired
    private FxiaokeEnterpriseAssociationEntityDao fxiaokeEnterpriseAssociationEntityDao;
    @Autowired
    private ObjectDataService publicEmployeeObjService;
    @Autowired
    private EmployeeCardDao employeeCardDao;
    @Autowired
    private EmployeeCardEntityDao employeeCardEntityDao;
    @Autowired
    private EnterpriseCardDao enterpriseCardDao;
    @Autowired
    private UpstreamPublicEmployeeDao upstreamPublicEmployeeDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Resource(name = "globalSyncCrossCloudDataManager")
    private SyncCrossCloudDataManager syncCrossCloudDataManager;

    @Override
    public List<Long> listAllUpstreamOuterTenantIds(Long downstreamOuterUid) {
        List<String> upstreamEas = listAllUpstreamEasByOuterUid(downstreamOuterUid);
        List<Long> outerTenantIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(upstreamEas)) {
            outerTenantIdList = Lists.newArrayList(fxiaokeEnterpriseAssociationEntityDao.batchGetOuterTenantIds(upstreamEas).values());
        }
        return outerTenantIdList;
    }

    @Override
    public List<String> listAllUpstreamEasByOuterUid(Long downstreamOuterUid) {
        List<Long> upstreamTenantIds = globalPublicEmployeeRelationDao.listAllUpstreamTenantIds(downstreamOuterUid, TYPE_IS_PUBLIC_EMPLOYEE.getType());
        List<String> upstreamEas = new ArrayList<>();
        if (!CollectionUtils.isEmpty(upstreamTenantIds)) {
            upstreamTenantIds.forEach(tenantId -> {
                String ea = eIEAConverter.enterpriseIdToAccount(tenantId.intValue());
                upstreamEas.add(ea);
            });
        }
        return upstreamEas;
    }

    @Override
    public List<GlobalPublicEmployeeData> listGlobalPublicEmployeesByLoginAccount(LoginAccountData loginAccountData) {
        if (loginAccountData == null) {
            return new ArrayList<>();
        }
        List<GlobalPublicEmployeeEntity> entities = new ArrayList<>();
        if (LoginAccountTypeEnum.isLoginEmail(loginAccountData.getAccountType())) {
            entities = globalPublicEmployeeRelationDao.listGlobalPublicEmployeesByLoginEmail(loginAccountData.getLoginAccount());
        } else {
            entities = globalPublicEmployeeRelationDao.listGlobalPublicEmployeesByLoginMobile(loginAccountData.getLoginAccount());
        }
        List<GlobalPublicEmployeeData> result = new ArrayList<>(entities.size());
        for (GlobalPublicEmployeeEntity entity : entities) {
            GlobalPublicEmployeeData globalPublicEmployeeData = new GlobalPublicEmployeeData();
            globalPublicEmployeeData.setDownstreamOuterTenantId(entity.getDownstreamOuterTenantId());
            globalPublicEmployeeData.setDownstreamOuterUid(entity.getDownstreamOuterUid());
            globalPublicEmployeeData.setLoginEmail(entity.getLoginEmail());
            globalPublicEmployeeData.setLoginMobile(entity.getLoginMobile());
            globalPublicEmployeeData.setType(entity.getType());
            globalPublicEmployeeData.setUpstreamTenantId(entity.getUpstreamTenantId());
            result.add(globalPublicEmployeeData);
        }
        return result;
    }

    @Override
    public Result<ListUpstreamPublicEmployeesResult> listUpstreamPublicEmployees(ListUpstreamPublicEmployeesArg arg, int limit, int offset) {
        Integer count = upstreamPublicEmployeeDao
            .countUpstreamPublicEmployees(arg.getUpstreamTenantId(), ListUtil.toIntegerList(arg.getEmployeeIds()), arg.getDownstreamOuterUid(), arg.getSearchText(), arg.getDownstreamOuterTenantIds());
        List<UpstreamPublicEmployeeVos> datas = upstreamPublicEmployeeDao
            .listUpstreamPublicEmployees(arg.getUpstreamTenantId(), ListUtil.toIntegerList(arg.getEmployeeIds()), arg.getSearchText(), arg.getDownstreamOuterTenantIds(), arg.getDownstreamOuterUid(),
                limit, offset);
        List<Integer> tenantIds = datas.stream().map(UpstreamPublicEmployeeVos::getTenantId).distinct().collect(Collectors.toList());
        Map<Integer, List<String>> tenantIdNamesMap = new HashMap<>();
        // 再次查询补上企业名称
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(tenantIds)) {
            List<EnterpriseCardEntity> enterpriseCardEntities = enterpriseCardDao.batchGetByTenantIds(tenantIds);
            // TODO 有脏数据？112能查出两条不一样的记录
            tenantIdNamesMap = enterpriseCardEntities.stream().collect(Collectors.groupingBy(EnterpriseCardEntity::getTenantId, Collectors.mapping(EnterpriseCardEntity::getEnterpriseName, Collectors.toList())));
        }

        for (UpstreamPublicEmployeeVos data : datas) {
            List<String> enterpriseNames = tenantIdNamesMap.get(data.getTenantId());
            if (!CollectionUtils.isEmpty(enterpriseNames)) {
                data.setEnterpriseName(enterpriseNames.get(0));
            }
        }
        ListUpstreamPublicEmployeesResult res = new ListUpstreamPublicEmployeesResult();
        res.setUpstreamPublicEmployeeVos(datas);
        res.setTotalCount(count);
        return Result.newSuccess(res);
    }

    @Override
    public Result<ListUpstreamPublicEmployeesResult> searchUpstreamPublicEmployeesByName(SearchUpstreamPublicEmployeesByNameArg arg) {
        List<UpstreamPublicEmployeeVos> datas = upstreamPublicEmployeeDao.searchUpstreamPublicEmployeesByName(arg.getSearchTexts(), arg.getDownstreamOuterTenantId());
        Map<Integer, String> tenantIdNameMap = new HashMap<>();
        List<Integer> tenantIds = datas.stream().map(UpstreamPublicEmployeeVos::getTenantId).distinct().collect(Collectors.toList());
        // 再次查询补上企业名称
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(tenantIds)) {
            List<EnterpriseCardEntity> enterpriseCardEntities = enterpriseCardDao.batchGetByTenantIds(tenantIds);
            tenantIdNameMap = enterpriseCardEntities.stream().collect(Collectors.toMap(EnterpriseCardEntity::getTenantId, EnterpriseCardEntity::getEnterpriseName));
        }
        ListUpstreamPublicEmployeesResult res = new ListUpstreamPublicEmployeesResult();
        res.setUpstreamPublicEmployeeVos(datas);
        for (UpstreamPublicEmployeeVos data : datas) {
            String enterpriseName = tenantIdNameMap.get(data.getTenantId());
            if (StringUtils.isNotBlank(enterpriseName)) {
                data.setEnterpriseName(enterpriseName);
            }
        }
        return Result.newSuccess(res);
    }

    @Override
    public Result<ListUpstreamEnterpriseRelationsResult> listUpstreamEnterpriseRelations(ListUpstreamEnterpriseRelationsArg arg, int limit, int offset) {
        Integer count = upstreamPublicEmployeeDao.countUpstreamEnterpriseRelations(arg.getSearchText(), arg.getDownstreamOuterTenantId());
        List<UpstreamEnterpriseRelationVo> datas = upstreamPublicEmployeeDao.listUpstreamEnterpriseRelations(arg.getSearchText(), arg.getDownstreamOuterTenantId(), limit, offset);
        ListUpstreamEnterpriseRelationsResult res = new ListUpstreamEnterpriseRelationsResult();
        res.setDataList(datas);
        res.setTotal(count);
        return Result.newSuccess(res);
    }

    @Override
    public Result<Boolean> hasUpstreamPublicEmployee(HasUpstreamPublicEmployeeArg arg) {
        Integer count = upstreamPublicEmployeeDao.countUpstreamPublicEmployeeByUpstreamTenantIdAndUserId(arg.getTenantId(), arg.getUserId());
        return Result.newSuccess(count != null && count > 0);
    }

    @Override
    public Result<List<UserInfoSimpleInfoData>> getUserInfoSimpleInfo(Integer tenantId, GetUserInfoSimpleInfoArg arg) {
        List<UserInfoSimpleInfoData> datas = Lists.newArrayList();
        // 内部员工信息
        List<EiUerIdData> eiUerIdDataList = arg.getFsAccountDataList().stream()
            .filter(x -> StringUtils.isNotEmpty(x.getEa()) && StringUtils.isNotEmpty(x.getFsUserId()) && Integer.valueOf(x.getFsUserId()) > 0).map(x -> {
                int ei = eieaConverter.enterpriseAccountToId(x.getEa());
                Integer employeeId = Integer.valueOf(x.getFsUserId());
                EiUerIdData eiUerIdData = new EiUerIdData();
                eiUerIdData.setTenantId(ei);
                eiUerIdData.setEmployeeId(employeeId);
                return eiUerIdData;
            }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(eiUerIdDataList)) {
            List<UserInfoSimpleInfoData> queryEmployeeData = employeeCardEntityDao.batchGetEmployeeCardByFsAccount(eiUerIdDataList);
            if (!CollectionUtils.isEmpty(queryEmployeeData)) {
                datas.addAll(queryEmployeeData);
            }
        }
        // 互联用户信息
        List<OuterUserIdData> outerUids = arg.getFsAccountDataList().stream().filter(x -> StringUtils.isNotEmpty(x.getUpstreamEa()) && x.getOuterUid() != null).map(x -> {
            OuterUserIdData userIdData = new OuterUserIdData();
            userIdData.setOuterUserId(Long.valueOf(x.getOuterUid()));
            userIdData.setUpstreamTenantId(eieaConverter.enterpriseAccountToId(x.getUpstreamEa()));
            return userIdData;
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(outerUids)) {
            List<UserInfoSimpleInfoData> employeeCardEntities = employeeCardEntityDao.batchGetEmployeeCardByOuterUserIds(outerUids);
            if (!CollectionUtils.isEmpty(employeeCardEntities)) {
                datas.addAll(employeeCardEntities);
            }
        }
        // 填充upstreamEa字段的值
        if (!CollectionUtils.isEmpty(datas)) {
            Map<String, String> outerUidToUpstreamEaMap = arg.getFsAccountDataList().stream().filter(x -> StringUtils.isNotEmpty(x.getUpstreamEa()))
                .collect(Collectors.toMap(x -> x.getOuterUid(), x -> x.getUpstreamEa(), (v1, v2) -> v1));
            datas.forEach(x -> {
                if (outerUidToUpstreamEaMap.containsKey(x.getOuterUid())) {
                    x.setUpstreamEa(outerUidToUpstreamEaMap.get(x.getOuterUid()));
                }
            });
        }
        return Result.newSuccess(datas);
    }

    @Override
    public Result<Object> batchUpsertUpstreamPublicEmployee(BatchUpsertUpstreamPublicEmployeeArg arg) {
        List<UpstreamPublicEmployeeEntity> updateEntitys = arg.getUpstreamUserIds().stream().map(x -> {
            UpstreamPublicEmployeeEntity upstreamPublicEmployeeEntity = new UpstreamPublicEmployeeEntity();
            upstreamPublicEmployeeEntity.setUpstreamUserId(x);
            upstreamPublicEmployeeEntity.setVersion(0);
            upstreamPublicEmployeeEntity.setId(IdUtil.generateId());
            upstreamPublicEmployeeEntity.init();
            return upstreamPublicEmployeeEntity;
        }).collect(Collectors.toList());

        List<UpstreamPublicEmployeeEntity> upstreamPublicEmployeeEntities = upstreamPublicEmployeeDao
                .batchUpsertUpstreamPublicEmployee(arg.getUpstreamTenantId(), arg.getDownstreamOuterTenantId(), updateEntitys, arg.getType(), new Date());
        List<Serializable> ids = upstreamPublicEmployeeEntities.stream().map(x -> (Serializable) x.getId()).collect(Collectors.toList());
        syncCrossCloudDataManager.sendUpsertSyncCrossDataMsgByIds(ids, UpstreamPublicEmployeeDao.TABLE_NAME);
        return Result.newSuccess();
    }

    @Override
    public List<GlobalPublicEmployeeEntity> listPageByUpstreamTenantIdWithCreateTimeOrder(Long upstreamTenantId, Long minOuterUserId, Long maxOuterUserId) {
        List<GlobalPublicEmployeeEntity> dataList = globalPublicEmployeeRelationDao.listPageByUpstreamTenantIdWithCreateTimeOrder(upstreamTenantId, minOuterUserId, maxOuterUserId);
        return dataList;
    }

    @Override
    public Integer updateOrCreatePublicEmployeeRelation(Integer upstreamTenantId, Long downstreamOuterUid) {
        HeaderObj headerObj = new HeaderObj(upstreamTenantId, -10000);
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdResult> queryResult = publicEmployeeObjService
            .getById(headerObj, PublicEmployeeObjConstant.API_NAME, "" + downstreamOuterUid, false, true, true, false);
        if (!queryResult.isSuccess() || queryResult.getData() == null) {
            log.warn("updateOrCreatePublicEmployeeRelation objectData is null,upstreamTenantId={}", upstreamTenantId);
            return 0;
        }
        ObjectData objectData = queryResult.getData().getObjectData();
        Date createTime = new Date(objectData.getCreateTime());
        PublicEmployeeObj obj = PublicEmployeeObj.newInstance(objectData);
        Date lastUpdateTime = new Date(obj.getLastModifiedTime());
        GlobalPublicEmployeeEntity entity = new GlobalPublicEmployeeEntity();
        entity.setUpstreamTenantId(upstreamTenantId);
        entity.setDownstreamOuterUid(obj.getOuterUid());
        entity.setDownstreamOuterTenantId(obj.getOuterTenantId());
        entity.setLoginEmail(obj.getLoginEmail());
        entity.setLoginMobile(obj.getMobile());
        entity.setType(obj.getType());
        entity.setPublicEmployeeVersion(obj.getVersion());
        entity.setIsDeleted(obj.getIsDeleted() ? 1 : 0);
        entity.setCreateTime(createTime);
        entity.setUpdateTime(lastUpdateTime);

        GlobalPublicEmployeeEntity oldRelation = globalPublicEmployeeRelationDao.findRelation(upstreamTenantId, obj.getOuterUid(), obj.getOuterTenantId());
        // 删除记录
        if (obj.getIsDeleted() && null != oldRelation) {
            GlobalPublicEmployeeEntity result = globalPublicEmployeeRelationDao.softDeleteData(upstreamTenantId, obj.getOuterTenantId(), obj.getOuterUid(), lastUpdateTime);
            if (result != null) {
                String deleteSql = String.format("UPDATE \"%s\" SET is_deleted=1,public_employee_version=0 WHERE id='%s' ", GlobalPublicEmployeeRelationDao.TABLE_NAME, oldRelation.getId());
                log.info("updatePublicEmployeeRelationData:soft_delete-->{}", deleteSql);
                syncCrossCloudDataManager.sendSqlSyncCrossDataMsg(deleteSql, GlobalPublicEmployeeRelationDao.TABLE_NAME);
            }
            return result == null ? 0 : 1;
        }
        // 新增或更新记录
        int result;
        if (null != oldRelation) {
            entity.setId(oldRelation.getId());
            result = globalPublicEmployeeRelationDao.updatePublicEmployeeRelation(entity);
            if (result > 0) {
                syncCrossCloudDataManager.sendUpsertSyncCrossDataMsg(oldRelation.getId(), GlobalPublicEmployeeRelationDao.TABLE_NAME);
            }
            log.info("updatePublicEmployeeRelationData:update-->{},result-->{}", entity, result);
        } else {
            entity.setId(IdUtil.generateId());
            Set<String> unionKeys = Sets.newHashSet("upstreamTenantId", "downstreamOuterUid");
            result = globalPublicEmployeeRelationDao.upsertByIdWithUnionKeys(entity, unionKeys);
            if (result > 0) {
                syncCrossCloudDataManager.sendUpsertSyncCrossDataMsg(entity.getId(), GlobalPublicEmployeeRelationDao.TABLE_NAME);
            }
            log.info("updatePublicEmployeeRelationData:insert-->{},result-->{}", entity, result);
        }
        return result;
    }

    @Override
    public Integer deletePublicEmployee(DeletePublicEmployeeArg deleteArg) {
        GlobalPublicEmployeeEntity globalPublicEmployeeEntity = globalPublicEmployeeRelationDao
                .deletePublicEmployeeRelation(deleteArg.getUpstreamTenantId(), deleteArg.getDownstreamOuterTenantId(), deleteArg.getDownstreamOuterUid(), deleteArg.getPublicEmployeeVersion());
        log.info("deletePublicEmployee:upstreamTenantId-->{},downstreamOuterUid-->{},downstreamOuterTenantId-->{},result-->{}", deleteArg.getUpstreamTenantId(), deleteArg.getDownstreamOuterUid(),
                deleteArg.getDownstreamOuterTenantId(), globalPublicEmployeeEntity);
        if (globalPublicEmployeeEntity != null) {
            syncCrossCloudDataManager.sendUpsertSyncCrossDataMsg(globalPublicEmployeeEntity.getId(), GlobalPublicEmployeeRelationDao.TABLE_NAME);
        }
        return globalPublicEmployeeEntity == null ? 0 : 1;
    }

    @Override
    public Integer deleteAllPublicEmployeeByUpstreamTenantId(Integer upstreamTenantId) {
        Integer num = globalPublicEmployeeRelationDao.deleteAllPublicEmployeeRelationByUpstreamTenantId(upstreamTenantId);
        if (num > 0) {
            try {
                eieaConverter.enterpriseIdToAccount(upstreamTenantId);
                String sql = "update global_public_employee" + "    set is_deleted = 1" + "    where upstream_tenant_id = " + upstreamTenantId;
                syncCrossCloudDataManager.sendSqlSyncCrossDataMsg(sql, GlobalPublicEmployeeRelationDao.TABLE_NAME);
            } catch (Exception e) {

            }
        }
        return num;
    }

    @Override
    public Integer countByUpstreamTenantId(int tenantId) {
        return globalPublicEmployeeRelationDao.countByUpstreamTenantId(tenantId);
    }
}
