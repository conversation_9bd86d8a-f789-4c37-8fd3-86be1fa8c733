package com.facishare.global.rest.dao.pg;

import com.facishare.global.rest.dao.base.BaseMapper;
import com.facishare.global.rest.data.UpstreamEnterpriseRelationVo;
import com.facishare.global.rest.data.UpstreamPublicEmployeeVos;
import com.facishare.global.rest.model.entity.UpstreamPublicEmployeeEntity;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 全局表：global_public_employee数据操作类
 *
 * <AUTHOR>
 * @since 2022/0622
 */
@Repository("globalUpstreamPublicEmployeeDao")
public interface UpstreamPublicEmployeeDao extends BaseMapper<UpstreamPublicEmployeeEntity> {
    String TABLE_NAME = "upstream_public_employee";

    Integer countUpstreamPublicEmployees(@Param("searchTenantId") Integer searchTenantId, @Param("employeeIds") List<Integer> employeeIds,@Param("downstreamOuterUid") Long downstreamOuterUid, @Param("searchText") String searchText, @Param("downstreamOuterTenantIds") List<Long> downstreamOuterTenantIds);

    List<UpstreamPublicEmployeeVos> listUpstreamPublicEmployees(@Param("searchTenantId")Integer searchTenantId,@Param("employeeIds") List<Integer> employeeIds,@Param("searchText") String searchText,@Param("downstreamOuterTenantIds") List<Long> downstreamOuterTenantIds,
        @Param("downstreamOuterUid") Long downstreamOuterUid,@Param("limit") int limit,@Param("offset") int offset);

    List<UpstreamPublicEmployeeVos> searchUpstreamPublicEmployeesByName(@Param("searchTexts") List<String> searchTexts,@Param("downstreamOuterTenantId") Long downstreamOuterTenantId);

    Integer countUpstreamEnterpriseRelations(@Param("searchText") String searchText, @Param("downstreamOuterTenantId") Long downstreamOuterTenantId);

    Integer countUpstreamPublicEmployeeByUpstreamTenantIdAndUserId(@Param("upstreamTenantId") Integer upstreamTenantId, @Param("upstreamUserId") Integer upstreamUserId);

    List<UpstreamEnterpriseRelationVo> listUpstreamEnterpriseRelations(@Param("searchText") String searchText, @Param("downstreamOuterTenantId") Long downstreamOuterTenantId, @Param("limit") int limit,@Param("offset") int offset);

    List<UpstreamPublicEmployeeEntity> batchUpsertUpstreamPublicEmployee(@Param("upstreamTenantId") Integer upstreamTenantId,@Param("downstreamOuterTenantId")  Long downstreamOuterTenantId, @Param("upstreamPublicEmployeeEntities") List<UpstreamPublicEmployeeEntity> upstreamPublicEmployeeEntities,@Param("isDeleted")  Integer isDeleted, @Param("updateTime") Date  updateTime);

    void batchUpsertUpstreamPublicEmployeeByEntity(@Param("upstreamPublicEmployeeEntities") List<UpstreamPublicEmployeeEntity> upstreamPublicEmployeeEntities);
}
