<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
    xmlns:p="http://www.springframework.org/schema/p" xmlns:tx="http://www.springframework.org/schema/tx" xsi:schemaLocation="http://www.springframework.org/schema/beans
			http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
			http://www.springframework.org/schema/context
			http://www.springframework.org/schema/context/spring-context-3.0.xsd
			http://www.springframework.org/schema/context
			http://www.springframework.org/schema/context/spring-context-3.0.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

  <context:annotation-config/>
  <!--分布式锁-->
  <bean id="jedisSupport" class="com.github.jedis.support.JedisFactoryBean" p:configName="redis-wechat-baichuan"/>
  <bean class="com.facishare.enterprise.common.cache.JetCacheGlobalCacheConfig" init-method="init" p:jedis-ref="jedisSupport"/>
  <bean id="distributeLock" class="com.facishare.enterprise.common.util.DistributeLock">
    <property name="defaultLockTimeOutSeconds" value="5"/>
    <property name="jedis" ref="jedisSupport"/>
  </bean>
  <!-- mongo数据源 -->
  <bean id="mongoDatastore" class="com.github.mongo.support.MongoDataStoreFactoryBean">
    <property name="configName" value="fs-er-mongo"/>
  </bean>
  <!-- pg全局数据源 -->
  <bean id="erPgDataSource" class="com.github.mybatis.spring.DynamicDataSource">
    <property name="configName" value="enterprise-relation-db"/>
  </bean>
  <bean id="erSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
    <property name="dataSource" ref="erPgDataSource"/>
    <property name="typeAliasesPackage" value="com.facishare.global.rest.model.entity"/>
    <property name="mapperLocations" value="classpath:mapper/*.xml"/>
    <property name="configLocation" value="classpath:spring/pg-mybatis-config.xml"/>
  </bean>
  <bean id="erJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
    <property name="dataSource" ref="erPgDataSource"/>
  </bean>
  <bean id="erTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
    <property name="dataSource" ref="erPgDataSource"/>
  </bean>
  <bean id="pgScanner" class="com.github.mybatis.spring.ScannerConfigurer">
    <property name="basePackage" value="com.facishare.global.rest.dao.pg"/>
    <property name="sqlSessionFactoryBeanName" value="erSqlSessionFactory"/>
    <property name="annotationClass" value="org.springframework.stereotype.Repository"/>
  </bean>
  <!-- pg 业务数据源 -->
  <bean id="erBizPgDataSource" class="com.github.mybatis.spring.DynamicDataSource">
    <property name="configName" value="enterprise-relation-biz-db"/>
  </bean>
  <bean id="erBizSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
    <property name="dataSource" ref="erBizPgDataSource"/>
    <property name="typeAliasesPackage" value="com.facishare.global.rest.model.entity"/>
    <property name="mapperLocations" value="classpath:bizmapper/*.xml"/>
    <property name="configLocation" value="classpath:spring/bizpg-mybatis-config.xml"/>
  </bean>
  <bean id="erBizPgJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
    <property name="dataSource" ref="erBizPgDataSource"/>
  </bean>
  <bean id="erBizTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
    <property name="dataSource" ref="erBizPgDataSource"/>
  </bean>
  <bean id="pgBizScanner" class="com.github.mybatis.spring.ScannerConfigurer">
    <property name="basePackage" value="com.facishare.global.rest.dao.bizpg"/>
    <property name="sqlSessionFactoryBeanName" value="erBizSqlSessionFactory"/>
    <property name="annotationClass" value="org.springframework.stereotype.Repository"/>
  </bean>
  <!-- mysql数据源 -->
  <bean id="mysqlDataSource" class="com.github.mybatis.spring.DynamicDataSource">
    <property name="configName" value="fs-enterprise-linkapp-db"/>
  </bean>
  <!--jdbcTemplate-->
  <bean id="mysqlJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
    <property name="dataSource" ref="mysqlDataSource"/>
  </bean>
  <bean id="dataSyncPgDataSource" class="com.github.mybatis.spring.DynamicDataSource">
    <property name="configName" value="fs-sync-data-db"/>
  </bean>
  <!-- 事务管理器 -->
  <bean id="mysqlTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
    <property name="dataSource" ref="mysqlDataSource"/>
  </bean>
  <bean id="mysqlSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
    <property name="dataSource" ref="mysqlDataSource"/>
    <property name="typeAliasesPackage" value="com.facishare.global.rest.model.entity"/>
    <property name="configLocation" value="classpath:spring/mysql-mybatis-config.xml"/>
  </bean>
  <tx:annotation-driven transaction-manager="mysqlTransactionManager"/>
  <bean id="mysqlScanner" class="com.github.mybatis.spring.ScannerConfigurer">
    <property name="basePackage" value="com.facishare.global.rest.dao.mysql"/>
    <property name="sqlSessionFactoryBeanName" value="mysqlSqlSessionFactory"/>
  </bean>
</beans>
