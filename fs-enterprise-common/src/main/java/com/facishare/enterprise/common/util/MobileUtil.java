package com.facishare.enterprise.common.util;

import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by wang<PERSON> on 16/9/1.
 */
@Slf4j
public class MobileUtil {
    //可以不输入国际码为前缀，这样会默认为国内地区，符合要求，所以这里只对输入了+为前缀的字段进行处理
    public static Boolean isMobileMatchI18nCode(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return true;
        }
        if (mobile.startsWith("+")) {
            Pattern pattern = Pattern.compile("^\\+(\\d{1,3})-\\d{1,15}$");
            Matcher matcher = pattern.matcher(mobile);
            if (!matcher.find()) {
                return false;
            }
            String code = "+" + matcher.group(1);
            try {
                String codeJson = new String(Files.readAllBytes(Paths.get("src/main/resources/internationalCode.json")));
                JSONObject jsonObject = new JSONObject(codeJson);
                JSONArray areaCodeList = jsonObject.getJSONObject("Value").getJSONArray("areaCodeList");
                for (int i = 0; i < areaCodeList.length(); i++) {
                    JSONObject area = areaCodeList.getJSONObject(i);
                    String curCode = area.getString("code");
                    if (code.equals(curCode)) {
                        return true;
                    }
                }
                return false;
            } catch (IOException | JSONException e) {
                e.printStackTrace();
            }

        }
        return true;
    }

    public static String replace86Code(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return mobile;
        }
        if (mobile.startsWith("+86")) {
            int index = mobile.indexOf("1");
            mobile = mobile.substring(index);
        }
        return mobile;
    }

    // 前面一层已经处理了"-",不会有 123-456-789
    // 检查手机号合法性
    public static Boolean checkMobile(String mobile) {
        return MobileUtil.checkMobileAndReturnMessage(mobile) == null;
    }

    public static String checkMobileAndReturnMessage(String mobile) {
        String errorMessage = null;
        if (mobile.startsWith("+")) {
            if (mobile.startsWith("+86")) {
                errorMessage = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_MOBILEUTIL_13);
            } else {
                if (!mobile.matches("^\\+\\d{1,3}-\\d{1,15}$")) {
                    errorMessage = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_MOBILEUTIL_16);
                }
            }
        } else {
            if (!mobile.matches("^1[\\d]{10}$")) {
                errorMessage = I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_MOBILEUTIL_20);
            }
        }
        return errorMessage;
    }

    /**
     * 获取第一个手机号
     *
     * @param mergeMobile crm 联系人 mobile 字段 = mobile1;:mobile2;:  拼接而成
     */
    public static String getFirstContactMobile(String mergeMobile) {
        if (mergeMobile == null) {
            return null;
        }
        String[] split = mergeMobile.split(";:");
        for (String mobile : split) {
            if (mobile != null && mobile.length() != 0 && !mobile.equals(" ")) {
                return mobile;
            }
        }
        return null;
    }

    /**
     * 判断手机号码是否为国际手机号
     */
    public static Boolean isInternationalMobile(String mobile) {
        Boolean interalRet = false;
        if (mobile.startsWith("+")) {
            if (mobile.matches("^\\+\\d{1,3}-\\d{1,15}$")) {
                interalRet = true;
            }
        }
        return interalRet;
    }

    public static String getPhoneWithoutSymbol(String mobile) {
        String result;
        result = mobile.replace("+", "");
        result = result.replace("-", "");
        return result;
    }

    private static final String phonepattern = "^[0-9+\\-;,]{0,100}$";

    /**
     * paas 手机号字段校验逻辑
     */
    public static boolean paasPhoneNumberValidate(String value) {
        String data = trim(value);
        if ("null".equals(data) || Strings.isNullOrEmpty(data)) {
            return true;
        }
        if (!data.matches(phonepattern)) {
            return false;
        }
        return true;
    }

    public static String trim(String value) {
        if (value == null) {
            return null;
        }
        int len = value.length();
        int st = 0;
        char[] val = value.toCharArray();

        while ((st < len) && ((val[st] <= ' ') || (val[st] == 0x202D) || (val[st] == 0x202C))) {
            st++;
        }
        while ((st < len) && ((val[len - 1] <= ' ') || (val[len - 1] == 0x202D) || (val[len - 1] == 0x202C))) {
            len--;
        }
        return ((st > 0) || (len < value.length())) ? value.substring(st, len) : value;
    }

    public static void main(String[] args) throws IOException {
        String phone = "phone";
        String json = new String(Files.readAllBytes(Paths.get("fs-enterprise-common/src/main/resources/internationalCode.json")));
        System.out.println(paasPhoneNumberValidate(phone));
        return;
    }
}
