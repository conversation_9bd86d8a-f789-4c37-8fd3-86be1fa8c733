package com.facishare.enterprise.common.enums;

import com.facishare.enterprise.common.annotation.IgnoreI18nFile;
import com.facishare.enterprise.common.util.I18nUtil;

//@IgnoreI18nFile
public enum BusinessErrorEnum {
    MISS_OBJINFO(1000, "缺少以下对象的信息 ： "),
    ILLEGAL_TYPE(1001, "非法的类型"),
    NEED_EXTENDS(1002, "需要继承 BaseOldManager 并在构造参数中传入对应biz dao 类的包名"),
    INVALID_ROLE(1003, "无效的角色类别"),
    ILLEGAL_APPROVAL(1004, "非法的审批操作"),
    PARAM_EXCEPTION(1005, "参数异常"),
    FILE_TRANSFER_FAILED(1006, "转存文件失败"),
    USER_DEPRECATED(1007, "互联用户[%s]已作废"),
    INVALID_DATA_PERMISSION(1008, "无效的数据权限范围"),
    INTERFACE_DEPRECATED(1009, "接口已废弃"),
    ACCOUNT_OR_PARTNER_MUST_FILLED(10010, "客户或者合作伙伴必须填写"),
    ACCOUNT_BINDED(10011, "客户已被其他企业绑定,绑定的互联企业ID："),
    ACCOUNT_ER_STOPPED(10012, "客户关联的互联企业已被停用"),
    PARTNER_ER_STOPPED(10013, "合作伙伴关联的互联企业已被停用"),
    MUST_BE(10014, "必须为"),
    REQUIRED_FIELD(10015, "必填"),
    NAME_REQUIRED(10016, "名称必填"),
    ER_NOT_STOPPED(10017, "该互联企业不是停用状态，不能作废"),
    ER_MAPPINGOBJ_ERR(10018, "该互联企业关联的客户或者合作伙伴已被作废或者关联到其它互联企业，请使用编辑功能重新关联客户或合作伙伴"),
    CONTACT_BINDED_USER(10019, "联系人已绑定了互联用户，互联用户外部Id="),
    USER_MAPPING_CONTACT_ERR(10020, "该互联用户关联的联系人已被作废或者关联到其它联系人，请使用编辑功能重新关联联系人"),
    ER_NOT_ENABLE(10021, "该互联企业不是启用状态，不能停用"),
    USER_NOT_ENABLE(10022, "该互联员工不是启用状态，不能停用"),
    ENTERPRISE_DEPRECATED(10023, "企业已停用"),
    SET_CRM_ADMIN_FIRST(10024, "请先设置crm管理员，如果无法设置请先查看是否有crm配额"),
    TENANT_CREATING(10025, "租户不能重复创建，已正在进行创建租户"),
    TENANT_BINDED(10026, "租户不能重复创建，已绑定租户"),
    ER_NON_PARTNER(10027, "互联企业无合作伙伴字段无法查询"),
    ER_DEPRECATED(10028, "该企业已作废"),
    UNSUPPORTED_OPERATION(10029, "不支持的操作"),
    TENANT_QUOTA_NOT_ENOUGH(10030, "下游企业的可以开通的CRM的配额不够"),
    ER_COPY_FAILED(10031, "复制企业失败"),
    LOGIN_MSG_TEMP(10032, "您好，%s为贵公司在纷享销客注册了企业帐号，企业名为%s，您可凭手机号%s和初始密码%s登录，点击链接%s下载纷享销客APP，网页端登陆地址：www.fxiaoke.com，客户服务热线%s"),
    COMMU_TENANT_QUOTA(10033, "配额不足，请沟通再使用"),
    ER_NAME_DUPLICATED(10034, "企业名称不能重复"),
    PAY_QUOTA_FIRST(10035, "请购买配额后再进行创建"),
    QUOTA_RUN_OUT(10036, "【%s】配额不足, 请根据配额调整后继续开通。"),
    CONTACT_NEED_MAPPINGOBJ(10037, "联系人需要先关联客户或者合作伙伴"),
    CONTACT_NON_MAPPINGOBJ(10038, "联系人未关联所选互联企业[%s]对应的客户/合作伙伴"),
    CONTACT_NON_ER_ACCOUNT(10039, "联系人未关联所选互联企业[%s]对应的客户"),
    CONTACT_NON_ER_PARTNER(10040, "联系人未关联所选互联企业[%s]对应的合作伙伴"),
    CONTACT_MAPACC_BINED(10041, "联系人对应的客户已关联其他互联企业，无法启用"),
    CONTACT_MAPPAR_BINED(10042, "联系人对应的合作伙伴已关联其他互联企业，无法启用"),
    ER_NOT_ADD(10043, "下游企业的管理方式不是企业代管，不能帮助下游企业创建互联帐号；下游企业登录到管理后台后，可以通过互联用户-上游企业，设置企业管理方式"),
    NON_WECHAT(10044, "微信公众号为空"),
    WECHAT_NOT_BIND(10045, "找不到公众号[%s]的绑定信息"),
    WECHAT_GUIDE(10046, "您在公众号【%s】有【%s】。可进入微信了解。"),
    UNSUPPORTED_MSG_TYPE(10047, "不支持的消息类型"),
    NOT_BIND_WECHAT(10048, "该用户没有绑定微信服务号，用户[%s]，微信服务号Id[%s]"),
    WECHAT_NOT_SET_APP(10049, "该企业微信服务号菜单没有设置该应用[%s]"),
    PARTNER_BINDED(10050, "合作伙伴已被其他企业绑定"),
    UNSUPPORTED_CHANNEL(10051, "暂不支持该渠道[%s]"),
    PARAMS_NOT_ENOUGH(10052, "参数不足，无法匹配模板消息"),
    ACCOUNT_BINDED_ER(10053, "客户已被其他企业绑定"),
    PM_NOT_STOP(10054, "该互联员工[%s]不是停用状态，不能作废"),
    CONTACT_DEPRECATED_OR_NOT_EXIST(10055, "联系人已作废或不存在"),
    PHONE_DUPLICATED(10056, "手机号码不能重复"),
    WAIT_CRM_OPENING(10057, "请先等待CRM状态开通成功再创建互联用户"),
    LOGIN_EMAIL_DUPLICATED(10058, "登录邮箱不能重复，如果不需要邮箱请在创建时去掉"),
    CREATE_PUBLIC_EMPLOYEE_APPROVAL_NOT_PASS(10059, "创建互联用户【%s】的审批流程未通过，数据未生效"),
    OUT_OF_MAX_EXPORT_RECORD_COUNT(10060, "超出最大导出数量(20000条)"),
    DOWNSTREAM_TENANT_EMPLOYEE_QUOTA_OUT(10061, "当前互联企业对应的下游企业的员工配额不足，创建互联用户失败。您可以移除无效的下游员工或者升级下游企业的员工配额后重新操作。"),
    PARTNER_FIRM_LIMIT_OUT(10062, "1+N企业数配额不足，请增加配额后再操作"),
    PARTNER_USER_FIRM_LIMIT_OUT(10063, "渠道用户数配额不足，请增加配额后再操作"),
    TERMINAL_USER_LIMIT_OUT(10064, "终端数配额不足，请增加配额后再操作"),
    ENTERPRISE_RELATION_EXISTS_BUT_INVALID(10065, "已经存在相同的互联企业信息，请您检查回收站的互联企业列表信息，处理已存在信息后重新连接。"),
    NO_ACCOUNT_OBJ_PERMISSION_ROLES(10066, "互联用户所属互联企业没有关联【客户】，因此设置有依赖客户对象的应用角色（如订货、快消相关角色）不能正常使用。【异常角色】：%s"),
    NO_PARTNER_OBJ_PERMISSION_ROLES(10067, "互联用户所属互联企业没有关联【合作伙伴】，因此设置有依赖合作伙伴的应用角色（如代理、服务商相关角色）不能正常使用。【异常角色】：%s"),
    MOBILE_AND_LOGIN_EMAIL_BOTH_EMPTY(10068, "您选择的员工没有手机号或邮箱号，不能开通为我方对接人。"),
    ER_DEP_NOT_STOP(10070, "该互联部门[%s]不是停用状态，不能作废"),
    INVALID_ER_DEP_NOT_VALID(10071, "仅支持作废无下级部门且无下级用户的互联部门，请转移该互联部门的下级信息后重新操作。"),
    NOT_UNBIND_THIRD_ACCOUNT(10072, "该用户没有解除绑定第三方账号，无法作废"),
    HAS_UNBIND_THIRD_ACCOUNT(10073, "该用户已经解除绑定第三方账号，无法作废"),
    NOT_RECORD_BIND_THIRD_ACCOUNT(10073, "用户绑定第三方账号数据不存在"),
    TEMPLATE_EA_NOT_NULL(10074, "模板企业账号不能为空"),
    ACCOUNT_MAPPER_REPEAT(10075, "客户已绑定互联企业，互联企业名称："),
    PARTNER_MAPPER_REPEAT(10076, "合作伙伴已绑定互联企业,互联企业名称："),
    ER_EMPLOYEE_NAME_DUPLICATED(10077, "互联用户名称在同一家企业下不能重复"),
    PASSWORD_TOO_WEAK(10078, "密码不符合强度"),
    ER_DEPARTMENT_NAME_DUPLICATED(10079, "互联部门名称[%s]在同一层级下不能重复"),
    ENTERPRISE_RELATION_STOP_NOT_VALID(10080, "互联企业对应的企业类型部门下存在其他企业类型的的部门，无法直接停用。请先移除对应的企业类型部门，再执行停用操作。"),
    PUBLIC_EMPLOYEE_NOT_STOP(10081, "该互联员工[%s]不是停用状态，不能删除");
    private Integer errorCode;
    private String desc;

    private BusinessErrorEnum(Integer errorCode, String desc) {
        this.errorCode = errorCode;
        this.desc = desc;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getDesc() {
        return I18nUtil.get("eip.business.info." + errorCode, desc);
    }
}
