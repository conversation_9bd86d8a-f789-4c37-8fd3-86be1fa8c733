package com.facishare.enterprise.common.model.paas;

import com.facishare.enterprise.common.constant.FieldPermissionEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class FieldPermissionV2 implements Serializable {
    private static final long serialVersionUID = 7685701521613717656L;
    /**
     * 字段apiName
     */
    private String fieldName;
    /**
     * 字段名称
     */
    private String fieldCaption;
    /**
     * 字段是否必填
     */
    private Boolean isRequired;
    /**
     * 是否可编辑
     */
    private Boolean isEditable = Boolean.TRUE;
    /**
     * 权限状态
     *
     * @see FieldPermissionEnum
     */
    private List<Integer> status;
    /**
     * 不可见是否可以选中
     */
    private Boolean isEditableOfInvisible = Boolean.TRUE;
    /**
     * 只读是否可以选中
     */
    private Boolean isEditableOfReadOnly = Boolean.TRUE;
    /**
     * 编辑是否可以选中
     */
    private Boolean isEditableOfWrite = Boolean.TRUE;
    /**
     * 导出是否可以选中
     */
    private Boolean isEditableOfExport = Boolean.TRUE;
}
