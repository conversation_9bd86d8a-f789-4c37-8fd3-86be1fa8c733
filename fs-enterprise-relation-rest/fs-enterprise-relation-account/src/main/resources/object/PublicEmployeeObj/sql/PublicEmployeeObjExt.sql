-- 互联用户对象升级为公共对象时需要使用到的扩展表定义
-- 采用分表方案，表名规则为 ${原描述的biz_public_employee}_ext_${序号}
-- 目前采用分表规则， ext_${序号} 其中序号 从 0～9 默认分成10个表
-- 列为具体类型，默认情况下 四种类型 "numeric", "bool", "varchar[]", "varchar" 一下为每种类型分配200列，具体可根据业务扩展，类型也决定每种字段具体可以创建私有字段数量为多少

CREATE TABLE IF NOT EXISTS biz_public_employee_ext_9(
    "id" VARCHAR (64)  NOT NULL,
    "tenant_id" VARCHAR (16)  NOT NULL,
    "created_by" VARCHAR (32) ,
    "object_describe_api_name" VARCHAR (100) ,
    "data_own_department" VARCHAR (32) ,
    "data_own_organization" VARCHAR (32) ,
    "out_data_own_department" varchar(64) ,
    "out_data_own_organization" varchar(64) ,
    "last_modified_by" VARCHAR(50),
    "out_tenant_id" VARCHAR(50),
    "out_owner" VARCHAR(50),
    "owner" VARCHAR (128) ,
    "dimension_d1" varchar[],
    "dimension_d2" varchar[],
    "dimension_d3" varchar[],
    "data_auth_id" int4,
    "out_data_auth_id" int4,
    "data_auth_code" varchar(64),
    "out_data_auth_code" varchar(64),
    "sys_modified_time" bigint ,
    value1 numeric,
    value2 numeric,
    value3 numeric,
    value4 numeric,
    value5 numeric,
    value6 numeric,
    value7 numeric,
    value8 numeric,
    value9 numeric,
    value10 numeric,
    value11 numeric,
    value12 numeric,
    value13 numeric,
    value14 numeric,
    value15 numeric,
    value16 numeric,
    value17 numeric,
    value18 numeric,
    value19 numeric,
    value20 numeric,
    value21 numeric,
    value22 numeric,
    value23 numeric,
    value24 numeric,
    value25 numeric,
    value26 numeric,
    value27 numeric,
    value28 numeric,
    value29 numeric,
    value30 numeric,
    value31 numeric,
    value32 numeric,
    value33 numeric,
    value34 numeric,
    value35 numeric,
    value36 numeric,
    value37 numeric,
    value38 numeric,
    value39 numeric,
    value40 numeric,
    value41 numeric,
    value42 numeric,
    value43 numeric,
    value44 numeric,
    value45 numeric,
    value46 numeric,
    value47 numeric,
    value48 numeric,
    value49 numeric,
    value50 numeric,
    value51 numeric,
    value52 numeric,
    value53 numeric,
    value54 numeric,
    value55 numeric,
    value56 numeric,
    value57 numeric,
    value58 numeric,
    value59 numeric,
    value60 numeric,
    value61 numeric,
    value62 numeric,
    value63 numeric,
    value64 numeric,
    value65 numeric,
    value66 numeric,
    value67 numeric,
    value68 numeric,
    value69 numeric,
    value70 numeric,
    value71 numeric,
    value72 numeric,
    value73 numeric,
    value74 numeric,
    value75 numeric,
    value76 numeric,
    value77 numeric,
    value78 numeric,
    value79 numeric,
    value80 numeric,
    value81 numeric,
    value82 numeric,
    value83 numeric,
    value84 numeric,
    value85 numeric,
    value86 numeric,
    value87 numeric,
    value88 numeric,
    value89 numeric,
    value90 numeric,
    value91 numeric,
    value92 numeric,
    value93 numeric,
    value94 numeric,
    value95 numeric,
    value96 numeric,
    value97 numeric,
    value98 numeric,
    value99 numeric,
    value100 numeric,
    value101 numeric,
    value102 numeric,
    value103 numeric,
    value104 numeric,
    value105 numeric,
    value106 numeric,
    value107 numeric,
    value108 numeric,
    value109 numeric,
    value110 numeric,
    value111 numeric,
    value112 numeric,
    value113 numeric,
    value114 numeric,
    value115 numeric,
    value116 numeric,
    value117 numeric,
    value118 numeric,
    value119 numeric,
    value120 numeric,
    value121 numeric,
    value122 numeric,
    value123 numeric,
    value124 numeric,
    value125 numeric,
    value126 numeric,
    value127 numeric,
    value128 numeric,
    value129 numeric,
    value130 numeric,
    value131 numeric,
    value132 numeric,
    value133 numeric,
    value134 numeric,
    value135 numeric,
    value136 numeric,
    value137 numeric,
    value138 numeric,
    value139 numeric,
    value140 numeric,
    value141 numeric,
    value142 numeric,
    value143 numeric,
    value144 numeric,
    value145 numeric,
    value146 numeric,
    value147 numeric,
    value148 numeric,
    value149 numeric,
    value150 numeric,
    value151 numeric,
    value152 numeric,
    value153 numeric,
    value154 numeric,
    value155 numeric,
    value156 numeric,
    value157 numeric,
    value158 numeric,
    value159 numeric,
    value160 numeric,
    value161 numeric,
    value162 numeric,
    value163 numeric,
    value164 numeric,
    value165 numeric,
    value166 numeric,
    value167 numeric,
    value168 numeric,
    value169 numeric,
    value170 numeric,
    value171 numeric,
    value172 numeric,
    value173 numeric,
    value174 numeric,
    value175 numeric,
    value176 numeric,
    value177 numeric,
    value178 numeric,
    value179 numeric,
    value180 numeric,
    value181 numeric,
    value182 numeric,
    value183 numeric,
    value184 numeric,
    value185 numeric,
    value186 numeric,
    value187 numeric,
    value188 numeric,
    value189 numeric,
    value190 numeric,
    value191 numeric,
    value192 numeric,
    value193 numeric,
    value194 numeric,
    value195 numeric,
    value196 numeric,
    value197 numeric,
    value198 numeric,
    value199 numeric,
    value200 numeric,
    value201 bool,
    value202 bool,
    value203 bool,
    value204 bool,
    value205 bool,
    value206 bool,
    value207 bool,
    value208 bool,
    value209 bool,
    value210 bool,
    value211 bool,
    value212 bool,
    value213 bool,
    value214 bool,
    value215 bool,
    value216 bool,
    value217 bool,
    value218 bool,
    value219 bool,
    value220 bool,
    value221 bool,
    value222 bool,
    value223 bool,
    value224 bool,
    value225 bool,
    value226 bool,
    value227 bool,
    value228 bool,
    value229 bool,
    value230 bool,
    value231 bool,
    value232 bool,
    value233 bool,
    value234 bool,
    value235 bool,
    value236 bool,
    value237 bool,
    value238 bool,
    value239 bool,
    value240 bool,
    value241 bool,
    value242 bool,
    value243 bool,
    value244 bool,
    value245 bool,
    value246 bool,
    value247 bool,
    value248 bool,
    value249 bool,
    value250 bool,
    value251 bool,
    value252 bool,
    value253 bool,
    value254 bool,
    value255 bool,
    value256 bool,
    value257 bool,
    value258 bool,
    value259 bool,
    value260 bool,
    value261 bool,
    value262 bool,
    value263 bool,
    value264 bool,
    value265 bool,
    value266 bool,
    value267 bool,
    value268 bool,
    value269 bool,
    value270 bool,
    value271 bool,
    value272 bool,
    value273 bool,
    value274 bool,
    value275 bool,
    value276 bool,
    value277 bool,
    value278 bool,
    value279 bool,
    value280 bool,
    value281 bool,
    value282 bool,
    value283 bool,
    value284 bool,
    value285 bool,
    value286 bool,
    value287 bool,
    value288 bool,
    value289 bool,
    value290 bool,
    value291 bool,
    value292 bool,
    value293 bool,
    value294 bool,
    value295 bool,
    value296 bool,
    value297 bool,
    value298 bool,
    value299 bool,
    value300 bool,
    value301 bool,
    value302 bool,
    value303 bool,
    value304 bool,
    value305 bool,
    value306 bool,
    value307 bool,
    value308 bool,
    value309 bool,
    value310 bool,
    value311 bool,
    value312 bool,
    value313 bool,
    value314 bool,
    value315 bool,
    value316 bool,
    value317 bool,
    value318 bool,
    value319 bool,
    value320 bool,
    value321 bool,
    value322 bool,
    value323 bool,
    value324 bool,
    value325 bool,
    value326 bool,
    value327 bool,
    value328 bool,
    value329 bool,
    value330 bool,
    value331 bool,
    value332 bool,
    value333 bool,
    value334 bool,
    value335 bool,
    value336 bool,
    value337 bool,
    value338 bool,
    value339 bool,
    value340 bool,
    value341 bool,
    value342 bool,
    value343 bool,
    value344 bool,
    value345 bool,
    value346 bool,
    value347 bool,
    value348 bool,
    value349 bool,
    value350 bool,
    value351 bool,
    value352 bool,
    value353 bool,
    value354 bool,
    value355 bool,
    value356 bool,
    value357 bool,
    value358 bool,
    value359 bool,
    value360 bool,
    value361 bool,
    value362 bool,
    value363 bool,
    value364 bool,
    value365 bool,
    value366 bool,
    value367 bool,
    value368 bool,
    value369 bool,
    value370 bool,
    value371 bool,
    value372 bool,
    value373 bool,
    value374 bool,
    value375 bool,
    value376 bool,
    value377 bool,
    value378 bool,
    value379 bool,
    value380 bool,
    value381 bool,
    value382 bool,
    value383 bool,
    value384 bool,
    value385 bool,
    value386 bool,
    value387 bool,
    value388 bool,
    value389 bool,
    value390 bool,
    value391 bool,
    value392 bool,
    value393 bool,
    value394 bool,
    value395 bool,
    value396 bool,
    value397 bool,
    value398 bool,
    value399 bool,
    value400 bool,
    value401 varchar[],
    value402 varchar[],
    value403 varchar[],
    value404 varchar[],
    value405 varchar[],
    value406 varchar[],
    value407 varchar[],
    value408 varchar[],
    value409 varchar[],
    value410 varchar[],
    value411 varchar[],
    value412 varchar[],
    value413 varchar[],
    value414 varchar[],
    value415 varchar[],
    value416 varchar[],
    value417 varchar[],
    value418 varchar[],
    value419 varchar[],
    value420 varchar[],
    value421 varchar[],
    value422 varchar[],
    value423 varchar[],
    value424 varchar[],
    value425 varchar[],
    value426 varchar[],
    value427 varchar[],
    value428 varchar[],
    value429 varchar[],
    value430 varchar[],
    value431 varchar[],
    value432 varchar[],
    value433 varchar[],
    value434 varchar[],
    value435 varchar[],
    value436 varchar[],
    value437 varchar[],
    value438 varchar[],
    value439 varchar[],
    value440 varchar[],
    value441 varchar[],
    value442 varchar[],
    value443 varchar[],
    value444 varchar[],
    value445 varchar[],
    value446 varchar[],
    value447 varchar[],
    value448 varchar[],
    value449 varchar[],
    value450 varchar[],
    value451 varchar[],
    value452 varchar[],
    value453 varchar[],
    value454 varchar[],
    value455 varchar[],
    value456 varchar[],
    value457 varchar[],
    value458 varchar[],
    value459 varchar[],
    value460 varchar[],
    value461 varchar[],
    value462 varchar[],
    value463 varchar[],
    value464 varchar[],
    value465 varchar[],
    value466 varchar[],
    value467 varchar[],
    value468 varchar[],
    value469 varchar[],
    value470 varchar[],
    value471 varchar[],
    value472 varchar[],
    value473 varchar[],
    value474 varchar[],
    value475 varchar[],
    value476 varchar[],
    value477 varchar[],
    value478 varchar[],
    value479 varchar[],
    value480 varchar[],
    value481 varchar[],
    value482 varchar[],
    value483 varchar[],
    value484 varchar[],
    value485 varchar[],
    value486 varchar[],
    value487 varchar[],
    value488 varchar[],
    value489 varchar[],
    value490 varchar[],
    value491 varchar[],
    value492 varchar[],
    value493 varchar[],
    value494 varchar[],
    value495 varchar[],
    value496 varchar[],
    value497 varchar[],
    value498 varchar[],
    value499 varchar[],
    value500 varchar[],
    value501 varchar[],
    value502 varchar[],
    value503 varchar[],
    value504 varchar[],
    value505 varchar[],
    value506 varchar[],
    value507 varchar[],
    value508 varchar[],
    value509 varchar[],
    value510 varchar[],
    value511 varchar[],
    value512 varchar[],
    value513 varchar[],
    value514 varchar[],
    value515 varchar[],
    value516 varchar[],
    value517 varchar[],
    value518 varchar[],
    value519 varchar[],
    value520 varchar[],
    value521 varchar[],
    value522 varchar[],
    value523 varchar[],
    value524 varchar[],
    value525 varchar[],
    value526 varchar[],
    value527 varchar[],
    value528 varchar[],
    value529 varchar[],
    value530 varchar[],
    value531 varchar[],
    value532 varchar[],
    value533 varchar[],
    value534 varchar[],
    value535 varchar[],
    value536 varchar[],
    value537 varchar[],
    value538 varchar[],
    value539 varchar[],
    value540 varchar[],
    value541 varchar[],
    value542 varchar[],
    value543 varchar[],
    value544 varchar[],
    value545 varchar[],
    value546 varchar[],
    value547 varchar[],
    value548 varchar[],
    value549 varchar[],
    value550 varchar[],
    value551 varchar[],
    value552 varchar[],
    value553 varchar[],
    value554 varchar[],
    value555 varchar[],
    value556 varchar[],
    value557 varchar[],
    value558 varchar[],
    value559 varchar[],
    value560 varchar[],
    value561 varchar[],
    value562 varchar[],
    value563 varchar[],
    value564 varchar[],
    value565 varchar[],
    value566 varchar[],
    value567 varchar[],
    value568 varchar[],
    value569 varchar[],
    value570 varchar[],
    value571 varchar[],
    value572 varchar[],
    value573 varchar[],
    value574 varchar[],
    value575 varchar[],
    value576 varchar[],
    value577 varchar[],
    value578 varchar[],
    value579 varchar[],
    value580 varchar[],
    value581 varchar[],
    value582 varchar[],
    value583 varchar[],
    value584 varchar[],
    value585 varchar[],
    value586 varchar[],
    value587 varchar[],
    value588 varchar[],
    value589 varchar[],
    value590 varchar[],
    value591 varchar[],
    value592 varchar[],
    value593 varchar[],
    value594 varchar[],
    value595 varchar[],
    value596 varchar[],
    value597 varchar[],
    value598 varchar[],
    value599 varchar[],
    value600 varchar[],
    value601 varchar,
    value602 varchar,
    value603 varchar,
    value604 varchar,
    value605 varchar,
    value606 varchar,
    value607 varchar,
    value608 varchar,
    value609 varchar,
    value610 varchar,
    value611 varchar,
    value612 varchar,
    value613 varchar,
    value614 varchar,
    value615 varchar,
    value616 varchar,
    value617 varchar,
    value618 varchar,
    value619 varchar,
    value620 varchar,
    value621 varchar,
    value622 varchar,
    value623 varchar,
    value624 varchar,
    value625 varchar,
    value626 varchar,
    value627 varchar,
    value628 varchar,
    value629 varchar,
    value630 varchar,
    value631 varchar,
    value632 varchar,
    value633 varchar,
    value634 varchar,
    value635 varchar,
    value636 varchar,
    value637 varchar,
    value638 varchar,
    value639 varchar,
    value640 varchar,
    value641 varchar,
    value642 varchar,
    value643 varchar,
    value644 varchar,
    value645 varchar,
    value646 varchar,
    value647 varchar,
    value648 varchar,
    value649 varchar,
    value650 varchar,
    value651 varchar,
    value652 varchar,
    value653 varchar,
    value654 varchar,
    value655 varchar,
    value656 varchar,
    value657 varchar,
    value658 varchar,
    value659 varchar,
    value660 varchar,
    value661 varchar,
    value662 varchar,
    value663 varchar,
    value664 varchar,
    value665 varchar,
    value666 varchar,
    value667 varchar,
    value668 varchar,
    value669 varchar,
    value670 varchar,
    value671 varchar,
    value672 varchar,
    value673 varchar,
    value674 varchar,
    value675 varchar,
    value676 varchar,
    value677 varchar,
    value678 varchar,
    value679 varchar,
    value680 varchar,
    value681 varchar,
    value682 varchar,
    value683 varchar,
    value684 varchar,
    value685 varchar,
    value686 varchar,
    value687 varchar,
    value688 varchar,
    value689 varchar,
    value690 varchar,
    value691 varchar,
    value692 varchar,
    value693 varchar,
    value694 varchar,
    value695 varchar,
    value696 varchar,
    value697 varchar,
    value698 varchar,
    value699 varchar,
    value700 varchar,
    value701 varchar,
    value702 varchar,
    value703 varchar,
    value704 varchar,
    value705 varchar,
    value706 varchar,
    value707 varchar,
    value708 varchar,
    value709 varchar,
    value710 varchar,
    value711 varchar,
    value712 varchar,
    value713 varchar,
    value714 varchar,
    value715 varchar,
    value716 varchar,
    value717 varchar,
    value718 varchar,
    value719 varchar,
    value720 varchar,
    value721 varchar,
    value722 varchar,
    value723 varchar,
    value724 varchar,
    value725 varchar,
    value726 varchar,
    value727 varchar,
    value728 varchar,
    value729 varchar,
    value730 varchar,
    value731 varchar,
    value732 varchar,
    value733 varchar,
    value734 varchar,
    value735 varchar,
    value736 varchar,
    value737 varchar,
    value738 varchar,
    value739 varchar,
    value740 varchar,
    value741 varchar,
    value742 varchar,
    value743 varchar,
    value744 varchar,
    value745 varchar,
    value746 varchar,
    value747 varchar,
    value748 varchar,
    value749 varchar,
    value750 varchar,
    value751 varchar,
    value752 varchar,
    value753 varchar,
    value754 varchar,
    value755 varchar,
    value756 varchar,
    value757 varchar,
    value758 varchar,
    value759 varchar,
    value760 varchar,
    value761 varchar,
    value762 varchar,
    value763 varchar,
    value764 varchar,
    value765 varchar,
    value766 varchar,
    value767 varchar,
    value768 varchar,
    value769 varchar,
    value770 varchar,
    value771 varchar,
    value772 varchar,
    value773 varchar,
    value774 varchar,
    value775 varchar,
    value776 varchar,
    value777 varchar,
    value778 varchar,
    value779 varchar,
    value780 varchar,
    value781 varchar,
    value782 varchar,
    value783 varchar,
    value784 varchar,
    value785 varchar,
    value786 varchar,
    value787 varchar,
    value788 varchar,
    value789 varchar,
    value790 varchar,
    value791 varchar,
    value792 varchar,
    value793 varchar,
    value794 varchar,
    value795 varchar,
    value796 varchar,
    value797 varchar,
    value798 varchar,
    value799 varchar,
    value800 varchar,
    CONSTRAINT "biz_public_employee_ext_9_pkey" PRIMARY KEY ( "id", "tenant_id" )
);

DROP TRIGGER IF EXISTS x_audit_changes ON biz_public_employee_ext_9;

CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON biz_public_employee_ext_9 FOR EACH ROW EXECUTE PROCEDURE public.f_change_detail('id','tenant_id','object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON biz_public_employee_ext_9;

CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON biz_public_employee_ext_9 FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();

CREATE TABLE IF NOT EXISTS biz_public_employee_ext_8(
    "id" VARCHAR (64)  NOT NULL,
    "tenant_id" VARCHAR (16)  NOT NULL,
    "created_by" VARCHAR (32) ,
    "object_describe_api_name" VARCHAR (100) ,
    "data_own_department" VARCHAR (32) ,
    "data_own_organization" VARCHAR (32) ,
    "out_data_own_department" varchar(64) ,
    "out_data_own_organization" varchar(64) ,
    "last_modified_by" VARCHAR(50),
    "out_tenant_id" VARCHAR(50),
    "out_owner" VARCHAR(50),
    "owner" VARCHAR (128) ,
    "dimension_d1" varchar[],
    "dimension_d2" varchar[],
    "dimension_d3" varchar[],
    "data_auth_id" int4,
    "out_data_auth_id" int4,
    "data_auth_code" varchar(64),
    "out_data_auth_code" varchar(64),
    "sys_modified_time" bigint ,
    value1 numeric,
    value2 numeric,
    value3 numeric,
    value4 numeric,
    value5 numeric,
    value6 numeric,
    value7 numeric,
    value8 numeric,
    value9 numeric,
    value10 numeric,
    value11 numeric,
    value12 numeric,
    value13 numeric,
    value14 numeric,
    value15 numeric,
    value16 numeric,
    value17 numeric,
    value18 numeric,
    value19 numeric,
    value20 numeric,
    value21 numeric,
    value22 numeric,
    value23 numeric,
    value24 numeric,
    value25 numeric,
    value26 numeric,
    value27 numeric,
    value28 numeric,
    value29 numeric,
    value30 numeric,
    value31 numeric,
    value32 numeric,
    value33 numeric,
    value34 numeric,
    value35 numeric,
    value36 numeric,
    value37 numeric,
    value38 numeric,
    value39 numeric,
    value40 numeric,
    value41 numeric,
    value42 numeric,
    value43 numeric,
    value44 numeric,
    value45 numeric,
    value46 numeric,
    value47 numeric,
    value48 numeric,
    value49 numeric,
    value50 numeric,
    value51 numeric,
    value52 numeric,
    value53 numeric,
    value54 numeric,
    value55 numeric,
    value56 numeric,
    value57 numeric,
    value58 numeric,
    value59 numeric,
    value60 numeric,
    value61 numeric,
    value62 numeric,
    value63 numeric,
    value64 numeric,
    value65 numeric,
    value66 numeric,
    value67 numeric,
    value68 numeric,
    value69 numeric,
    value70 numeric,
    value71 numeric,
    value72 numeric,
    value73 numeric,
    value74 numeric,
    value75 numeric,
    value76 numeric,
    value77 numeric,
    value78 numeric,
    value79 numeric,
    value80 numeric,
    value81 numeric,
    value82 numeric,
    value83 numeric,
    value84 numeric,
    value85 numeric,
    value86 numeric,
    value87 numeric,
    value88 numeric,
    value89 numeric,
    value90 numeric,
    value91 numeric,
    value92 numeric,
    value93 numeric,
    value94 numeric,
    value95 numeric,
    value96 numeric,
    value97 numeric,
    value98 numeric,
    value99 numeric,
    value100 numeric,
    value101 numeric,
    value102 numeric,
    value103 numeric,
    value104 numeric,
    value105 numeric,
    value106 numeric,
    value107 numeric,
    value108 numeric,
    value109 numeric,
    value110 numeric,
    value111 numeric,
    value112 numeric,
    value113 numeric,
    value114 numeric,
    value115 numeric,
    value116 numeric,
    value117 numeric,
    value118 numeric,
    value119 numeric,
    value120 numeric,
    value121 numeric,
    value122 numeric,
    value123 numeric,
    value124 numeric,
    value125 numeric,
    value126 numeric,
    value127 numeric,
    value128 numeric,
    value129 numeric,
    value130 numeric,
    value131 numeric,
    value132 numeric,
    value133 numeric,
    value134 numeric,
    value135 numeric,
    value136 numeric,
    value137 numeric,
    value138 numeric,
    value139 numeric,
    value140 numeric,
    value141 numeric,
    value142 numeric,
    value143 numeric,
    value144 numeric,
    value145 numeric,
    value146 numeric,
    value147 numeric,
    value148 numeric,
    value149 numeric,
    value150 numeric,
    value151 numeric,
    value152 numeric,
    value153 numeric,
    value154 numeric,
    value155 numeric,
    value156 numeric,
    value157 numeric,
    value158 numeric,
    value159 numeric,
    value160 numeric,
    value161 numeric,
    value162 numeric,
    value163 numeric,
    value164 numeric,
    value165 numeric,
    value166 numeric,
    value167 numeric,
    value168 numeric,
    value169 numeric,
    value170 numeric,
    value171 numeric,
    value172 numeric,
    value173 numeric,
    value174 numeric,
    value175 numeric,
    value176 numeric,
    value177 numeric,
    value178 numeric,
    value179 numeric,
    value180 numeric,
    value181 numeric,
    value182 numeric,
    value183 numeric,
    value184 numeric,
    value185 numeric,
    value186 numeric,
    value187 numeric,
    value188 numeric,
    value189 numeric,
    value190 numeric,
    value191 numeric,
    value192 numeric,
    value193 numeric,
    value194 numeric,
    value195 numeric,
    value196 numeric,
    value197 numeric,
    value198 numeric,
    value199 numeric,
    value200 numeric,
    value201 bool,
    value202 bool,
    value203 bool,
    value204 bool,
    value205 bool,
    value206 bool,
    value207 bool,
    value208 bool,
    value209 bool,
    value210 bool,
    value211 bool,
    value212 bool,
    value213 bool,
    value214 bool,
    value215 bool,
    value216 bool,
    value217 bool,
    value218 bool,
    value219 bool,
    value220 bool,
    value221 bool,
    value222 bool,
    value223 bool,
    value224 bool,
    value225 bool,
    value226 bool,
    value227 bool,
    value228 bool,
    value229 bool,
    value230 bool,
    value231 bool,
    value232 bool,
    value233 bool,
    value234 bool,
    value235 bool,
    value236 bool,
    value237 bool,
    value238 bool,
    value239 bool,
    value240 bool,
    value241 bool,
    value242 bool,
    value243 bool,
    value244 bool,
    value245 bool,
    value246 bool,
    value247 bool,
    value248 bool,
    value249 bool,
    value250 bool,
    value251 bool,
    value252 bool,
    value253 bool,
    value254 bool,
    value255 bool,
    value256 bool,
    value257 bool,
    value258 bool,
    value259 bool,
    value260 bool,
    value261 bool,
    value262 bool,
    value263 bool,
    value264 bool,
    value265 bool,
    value266 bool,
    value267 bool,
    value268 bool,
    value269 bool,
    value270 bool,
    value271 bool,
    value272 bool,
    value273 bool,
    value274 bool,
    value275 bool,
    value276 bool,
    value277 bool,
    value278 bool,
    value279 bool,
    value280 bool,
    value281 bool,
    value282 bool,
    value283 bool,
    value284 bool,
    value285 bool,
    value286 bool,
    value287 bool,
    value288 bool,
    value289 bool,
    value290 bool,
    value291 bool,
    value292 bool,
    value293 bool,
    value294 bool,
    value295 bool,
    value296 bool,
    value297 bool,
    value298 bool,
    value299 bool,
    value300 bool,
    value301 bool,
    value302 bool,
    value303 bool,
    value304 bool,
    value305 bool,
    value306 bool,
    value307 bool,
    value308 bool,
    value309 bool,
    value310 bool,
    value311 bool,
    value312 bool,
    value313 bool,
    value314 bool,
    value315 bool,
    value316 bool,
    value317 bool,
    value318 bool,
    value319 bool,
    value320 bool,
    value321 bool,
    value322 bool,
    value323 bool,
    value324 bool,
    value325 bool,
    value326 bool,
    value327 bool,
    value328 bool,
    value329 bool,
    value330 bool,
    value331 bool,
    value332 bool,
    value333 bool,
    value334 bool,
    value335 bool,
    value336 bool,
    value337 bool,
    value338 bool,
    value339 bool,
    value340 bool,
    value341 bool,
    value342 bool,
    value343 bool,
    value344 bool,
    value345 bool,
    value346 bool,
    value347 bool,
    value348 bool,
    value349 bool,
    value350 bool,
    value351 bool,
    value352 bool,
    value353 bool,
    value354 bool,
    value355 bool,
    value356 bool,
    value357 bool,
    value358 bool,
    value359 bool,
    value360 bool,
    value361 bool,
    value362 bool,
    value363 bool,
    value364 bool,
    value365 bool,
    value366 bool,
    value367 bool,
    value368 bool,
    value369 bool,
    value370 bool,
    value371 bool,
    value372 bool,
    value373 bool,
    value374 bool,
    value375 bool,
    value376 bool,
    value377 bool,
    value378 bool,
    value379 bool,
    value380 bool,
    value381 bool,
    value382 bool,
    value383 bool,
    value384 bool,
    value385 bool,
    value386 bool,
    value387 bool,
    value388 bool,
    value389 bool,
    value390 bool,
    value391 bool,
    value392 bool,
    value393 bool,
    value394 bool,
    value395 bool,
    value396 bool,
    value397 bool,
    value398 bool,
    value399 bool,
    value400 bool,
    value401 varchar[],
    value402 varchar[],
    value403 varchar[],
    value404 varchar[],
    value405 varchar[],
    value406 varchar[],
    value407 varchar[],
    value408 varchar[],
    value409 varchar[],
    value410 varchar[],
    value411 varchar[],
    value412 varchar[],
    value413 varchar[],
    value414 varchar[],
    value415 varchar[],
    value416 varchar[],
    value417 varchar[],
    value418 varchar[],
    value419 varchar[],
    value420 varchar[],
    value421 varchar[],
    value422 varchar[],
    value423 varchar[],
    value424 varchar[],
    value425 varchar[],
    value426 varchar[],
    value427 varchar[],
    value428 varchar[],
    value429 varchar[],
    value430 varchar[],
    value431 varchar[],
    value432 varchar[],
    value433 varchar[],
    value434 varchar[],
    value435 varchar[],
    value436 varchar[],
    value437 varchar[],
    value438 varchar[],
    value439 varchar[],
    value440 varchar[],
    value441 varchar[],
    value442 varchar[],
    value443 varchar[],
    value444 varchar[],
    value445 varchar[],
    value446 varchar[],
    value447 varchar[],
    value448 varchar[],
    value449 varchar[],
    value450 varchar[],
    value451 varchar[],
    value452 varchar[],
    value453 varchar[],
    value454 varchar[],
    value455 varchar[],
    value456 varchar[],
    value457 varchar[],
    value458 varchar[],
    value459 varchar[],
    value460 varchar[],
    value461 varchar[],
    value462 varchar[],
    value463 varchar[],
    value464 varchar[],
    value465 varchar[],
    value466 varchar[],
    value467 varchar[],
    value468 varchar[],
    value469 varchar[],
    value470 varchar[],
    value471 varchar[],
    value472 varchar[],
    value473 varchar[],
    value474 varchar[],
    value475 varchar[],
    value476 varchar[],
    value477 varchar[],
    value478 varchar[],
    value479 varchar[],
    value480 varchar[],
    value481 varchar[],
    value482 varchar[],
    value483 varchar[],
    value484 varchar[],
    value485 varchar[],
    value486 varchar[],
    value487 varchar[],
    value488 varchar[],
    value489 varchar[],
    value490 varchar[],
    value491 varchar[],
    value492 varchar[],
    value493 varchar[],
    value494 varchar[],
    value495 varchar[],
    value496 varchar[],
    value497 varchar[],
    value498 varchar[],
    value499 varchar[],
    value500 varchar[],
    value501 varchar[],
    value502 varchar[],
    value503 varchar[],
    value504 varchar[],
    value505 varchar[],
    value506 varchar[],
    value507 varchar[],
    value508 varchar[],
    value509 varchar[],
    value510 varchar[],
    value511 varchar[],
    value512 varchar[],
    value513 varchar[],
    value514 varchar[],
    value515 varchar[],
    value516 varchar[],
    value517 varchar[],
    value518 varchar[],
    value519 varchar[],
    value520 varchar[],
    value521 varchar[],
    value522 varchar[],
    value523 varchar[],
    value524 varchar[],
    value525 varchar[],
    value526 varchar[],
    value527 varchar[],
    value528 varchar[],
    value529 varchar[],
    value530 varchar[],
    value531 varchar[],
    value532 varchar[],
    value533 varchar[],
    value534 varchar[],
    value535 varchar[],
    value536 varchar[],
    value537 varchar[],
    value538 varchar[],
    value539 varchar[],
    value540 varchar[],
    value541 varchar[],
    value542 varchar[],
    value543 varchar[],
    value544 varchar[],
    value545 varchar[],
    value546 varchar[],
    value547 varchar[],
    value548 varchar[],
    value549 varchar[],
    value550 varchar[],
    value551 varchar[],
    value552 varchar[],
    value553 varchar[],
    value554 varchar[],
    value555 varchar[],
    value556 varchar[],
    value557 varchar[],
    value558 varchar[],
    value559 varchar[],
    value560 varchar[],
    value561 varchar[],
    value562 varchar[],
    value563 varchar[],
    value564 varchar[],
    value565 varchar[],
    value566 varchar[],
    value567 varchar[],
    value568 varchar[],
    value569 varchar[],
    value570 varchar[],
    value571 varchar[],
    value572 varchar[],
    value573 varchar[],
    value574 varchar[],
    value575 varchar[],
    value576 varchar[],
    value577 varchar[],
    value578 varchar[],
    value579 varchar[],
    value580 varchar[],
    value581 varchar[],
    value582 varchar[],
    value583 varchar[],
    value584 varchar[],
    value585 varchar[],
    value586 varchar[],
    value587 varchar[],
    value588 varchar[],
    value589 varchar[],
    value590 varchar[],
    value591 varchar[],
    value592 varchar[],
    value593 varchar[],
    value594 varchar[],
    value595 varchar[],
    value596 varchar[],
    value597 varchar[],
    value598 varchar[],
    value599 varchar[],
    value600 varchar[],
    value601 varchar,
    value602 varchar,
    value603 varchar,
    value604 varchar,
    value605 varchar,
    value606 varchar,
    value607 varchar,
    value608 varchar,
    value609 varchar,
    value610 varchar,
    value611 varchar,
    value612 varchar,
    value613 varchar,
    value614 varchar,
    value615 varchar,
    value616 varchar,
    value617 varchar,
    value618 varchar,
    value619 varchar,
    value620 varchar,
    value621 varchar,
    value622 varchar,
    value623 varchar,
    value624 varchar,
    value625 varchar,
    value626 varchar,
    value627 varchar,
    value628 varchar,
    value629 varchar,
    value630 varchar,
    value631 varchar,
    value632 varchar,
    value633 varchar,
    value634 varchar,
    value635 varchar,
    value636 varchar,
    value637 varchar,
    value638 varchar,
    value639 varchar,
    value640 varchar,
    value641 varchar,
    value642 varchar,
    value643 varchar,
    value644 varchar,
    value645 varchar,
    value646 varchar,
    value647 varchar,
    value648 varchar,
    value649 varchar,
    value650 varchar,
    value651 varchar,
    value652 varchar,
    value653 varchar,
    value654 varchar,
    value655 varchar,
    value656 varchar,
    value657 varchar,
    value658 varchar,
    value659 varchar,
    value660 varchar,
    value661 varchar,
    value662 varchar,
    value663 varchar,
    value664 varchar,
    value665 varchar,
    value666 varchar,
    value667 varchar,
    value668 varchar,
    value669 varchar,
    value670 varchar,
    value671 varchar,
    value672 varchar,
    value673 varchar,
    value674 varchar,
    value675 varchar,
    value676 varchar,
    value677 varchar,
    value678 varchar,
    value679 varchar,
    value680 varchar,
    value681 varchar,
    value682 varchar,
    value683 varchar,
    value684 varchar,
    value685 varchar,
    value686 varchar,
    value687 varchar,
    value688 varchar,
    value689 varchar,
    value690 varchar,
    value691 varchar,
    value692 varchar,
    value693 varchar,
    value694 varchar,
    value695 varchar,
    value696 varchar,
    value697 varchar,
    value698 varchar,
    value699 varchar,
    value700 varchar,
    value701 varchar,
    value702 varchar,
    value703 varchar,
    value704 varchar,
    value705 varchar,
    value706 varchar,
    value707 varchar,
    value708 varchar,
    value709 varchar,
    value710 varchar,
    value711 varchar,
    value712 varchar,
    value713 varchar,
    value714 varchar,
    value715 varchar,
    value716 varchar,
    value717 varchar,
    value718 varchar,
    value719 varchar,
    value720 varchar,
    value721 varchar,
    value722 varchar,
    value723 varchar,
    value724 varchar,
    value725 varchar,
    value726 varchar,
    value727 varchar,
    value728 varchar,
    value729 varchar,
    value730 varchar,
    value731 varchar,
    value732 varchar,
    value733 varchar,
    value734 varchar,
    value735 varchar,
    value736 varchar,
    value737 varchar,
    value738 varchar,
    value739 varchar,
    value740 varchar,
    value741 varchar,
    value742 varchar,
    value743 varchar,
    value744 varchar,
    value745 varchar,
    value746 varchar,
    value747 varchar,
    value748 varchar,
    value749 varchar,
    value750 varchar,
    value751 varchar,
    value752 varchar,
    value753 varchar,
    value754 varchar,
    value755 varchar,
    value756 varchar,
    value757 varchar,
    value758 varchar,
    value759 varchar,
    value760 varchar,
    value761 varchar,
    value762 varchar,
    value763 varchar,
    value764 varchar,
    value765 varchar,
    value766 varchar,
    value767 varchar,
    value768 varchar,
    value769 varchar,
    value770 varchar,
    value771 varchar,
    value772 varchar,
    value773 varchar,
    value774 varchar,
    value775 varchar,
    value776 varchar,
    value777 varchar,
    value778 varchar,
    value779 varchar,
    value780 varchar,
    value781 varchar,
    value782 varchar,
    value783 varchar,
    value784 varchar,
    value785 varchar,
    value786 varchar,
    value787 varchar,
    value788 varchar,
    value789 varchar,
    value790 varchar,
    value791 varchar,
    value792 varchar,
    value793 varchar,
    value794 varchar,
    value795 varchar,
    value796 varchar,
    value797 varchar,
    value798 varchar,
    value799 varchar,
    value800 varchar,
    CONSTRAINT "biz_public_employee_ext_8_pkey" PRIMARY KEY ( "id", "tenant_id" )
);

DROP TRIGGER IF EXISTS x_audit_changes ON biz_public_employee_ext_8;

CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON biz_public_employee_ext_8 FOR EACH ROW EXECUTE PROCEDURE public.f_change_detail('id','tenant_id','object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON biz_public_employee_ext_8;

CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON biz_public_employee_ext_8 FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();

CREATE TABLE IF NOT EXISTS biz_public_employee_ext_7(
    "id" VARCHAR (64)  NOT NULL,
    "tenant_id" VARCHAR (16)  NOT NULL,
    "created_by" VARCHAR (32) ,
    "object_describe_api_name" VARCHAR (100) ,
    "data_own_department" VARCHAR (32) ,
    "data_own_organization" VARCHAR (32) ,
    "out_data_own_department" varchar(64) ,
    "out_data_own_organization" varchar(64) ,
    "last_modified_by" VARCHAR(50),
    "out_tenant_id" VARCHAR(50),
    "out_owner" VARCHAR(50),
    "owner" VARCHAR (128) ,
    "dimension_d1" varchar[],
    "dimension_d2" varchar[],
    "dimension_d3" varchar[],
    "data_auth_id" int4,
    "out_data_auth_id" int4,
    "data_auth_code" varchar(64),
    "out_data_auth_code" varchar(64),
    "sys_modified_time" bigint ,
    value1 numeric,
    value2 numeric,
    value3 numeric,
    value4 numeric,
    value5 numeric,
    value6 numeric,
    value7 numeric,
    value8 numeric,
    value9 numeric,
    value10 numeric,
    value11 numeric,
    value12 numeric,
    value13 numeric,
    value14 numeric,
    value15 numeric,
    value16 numeric,
    value17 numeric,
    value18 numeric,
    value19 numeric,
    value20 numeric,
    value21 numeric,
    value22 numeric,
    value23 numeric,
    value24 numeric,
    value25 numeric,
    value26 numeric,
    value27 numeric,
    value28 numeric,
    value29 numeric,
    value30 numeric,
    value31 numeric,
    value32 numeric,
    value33 numeric,
    value34 numeric,
    value35 numeric,
    value36 numeric,
    value37 numeric,
    value38 numeric,
    value39 numeric,
    value40 numeric,
    value41 numeric,
    value42 numeric,
    value43 numeric,
    value44 numeric,
    value45 numeric,
    value46 numeric,
    value47 numeric,
    value48 numeric,
    value49 numeric,
    value50 numeric,
    value51 numeric,
    value52 numeric,
    value53 numeric,
    value54 numeric,
    value55 numeric,
    value56 numeric,
    value57 numeric,
    value58 numeric,
    value59 numeric,
    value60 numeric,
    value61 numeric,
    value62 numeric,
    value63 numeric,
    value64 numeric,
    value65 numeric,
    value66 numeric,
    value67 numeric,
    value68 numeric,
    value69 numeric,
    value70 numeric,
    value71 numeric,
    value72 numeric,
    value73 numeric,
    value74 numeric,
    value75 numeric,
    value76 numeric,
    value77 numeric,
    value78 numeric,
    value79 numeric,
    value80 numeric,
    value81 numeric,
    value82 numeric,
    value83 numeric,
    value84 numeric,
    value85 numeric,
    value86 numeric,
    value87 numeric,
    value88 numeric,
    value89 numeric,
    value90 numeric,
    value91 numeric,
    value92 numeric,
    value93 numeric,
    value94 numeric,
    value95 numeric,
    value96 numeric,
    value97 numeric,
    value98 numeric,
    value99 numeric,
    value100 numeric,
    value101 numeric,
    value102 numeric,
    value103 numeric,
    value104 numeric,
    value105 numeric,
    value106 numeric,
    value107 numeric,
    value108 numeric,
    value109 numeric,
    value110 numeric,
    value111 numeric,
    value112 numeric,
    value113 numeric,
    value114 numeric,
    value115 numeric,
    value116 numeric,
    value117 numeric,
    value118 numeric,
    value119 numeric,
    value120 numeric,
    value121 numeric,
    value122 numeric,
    value123 numeric,
    value124 numeric,
    value125 numeric,
    value126 numeric,
    value127 numeric,
    value128 numeric,
    value129 numeric,
    value130 numeric,
    value131 numeric,
    value132 numeric,
    value133 numeric,
    value134 numeric,
    value135 numeric,
    value136 numeric,
    value137 numeric,
    value138 numeric,
    value139 numeric,
    value140 numeric,
    value141 numeric,
    value142 numeric,
    value143 numeric,
    value144 numeric,
    value145 numeric,
    value146 numeric,
    value147 numeric,
    value148 numeric,
    value149 numeric,
    value150 numeric,
    value151 numeric,
    value152 numeric,
    value153 numeric,
    value154 numeric,
    value155 numeric,
    value156 numeric,
    value157 numeric,
    value158 numeric,
    value159 numeric,
    value160 numeric,
    value161 numeric,
    value162 numeric,
    value163 numeric,
    value164 numeric,
    value165 numeric,
    value166 numeric,
    value167 numeric,
    value168 numeric,
    value169 numeric,
    value170 numeric,
    value171 numeric,
    value172 numeric,
    value173 numeric,
    value174 numeric,
    value175 numeric,
    value176 numeric,
    value177 numeric,
    value178 numeric,
    value179 numeric,
    value180 numeric,
    value181 numeric,
    value182 numeric,
    value183 numeric,
    value184 numeric,
    value185 numeric,
    value186 numeric,
    value187 numeric,
    value188 numeric,
    value189 numeric,
    value190 numeric,
    value191 numeric,
    value192 numeric,
    value193 numeric,
    value194 numeric,
    value195 numeric,
    value196 numeric,
    value197 numeric,
    value198 numeric,
    value199 numeric,
    value200 numeric,
    value201 bool,
    value202 bool,
    value203 bool,
    value204 bool,
    value205 bool,
    value206 bool,
    value207 bool,
    value208 bool,
    value209 bool,
    value210 bool,
    value211 bool,
    value212 bool,
    value213 bool,
    value214 bool,
    value215 bool,
    value216 bool,
    value217 bool,
    value218 bool,
    value219 bool,
    value220 bool,
    value221 bool,
    value222 bool,
    value223 bool,
    value224 bool,
    value225 bool,
    value226 bool,
    value227 bool,
    value228 bool,
    value229 bool,
    value230 bool,
    value231 bool,
    value232 bool,
    value233 bool,
    value234 bool,
    value235 bool,
    value236 bool,
    value237 bool,
    value238 bool,
    value239 bool,
    value240 bool,
    value241 bool,
    value242 bool,
    value243 bool,
    value244 bool,
    value245 bool,
    value246 bool,
    value247 bool,
    value248 bool,
    value249 bool,
    value250 bool,
    value251 bool,
    value252 bool,
    value253 bool,
    value254 bool,
    value255 bool,
    value256 bool,
    value257 bool,
    value258 bool,
    value259 bool,
    value260 bool,
    value261 bool,
    value262 bool,
    value263 bool,
    value264 bool,
    value265 bool,
    value266 bool,
    value267 bool,
    value268 bool,
    value269 bool,
    value270 bool,
    value271 bool,
    value272 bool,
    value273 bool,
    value274 bool,
    value275 bool,
    value276 bool,
    value277 bool,
    value278 bool,
    value279 bool,
    value280 bool,
    value281 bool,
    value282 bool,
    value283 bool,
    value284 bool,
    value285 bool,
    value286 bool,
    value287 bool,
    value288 bool,
    value289 bool,
    value290 bool,
    value291 bool,
    value292 bool,
    value293 bool,
    value294 bool,
    value295 bool,
    value296 bool,
    value297 bool,
    value298 bool,
    value299 bool,
    value300 bool,
    value301 bool,
    value302 bool,
    value303 bool,
    value304 bool,
    value305 bool,
    value306 bool,
    value307 bool,
    value308 bool,
    value309 bool,
    value310 bool,
    value311 bool,
    value312 bool,
    value313 bool,
    value314 bool,
    value315 bool,
    value316 bool,
    value317 bool,
    value318 bool,
    value319 bool,
    value320 bool,
    value321 bool,
    value322 bool,
    value323 bool,
    value324 bool,
    value325 bool,
    value326 bool,
    value327 bool,
    value328 bool,
    value329 bool,
    value330 bool,
    value331 bool,
    value332 bool,
    value333 bool,
    value334 bool,
    value335 bool,
    value336 bool,
    value337 bool,
    value338 bool,
    value339 bool,
    value340 bool,
    value341 bool,
    value342 bool,
    value343 bool,
    value344 bool,
    value345 bool,
    value346 bool,
    value347 bool,
    value348 bool,
    value349 bool,
    value350 bool,
    value351 bool,
    value352 bool,
    value353 bool,
    value354 bool,
    value355 bool,
    value356 bool,
    value357 bool,
    value358 bool,
    value359 bool,
    value360 bool,
    value361 bool,
    value362 bool,
    value363 bool,
    value364 bool,
    value365 bool,
    value366 bool,
    value367 bool,
    value368 bool,
    value369 bool,
    value370 bool,
    value371 bool,
    value372 bool,
    value373 bool,
    value374 bool,
    value375 bool,
    value376 bool,
    value377 bool,
    value378 bool,
    value379 bool,
    value380 bool,
    value381 bool,
    value382 bool,
    value383 bool,
    value384 bool,
    value385 bool,
    value386 bool,
    value387 bool,
    value388 bool,
    value389 bool,
    value390 bool,
    value391 bool,
    value392 bool,
    value393 bool,
    value394 bool,
    value395 bool,
    value396 bool,
    value397 bool,
    value398 bool,
    value399 bool,
    value400 bool,
    value401 varchar[],
    value402 varchar[],
    value403 varchar[],
    value404 varchar[],
    value405 varchar[],
    value406 varchar[],
    value407 varchar[],
    value408 varchar[],
    value409 varchar[],
    value410 varchar[],
    value411 varchar[],
    value412 varchar[],
    value413 varchar[],
    value414 varchar[],
    value415 varchar[],
    value416 varchar[],
    value417 varchar[],
    value418 varchar[],
    value419 varchar[],
    value420 varchar[],
    value421 varchar[],
    value422 varchar[],
    value423 varchar[],
    value424 varchar[],
    value425 varchar[],
    value426 varchar[],
    value427 varchar[],
    value428 varchar[],
    value429 varchar[],
    value430 varchar[],
    value431 varchar[],
    value432 varchar[],
    value433 varchar[],
    value434 varchar[],
    value435 varchar[],
    value436 varchar[],
    value437 varchar[],
    value438 varchar[],
    value439 varchar[],
    value440 varchar[],
    value441 varchar[],
    value442 varchar[],
    value443 varchar[],
    value444 varchar[],
    value445 varchar[],
    value446 varchar[],
    value447 varchar[],
    value448 varchar[],
    value449 varchar[],
    value450 varchar[],
    value451 varchar[],
    value452 varchar[],
    value453 varchar[],
    value454 varchar[],
    value455 varchar[],
    value456 varchar[],
    value457 varchar[],
    value458 varchar[],
    value459 varchar[],
    value460 varchar[],
    value461 varchar[],
    value462 varchar[],
    value463 varchar[],
    value464 varchar[],
    value465 varchar[],
    value466 varchar[],
    value467 varchar[],
    value468 varchar[],
    value469 varchar[],
    value470 varchar[],
    value471 varchar[],
    value472 varchar[],
    value473 varchar[],
    value474 varchar[],
    value475 varchar[],
    value476 varchar[],
    value477 varchar[],
    value478 varchar[],
    value479 varchar[],
    value480 varchar[],
    value481 varchar[],
    value482 varchar[],
    value483 varchar[],
    value484 varchar[],
    value485 varchar[],
    value486 varchar[],
    value487 varchar[],
    value488 varchar[],
    value489 varchar[],
    value490 varchar[],
    value491 varchar[],
    value492 varchar[],
    value493 varchar[],
    value494 varchar[],
    value495 varchar[],
    value496 varchar[],
    value497 varchar[],
    value498 varchar[],
    value499 varchar[],
    value500 varchar[],
    value501 varchar[],
    value502 varchar[],
    value503 varchar[],
    value504 varchar[],
    value505 varchar[],
    value506 varchar[],
    value507 varchar[],
    value508 varchar[],
    value509 varchar[],
    value510 varchar[],
    value511 varchar[],
    value512 varchar[],
    value513 varchar[],
    value514 varchar[],
    value515 varchar[],
    value516 varchar[],
    value517 varchar[],
    value518 varchar[],
    value519 varchar[],
    value520 varchar[],
    value521 varchar[],
    value522 varchar[],
    value523 varchar[],
    value524 varchar[],
    value525 varchar[],
    value526 varchar[],
    value527 varchar[],
    value528 varchar[],
    value529 varchar[],
    value530 varchar[],
    value531 varchar[],
    value532 varchar[],
    value533 varchar[],
    value534 varchar[],
    value535 varchar[],
    value536 varchar[],
    value537 varchar[],
    value538 varchar[],
    value539 varchar[],
    value540 varchar[],
    value541 varchar[],
    value542 varchar[],
    value543 varchar[],
    value544 varchar[],
    value545 varchar[],
    value546 varchar[],
    value547 varchar[],
    value548 varchar[],
    value549 varchar[],
    value550 varchar[],
    value551 varchar[],
    value552 varchar[],
    value553 varchar[],
    value554 varchar[],
    value555 varchar[],
    value556 varchar[],
    value557 varchar[],
    value558 varchar[],
    value559 varchar[],
    value560 varchar[],
    value561 varchar[],
    value562 varchar[],
    value563 varchar[],
    value564 varchar[],
    value565 varchar[],
    value566 varchar[],
    value567 varchar[],
    value568 varchar[],
    value569 varchar[],
    value570 varchar[],
    value571 varchar[],
    value572 varchar[],
    value573 varchar[],
    value574 varchar[],
    value575 varchar[],
    value576 varchar[],
    value577 varchar[],
    value578 varchar[],
    value579 varchar[],
    value580 varchar[],
    value581 varchar[],
    value582 varchar[],
    value583 varchar[],
    value584 varchar[],
    value585 varchar[],
    value586 varchar[],
    value587 varchar[],
    value588 varchar[],
    value589 varchar[],
    value590 varchar[],
    value591 varchar[],
    value592 varchar[],
    value593 varchar[],
    value594 varchar[],
    value595 varchar[],
    value596 varchar[],
    value597 varchar[],
    value598 varchar[],
    value599 varchar[],
    value600 varchar[],
    value601 varchar,
    value602 varchar,
    value603 varchar,
    value604 varchar,
    value605 varchar,
    value606 varchar,
    value607 varchar,
    value608 varchar,
    value609 varchar,
    value610 varchar,
    value611 varchar,
    value612 varchar,
    value613 varchar,
    value614 varchar,
    value615 varchar,
    value616 varchar,
    value617 varchar,
    value618 varchar,
    value619 varchar,
    value620 varchar,
    value621 varchar,
    value622 varchar,
    value623 varchar,
    value624 varchar,
    value625 varchar,
    value626 varchar,
    value627 varchar,
    value628 varchar,
    value629 varchar,
    value630 varchar,
    value631 varchar,
    value632 varchar,
    value633 varchar,
    value634 varchar,
    value635 varchar,
    value636 varchar,
    value637 varchar,
    value638 varchar,
    value639 varchar,
    value640 varchar,
    value641 varchar,
    value642 varchar,
    value643 varchar,
    value644 varchar,
    value645 varchar,
    value646 varchar,
    value647 varchar,
    value648 varchar,
    value649 varchar,
    value650 varchar,
    value651 varchar,
    value652 varchar,
    value653 varchar,
    value654 varchar,
    value655 varchar,
    value656 varchar,
    value657 varchar,
    value658 varchar,
    value659 varchar,
    value660 varchar,
    value661 varchar,
    value662 varchar,
    value663 varchar,
    value664 varchar,
    value665 varchar,
    value666 varchar,
    value667 varchar,
    value668 varchar,
    value669 varchar,
    value670 varchar,
    value671 varchar,
    value672 varchar,
    value673 varchar,
    value674 varchar,
    value675 varchar,
    value676 varchar,
    value677 varchar,
    value678 varchar,
    value679 varchar,
    value680 varchar,
    value681 varchar,
    value682 varchar,
    value683 varchar,
    value684 varchar,
    value685 varchar,
    value686 varchar,
    value687 varchar,
    value688 varchar,
    value689 varchar,
    value690 varchar,
    value691 varchar,
    value692 varchar,
    value693 varchar,
    value694 varchar,
    value695 varchar,
    value696 varchar,
    value697 varchar,
    value698 varchar,
    value699 varchar,
    value700 varchar,
    value701 varchar,
    value702 varchar,
    value703 varchar,
    value704 varchar,
    value705 varchar,
    value706 varchar,
    value707 varchar,
    value708 varchar,
    value709 varchar,
    value710 varchar,
    value711 varchar,
    value712 varchar,
    value713 varchar,
    value714 varchar,
    value715 varchar,
    value716 varchar,
    value717 varchar,
    value718 varchar,
    value719 varchar,
    value720 varchar,
    value721 varchar,
    value722 varchar,
    value723 varchar,
    value724 varchar,
    value725 varchar,
    value726 varchar,
    value727 varchar,
    value728 varchar,
    value729 varchar,
    value730 varchar,
    value731 varchar,
    value732 varchar,
    value733 varchar,
    value734 varchar,
    value735 varchar,
    value736 varchar,
    value737 varchar,
    value738 varchar,
    value739 varchar,
    value740 varchar,
    value741 varchar,
    value742 varchar,
    value743 varchar,
    value744 varchar,
    value745 varchar,
    value746 varchar,
    value747 varchar,
    value748 varchar,
    value749 varchar,
    value750 varchar,
    value751 varchar,
    value752 varchar,
    value753 varchar,
    value754 varchar,
    value755 varchar,
    value756 varchar,
    value757 varchar,
    value758 varchar,
    value759 varchar,
    value760 varchar,
    value761 varchar,
    value762 varchar,
    value763 varchar,
    value764 varchar,
    value765 varchar,
    value766 varchar,
    value767 varchar,
    value768 varchar,
    value769 varchar,
    value770 varchar,
    value771 varchar,
    value772 varchar,
    value773 varchar,
    value774 varchar,
    value775 varchar,
    value776 varchar,
    value777 varchar,
    value778 varchar,
    value779 varchar,
    value780 varchar,
    value781 varchar,
    value782 varchar,
    value783 varchar,
    value784 varchar,
    value785 varchar,
    value786 varchar,
    value787 varchar,
    value788 varchar,
    value789 varchar,
    value790 varchar,
    value791 varchar,
    value792 varchar,
    value793 varchar,
    value794 varchar,
    value795 varchar,
    value796 varchar,
    value797 varchar,
    value798 varchar,
    value799 varchar,
    value800 varchar,
    CONSTRAINT "biz_public_employee_ext_7_pkey" PRIMARY KEY ( "id", "tenant_id" )
);

DROP TRIGGER IF EXISTS x_audit_changes ON biz_public_employee_ext_7;

CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON biz_public_employee_ext_7 FOR EACH ROW EXECUTE PROCEDURE public.f_change_detail('id','tenant_id','object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON biz_public_employee_ext_7;

CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON biz_public_employee_ext_7 FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();

CREATE TABLE IF NOT EXISTS biz_public_employee_ext_6(
    "id" VARCHAR (64)  NOT NULL,
    "tenant_id" VARCHAR (16)  NOT NULL,
    "created_by" VARCHAR (32) ,
    "object_describe_api_name" VARCHAR (100) ,
    "data_own_department" VARCHAR (32) ,
    "data_own_organization" VARCHAR (32) ,
    "out_data_own_department" varchar(64) ,
    "out_data_own_organization" varchar(64) ,
    "last_modified_by" VARCHAR(50),
    "out_tenant_id" VARCHAR(50),
    "out_owner" VARCHAR(50),
    "owner" VARCHAR (128) ,
    "dimension_d1" varchar[],
    "dimension_d2" varchar[],
    "dimension_d3" varchar[],
    "data_auth_id" int4,
    "out_data_auth_id" int4,
    "data_auth_code" varchar(64),
    "out_data_auth_code" varchar(64),
    "sys_modified_time" bigint ,
    value1 numeric,
    value2 numeric,
    value3 numeric,
    value4 numeric,
    value5 numeric,
    value6 numeric,
    value7 numeric,
    value8 numeric,
    value9 numeric,
    value10 numeric,
    value11 numeric,
    value12 numeric,
    value13 numeric,
    value14 numeric,
    value15 numeric,
    value16 numeric,
    value17 numeric,
    value18 numeric,
    value19 numeric,
    value20 numeric,
    value21 numeric,
    value22 numeric,
    value23 numeric,
    value24 numeric,
    value25 numeric,
    value26 numeric,
    value27 numeric,
    value28 numeric,
    value29 numeric,
    value30 numeric,
    value31 numeric,
    value32 numeric,
    value33 numeric,
    value34 numeric,
    value35 numeric,
    value36 numeric,
    value37 numeric,
    value38 numeric,
    value39 numeric,
    value40 numeric,
    value41 numeric,
    value42 numeric,
    value43 numeric,
    value44 numeric,
    value45 numeric,
    value46 numeric,
    value47 numeric,
    value48 numeric,
    value49 numeric,
    value50 numeric,
    value51 numeric,
    value52 numeric,
    value53 numeric,
    value54 numeric,
    value55 numeric,
    value56 numeric,
    value57 numeric,
    value58 numeric,
    value59 numeric,
    value60 numeric,
    value61 numeric,
    value62 numeric,
    value63 numeric,
    value64 numeric,
    value65 numeric,
    value66 numeric,
    value67 numeric,
    value68 numeric,
    value69 numeric,
    value70 numeric,
    value71 numeric,
    value72 numeric,
    value73 numeric,
    value74 numeric,
    value75 numeric,
    value76 numeric,
    value77 numeric,
    value78 numeric,
    value79 numeric,
    value80 numeric,
    value81 numeric,
    value82 numeric,
    value83 numeric,
    value84 numeric,
    value85 numeric,
    value86 numeric,
    value87 numeric,
    value88 numeric,
    value89 numeric,
    value90 numeric,
    value91 numeric,
    value92 numeric,
    value93 numeric,
    value94 numeric,
    value95 numeric,
    value96 numeric,
    value97 numeric,
    value98 numeric,
    value99 numeric,
    value100 numeric,
    value101 numeric,
    value102 numeric,
    value103 numeric,
    value104 numeric,
    value105 numeric,
    value106 numeric,
    value107 numeric,
    value108 numeric,
    value109 numeric,
    value110 numeric,
    value111 numeric,
    value112 numeric,
    value113 numeric,
    value114 numeric,
    value115 numeric,
    value116 numeric,
    value117 numeric,
    value118 numeric,
    value119 numeric,
    value120 numeric,
    value121 numeric,
    value122 numeric,
    value123 numeric,
    value124 numeric,
    value125 numeric,
    value126 numeric,
    value127 numeric,
    value128 numeric,
    value129 numeric,
    value130 numeric,
    value131 numeric,
    value132 numeric,
    value133 numeric,
    value134 numeric,
    value135 numeric,
    value136 numeric,
    value137 numeric,
    value138 numeric,
    value139 numeric,
    value140 numeric,
    value141 numeric,
    value142 numeric,
    value143 numeric,
    value144 numeric,
    value145 numeric,
    value146 numeric,
    value147 numeric,
    value148 numeric,
    value149 numeric,
    value150 numeric,
    value151 numeric,
    value152 numeric,
    value153 numeric,
    value154 numeric,
    value155 numeric,
    value156 numeric,
    value157 numeric,
    value158 numeric,
    value159 numeric,
    value160 numeric,
    value161 numeric,
    value162 numeric,
    value163 numeric,
    value164 numeric,
    value165 numeric,
    value166 numeric,
    value167 numeric,
    value168 numeric,
    value169 numeric,
    value170 numeric,
    value171 numeric,
    value172 numeric,
    value173 numeric,
    value174 numeric,
    value175 numeric,
    value176 numeric,
    value177 numeric,
    value178 numeric,
    value179 numeric,
    value180 numeric,
    value181 numeric,
    value182 numeric,
    value183 numeric,
    value184 numeric,
    value185 numeric,
    value186 numeric,
    value187 numeric,
    value188 numeric,
    value189 numeric,
    value190 numeric,
    value191 numeric,
    value192 numeric,
    value193 numeric,
    value194 numeric,
    value195 numeric,
    value196 numeric,
    value197 numeric,
    value198 numeric,
    value199 numeric,
    value200 numeric,
    value201 bool,
    value202 bool,
    value203 bool,
    value204 bool,
    value205 bool,
    value206 bool,
    value207 bool,
    value208 bool,
    value209 bool,
    value210 bool,
    value211 bool,
    value212 bool,
    value213 bool,
    value214 bool,
    value215 bool,
    value216 bool,
    value217 bool,
    value218 bool,
    value219 bool,
    value220 bool,
    value221 bool,
    value222 bool,
    value223 bool,
    value224 bool,
    value225 bool,
    value226 bool,
    value227 bool,
    value228 bool,
    value229 bool,
    value230 bool,
    value231 bool,
    value232 bool,
    value233 bool,
    value234 bool,
    value235 bool,
    value236 bool,
    value237 bool,
    value238 bool,
    value239 bool,
    value240 bool,
    value241 bool,
    value242 bool,
    value243 bool,
    value244 bool,
    value245 bool,
    value246 bool,
    value247 bool,
    value248 bool,
    value249 bool,
    value250 bool,
    value251 bool,
    value252 bool,
    value253 bool,
    value254 bool,
    value255 bool,
    value256 bool,
    value257 bool,
    value258 bool,
    value259 bool,
    value260 bool,
    value261 bool,
    value262 bool,
    value263 bool,
    value264 bool,
    value265 bool,
    value266 bool,
    value267 bool,
    value268 bool,
    value269 bool,
    value270 bool,
    value271 bool,
    value272 bool,
    value273 bool,
    value274 bool,
    value275 bool,
    value276 bool,
    value277 bool,
    value278 bool,
    value279 bool,
    value280 bool,
    value281 bool,
    value282 bool,
    value283 bool,
    value284 bool,
    value285 bool,
    value286 bool,
    value287 bool,
    value288 bool,
    value289 bool,
    value290 bool,
    value291 bool,
    value292 bool,
    value293 bool,
    value294 bool,
    value295 bool,
    value296 bool,
    value297 bool,
    value298 bool,
    value299 bool,
    value300 bool,
    value301 bool,
    value302 bool,
    value303 bool,
    value304 bool,
    value305 bool,
    value306 bool,
    value307 bool,
    value308 bool,
    value309 bool,
    value310 bool,
    value311 bool,
    value312 bool,
    value313 bool,
    value314 bool,
    value315 bool,
    value316 bool,
    value317 bool,
    value318 bool,
    value319 bool,
    value320 bool,
    value321 bool,
    value322 bool,
    value323 bool,
    value324 bool,
    value325 bool,
    value326 bool,
    value327 bool,
    value328 bool,
    value329 bool,
    value330 bool,
    value331 bool,
    value332 bool,
    value333 bool,
    value334 bool,
    value335 bool,
    value336 bool,
    value337 bool,
    value338 bool,
    value339 bool,
    value340 bool,
    value341 bool,
    value342 bool,
    value343 bool,
    value344 bool,
    value345 bool,
    value346 bool,
    value347 bool,
    value348 bool,
    value349 bool,
    value350 bool,
    value351 bool,
    value352 bool,
    value353 bool,
    value354 bool,
    value355 bool,
    value356 bool,
    value357 bool,
    value358 bool,
    value359 bool,
    value360 bool,
    value361 bool,
    value362 bool,
    value363 bool,
    value364 bool,
    value365 bool,
    value366 bool,
    value367 bool,
    value368 bool,
    value369 bool,
    value370 bool,
    value371 bool,
    value372 bool,
    value373 bool,
    value374 bool,
    value375 bool,
    value376 bool,
    value377 bool,
    value378 bool,
    value379 bool,
    value380 bool,
    value381 bool,
    value382 bool,
    value383 bool,
    value384 bool,
    value385 bool,
    value386 bool,
    value387 bool,
    value388 bool,
    value389 bool,
    value390 bool,
    value391 bool,
    value392 bool,
    value393 bool,
    value394 bool,
    value395 bool,
    value396 bool,
    value397 bool,
    value398 bool,
    value399 bool,
    value400 bool,
    value401 varchar[],
    value402 varchar[],
    value403 varchar[],
    value404 varchar[],
    value405 varchar[],
    value406 varchar[],
    value407 varchar[],
    value408 varchar[],
    value409 varchar[],
    value410 varchar[],
    value411 varchar[],
    value412 varchar[],
    value413 varchar[],
    value414 varchar[],
    value415 varchar[],
    value416 varchar[],
    value417 varchar[],
    value418 varchar[],
    value419 varchar[],
    value420 varchar[],
    value421 varchar[],
    value422 varchar[],
    value423 varchar[],
    value424 varchar[],
    value425 varchar[],
    value426 varchar[],
    value427 varchar[],
    value428 varchar[],
    value429 varchar[],
    value430 varchar[],
    value431 varchar[],
    value432 varchar[],
    value433 varchar[],
    value434 varchar[],
    value435 varchar[],
    value436 varchar[],
    value437 varchar[],
    value438 varchar[],
    value439 varchar[],
    value440 varchar[],
    value441 varchar[],
    value442 varchar[],
    value443 varchar[],
    value444 varchar[],
    value445 varchar[],
    value446 varchar[],
    value447 varchar[],
    value448 varchar[],
    value449 varchar[],
    value450 varchar[],
    value451 varchar[],
    value452 varchar[],
    value453 varchar[],
    value454 varchar[],
    value455 varchar[],
    value456 varchar[],
    value457 varchar[],
    value458 varchar[],
    value459 varchar[],
    value460 varchar[],
    value461 varchar[],
    value462 varchar[],
    value463 varchar[],
    value464 varchar[],
    value465 varchar[],
    value466 varchar[],
    value467 varchar[],
    value468 varchar[],
    value469 varchar[],
    value470 varchar[],
    value471 varchar[],
    value472 varchar[],
    value473 varchar[],
    value474 varchar[],
    value475 varchar[],
    value476 varchar[],
    value477 varchar[],
    value478 varchar[],
    value479 varchar[],
    value480 varchar[],
    value481 varchar[],
    value482 varchar[],
    value483 varchar[],
    value484 varchar[],
    value485 varchar[],
    value486 varchar[],
    value487 varchar[],
    value488 varchar[],
    value489 varchar[],
    value490 varchar[],
    value491 varchar[],
    value492 varchar[],
    value493 varchar[],
    value494 varchar[],
    value495 varchar[],
    value496 varchar[],
    value497 varchar[],
    value498 varchar[],
    value499 varchar[],
    value500 varchar[],
    value501 varchar[],
    value502 varchar[],
    value503 varchar[],
    value504 varchar[],
    value505 varchar[],
    value506 varchar[],
    value507 varchar[],
    value508 varchar[],
    value509 varchar[],
    value510 varchar[],
    value511 varchar[],
    value512 varchar[],
    value513 varchar[],
    value514 varchar[],
    value515 varchar[],
    value516 varchar[],
    value517 varchar[],
    value518 varchar[],
    value519 varchar[],
    value520 varchar[],
    value521 varchar[],
    value522 varchar[],
    value523 varchar[],
    value524 varchar[],
    value525 varchar[],
    value526 varchar[],
    value527 varchar[],
    value528 varchar[],
    value529 varchar[],
    value530 varchar[],
    value531 varchar[],
    value532 varchar[],
    value533 varchar[],
    value534 varchar[],
    value535 varchar[],
    value536 varchar[],
    value537 varchar[],
    value538 varchar[],
    value539 varchar[],
    value540 varchar[],
    value541 varchar[],
    value542 varchar[],
    value543 varchar[],
    value544 varchar[],
    value545 varchar[],
    value546 varchar[],
    value547 varchar[],
    value548 varchar[],
    value549 varchar[],
    value550 varchar[],
    value551 varchar[],
    value552 varchar[],
    value553 varchar[],
    value554 varchar[],
    value555 varchar[],
    value556 varchar[],
    value557 varchar[],
    value558 varchar[],
    value559 varchar[],
    value560 varchar[],
    value561 varchar[],
    value562 varchar[],
    value563 varchar[],
    value564 varchar[],
    value565 varchar[],
    value566 varchar[],
    value567 varchar[],
    value568 varchar[],
    value569 varchar[],
    value570 varchar[],
    value571 varchar[],
    value572 varchar[],
    value573 varchar[],
    value574 varchar[],
    value575 varchar[],
    value576 varchar[],
    value577 varchar[],
    value578 varchar[],
    value579 varchar[],
    value580 varchar[],
    value581 varchar[],
    value582 varchar[],
    value583 varchar[],
    value584 varchar[],
    value585 varchar[],
    value586 varchar[],
    value587 varchar[],
    value588 varchar[],
    value589 varchar[],
    value590 varchar[],
    value591 varchar[],
    value592 varchar[],
    value593 varchar[],
    value594 varchar[],
    value595 varchar[],
    value596 varchar[],
    value597 varchar[],
    value598 varchar[],
    value599 varchar[],
    value600 varchar[],
    value601 varchar,
    value602 varchar,
    value603 varchar,
    value604 varchar,
    value605 varchar,
    value606 varchar,
    value607 varchar,
    value608 varchar,
    value609 varchar,
    value610 varchar,
    value611 varchar,
    value612 varchar,
    value613 varchar,
    value614 varchar,
    value615 varchar,
    value616 varchar,
    value617 varchar,
    value618 varchar,
    value619 varchar,
    value620 varchar,
    value621 varchar,
    value622 varchar,
    value623 varchar,
    value624 varchar,
    value625 varchar,
    value626 varchar,
    value627 varchar,
    value628 varchar,
    value629 varchar,
    value630 varchar,
    value631 varchar,
    value632 varchar,
    value633 varchar,
    value634 varchar,
    value635 varchar,
    value636 varchar,
    value637 varchar,
    value638 varchar,
    value639 varchar,
    value640 varchar,
    value641 varchar,
    value642 varchar,
    value643 varchar,
    value644 varchar,
    value645 varchar,
    value646 varchar,
    value647 varchar,
    value648 varchar,
    value649 varchar,
    value650 varchar,
    value651 varchar,
    value652 varchar,
    value653 varchar,
    value654 varchar,
    value655 varchar,
    value656 varchar,
    value657 varchar,
    value658 varchar,
    value659 varchar,
    value660 varchar,
    value661 varchar,
    value662 varchar,
    value663 varchar,
    value664 varchar,
    value665 varchar,
    value666 varchar,
    value667 varchar,
    value668 varchar,
    value669 varchar,
    value670 varchar,
    value671 varchar,
    value672 varchar,
    value673 varchar,
    value674 varchar,
    value675 varchar,
    value676 varchar,
    value677 varchar,
    value678 varchar,
    value679 varchar,
    value680 varchar,
    value681 varchar,
    value682 varchar,
    value683 varchar,
    value684 varchar,
    value685 varchar,
    value686 varchar,
    value687 varchar,
    value688 varchar,
    value689 varchar,
    value690 varchar,
    value691 varchar,
    value692 varchar,
    value693 varchar,
    value694 varchar,
    value695 varchar,
    value696 varchar,
    value697 varchar,
    value698 varchar,
    value699 varchar,
    value700 varchar,
    value701 varchar,
    value702 varchar,
    value703 varchar,
    value704 varchar,
    value705 varchar,
    value706 varchar,
    value707 varchar,
    value708 varchar,
    value709 varchar,
    value710 varchar,
    value711 varchar,
    value712 varchar,
    value713 varchar,
    value714 varchar,
    value715 varchar,
    value716 varchar,
    value717 varchar,
    value718 varchar,
    value719 varchar,
    value720 varchar,
    value721 varchar,
    value722 varchar,
    value723 varchar,
    value724 varchar,
    value725 varchar,
    value726 varchar,
    value727 varchar,
    value728 varchar,
    value729 varchar,
    value730 varchar,
    value731 varchar,
    value732 varchar,
    value733 varchar,
    value734 varchar,
    value735 varchar,
    value736 varchar,
    value737 varchar,
    value738 varchar,
    value739 varchar,
    value740 varchar,
    value741 varchar,
    value742 varchar,
    value743 varchar,
    value744 varchar,
    value745 varchar,
    value746 varchar,
    value747 varchar,
    value748 varchar,
    value749 varchar,
    value750 varchar,
    value751 varchar,
    value752 varchar,
    value753 varchar,
    value754 varchar,
    value755 varchar,
    value756 varchar,
    value757 varchar,
    value758 varchar,
    value759 varchar,
    value760 varchar,
    value761 varchar,
    value762 varchar,
    value763 varchar,
    value764 varchar,
    value765 varchar,
    value766 varchar,
    value767 varchar,
    value768 varchar,
    value769 varchar,
    value770 varchar,
    value771 varchar,
    value772 varchar,
    value773 varchar,
    value774 varchar,
    value775 varchar,
    value776 varchar,
    value777 varchar,
    value778 varchar,
    value779 varchar,
    value780 varchar,
    value781 varchar,
    value782 varchar,
    value783 varchar,
    value784 varchar,
    value785 varchar,
    value786 varchar,
    value787 varchar,
    value788 varchar,
    value789 varchar,
    value790 varchar,
    value791 varchar,
    value792 varchar,
    value793 varchar,
    value794 varchar,
    value795 varchar,
    value796 varchar,
    value797 varchar,
    value798 varchar,
    value799 varchar,
    value800 varchar,
    CONSTRAINT "biz_public_employee_ext_6_pkey" PRIMARY KEY ( "id", "tenant_id" )
);

DROP TRIGGER IF EXISTS x_audit_changes ON biz_public_employee_ext_6;

CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON biz_public_employee_ext_6 FOR EACH ROW EXECUTE PROCEDURE public.f_change_detail('id','tenant_id','object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON biz_public_employee_ext_6;

CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON biz_public_employee_ext_6 FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();

CREATE TABLE IF NOT EXISTS biz_public_employee_ext_5(
    "id" VARCHAR (64)  NOT NULL,
    "tenant_id" VARCHAR (16)  NOT NULL,
    "created_by" VARCHAR (32) ,
    "object_describe_api_name" VARCHAR (100) ,
    "data_own_department" VARCHAR (32) ,
    "data_own_organization" VARCHAR (32) ,
    "out_data_own_department" varchar(64) ,
    "out_data_own_organization" varchar(64) ,
    "last_modified_by" VARCHAR(50),
    "out_tenant_id" VARCHAR(50),
    "out_owner" VARCHAR(50),
    "owner" VARCHAR (128) ,
    "dimension_d1" varchar[],
    "dimension_d2" varchar[],
    "dimension_d3" varchar[],
    "data_auth_id" int4,
    "out_data_auth_id" int4,
    "data_auth_code" varchar(64),
    "out_data_auth_code" varchar(64),
    "sys_modified_time" bigint ,
    value1 numeric,
    value2 numeric,
    value3 numeric,
    value4 numeric,
    value5 numeric,
    value6 numeric,
    value7 numeric,
    value8 numeric,
    value9 numeric,
    value10 numeric,
    value11 numeric,
    value12 numeric,
    value13 numeric,
    value14 numeric,
    value15 numeric,
    value16 numeric,
    value17 numeric,
    value18 numeric,
    value19 numeric,
    value20 numeric,
    value21 numeric,
    value22 numeric,
    value23 numeric,
    value24 numeric,
    value25 numeric,
    value26 numeric,
    value27 numeric,
    value28 numeric,
    value29 numeric,
    value30 numeric,
    value31 numeric,
    value32 numeric,
    value33 numeric,
    value34 numeric,
    value35 numeric,
    value36 numeric,
    value37 numeric,
    value38 numeric,
    value39 numeric,
    value40 numeric,
    value41 numeric,
    value42 numeric,
    value43 numeric,
    value44 numeric,
    value45 numeric,
    value46 numeric,
    value47 numeric,
    value48 numeric,
    value49 numeric,
    value50 numeric,
    value51 numeric,
    value52 numeric,
    value53 numeric,
    value54 numeric,
    value55 numeric,
    value56 numeric,
    value57 numeric,
    value58 numeric,
    value59 numeric,
    value60 numeric,
    value61 numeric,
    value62 numeric,
    value63 numeric,
    value64 numeric,
    value65 numeric,
    value66 numeric,
    value67 numeric,
    value68 numeric,
    value69 numeric,
    value70 numeric,
    value71 numeric,
    value72 numeric,
    value73 numeric,
    value74 numeric,
    value75 numeric,
    value76 numeric,
    value77 numeric,
    value78 numeric,
    value79 numeric,
    value80 numeric,
    value81 numeric,
    value82 numeric,
    value83 numeric,
    value84 numeric,
    value85 numeric,
    value86 numeric,
    value87 numeric,
    value88 numeric,
    value89 numeric,
    value90 numeric,
    value91 numeric,
    value92 numeric,
    value93 numeric,
    value94 numeric,
    value95 numeric,
    value96 numeric,
    value97 numeric,
    value98 numeric,
    value99 numeric,
    value100 numeric,
    value101 numeric,
    value102 numeric,
    value103 numeric,
    value104 numeric,
    value105 numeric,
    value106 numeric,
    value107 numeric,
    value108 numeric,
    value109 numeric,
    value110 numeric,
    value111 numeric,
    value112 numeric,
    value113 numeric,
    value114 numeric,
    value115 numeric,
    value116 numeric,
    value117 numeric,
    value118 numeric,
    value119 numeric,
    value120 numeric,
    value121 numeric,
    value122 numeric,
    value123 numeric,
    value124 numeric,
    value125 numeric,
    value126 numeric,
    value127 numeric,
    value128 numeric,
    value129 numeric,
    value130 numeric,
    value131 numeric,
    value132 numeric,
    value133 numeric,
    value134 numeric,
    value135 numeric,
    value136 numeric,
    value137 numeric,
    value138 numeric,
    value139 numeric,
    value140 numeric,
    value141 numeric,
    value142 numeric,
    value143 numeric,
    value144 numeric,
    value145 numeric,
    value146 numeric,
    value147 numeric,
    value148 numeric,
    value149 numeric,
    value150 numeric,
    value151 numeric,
    value152 numeric,
    value153 numeric,
    value154 numeric,
    value155 numeric,
    value156 numeric,
    value157 numeric,
    value158 numeric,
    value159 numeric,
    value160 numeric,
    value161 numeric,
    value162 numeric,
    value163 numeric,
    value164 numeric,
    value165 numeric,
    value166 numeric,
    value167 numeric,
    value168 numeric,
    value169 numeric,
    value170 numeric,
    value171 numeric,
    value172 numeric,
    value173 numeric,
    value174 numeric,
    value175 numeric,
    value176 numeric,
    value177 numeric,
    value178 numeric,
    value179 numeric,
    value180 numeric,
    value181 numeric,
    value182 numeric,
    value183 numeric,
    value184 numeric,
    value185 numeric,
    value186 numeric,
    value187 numeric,
    value188 numeric,
    value189 numeric,
    value190 numeric,
    value191 numeric,
    value192 numeric,
    value193 numeric,
    value194 numeric,
    value195 numeric,
    value196 numeric,
    value197 numeric,
    value198 numeric,
    value199 numeric,
    value200 numeric,
    value201 bool,
    value202 bool,
    value203 bool,
    value204 bool,
    value205 bool,
    value206 bool,
    value207 bool,
    value208 bool,
    value209 bool,
    value210 bool,
    value211 bool,
    value212 bool,
    value213 bool,
    value214 bool,
    value215 bool,
    value216 bool,
    value217 bool,
    value218 bool,
    value219 bool,
    value220 bool,
    value221 bool,
    value222 bool,
    value223 bool,
    value224 bool,
    value225 bool,
    value226 bool,
    value227 bool,
    value228 bool,
    value229 bool,
    value230 bool,
    value231 bool,
    value232 bool,
    value233 bool,
    value234 bool,
    value235 bool,
    value236 bool,
    value237 bool,
    value238 bool,
    value239 bool,
    value240 bool,
    value241 bool,
    value242 bool,
    value243 bool,
    value244 bool,
    value245 bool,
    value246 bool,
    value247 bool,
    value248 bool,
    value249 bool,
    value250 bool,
    value251 bool,
    value252 bool,
    value253 bool,
    value254 bool,
    value255 bool,
    value256 bool,
    value257 bool,
    value258 bool,
    value259 bool,
    value260 bool,
    value261 bool,
    value262 bool,
    value263 bool,
    value264 bool,
    value265 bool,
    value266 bool,
    value267 bool,
    value268 bool,
    value269 bool,
    value270 bool,
    value271 bool,
    value272 bool,
    value273 bool,
    value274 bool,
    value275 bool,
    value276 bool,
    value277 bool,
    value278 bool,
    value279 bool,
    value280 bool,
    value281 bool,
    value282 bool,
    value283 bool,
    value284 bool,
    value285 bool,
    value286 bool,
    value287 bool,
    value288 bool,
    value289 bool,
    value290 bool,
    value291 bool,
    value292 bool,
    value293 bool,
    value294 bool,
    value295 bool,
    value296 bool,
    value297 bool,
    value298 bool,
    value299 bool,
    value300 bool,
    value301 bool,
    value302 bool,
    value303 bool,
    value304 bool,
    value305 bool,
    value306 bool,
    value307 bool,
    value308 bool,
    value309 bool,
    value310 bool,
    value311 bool,
    value312 bool,
    value313 bool,
    value314 bool,
    value315 bool,
    value316 bool,
    value317 bool,
    value318 bool,
    value319 bool,
    value320 bool,
    value321 bool,
    value322 bool,
    value323 bool,
    value324 bool,
    value325 bool,
    value326 bool,
    value327 bool,
    value328 bool,
    value329 bool,
    value330 bool,
    value331 bool,
    value332 bool,
    value333 bool,
    value334 bool,
    value335 bool,
    value336 bool,
    value337 bool,
    value338 bool,
    value339 bool,
    value340 bool,
    value341 bool,
    value342 bool,
    value343 bool,
    value344 bool,
    value345 bool,
    value346 bool,
    value347 bool,
    value348 bool,
    value349 bool,
    value350 bool,
    value351 bool,
    value352 bool,
    value353 bool,
    value354 bool,
    value355 bool,
    value356 bool,
    value357 bool,
    value358 bool,
    value359 bool,
    value360 bool,
    value361 bool,
    value362 bool,
    value363 bool,
    value364 bool,
    value365 bool,
    value366 bool,
    value367 bool,
    value368 bool,
    value369 bool,
    value370 bool,
    value371 bool,
    value372 bool,
    value373 bool,
    value374 bool,
    value375 bool,
    value376 bool,
    value377 bool,
    value378 bool,
    value379 bool,
    value380 bool,
    value381 bool,
    value382 bool,
    value383 bool,
    value384 bool,
    value385 bool,
    value386 bool,
    value387 bool,
    value388 bool,
    value389 bool,
    value390 bool,
    value391 bool,
    value392 bool,
    value393 bool,
    value394 bool,
    value395 bool,
    value396 bool,
    value397 bool,
    value398 bool,
    value399 bool,
    value400 bool,
    value401 varchar[],
    value402 varchar[],
    value403 varchar[],
    value404 varchar[],
    value405 varchar[],
    value406 varchar[],
    value407 varchar[],
    value408 varchar[],
    value409 varchar[],
    value410 varchar[],
    value411 varchar[],
    value412 varchar[],
    value413 varchar[],
    value414 varchar[],
    value415 varchar[],
    value416 varchar[],
    value417 varchar[],
    value418 varchar[],
    value419 varchar[],
    value420 varchar[],
    value421 varchar[],
    value422 varchar[],
    value423 varchar[],
    value424 varchar[],
    value425 varchar[],
    value426 varchar[],
    value427 varchar[],
    value428 varchar[],
    value429 varchar[],
    value430 varchar[],
    value431 varchar[],
    value432 varchar[],
    value433 varchar[],
    value434 varchar[],
    value435 varchar[],
    value436 varchar[],
    value437 varchar[],
    value438 varchar[],
    value439 varchar[],
    value440 varchar[],
    value441 varchar[],
    value442 varchar[],
    value443 varchar[],
    value444 varchar[],
    value445 varchar[],
    value446 varchar[],
    value447 varchar[],
    value448 varchar[],
    value449 varchar[],
    value450 varchar[],
    value451 varchar[],
    value452 varchar[],
    value453 varchar[],
    value454 varchar[],
    value455 varchar[],
    value456 varchar[],
    value457 varchar[],
    value458 varchar[],
    value459 varchar[],
    value460 varchar[],
    value461 varchar[],
    value462 varchar[],
    value463 varchar[],
    value464 varchar[],
    value465 varchar[],
    value466 varchar[],
    value467 varchar[],
    value468 varchar[],
    value469 varchar[],
    value470 varchar[],
    value471 varchar[],
    value472 varchar[],
    value473 varchar[],
    value474 varchar[],
    value475 varchar[],
    value476 varchar[],
    value477 varchar[],
    value478 varchar[],
    value479 varchar[],
    value480 varchar[],
    value481 varchar[],
    value482 varchar[],
    value483 varchar[],
    value484 varchar[],
    value485 varchar[],
    value486 varchar[],
    value487 varchar[],
    value488 varchar[],
    value489 varchar[],
    value490 varchar[],
    value491 varchar[],
    value492 varchar[],
    value493 varchar[],
    value494 varchar[],
    value495 varchar[],
    value496 varchar[],
    value497 varchar[],
    value498 varchar[],
    value499 varchar[],
    value500 varchar[],
    value501 varchar[],
    value502 varchar[],
    value503 varchar[],
    value504 varchar[],
    value505 varchar[],
    value506 varchar[],
    value507 varchar[],
    value508 varchar[],
    value509 varchar[],
    value510 varchar[],
    value511 varchar[],
    value512 varchar[],
    value513 varchar[],
    value514 varchar[],
    value515 varchar[],
    value516 varchar[],
    value517 varchar[],
    value518 varchar[],
    value519 varchar[],
    value520 varchar[],
    value521 varchar[],
    value522 varchar[],
    value523 varchar[],
    value524 varchar[],
    value525 varchar[],
    value526 varchar[],
    value527 varchar[],
    value528 varchar[],
    value529 varchar[],
    value530 varchar[],
    value531 varchar[],
    value532 varchar[],
    value533 varchar[],
    value534 varchar[],
    value535 varchar[],
    value536 varchar[],
    value537 varchar[],
    value538 varchar[],
    value539 varchar[],
    value540 varchar[],
    value541 varchar[],
    value542 varchar[],
    value543 varchar[],
    value544 varchar[],
    value545 varchar[],
    value546 varchar[],
    value547 varchar[],
    value548 varchar[],
    value549 varchar[],
    value550 varchar[],
    value551 varchar[],
    value552 varchar[],
    value553 varchar[],
    value554 varchar[],
    value555 varchar[],
    value556 varchar[],
    value557 varchar[],
    value558 varchar[],
    value559 varchar[],
    value560 varchar[],
    value561 varchar[],
    value562 varchar[],
    value563 varchar[],
    value564 varchar[],
    value565 varchar[],
    value566 varchar[],
    value567 varchar[],
    value568 varchar[],
    value569 varchar[],
    value570 varchar[],
    value571 varchar[],
    value572 varchar[],
    value573 varchar[],
    value574 varchar[],
    value575 varchar[],
    value576 varchar[],
    value577 varchar[],
    value578 varchar[],
    value579 varchar[],
    value580 varchar[],
    value581 varchar[],
    value582 varchar[],
    value583 varchar[],
    value584 varchar[],
    value585 varchar[],
    value586 varchar[],
    value587 varchar[],
    value588 varchar[],
    value589 varchar[],
    value590 varchar[],
    value591 varchar[],
    value592 varchar[],
    value593 varchar[],
    value594 varchar[],
    value595 varchar[],
    value596 varchar[],
    value597 varchar[],
    value598 varchar[],
    value599 varchar[],
    value600 varchar[],
    value601 varchar,
    value602 varchar,
    value603 varchar,
    value604 varchar,
    value605 varchar,
    value606 varchar,
    value607 varchar,
    value608 varchar,
    value609 varchar,
    value610 varchar,
    value611 varchar,
    value612 varchar,
    value613 varchar,
    value614 varchar,
    value615 varchar,
    value616 varchar,
    value617 varchar,
    value618 varchar,
    value619 varchar,
    value620 varchar,
    value621 varchar,
    value622 varchar,
    value623 varchar,
    value624 varchar,
    value625 varchar,
    value626 varchar,
    value627 varchar,
    value628 varchar,
    value629 varchar,
    value630 varchar,
    value631 varchar,
    value632 varchar,
    value633 varchar,
    value634 varchar,
    value635 varchar,
    value636 varchar,
    value637 varchar,
    value638 varchar,
    value639 varchar,
    value640 varchar,
    value641 varchar,
    value642 varchar,
    value643 varchar,
    value644 varchar,
    value645 varchar,
    value646 varchar,
    value647 varchar,
    value648 varchar,
    value649 varchar,
    value650 varchar,
    value651 varchar,
    value652 varchar,
    value653 varchar,
    value654 varchar,
    value655 varchar,
    value656 varchar,
    value657 varchar,
    value658 varchar,
    value659 varchar,
    value660 varchar,
    value661 varchar,
    value662 varchar,
    value663 varchar,
    value664 varchar,
    value665 varchar,
    value666 varchar,
    value667 varchar,
    value668 varchar,
    value669 varchar,
    value670 varchar,
    value671 varchar,
    value672 varchar,
    value673 varchar,
    value674 varchar,
    value675 varchar,
    value676 varchar,
    value677 varchar,
    value678 varchar,
    value679 varchar,
    value680 varchar,
    value681 varchar,
    value682 varchar,
    value683 varchar,
    value684 varchar,
    value685 varchar,
    value686 varchar,
    value687 varchar,
    value688 varchar,
    value689 varchar,
    value690 varchar,
    value691 varchar,
    value692 varchar,
    value693 varchar,
    value694 varchar,
    value695 varchar,
    value696 varchar,
    value697 varchar,
    value698 varchar,
    value699 varchar,
    value700 varchar,
    value701 varchar,
    value702 varchar,
    value703 varchar,
    value704 varchar,
    value705 varchar,
    value706 varchar,
    value707 varchar,
    value708 varchar,
    value709 varchar,
    value710 varchar,
    value711 varchar,
    value712 varchar,
    value713 varchar,
    value714 varchar,
    value715 varchar,
    value716 varchar,
    value717 varchar,
    value718 varchar,
    value719 varchar,
    value720 varchar,
    value721 varchar,
    value722 varchar,
    value723 varchar,
    value724 varchar,
    value725 varchar,
    value726 varchar,
    value727 varchar,
    value728 varchar,
    value729 varchar,
    value730 varchar,
    value731 varchar,
    value732 varchar,
    value733 varchar,
    value734 varchar,
    value735 varchar,
    value736 varchar,
    value737 varchar,
    value738 varchar,
    value739 varchar,
    value740 varchar,
    value741 varchar,
    value742 varchar,
    value743 varchar,
    value744 varchar,
    value745 varchar,
    value746 varchar,
    value747 varchar,
    value748 varchar,
    value749 varchar,
    value750 varchar,
    value751 varchar,
    value752 varchar,
    value753 varchar,
    value754 varchar,
    value755 varchar,
    value756 varchar,
    value757 varchar,
    value758 varchar,
    value759 varchar,
    value760 varchar,
    value761 varchar,
    value762 varchar,
    value763 varchar,
    value764 varchar,
    value765 varchar,
    value766 varchar,
    value767 varchar,
    value768 varchar,
    value769 varchar,
    value770 varchar,
    value771 varchar,
    value772 varchar,
    value773 varchar,
    value774 varchar,
    value775 varchar,
    value776 varchar,
    value777 varchar,
    value778 varchar,
    value779 varchar,
    value780 varchar,
    value781 varchar,
    value782 varchar,
    value783 varchar,
    value784 varchar,
    value785 varchar,
    value786 varchar,
    value787 varchar,
    value788 varchar,
    value789 varchar,
    value790 varchar,
    value791 varchar,
    value792 varchar,
    value793 varchar,
    value794 varchar,
    value795 varchar,
    value796 varchar,
    value797 varchar,
    value798 varchar,
    value799 varchar,
    value800 varchar,
    CONSTRAINT "biz_public_employee_ext_5_pkey" PRIMARY KEY ( "id", "tenant_id" )
);

DROP TRIGGER IF EXISTS x_audit_changes ON biz_public_employee_ext_5;

CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON biz_public_employee_ext_5 FOR EACH ROW EXECUTE PROCEDURE public.f_change_detail('id','tenant_id','object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON biz_public_employee_ext_5;

CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON biz_public_employee_ext_5 FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();

CREATE TABLE IF NOT EXISTS biz_public_employee_ext_4(
    "id" VARCHAR (64)  NOT NULL,
    "tenant_id" VARCHAR (16)  NOT NULL,
    "created_by" VARCHAR (32) ,
    "object_describe_api_name" VARCHAR (100) ,
    "data_own_department" VARCHAR (32) ,
    "data_own_organization" VARCHAR (32) ,
    "out_data_own_department" varchar(64) ,
    "out_data_own_organization" varchar(64) ,
    "last_modified_by" VARCHAR(50),
    "out_tenant_id" VARCHAR(50),
    "out_owner" VARCHAR(50),
    "owner" VARCHAR (128) ,
    "dimension_d1" varchar[],
    "dimension_d2" varchar[],
    "dimension_d3" varchar[],
    "data_auth_id" int4,
    "out_data_auth_id" int4,
    "data_auth_code" varchar(64),
    "out_data_auth_code" varchar(64),
    "sys_modified_time" bigint ,
    value1 numeric,
    value2 numeric,
    value3 numeric,
    value4 numeric,
    value5 numeric,
    value6 numeric,
    value7 numeric,
    value8 numeric,
    value9 numeric,
    value10 numeric,
    value11 numeric,
    value12 numeric,
    value13 numeric,
    value14 numeric,
    value15 numeric,
    value16 numeric,
    value17 numeric,
    value18 numeric,
    value19 numeric,
    value20 numeric,
    value21 numeric,
    value22 numeric,
    value23 numeric,
    value24 numeric,
    value25 numeric,
    value26 numeric,
    value27 numeric,
    value28 numeric,
    value29 numeric,
    value30 numeric,
    value31 numeric,
    value32 numeric,
    value33 numeric,
    value34 numeric,
    value35 numeric,
    value36 numeric,
    value37 numeric,
    value38 numeric,
    value39 numeric,
    value40 numeric,
    value41 numeric,
    value42 numeric,
    value43 numeric,
    value44 numeric,
    value45 numeric,
    value46 numeric,
    value47 numeric,
    value48 numeric,
    value49 numeric,
    value50 numeric,
    value51 numeric,
    value52 numeric,
    value53 numeric,
    value54 numeric,
    value55 numeric,
    value56 numeric,
    value57 numeric,
    value58 numeric,
    value59 numeric,
    value60 numeric,
    value61 numeric,
    value62 numeric,
    value63 numeric,
    value64 numeric,
    value65 numeric,
    value66 numeric,
    value67 numeric,
    value68 numeric,
    value69 numeric,
    value70 numeric,
    value71 numeric,
    value72 numeric,
    value73 numeric,
    value74 numeric,
    value75 numeric,
    value76 numeric,
    value77 numeric,
    value78 numeric,
    value79 numeric,
    value80 numeric,
    value81 numeric,
    value82 numeric,
    value83 numeric,
    value84 numeric,
    value85 numeric,
    value86 numeric,
    value87 numeric,
    value88 numeric,
    value89 numeric,
    value90 numeric,
    value91 numeric,
    value92 numeric,
    value93 numeric,
    value94 numeric,
    value95 numeric,
    value96 numeric,
    value97 numeric,
    value98 numeric,
    value99 numeric,
    value100 numeric,
    value101 numeric,
    value102 numeric,
    value103 numeric,
    value104 numeric,
    value105 numeric,
    value106 numeric,
    value107 numeric,
    value108 numeric,
    value109 numeric,
    value110 numeric,
    value111 numeric,
    value112 numeric,
    value113 numeric,
    value114 numeric,
    value115 numeric,
    value116 numeric,
    value117 numeric,
    value118 numeric,
    value119 numeric,
    value120 numeric,
    value121 numeric,
    value122 numeric,
    value123 numeric,
    value124 numeric,
    value125 numeric,
    value126 numeric,
    value127 numeric,
    value128 numeric,
    value129 numeric,
    value130 numeric,
    value131 numeric,
    value132 numeric,
    value133 numeric,
    value134 numeric,
    value135 numeric,
    value136 numeric,
    value137 numeric,
    value138 numeric,
    value139 numeric,
    value140 numeric,
    value141 numeric,
    value142 numeric,
    value143 numeric,
    value144 numeric,
    value145 numeric,
    value146 numeric,
    value147 numeric,
    value148 numeric,
    value149 numeric,
    value150 numeric,
    value151 numeric,
    value152 numeric,
    value153 numeric,
    value154 numeric,
    value155 numeric,
    value156 numeric,
    value157 numeric,
    value158 numeric,
    value159 numeric,
    value160 numeric,
    value161 numeric,
    value162 numeric,
    value163 numeric,
    value164 numeric,
    value165 numeric,
    value166 numeric,
    value167 numeric,
    value168 numeric,
    value169 numeric,
    value170 numeric,
    value171 numeric,
    value172 numeric,
    value173 numeric,
    value174 numeric,
    value175 numeric,
    value176 numeric,
    value177 numeric,
    value178 numeric,
    value179 numeric,
    value180 numeric,
    value181 numeric,
    value182 numeric,
    value183 numeric,
    value184 numeric,
    value185 numeric,
    value186 numeric,
    value187 numeric,
    value188 numeric,
    value189 numeric,
    value190 numeric,
    value191 numeric,
    value192 numeric,
    value193 numeric,
    value194 numeric,
    value195 numeric,
    value196 numeric,
    value197 numeric,
    value198 numeric,
    value199 numeric,
    value200 numeric,
    value201 bool,
    value202 bool,
    value203 bool,
    value204 bool,
    value205 bool,
    value206 bool,
    value207 bool,
    value208 bool,
    value209 bool,
    value210 bool,
    value211 bool,
    value212 bool,
    value213 bool,
    value214 bool,
    value215 bool,
    value216 bool,
    value217 bool,
    value218 bool,
    value219 bool,
    value220 bool,
    value221 bool,
    value222 bool,
    value223 bool,
    value224 bool,
    value225 bool,
    value226 bool,
    value227 bool,
    value228 bool,
    value229 bool,
    value230 bool,
    value231 bool,
    value232 bool,
    value233 bool,
    value234 bool,
    value235 bool,
    value236 bool,
    value237 bool,
    value238 bool,
    value239 bool,
    value240 bool,
    value241 bool,
    value242 bool,
    value243 bool,
    value244 bool,
    value245 bool,
    value246 bool,
    value247 bool,
    value248 bool,
    value249 bool,
    value250 bool,
    value251 bool,
    value252 bool,
    value253 bool,
    value254 bool,
    value255 bool,
    value256 bool,
    value257 bool,
    value258 bool,
    value259 bool,
    value260 bool,
    value261 bool,
    value262 bool,
    value263 bool,
    value264 bool,
    value265 bool,
    value266 bool,
    value267 bool,
    value268 bool,
    value269 bool,
    value270 bool,
    value271 bool,
    value272 bool,
    value273 bool,
    value274 bool,
    value275 bool,
    value276 bool,
    value277 bool,
    value278 bool,
    value279 bool,
    value280 bool,
    value281 bool,
    value282 bool,
    value283 bool,
    value284 bool,
    value285 bool,
    value286 bool,
    value287 bool,
    value288 bool,
    value289 bool,
    value290 bool,
    value291 bool,
    value292 bool,
    value293 bool,
    value294 bool,
    value295 bool,
    value296 bool,
    value297 bool,
    value298 bool,
    value299 bool,
    value300 bool,
    value301 bool,
    value302 bool,
    value303 bool,
    value304 bool,
    value305 bool,
    value306 bool,
    value307 bool,
    value308 bool,
    value309 bool,
    value310 bool,
    value311 bool,
    value312 bool,
    value313 bool,
    value314 bool,
    value315 bool,
    value316 bool,
    value317 bool,
    value318 bool,
    value319 bool,
    value320 bool,
    value321 bool,
    value322 bool,
    value323 bool,
    value324 bool,
    value325 bool,
    value326 bool,
    value327 bool,
    value328 bool,
    value329 bool,
    value330 bool,
    value331 bool,
    value332 bool,
    value333 bool,
    value334 bool,
    value335 bool,
    value336 bool,
    value337 bool,
    value338 bool,
    value339 bool,
    value340 bool,
    value341 bool,
    value342 bool,
    value343 bool,
    value344 bool,
    value345 bool,
    value346 bool,
    value347 bool,
    value348 bool,
    value349 bool,
    value350 bool,
    value351 bool,
    value352 bool,
    value353 bool,
    value354 bool,
    value355 bool,
    value356 bool,
    value357 bool,
    value358 bool,
    value359 bool,
    value360 bool,
    value361 bool,
    value362 bool,
    value363 bool,
    value364 bool,
    value365 bool,
    value366 bool,
    value367 bool,
    value368 bool,
    value369 bool,
    value370 bool,
    value371 bool,
    value372 bool,
    value373 bool,
    value374 bool,
    value375 bool,
    value376 bool,
    value377 bool,
    value378 bool,
    value379 bool,
    value380 bool,
    value381 bool,
    value382 bool,
    value383 bool,
    value384 bool,
    value385 bool,
    value386 bool,
    value387 bool,
    value388 bool,
    value389 bool,
    value390 bool,
    value391 bool,
    value392 bool,
    value393 bool,
    value394 bool,
    value395 bool,
    value396 bool,
    value397 bool,
    value398 bool,
    value399 bool,
    value400 bool,
    value401 varchar[],
    value402 varchar[],
    value403 varchar[],
    value404 varchar[],
    value405 varchar[],
    value406 varchar[],
    value407 varchar[],
    value408 varchar[],
    value409 varchar[],
    value410 varchar[],
    value411 varchar[],
    value412 varchar[],
    value413 varchar[],
    value414 varchar[],
    value415 varchar[],
    value416 varchar[],
    value417 varchar[],
    value418 varchar[],
    value419 varchar[],
    value420 varchar[],
    value421 varchar[],
    value422 varchar[],
    value423 varchar[],
    value424 varchar[],
    value425 varchar[],
    value426 varchar[],
    value427 varchar[],
    value428 varchar[],
    value429 varchar[],
    value430 varchar[],
    value431 varchar[],
    value432 varchar[],
    value433 varchar[],
    value434 varchar[],
    value435 varchar[],
    value436 varchar[],
    value437 varchar[],
    value438 varchar[],
    value439 varchar[],
    value440 varchar[],
    value441 varchar[],
    value442 varchar[],
    value443 varchar[],
    value444 varchar[],
    value445 varchar[],
    value446 varchar[],
    value447 varchar[],
    value448 varchar[],
    value449 varchar[],
    value450 varchar[],
    value451 varchar[],
    value452 varchar[],
    value453 varchar[],
    value454 varchar[],
    value455 varchar[],
    value456 varchar[],
    value457 varchar[],
    value458 varchar[],
    value459 varchar[],
    value460 varchar[],
    value461 varchar[],
    value462 varchar[],
    value463 varchar[],
    value464 varchar[],
    value465 varchar[],
    value466 varchar[],
    value467 varchar[],
    value468 varchar[],
    value469 varchar[],
    value470 varchar[],
    value471 varchar[],
    value472 varchar[],
    value473 varchar[],
    value474 varchar[],
    value475 varchar[],
    value476 varchar[],
    value477 varchar[],
    value478 varchar[],
    value479 varchar[],
    value480 varchar[],
    value481 varchar[],
    value482 varchar[],
    value483 varchar[],
    value484 varchar[],
    value485 varchar[],
    value486 varchar[],
    value487 varchar[],
    value488 varchar[],
    value489 varchar[],
    value490 varchar[],
    value491 varchar[],
    value492 varchar[],
    value493 varchar[],
    value494 varchar[],
    value495 varchar[],
    value496 varchar[],
    value497 varchar[],
    value498 varchar[],
    value499 varchar[],
    value500 varchar[],
    value501 varchar[],
    value502 varchar[],
    value503 varchar[],
    value504 varchar[],
    value505 varchar[],
    value506 varchar[],
    value507 varchar[],
    value508 varchar[],
    value509 varchar[],
    value510 varchar[],
    value511 varchar[],
    value512 varchar[],
    value513 varchar[],
    value514 varchar[],
    value515 varchar[],
    value516 varchar[],
    value517 varchar[],
    value518 varchar[],
    value519 varchar[],
    value520 varchar[],
    value521 varchar[],
    value522 varchar[],
    value523 varchar[],
    value524 varchar[],
    value525 varchar[],
    value526 varchar[],
    value527 varchar[],
    value528 varchar[],
    value529 varchar[],
    value530 varchar[],
    value531 varchar[],
    value532 varchar[],
    value533 varchar[],
    value534 varchar[],
    value535 varchar[],
    value536 varchar[],
    value537 varchar[],
    value538 varchar[],
    value539 varchar[],
    value540 varchar[],
    value541 varchar[],
    value542 varchar[],
    value543 varchar[],
    value544 varchar[],
    value545 varchar[],
    value546 varchar[],
    value547 varchar[],
    value548 varchar[],
    value549 varchar[],
    value550 varchar[],
    value551 varchar[],
    value552 varchar[],
    value553 varchar[],
    value554 varchar[],
    value555 varchar[],
    value556 varchar[],
    value557 varchar[],
    value558 varchar[],
    value559 varchar[],
    value560 varchar[],
    value561 varchar[],
    value562 varchar[],
    value563 varchar[],
    value564 varchar[],
    value565 varchar[],
    value566 varchar[],
    value567 varchar[],
    value568 varchar[],
    value569 varchar[],
    value570 varchar[],
    value571 varchar[],
    value572 varchar[],
    value573 varchar[],
    value574 varchar[],
    value575 varchar[],
    value576 varchar[],
    value577 varchar[],
    value578 varchar[],
    value579 varchar[],
    value580 varchar[],
    value581 varchar[],
    value582 varchar[],
    value583 varchar[],
    value584 varchar[],
    value585 varchar[],
    value586 varchar[],
    value587 varchar[],
    value588 varchar[],
    value589 varchar[],
    value590 varchar[],
    value591 varchar[],
    value592 varchar[],
    value593 varchar[],
    value594 varchar[],
    value595 varchar[],
    value596 varchar[],
    value597 varchar[],
    value598 varchar[],
    value599 varchar[],
    value600 varchar[],
    value601 varchar,
    value602 varchar,
    value603 varchar,
    value604 varchar,
    value605 varchar,
    value606 varchar,
    value607 varchar,
    value608 varchar,
    value609 varchar,
    value610 varchar,
    value611 varchar,
    value612 varchar,
    value613 varchar,
    value614 varchar,
    value615 varchar,
    value616 varchar,
    value617 varchar,
    value618 varchar,
    value619 varchar,
    value620 varchar,
    value621 varchar,
    value622 varchar,
    value623 varchar,
    value624 varchar,
    value625 varchar,
    value626 varchar,
    value627 varchar,
    value628 varchar,
    value629 varchar,
    value630 varchar,
    value631 varchar,
    value632 varchar,
    value633 varchar,
    value634 varchar,
    value635 varchar,
    value636 varchar,
    value637 varchar,
    value638 varchar,
    value639 varchar,
    value640 varchar,
    value641 varchar,
    value642 varchar,
    value643 varchar,
    value644 varchar,
    value645 varchar,
    value646 varchar,
    value647 varchar,
    value648 varchar,
    value649 varchar,
    value650 varchar,
    value651 varchar,
    value652 varchar,
    value653 varchar,
    value654 varchar,
    value655 varchar,
    value656 varchar,
    value657 varchar,
    value658 varchar,
    value659 varchar,
    value660 varchar,
    value661 varchar,
    value662 varchar,
    value663 varchar,
    value664 varchar,
    value665 varchar,
    value666 varchar,
    value667 varchar,
    value668 varchar,
    value669 varchar,
    value670 varchar,
    value671 varchar,
    value672 varchar,
    value673 varchar,
    value674 varchar,
    value675 varchar,
    value676 varchar,
    value677 varchar,
    value678 varchar,
    value679 varchar,
    value680 varchar,
    value681 varchar,
    value682 varchar,
    value683 varchar,
    value684 varchar,
    value685 varchar,
    value686 varchar,
    value687 varchar,
    value688 varchar,
    value689 varchar,
    value690 varchar,
    value691 varchar,
    value692 varchar,
    value693 varchar,
    value694 varchar,
    value695 varchar,
    value696 varchar,
    value697 varchar,
    value698 varchar,
    value699 varchar,
    value700 varchar,
    value701 varchar,
    value702 varchar,
    value703 varchar,
    value704 varchar,
    value705 varchar,
    value706 varchar,
    value707 varchar,
    value708 varchar,
    value709 varchar,
    value710 varchar,
    value711 varchar,
    value712 varchar,
    value713 varchar,
    value714 varchar,
    value715 varchar,
    value716 varchar,
    value717 varchar,
    value718 varchar,
    value719 varchar,
    value720 varchar,
    value721 varchar,
    value722 varchar,
    value723 varchar,
    value724 varchar,
    value725 varchar,
    value726 varchar,
    value727 varchar,
    value728 varchar,
    value729 varchar,
    value730 varchar,
    value731 varchar,
    value732 varchar,
    value733 varchar,
    value734 varchar,
    value735 varchar,
    value736 varchar,
    value737 varchar,
    value738 varchar,
    value739 varchar,
    value740 varchar,
    value741 varchar,
    value742 varchar,
    value743 varchar,
    value744 varchar,
    value745 varchar,
    value746 varchar,
    value747 varchar,
    value748 varchar,
    value749 varchar,
    value750 varchar,
    value751 varchar,
    value752 varchar,
    value753 varchar,
    value754 varchar,
    value755 varchar,
    value756 varchar,
    value757 varchar,
    value758 varchar,
    value759 varchar,
    value760 varchar,
    value761 varchar,
    value762 varchar,
    value763 varchar,
    value764 varchar,
    value765 varchar,
    value766 varchar,
    value767 varchar,
    value768 varchar,
    value769 varchar,
    value770 varchar,
    value771 varchar,
    value772 varchar,
    value773 varchar,
    value774 varchar,
    value775 varchar,
    value776 varchar,
    value777 varchar,
    value778 varchar,
    value779 varchar,
    value780 varchar,
    value781 varchar,
    value782 varchar,
    value783 varchar,
    value784 varchar,
    value785 varchar,
    value786 varchar,
    value787 varchar,
    value788 varchar,
    value789 varchar,
    value790 varchar,
    value791 varchar,
    value792 varchar,
    value793 varchar,
    value794 varchar,
    value795 varchar,
    value796 varchar,
    value797 varchar,
    value798 varchar,
    value799 varchar,
    value800 varchar,
    CONSTRAINT "biz_public_employee_ext_4_pkey" PRIMARY KEY ( "id", "tenant_id" )
);

DROP TRIGGER IF EXISTS x_audit_changes ON biz_public_employee_ext_4;

CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON biz_public_employee_ext_4 FOR EACH ROW EXECUTE PROCEDURE public.f_change_detail('id','tenant_id','object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON biz_public_employee_ext_4;

CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON biz_public_employee_ext_4 FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();

CREATE TABLE IF NOT EXISTS biz_public_employee_ext_3(
    "id" VARCHAR (64)  NOT NULL,
    "tenant_id" VARCHAR (16)  NOT NULL,
    "created_by" VARCHAR (32) ,
    "object_describe_api_name" VARCHAR (100) ,
    "data_own_department" VARCHAR (32) ,
    "data_own_organization" VARCHAR (32) ,
    "out_data_own_department" varchar(64) ,
    "out_data_own_organization" varchar(64) ,
    "last_modified_by" VARCHAR(50),
    "out_tenant_id" VARCHAR(50),
    "out_owner" VARCHAR(50),
    "owner" VARCHAR (128) ,
    "dimension_d1" varchar[],
    "dimension_d2" varchar[],
    "dimension_d3" varchar[],
    "data_auth_id" int4,
    "out_data_auth_id" int4,
    "data_auth_code" varchar(64),
    "out_data_auth_code" varchar(64),
    "sys_modified_time" bigint ,
    value1 numeric,
    value2 numeric,
    value3 numeric,
    value4 numeric,
    value5 numeric,
    value6 numeric,
    value7 numeric,
    value8 numeric,
    value9 numeric,
    value10 numeric,
    value11 numeric,
    value12 numeric,
    value13 numeric,
    value14 numeric,
    value15 numeric,
    value16 numeric,
    value17 numeric,
    value18 numeric,
    value19 numeric,
    value20 numeric,
    value21 numeric,
    value22 numeric,
    value23 numeric,
    value24 numeric,
    value25 numeric,
    value26 numeric,
    value27 numeric,
    value28 numeric,
    value29 numeric,
    value30 numeric,
    value31 numeric,
    value32 numeric,
    value33 numeric,
    value34 numeric,
    value35 numeric,
    value36 numeric,
    value37 numeric,
    value38 numeric,
    value39 numeric,
    value40 numeric,
    value41 numeric,
    value42 numeric,
    value43 numeric,
    value44 numeric,
    value45 numeric,
    value46 numeric,
    value47 numeric,
    value48 numeric,
    value49 numeric,
    value50 numeric,
    value51 numeric,
    value52 numeric,
    value53 numeric,
    value54 numeric,
    value55 numeric,
    value56 numeric,
    value57 numeric,
    value58 numeric,
    value59 numeric,
    value60 numeric,
    value61 numeric,
    value62 numeric,
    value63 numeric,
    value64 numeric,
    value65 numeric,
    value66 numeric,
    value67 numeric,
    value68 numeric,
    value69 numeric,
    value70 numeric,
    value71 numeric,
    value72 numeric,
    value73 numeric,
    value74 numeric,
    value75 numeric,
    value76 numeric,
    value77 numeric,
    value78 numeric,
    value79 numeric,
    value80 numeric,
    value81 numeric,
    value82 numeric,
    value83 numeric,
    value84 numeric,
    value85 numeric,
    value86 numeric,
    value87 numeric,
    value88 numeric,
    value89 numeric,
    value90 numeric,
    value91 numeric,
    value92 numeric,
    value93 numeric,
    value94 numeric,
    value95 numeric,
    value96 numeric,
    value97 numeric,
    value98 numeric,
    value99 numeric,
    value100 numeric,
    value101 numeric,
    value102 numeric,
    value103 numeric,
    value104 numeric,
    value105 numeric,
    value106 numeric,
    value107 numeric,
    value108 numeric,
    value109 numeric,
    value110 numeric,
    value111 numeric,
    value112 numeric,
    value113 numeric,
    value114 numeric,
    value115 numeric,
    value116 numeric,
    value117 numeric,
    value118 numeric,
    value119 numeric,
    value120 numeric,
    value121 numeric,
    value122 numeric,
    value123 numeric,
    value124 numeric,
    value125 numeric,
    value126 numeric,
    value127 numeric,
    value128 numeric,
    value129 numeric,
    value130 numeric,
    value131 numeric,
    value132 numeric,
    value133 numeric,
    value134 numeric,
    value135 numeric,
    value136 numeric,
    value137 numeric,
    value138 numeric,
    value139 numeric,
    value140 numeric,
    value141 numeric,
    value142 numeric,
    value143 numeric,
    value144 numeric,
    value145 numeric,
    value146 numeric,
    value147 numeric,
    value148 numeric,
    value149 numeric,
    value150 numeric,
    value151 numeric,
    value152 numeric,
    value153 numeric,
    value154 numeric,
    value155 numeric,
    value156 numeric,
    value157 numeric,
    value158 numeric,
    value159 numeric,
    value160 numeric,
    value161 numeric,
    value162 numeric,
    value163 numeric,
    value164 numeric,
    value165 numeric,
    value166 numeric,
    value167 numeric,
    value168 numeric,
    value169 numeric,
    value170 numeric,
    value171 numeric,
    value172 numeric,
    value173 numeric,
    value174 numeric,
    value175 numeric,
    value176 numeric,
    value177 numeric,
    value178 numeric,
    value179 numeric,
    value180 numeric,
    value181 numeric,
    value182 numeric,
    value183 numeric,
    value184 numeric,
    value185 numeric,
    value186 numeric,
    value187 numeric,
    value188 numeric,
    value189 numeric,
    value190 numeric,
    value191 numeric,
    value192 numeric,
    value193 numeric,
    value194 numeric,
    value195 numeric,
    value196 numeric,
    value197 numeric,
    value198 numeric,
    value199 numeric,
    value200 numeric,
    value201 bool,
    value202 bool,
    value203 bool,
    value204 bool,
    value205 bool,
    value206 bool,
    value207 bool,
    value208 bool,
    value209 bool,
    value210 bool,
    value211 bool,
    value212 bool,
    value213 bool,
    value214 bool,
    value215 bool,
    value216 bool,
    value217 bool,
    value218 bool,
    value219 bool,
    value220 bool,
    value221 bool,
    value222 bool,
    value223 bool,
    value224 bool,
    value225 bool,
    value226 bool,
    value227 bool,
    value228 bool,
    value229 bool,
    value230 bool,
    value231 bool,
    value232 bool,
    value233 bool,
    value234 bool,
    value235 bool,
    value236 bool,
    value237 bool,
    value238 bool,
    value239 bool,
    value240 bool,
    value241 bool,
    value242 bool,
    value243 bool,
    value244 bool,
    value245 bool,
    value246 bool,
    value247 bool,
    value248 bool,
    value249 bool,
    value250 bool,
    value251 bool,
    value252 bool,
    value253 bool,
    value254 bool,
    value255 bool,
    value256 bool,
    value257 bool,
    value258 bool,
    value259 bool,
    value260 bool,
    value261 bool,
    value262 bool,
    value263 bool,
    value264 bool,
    value265 bool,
    value266 bool,
    value267 bool,
    value268 bool,
    value269 bool,
    value270 bool,
    value271 bool,
    value272 bool,
    value273 bool,
    value274 bool,
    value275 bool,
    value276 bool,
    value277 bool,
    value278 bool,
    value279 bool,
    value280 bool,
    value281 bool,
    value282 bool,
    value283 bool,
    value284 bool,
    value285 bool,
    value286 bool,
    value287 bool,
    value288 bool,
    value289 bool,
    value290 bool,
    value291 bool,
    value292 bool,
    value293 bool,
    value294 bool,
    value295 bool,
    value296 bool,
    value297 bool,
    value298 bool,
    value299 bool,
    value300 bool,
    value301 bool,
    value302 bool,
    value303 bool,
    value304 bool,
    value305 bool,
    value306 bool,
    value307 bool,
    value308 bool,
    value309 bool,
    value310 bool,
    value311 bool,
    value312 bool,
    value313 bool,
    value314 bool,
    value315 bool,
    value316 bool,
    value317 bool,
    value318 bool,
    value319 bool,
    value320 bool,
    value321 bool,
    value322 bool,
    value323 bool,
    value324 bool,
    value325 bool,
    value326 bool,
    value327 bool,
    value328 bool,
    value329 bool,
    value330 bool,
    value331 bool,
    value332 bool,
    value333 bool,
    value334 bool,
    value335 bool,
    value336 bool,
    value337 bool,
    value338 bool,
    value339 bool,
    value340 bool,
    value341 bool,
    value342 bool,
    value343 bool,
    value344 bool,
    value345 bool,
    value346 bool,
    value347 bool,
    value348 bool,
    value349 bool,
    value350 bool,
    value351 bool,
    value352 bool,
    value353 bool,
    value354 bool,
    value355 bool,
    value356 bool,
    value357 bool,
    value358 bool,
    value359 bool,
    value360 bool,
    value361 bool,
    value362 bool,
    value363 bool,
    value364 bool,
    value365 bool,
    value366 bool,
    value367 bool,
    value368 bool,
    value369 bool,
    value370 bool,
    value371 bool,
    value372 bool,
    value373 bool,
    value374 bool,
    value375 bool,
    value376 bool,
    value377 bool,
    value378 bool,
    value379 bool,
    value380 bool,
    value381 bool,
    value382 bool,
    value383 bool,
    value384 bool,
    value385 bool,
    value386 bool,
    value387 bool,
    value388 bool,
    value389 bool,
    value390 bool,
    value391 bool,
    value392 bool,
    value393 bool,
    value394 bool,
    value395 bool,
    value396 bool,
    value397 bool,
    value398 bool,
    value399 bool,
    value400 bool,
    value401 varchar[],
    value402 varchar[],
    value403 varchar[],
    value404 varchar[],
    value405 varchar[],
    value406 varchar[],
    value407 varchar[],
    value408 varchar[],
    value409 varchar[],
    value410 varchar[],
    value411 varchar[],
    value412 varchar[],
    value413 varchar[],
    value414 varchar[],
    value415 varchar[],
    value416 varchar[],
    value417 varchar[],
    value418 varchar[],
    value419 varchar[],
    value420 varchar[],
    value421 varchar[],
    value422 varchar[],
    value423 varchar[],
    value424 varchar[],
    value425 varchar[],
    value426 varchar[],
    value427 varchar[],
    value428 varchar[],
    value429 varchar[],
    value430 varchar[],
    value431 varchar[],
    value432 varchar[],
    value433 varchar[],
    value434 varchar[],
    value435 varchar[],
    value436 varchar[],
    value437 varchar[],
    value438 varchar[],
    value439 varchar[],
    value440 varchar[],
    value441 varchar[],
    value442 varchar[],
    value443 varchar[],
    value444 varchar[],
    value445 varchar[],
    value446 varchar[],
    value447 varchar[],
    value448 varchar[],
    value449 varchar[],
    value450 varchar[],
    value451 varchar[],
    value452 varchar[],
    value453 varchar[],
    value454 varchar[],
    value455 varchar[],
    value456 varchar[],
    value457 varchar[],
    value458 varchar[],
    value459 varchar[],
    value460 varchar[],
    value461 varchar[],
    value462 varchar[],
    value463 varchar[],
    value464 varchar[],
    value465 varchar[],
    value466 varchar[],
    value467 varchar[],
    value468 varchar[],
    value469 varchar[],
    value470 varchar[],
    value471 varchar[],
    value472 varchar[],
    value473 varchar[],
    value474 varchar[],
    value475 varchar[],
    value476 varchar[],
    value477 varchar[],
    value478 varchar[],
    value479 varchar[],
    value480 varchar[],
    value481 varchar[],
    value482 varchar[],
    value483 varchar[],
    value484 varchar[],
    value485 varchar[],
    value486 varchar[],
    value487 varchar[],
    value488 varchar[],
    value489 varchar[],
    value490 varchar[],
    value491 varchar[],
    value492 varchar[],
    value493 varchar[],
    value494 varchar[],
    value495 varchar[],
    value496 varchar[],
    value497 varchar[],
    value498 varchar[],
    value499 varchar[],
    value500 varchar[],
    value501 varchar[],
    value502 varchar[],
    value503 varchar[],
    value504 varchar[],
    value505 varchar[],
    value506 varchar[],
    value507 varchar[],
    value508 varchar[],
    value509 varchar[],
    value510 varchar[],
    value511 varchar[],
    value512 varchar[],
    value513 varchar[],
    value514 varchar[],
    value515 varchar[],
    value516 varchar[],
    value517 varchar[],
    value518 varchar[],
    value519 varchar[],
    value520 varchar[],
    value521 varchar[],
    value522 varchar[],
    value523 varchar[],
    value524 varchar[],
    value525 varchar[],
    value526 varchar[],
    value527 varchar[],
    value528 varchar[],
    value529 varchar[],
    value530 varchar[],
    value531 varchar[],
    value532 varchar[],
    value533 varchar[],
    value534 varchar[],
    value535 varchar[],
    value536 varchar[],
    value537 varchar[],
    value538 varchar[],
    value539 varchar[],
    value540 varchar[],
    value541 varchar[],
    value542 varchar[],
    value543 varchar[],
    value544 varchar[],
    value545 varchar[],
    value546 varchar[],
    value547 varchar[],
    value548 varchar[],
    value549 varchar[],
    value550 varchar[],
    value551 varchar[],
    value552 varchar[],
    value553 varchar[],
    value554 varchar[],
    value555 varchar[],
    value556 varchar[],
    value557 varchar[],
    value558 varchar[],
    value559 varchar[],
    value560 varchar[],
    value561 varchar[],
    value562 varchar[],
    value563 varchar[],
    value564 varchar[],
    value565 varchar[],
    value566 varchar[],
    value567 varchar[],
    value568 varchar[],
    value569 varchar[],
    value570 varchar[],
    value571 varchar[],
    value572 varchar[],
    value573 varchar[],
    value574 varchar[],
    value575 varchar[],
    value576 varchar[],
    value577 varchar[],
    value578 varchar[],
    value579 varchar[],
    value580 varchar[],
    value581 varchar[],
    value582 varchar[],
    value583 varchar[],
    value584 varchar[],
    value585 varchar[],
    value586 varchar[],
    value587 varchar[],
    value588 varchar[],
    value589 varchar[],
    value590 varchar[],
    value591 varchar[],
    value592 varchar[],
    value593 varchar[],
    value594 varchar[],
    value595 varchar[],
    value596 varchar[],
    value597 varchar[],
    value598 varchar[],
    value599 varchar[],
    value600 varchar[],
    value601 varchar,
    value602 varchar,
    value603 varchar,
    value604 varchar,
    value605 varchar,
    value606 varchar,
    value607 varchar,
    value608 varchar,
    value609 varchar,
    value610 varchar,
    value611 varchar,
    value612 varchar,
    value613 varchar,
    value614 varchar,
    value615 varchar,
    value616 varchar,
    value617 varchar,
    value618 varchar,
    value619 varchar,
    value620 varchar,
    value621 varchar,
    value622 varchar,
    value623 varchar,
    value624 varchar,
    value625 varchar,
    value626 varchar,
    value627 varchar,
    value628 varchar,
    value629 varchar,
    value630 varchar,
    value631 varchar,
    value632 varchar,
    value633 varchar,
    value634 varchar,
    value635 varchar,
    value636 varchar,
    value637 varchar,
    value638 varchar,
    value639 varchar,
    value640 varchar,
    value641 varchar,
    value642 varchar,
    value643 varchar,
    value644 varchar,
    value645 varchar,
    value646 varchar,
    value647 varchar,
    value648 varchar,
    value649 varchar,
    value650 varchar,
    value651 varchar,
    value652 varchar,
    value653 varchar,
    value654 varchar,
    value655 varchar,
    value656 varchar,
    value657 varchar,
    value658 varchar,
    value659 varchar,
    value660 varchar,
    value661 varchar,
    value662 varchar,
    value663 varchar,
    value664 varchar,
    value665 varchar,
    value666 varchar,
    value667 varchar,
    value668 varchar,
    value669 varchar,
    value670 varchar,
    value671 varchar,
    value672 varchar,
    value673 varchar,
    value674 varchar,
    value675 varchar,
    value676 varchar,
    value677 varchar,
    value678 varchar,
    value679 varchar,
    value680 varchar,
    value681 varchar,
    value682 varchar,
    value683 varchar,
    value684 varchar,
    value685 varchar,
    value686 varchar,
    value687 varchar,
    value688 varchar,
    value689 varchar,
    value690 varchar,
    value691 varchar,
    value692 varchar,
    value693 varchar,
    value694 varchar,
    value695 varchar,
    value696 varchar,
    value697 varchar,
    value698 varchar,
    value699 varchar,
    value700 varchar,
    value701 varchar,
    value702 varchar,
    value703 varchar,
    value704 varchar,
    value705 varchar,
    value706 varchar,
    value707 varchar,
    value708 varchar,
    value709 varchar,
    value710 varchar,
    value711 varchar,
    value712 varchar,
    value713 varchar,
    value714 varchar,
    value715 varchar,
    value716 varchar,
    value717 varchar,
    value718 varchar,
    value719 varchar,
    value720 varchar,
    value721 varchar,
    value722 varchar,
    value723 varchar,
    value724 varchar,
    value725 varchar,
    value726 varchar,
    value727 varchar,
    value728 varchar,
    value729 varchar,
    value730 varchar,
    value731 varchar,
    value732 varchar,
    value733 varchar,
    value734 varchar,
    value735 varchar,
    value736 varchar,
    value737 varchar,
    value738 varchar,
    value739 varchar,
    value740 varchar,
    value741 varchar,
    value742 varchar,
    value743 varchar,
    value744 varchar,
    value745 varchar,
    value746 varchar,
    value747 varchar,
    value748 varchar,
    value749 varchar,
    value750 varchar,
    value751 varchar,
    value752 varchar,
    value753 varchar,
    value754 varchar,
    value755 varchar,
    value756 varchar,
    value757 varchar,
    value758 varchar,
    value759 varchar,
    value760 varchar,
    value761 varchar,
    value762 varchar,
    value763 varchar,
    value764 varchar,
    value765 varchar,
    value766 varchar,
    value767 varchar,
    value768 varchar,
    value769 varchar,
    value770 varchar,
    value771 varchar,
    value772 varchar,
    value773 varchar,
    value774 varchar,
    value775 varchar,
    value776 varchar,
    value777 varchar,
    value778 varchar,
    value779 varchar,
    value780 varchar,
    value781 varchar,
    value782 varchar,
    value783 varchar,
    value784 varchar,
    value785 varchar,
    value786 varchar,
    value787 varchar,
    value788 varchar,
    value789 varchar,
    value790 varchar,
    value791 varchar,
    value792 varchar,
    value793 varchar,
    value794 varchar,
    value795 varchar,
    value796 varchar,
    value797 varchar,
    value798 varchar,
    value799 varchar,
    value800 varchar,
    CONSTRAINT "biz_public_employee_ext_3_pkey" PRIMARY KEY ( "id", "tenant_id" )
);

DROP TRIGGER IF EXISTS x_audit_changes ON biz_public_employee_ext_3;

CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON biz_public_employee_ext_3 FOR EACH ROW EXECUTE PROCEDURE public.f_change_detail('id','tenant_id','object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON biz_public_employee_ext_3;

CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON biz_public_employee_ext_3 FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();

CREATE TABLE IF NOT EXISTS biz_public_employee_ext_2(
    "id" VARCHAR (64)  NOT NULL,
    "tenant_id" VARCHAR (16)  NOT NULL,
    "created_by" VARCHAR (32) ,
    "object_describe_api_name" VARCHAR (100) ,
    "data_own_department" VARCHAR (32) ,
    "data_own_organization" VARCHAR (32) ,
    "out_data_own_department" varchar(64) ,
    "out_data_own_organization" varchar(64) ,
    "last_modified_by" VARCHAR(50),
    "out_tenant_id" VARCHAR(50),
    "out_owner" VARCHAR(50),
    "owner" VARCHAR (128) ,
    "dimension_d1" varchar[],
    "dimension_d2" varchar[],
    "dimension_d3" varchar[],
    "data_auth_id" int4,
    "out_data_auth_id" int4,
    "data_auth_code" varchar(64),
    "out_data_auth_code" varchar(64),
    "sys_modified_time" bigint ,
    value1 numeric,
    value2 numeric,
    value3 numeric,
    value4 numeric,
    value5 numeric,
    value6 numeric,
    value7 numeric,
    value8 numeric,
    value9 numeric,
    value10 numeric,
    value11 numeric,
    value12 numeric,
    value13 numeric,
    value14 numeric,
    value15 numeric,
    value16 numeric,
    value17 numeric,
    value18 numeric,
    value19 numeric,
    value20 numeric,
    value21 numeric,
    value22 numeric,
    value23 numeric,
    value24 numeric,
    value25 numeric,
    value26 numeric,
    value27 numeric,
    value28 numeric,
    value29 numeric,
    value30 numeric,
    value31 numeric,
    value32 numeric,
    value33 numeric,
    value34 numeric,
    value35 numeric,
    value36 numeric,
    value37 numeric,
    value38 numeric,
    value39 numeric,
    value40 numeric,
    value41 numeric,
    value42 numeric,
    value43 numeric,
    value44 numeric,
    value45 numeric,
    value46 numeric,
    value47 numeric,
    value48 numeric,
    value49 numeric,
    value50 numeric,
    value51 numeric,
    value52 numeric,
    value53 numeric,
    value54 numeric,
    value55 numeric,
    value56 numeric,
    value57 numeric,
    value58 numeric,
    value59 numeric,
    value60 numeric,
    value61 numeric,
    value62 numeric,
    value63 numeric,
    value64 numeric,
    value65 numeric,
    value66 numeric,
    value67 numeric,
    value68 numeric,
    value69 numeric,
    value70 numeric,
    value71 numeric,
    value72 numeric,
    value73 numeric,
    value74 numeric,
    value75 numeric,
    value76 numeric,
    value77 numeric,
    value78 numeric,
    value79 numeric,
    value80 numeric,
    value81 numeric,
    value82 numeric,
    value83 numeric,
    value84 numeric,
    value85 numeric,
    value86 numeric,
    value87 numeric,
    value88 numeric,
    value89 numeric,
    value90 numeric,
    value91 numeric,
    value92 numeric,
    value93 numeric,
    value94 numeric,
    value95 numeric,
    value96 numeric,
    value97 numeric,
    value98 numeric,
    value99 numeric,
    value100 numeric,
    value101 numeric,
    value102 numeric,
    value103 numeric,
    value104 numeric,
    value105 numeric,
    value106 numeric,
    value107 numeric,
    value108 numeric,
    value109 numeric,
    value110 numeric,
    value111 numeric,
    value112 numeric,
    value113 numeric,
    value114 numeric,
    value115 numeric,
    value116 numeric,
    value117 numeric,
    value118 numeric,
    value119 numeric,
    value120 numeric,
    value121 numeric,
    value122 numeric,
    value123 numeric,
    value124 numeric,
    value125 numeric,
    value126 numeric,
    value127 numeric,
    value128 numeric,
    value129 numeric,
    value130 numeric,
    value131 numeric,
    value132 numeric,
    value133 numeric,
    value134 numeric,
    value135 numeric,
    value136 numeric,
    value137 numeric,
    value138 numeric,
    value139 numeric,
    value140 numeric,
    value141 numeric,
    value142 numeric,
    value143 numeric,
    value144 numeric,
    value145 numeric,
    value146 numeric,
    value147 numeric,
    value148 numeric,
    value149 numeric,
    value150 numeric,
    value151 numeric,
    value152 numeric,
    value153 numeric,
    value154 numeric,
    value155 numeric,
    value156 numeric,
    value157 numeric,
    value158 numeric,
    value159 numeric,
    value160 numeric,
    value161 numeric,
    value162 numeric,
    value163 numeric,
    value164 numeric,
    value165 numeric,
    value166 numeric,
    value167 numeric,
    value168 numeric,
    value169 numeric,
    value170 numeric,
    value171 numeric,
    value172 numeric,
    value173 numeric,
    value174 numeric,
    value175 numeric,
    value176 numeric,
    value177 numeric,
    value178 numeric,
    value179 numeric,
    value180 numeric,
    value181 numeric,
    value182 numeric,
    value183 numeric,
    value184 numeric,
    value185 numeric,
    value186 numeric,
    value187 numeric,
    value188 numeric,
    value189 numeric,
    value190 numeric,
    value191 numeric,
    value192 numeric,
    value193 numeric,
    value194 numeric,
    value195 numeric,
    value196 numeric,
    value197 numeric,
    value198 numeric,
    value199 numeric,
    value200 numeric,
    value201 bool,
    value202 bool,
    value203 bool,
    value204 bool,
    value205 bool,
    value206 bool,
    value207 bool,
    value208 bool,
    value209 bool,
    value210 bool,
    value211 bool,
    value212 bool,
    value213 bool,
    value214 bool,
    value215 bool,
    value216 bool,
    value217 bool,
    value218 bool,
    value219 bool,
    value220 bool,
    value221 bool,
    value222 bool,
    value223 bool,
    value224 bool,
    value225 bool,
    value226 bool,
    value227 bool,
    value228 bool,
    value229 bool,
    value230 bool,
    value231 bool,
    value232 bool,
    value233 bool,
    value234 bool,
    value235 bool,
    value236 bool,
    value237 bool,
    value238 bool,
    value239 bool,
    value240 bool,
    value241 bool,
    value242 bool,
    value243 bool,
    value244 bool,
    value245 bool,
    value246 bool,
    value247 bool,
    value248 bool,
    value249 bool,
    value250 bool,
    value251 bool,
    value252 bool,
    value253 bool,
    value254 bool,
    value255 bool,
    value256 bool,
    value257 bool,
    value258 bool,
    value259 bool,
    value260 bool,
    value261 bool,
    value262 bool,
    value263 bool,
    value264 bool,
    value265 bool,
    value266 bool,
    value267 bool,
    value268 bool,
    value269 bool,
    value270 bool,
    value271 bool,
    value272 bool,
    value273 bool,
    value274 bool,
    value275 bool,
    value276 bool,
    value277 bool,
    value278 bool,
    value279 bool,
    value280 bool,
    value281 bool,
    value282 bool,
    value283 bool,
    value284 bool,
    value285 bool,
    value286 bool,
    value287 bool,
    value288 bool,
    value289 bool,
    value290 bool,
    value291 bool,
    value292 bool,
    value293 bool,
    value294 bool,
    value295 bool,
    value296 bool,
    value297 bool,
    value298 bool,
    value299 bool,
    value300 bool,
    value301 bool,
    value302 bool,
    value303 bool,
    value304 bool,
    value305 bool,
    value306 bool,
    value307 bool,
    value308 bool,
    value309 bool,
    value310 bool,
    value311 bool,
    value312 bool,
    value313 bool,
    value314 bool,
    value315 bool,
    value316 bool,
    value317 bool,
    value318 bool,
    value319 bool,
    value320 bool,
    value321 bool,
    value322 bool,
    value323 bool,
    value324 bool,
    value325 bool,
    value326 bool,
    value327 bool,
    value328 bool,
    value329 bool,
    value330 bool,
    value331 bool,
    value332 bool,
    value333 bool,
    value334 bool,
    value335 bool,
    value336 bool,
    value337 bool,
    value338 bool,
    value339 bool,
    value340 bool,
    value341 bool,
    value342 bool,
    value343 bool,
    value344 bool,
    value345 bool,
    value346 bool,
    value347 bool,
    value348 bool,
    value349 bool,
    value350 bool,
    value351 bool,
    value352 bool,
    value353 bool,
    value354 bool,
    value355 bool,
    value356 bool,
    value357 bool,
    value358 bool,
    value359 bool,
    value360 bool,
    value361 bool,
    value362 bool,
    value363 bool,
    value364 bool,
    value365 bool,
    value366 bool,
    value367 bool,
    value368 bool,
    value369 bool,
    value370 bool,
    value371 bool,
    value372 bool,
    value373 bool,
    value374 bool,
    value375 bool,
    value376 bool,
    value377 bool,
    value378 bool,
    value379 bool,
    value380 bool,
    value381 bool,
    value382 bool,
    value383 bool,
    value384 bool,
    value385 bool,
    value386 bool,
    value387 bool,
    value388 bool,
    value389 bool,
    value390 bool,
    value391 bool,
    value392 bool,
    value393 bool,
    value394 bool,
    value395 bool,
    value396 bool,
    value397 bool,
    value398 bool,
    value399 bool,
    value400 bool,
    value401 varchar[],
    value402 varchar[],
    value403 varchar[],
    value404 varchar[],
    value405 varchar[],
    value406 varchar[],
    value407 varchar[],
    value408 varchar[],
    value409 varchar[],
    value410 varchar[],
    value411 varchar[],
    value412 varchar[],
    value413 varchar[],
    value414 varchar[],
    value415 varchar[],
    value416 varchar[],
    value417 varchar[],
    value418 varchar[],
    value419 varchar[],
    value420 varchar[],
    value421 varchar[],
    value422 varchar[],
    value423 varchar[],
    value424 varchar[],
    value425 varchar[],
    value426 varchar[],
    value427 varchar[],
    value428 varchar[],
    value429 varchar[],
    value430 varchar[],
    value431 varchar[],
    value432 varchar[],
    value433 varchar[],
    value434 varchar[],
    value435 varchar[],
    value436 varchar[],
    value437 varchar[],
    value438 varchar[],
    value439 varchar[],
    value440 varchar[],
    value441 varchar[],
    value442 varchar[],
    value443 varchar[],
    value444 varchar[],
    value445 varchar[],
    value446 varchar[],
    value447 varchar[],
    value448 varchar[],
    value449 varchar[],
    value450 varchar[],
    value451 varchar[],
    value452 varchar[],
    value453 varchar[],
    value454 varchar[],
    value455 varchar[],
    value456 varchar[],
    value457 varchar[],
    value458 varchar[],
    value459 varchar[],
    value460 varchar[],
    value461 varchar[],
    value462 varchar[],
    value463 varchar[],
    value464 varchar[],
    value465 varchar[],
    value466 varchar[],
    value467 varchar[],
    value468 varchar[],
    value469 varchar[],
    value470 varchar[],
    value471 varchar[],
    value472 varchar[],
    value473 varchar[],
    value474 varchar[],
    value475 varchar[],
    value476 varchar[],
    value477 varchar[],
    value478 varchar[],
    value479 varchar[],
    value480 varchar[],
    value481 varchar[],
    value482 varchar[],
    value483 varchar[],
    value484 varchar[],
    value485 varchar[],
    value486 varchar[],
    value487 varchar[],
    value488 varchar[],
    value489 varchar[],
    value490 varchar[],
    value491 varchar[],
    value492 varchar[],
    value493 varchar[],
    value494 varchar[],
    value495 varchar[],
    value496 varchar[],
    value497 varchar[],
    value498 varchar[],
    value499 varchar[],
    value500 varchar[],
    value501 varchar[],
    value502 varchar[],
    value503 varchar[],
    value504 varchar[],
    value505 varchar[],
    value506 varchar[],
    value507 varchar[],
    value508 varchar[],
    value509 varchar[],
    value510 varchar[],
    value511 varchar[],
    value512 varchar[],
    value513 varchar[],
    value514 varchar[],
    value515 varchar[],
    value516 varchar[],
    value517 varchar[],
    value518 varchar[],
    value519 varchar[],
    value520 varchar[],
    value521 varchar[],
    value522 varchar[],
    value523 varchar[],
    value524 varchar[],
    value525 varchar[],
    value526 varchar[],
    value527 varchar[],
    value528 varchar[],
    value529 varchar[],
    value530 varchar[],
    value531 varchar[],
    value532 varchar[],
    value533 varchar[],
    value534 varchar[],
    value535 varchar[],
    value536 varchar[],
    value537 varchar[],
    value538 varchar[],
    value539 varchar[],
    value540 varchar[],
    value541 varchar[],
    value542 varchar[],
    value543 varchar[],
    value544 varchar[],
    value545 varchar[],
    value546 varchar[],
    value547 varchar[],
    value548 varchar[],
    value549 varchar[],
    value550 varchar[],
    value551 varchar[],
    value552 varchar[],
    value553 varchar[],
    value554 varchar[],
    value555 varchar[],
    value556 varchar[],
    value557 varchar[],
    value558 varchar[],
    value559 varchar[],
    value560 varchar[],
    value561 varchar[],
    value562 varchar[],
    value563 varchar[],
    value564 varchar[],
    value565 varchar[],
    value566 varchar[],
    value567 varchar[],
    value568 varchar[],
    value569 varchar[],
    value570 varchar[],
    value571 varchar[],
    value572 varchar[],
    value573 varchar[],
    value574 varchar[],
    value575 varchar[],
    value576 varchar[],
    value577 varchar[],
    value578 varchar[],
    value579 varchar[],
    value580 varchar[],
    value581 varchar[],
    value582 varchar[],
    value583 varchar[],
    value584 varchar[],
    value585 varchar[],
    value586 varchar[],
    value587 varchar[],
    value588 varchar[],
    value589 varchar[],
    value590 varchar[],
    value591 varchar[],
    value592 varchar[],
    value593 varchar[],
    value594 varchar[],
    value595 varchar[],
    value596 varchar[],
    value597 varchar[],
    value598 varchar[],
    value599 varchar[],
    value600 varchar[],
    value601 varchar,
    value602 varchar,
    value603 varchar,
    value604 varchar,
    value605 varchar,
    value606 varchar,
    value607 varchar,
    value608 varchar,
    value609 varchar,
    value610 varchar,
    value611 varchar,
    value612 varchar,
    value613 varchar,
    value614 varchar,
    value615 varchar,
    value616 varchar,
    value617 varchar,
    value618 varchar,
    value619 varchar,
    value620 varchar,
    value621 varchar,
    value622 varchar,
    value623 varchar,
    value624 varchar,
    value625 varchar,
    value626 varchar,
    value627 varchar,
    value628 varchar,
    value629 varchar,
    value630 varchar,
    value631 varchar,
    value632 varchar,
    value633 varchar,
    value634 varchar,
    value635 varchar,
    value636 varchar,
    value637 varchar,
    value638 varchar,
    value639 varchar,
    value640 varchar,
    value641 varchar,
    value642 varchar,
    value643 varchar,
    value644 varchar,
    value645 varchar,
    value646 varchar,
    value647 varchar,
    value648 varchar,
    value649 varchar,
    value650 varchar,
    value651 varchar,
    value652 varchar,
    value653 varchar,
    value654 varchar,
    value655 varchar,
    value656 varchar,
    value657 varchar,
    value658 varchar,
    value659 varchar,
    value660 varchar,
    value661 varchar,
    value662 varchar,
    value663 varchar,
    value664 varchar,
    value665 varchar,
    value666 varchar,
    value667 varchar,
    value668 varchar,
    value669 varchar,
    value670 varchar,
    value671 varchar,
    value672 varchar,
    value673 varchar,
    value674 varchar,
    value675 varchar,
    value676 varchar,
    value677 varchar,
    value678 varchar,
    value679 varchar,
    value680 varchar,
    value681 varchar,
    value682 varchar,
    value683 varchar,
    value684 varchar,
    value685 varchar,
    value686 varchar,
    value687 varchar,
    value688 varchar,
    value689 varchar,
    value690 varchar,
    value691 varchar,
    value692 varchar,
    value693 varchar,
    value694 varchar,
    value695 varchar,
    value696 varchar,
    value697 varchar,
    value698 varchar,
    value699 varchar,
    value700 varchar,
    value701 varchar,
    value702 varchar,
    value703 varchar,
    value704 varchar,
    value705 varchar,
    value706 varchar,
    value707 varchar,
    value708 varchar,
    value709 varchar,
    value710 varchar,
    value711 varchar,
    value712 varchar,
    value713 varchar,
    value714 varchar,
    value715 varchar,
    value716 varchar,
    value717 varchar,
    value718 varchar,
    value719 varchar,
    value720 varchar,
    value721 varchar,
    value722 varchar,
    value723 varchar,
    value724 varchar,
    value725 varchar,
    value726 varchar,
    value727 varchar,
    value728 varchar,
    value729 varchar,
    value730 varchar,
    value731 varchar,
    value732 varchar,
    value733 varchar,
    value734 varchar,
    value735 varchar,
    value736 varchar,
    value737 varchar,
    value738 varchar,
    value739 varchar,
    value740 varchar,
    value741 varchar,
    value742 varchar,
    value743 varchar,
    value744 varchar,
    value745 varchar,
    value746 varchar,
    value747 varchar,
    value748 varchar,
    value749 varchar,
    value750 varchar,
    value751 varchar,
    value752 varchar,
    value753 varchar,
    value754 varchar,
    value755 varchar,
    value756 varchar,
    value757 varchar,
    value758 varchar,
    value759 varchar,
    value760 varchar,
    value761 varchar,
    value762 varchar,
    value763 varchar,
    value764 varchar,
    value765 varchar,
    value766 varchar,
    value767 varchar,
    value768 varchar,
    value769 varchar,
    value770 varchar,
    value771 varchar,
    value772 varchar,
    value773 varchar,
    value774 varchar,
    value775 varchar,
    value776 varchar,
    value777 varchar,
    value778 varchar,
    value779 varchar,
    value780 varchar,
    value781 varchar,
    value782 varchar,
    value783 varchar,
    value784 varchar,
    value785 varchar,
    value786 varchar,
    value787 varchar,
    value788 varchar,
    value789 varchar,
    value790 varchar,
    value791 varchar,
    value792 varchar,
    value793 varchar,
    value794 varchar,
    value795 varchar,
    value796 varchar,
    value797 varchar,
    value798 varchar,
    value799 varchar,
    value800 varchar,
    CONSTRAINT "biz_public_employee_ext_2_pkey" PRIMARY KEY ( "id", "tenant_id" )
);

DROP TRIGGER IF EXISTS x_audit_changes ON biz_public_employee_ext_2;

CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON biz_public_employee_ext_2 FOR EACH ROW EXECUTE PROCEDURE public.f_change_detail('id','tenant_id','object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON biz_public_employee_ext_2;

CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON biz_public_employee_ext_2 FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();

CREATE TABLE IF NOT EXISTS biz_public_employee_ext_1(
    "id" VARCHAR (64)  NOT NULL,
    "tenant_id" VARCHAR (16)  NOT NULL,
    "created_by" VARCHAR (32) ,
    "object_describe_api_name" VARCHAR (100) ,
    "data_own_department" VARCHAR (32) ,
    "data_own_organization" VARCHAR (32) ,
    "out_data_own_department" varchar(64) ,
    "out_data_own_organization" varchar(64) ,
    "last_modified_by" VARCHAR(50),
    "out_tenant_id" VARCHAR(50),
    "out_owner" VARCHAR(50),
    "owner" VARCHAR (128) ,
    "dimension_d1" varchar[],
    "dimension_d2" varchar[],
    "dimension_d3" varchar[],
    "data_auth_id" int4,
    "out_data_auth_id" int4,
    "data_auth_code" varchar(64),
    "out_data_auth_code" varchar(64),
    "sys_modified_time" bigint ,
    value1 numeric,
    value2 numeric,
    value3 numeric,
    value4 numeric,
    value5 numeric,
    value6 numeric,
    value7 numeric,
    value8 numeric,
    value9 numeric,
    value10 numeric,
    value11 numeric,
    value12 numeric,
    value13 numeric,
    value14 numeric,
    value15 numeric,
    value16 numeric,
    value17 numeric,
    value18 numeric,
    value19 numeric,
    value20 numeric,
    value21 numeric,
    value22 numeric,
    value23 numeric,
    value24 numeric,
    value25 numeric,
    value26 numeric,
    value27 numeric,
    value28 numeric,
    value29 numeric,
    value30 numeric,
    value31 numeric,
    value32 numeric,
    value33 numeric,
    value34 numeric,
    value35 numeric,
    value36 numeric,
    value37 numeric,
    value38 numeric,
    value39 numeric,
    value40 numeric,
    value41 numeric,
    value42 numeric,
    value43 numeric,
    value44 numeric,
    value45 numeric,
    value46 numeric,
    value47 numeric,
    value48 numeric,
    value49 numeric,
    value50 numeric,
    value51 numeric,
    value52 numeric,
    value53 numeric,
    value54 numeric,
    value55 numeric,
    value56 numeric,
    value57 numeric,
    value58 numeric,
    value59 numeric,
    value60 numeric,
    value61 numeric,
    value62 numeric,
    value63 numeric,
    value64 numeric,
    value65 numeric,
    value66 numeric,
    value67 numeric,
    value68 numeric,
    value69 numeric,
    value70 numeric,
    value71 numeric,
    value72 numeric,
    value73 numeric,
    value74 numeric,
    value75 numeric,
    value76 numeric,
    value77 numeric,
    value78 numeric,
    value79 numeric,
    value80 numeric,
    value81 numeric,
    value82 numeric,
    value83 numeric,
    value84 numeric,
    value85 numeric,
    value86 numeric,
    value87 numeric,
    value88 numeric,
    value89 numeric,
    value90 numeric,
    value91 numeric,
    value92 numeric,
    value93 numeric,
    value94 numeric,
    value95 numeric,
    value96 numeric,
    value97 numeric,
    value98 numeric,
    value99 numeric,
    value100 numeric,
    value101 numeric,
    value102 numeric,
    value103 numeric,
    value104 numeric,
    value105 numeric,
    value106 numeric,
    value107 numeric,
    value108 numeric,
    value109 numeric,
    value110 numeric,
    value111 numeric,
    value112 numeric,
    value113 numeric,
    value114 numeric,
    value115 numeric,
    value116 numeric,
    value117 numeric,
    value118 numeric,
    value119 numeric,
    value120 numeric,
    value121 numeric,
    value122 numeric,
    value123 numeric,
    value124 numeric,
    value125 numeric,
    value126 numeric,
    value127 numeric,
    value128 numeric,
    value129 numeric,
    value130 numeric,
    value131 numeric,
    value132 numeric,
    value133 numeric,
    value134 numeric,
    value135 numeric,
    value136 numeric,
    value137 numeric,
    value138 numeric,
    value139 numeric,
    value140 numeric,
    value141 numeric,
    value142 numeric,
    value143 numeric,
    value144 numeric,
    value145 numeric,
    value146 numeric,
    value147 numeric,
    value148 numeric,
    value149 numeric,
    value150 numeric,
    value151 numeric,
    value152 numeric,
    value153 numeric,
    value154 numeric,
    value155 numeric,
    value156 numeric,
    value157 numeric,
    value158 numeric,
    value159 numeric,
    value160 numeric,
    value161 numeric,
    value162 numeric,
    value163 numeric,
    value164 numeric,
    value165 numeric,
    value166 numeric,
    value167 numeric,
    value168 numeric,
    value169 numeric,
    value170 numeric,
    value171 numeric,
    value172 numeric,
    value173 numeric,
    value174 numeric,
    value175 numeric,
    value176 numeric,
    value177 numeric,
    value178 numeric,
    value179 numeric,
    value180 numeric,
    value181 numeric,
    value182 numeric,
    value183 numeric,
    value184 numeric,
    value185 numeric,
    value186 numeric,
    value187 numeric,
    value188 numeric,
    value189 numeric,
    value190 numeric,
    value191 numeric,
    value192 numeric,
    value193 numeric,
    value194 numeric,
    value195 numeric,
    value196 numeric,
    value197 numeric,
    value198 numeric,
    value199 numeric,
    value200 numeric,
    value201 bool,
    value202 bool,
    value203 bool,
    value204 bool,
    value205 bool,
    value206 bool,
    value207 bool,
    value208 bool,
    value209 bool,
    value210 bool,
    value211 bool,
    value212 bool,
    value213 bool,
    value214 bool,
    value215 bool,
    value216 bool,
    value217 bool,
    value218 bool,
    value219 bool,
    value220 bool,
    value221 bool,
    value222 bool,
    value223 bool,
    value224 bool,
    value225 bool,
    value226 bool,
    value227 bool,
    value228 bool,
    value229 bool,
    value230 bool,
    value231 bool,
    value232 bool,
    value233 bool,
    value234 bool,
    value235 bool,
    value236 bool,
    value237 bool,
    value238 bool,
    value239 bool,
    value240 bool,
    value241 bool,
    value242 bool,
    value243 bool,
    value244 bool,
    value245 bool,
    value246 bool,
    value247 bool,
    value248 bool,
    value249 bool,
    value250 bool,
    value251 bool,
    value252 bool,
    value253 bool,
    value254 bool,
    value255 bool,
    value256 bool,
    value257 bool,
    value258 bool,
    value259 bool,
    value260 bool,
    value261 bool,
    value262 bool,
    value263 bool,
    value264 bool,
    value265 bool,
    value266 bool,
    value267 bool,
    value268 bool,
    value269 bool,
    value270 bool,
    value271 bool,
    value272 bool,
    value273 bool,
    value274 bool,
    value275 bool,
    value276 bool,
    value277 bool,
    value278 bool,
    value279 bool,
    value280 bool,
    value281 bool,
    value282 bool,
    value283 bool,
    value284 bool,
    value285 bool,
    value286 bool,
    value287 bool,
    value288 bool,
    value289 bool,
    value290 bool,
    value291 bool,
    value292 bool,
    value293 bool,
    value294 bool,
    value295 bool,
    value296 bool,
    value297 bool,
    value298 bool,
    value299 bool,
    value300 bool,
    value301 bool,
    value302 bool,
    value303 bool,
    value304 bool,
    value305 bool,
    value306 bool,
    value307 bool,
    value308 bool,
    value309 bool,
    value310 bool,
    value311 bool,
    value312 bool,
    value313 bool,
    value314 bool,
    value315 bool,
    value316 bool,
    value317 bool,
    value318 bool,
    value319 bool,
    value320 bool,
    value321 bool,
    value322 bool,
    value323 bool,
    value324 bool,
    value325 bool,
    value326 bool,
    value327 bool,
    value328 bool,
    value329 bool,
    value330 bool,
    value331 bool,
    value332 bool,
    value333 bool,
    value334 bool,
    value335 bool,
    value336 bool,
    value337 bool,
    value338 bool,
    value339 bool,
    value340 bool,
    value341 bool,
    value342 bool,
    value343 bool,
    value344 bool,
    value345 bool,
    value346 bool,
    value347 bool,
    value348 bool,
    value349 bool,
    value350 bool,
    value351 bool,
    value352 bool,
    value353 bool,
    value354 bool,
    value355 bool,
    value356 bool,
    value357 bool,
    value358 bool,
    value359 bool,
    value360 bool,
    value361 bool,
    value362 bool,
    value363 bool,
    value364 bool,
    value365 bool,
    value366 bool,
    value367 bool,
    value368 bool,
    value369 bool,
    value370 bool,
    value371 bool,
    value372 bool,
    value373 bool,
    value374 bool,
    value375 bool,
    value376 bool,
    value377 bool,
    value378 bool,
    value379 bool,
    value380 bool,
    value381 bool,
    value382 bool,
    value383 bool,
    value384 bool,
    value385 bool,
    value386 bool,
    value387 bool,
    value388 bool,
    value389 bool,
    value390 bool,
    value391 bool,
    value392 bool,
    value393 bool,
    value394 bool,
    value395 bool,
    value396 bool,
    value397 bool,
    value398 bool,
    value399 bool,
    value400 bool,
    value401 varchar[],
    value402 varchar[],
    value403 varchar[],
    value404 varchar[],
    value405 varchar[],
    value406 varchar[],
    value407 varchar[],
    value408 varchar[],
    value409 varchar[],
    value410 varchar[],
    value411 varchar[],
    value412 varchar[],
    value413 varchar[],
    value414 varchar[],
    value415 varchar[],
    value416 varchar[],
    value417 varchar[],
    value418 varchar[],
    value419 varchar[],
    value420 varchar[],
    value421 varchar[],
    value422 varchar[],
    value423 varchar[],
    value424 varchar[],
    value425 varchar[],
    value426 varchar[],
    value427 varchar[],
    value428 varchar[],
    value429 varchar[],
    value430 varchar[],
    value431 varchar[],
    value432 varchar[],
    value433 varchar[],
    value434 varchar[],
    value435 varchar[],
    value436 varchar[],
    value437 varchar[],
    value438 varchar[],
    value439 varchar[],
    value440 varchar[],
    value441 varchar[],
    value442 varchar[],
    value443 varchar[],
    value444 varchar[],
    value445 varchar[],
    value446 varchar[],
    value447 varchar[],
    value448 varchar[],
    value449 varchar[],
    value450 varchar[],
    value451 varchar[],
    value452 varchar[],
    value453 varchar[],
    value454 varchar[],
    value455 varchar[],
    value456 varchar[],
    value457 varchar[],
    value458 varchar[],
    value459 varchar[],
    value460 varchar[],
    value461 varchar[],
    value462 varchar[],
    value463 varchar[],
    value464 varchar[],
    value465 varchar[],
    value466 varchar[],
    value467 varchar[],
    value468 varchar[],
    value469 varchar[],
    value470 varchar[],
    value471 varchar[],
    value472 varchar[],
    value473 varchar[],
    value474 varchar[],
    value475 varchar[],
    value476 varchar[],
    value477 varchar[],
    value478 varchar[],
    value479 varchar[],
    value480 varchar[],
    value481 varchar[],
    value482 varchar[],
    value483 varchar[],
    value484 varchar[],
    value485 varchar[],
    value486 varchar[],
    value487 varchar[],
    value488 varchar[],
    value489 varchar[],
    value490 varchar[],
    value491 varchar[],
    value492 varchar[],
    value493 varchar[],
    value494 varchar[],
    value495 varchar[],
    value496 varchar[],
    value497 varchar[],
    value498 varchar[],
    value499 varchar[],
    value500 varchar[],
    value501 varchar[],
    value502 varchar[],
    value503 varchar[],
    value504 varchar[],
    value505 varchar[],
    value506 varchar[],
    value507 varchar[],
    value508 varchar[],
    value509 varchar[],
    value510 varchar[],
    value511 varchar[],
    value512 varchar[],
    value513 varchar[],
    value514 varchar[],
    value515 varchar[],
    value516 varchar[],
    value517 varchar[],
    value518 varchar[],
    value519 varchar[],
    value520 varchar[],
    value521 varchar[],
    value522 varchar[],
    value523 varchar[],
    value524 varchar[],
    value525 varchar[],
    value526 varchar[],
    value527 varchar[],
    value528 varchar[],
    value529 varchar[],
    value530 varchar[],
    value531 varchar[],
    value532 varchar[],
    value533 varchar[],
    value534 varchar[],
    value535 varchar[],
    value536 varchar[],
    value537 varchar[],
    value538 varchar[],
    value539 varchar[],
    value540 varchar[],
    value541 varchar[],
    value542 varchar[],
    value543 varchar[],
    value544 varchar[],
    value545 varchar[],
    value546 varchar[],
    value547 varchar[],
    value548 varchar[],
    value549 varchar[],
    value550 varchar[],
    value551 varchar[],
    value552 varchar[],
    value553 varchar[],
    value554 varchar[],
    value555 varchar[],
    value556 varchar[],
    value557 varchar[],
    value558 varchar[],
    value559 varchar[],
    value560 varchar[],
    value561 varchar[],
    value562 varchar[],
    value563 varchar[],
    value564 varchar[],
    value565 varchar[],
    value566 varchar[],
    value567 varchar[],
    value568 varchar[],
    value569 varchar[],
    value570 varchar[],
    value571 varchar[],
    value572 varchar[],
    value573 varchar[],
    value574 varchar[],
    value575 varchar[],
    value576 varchar[],
    value577 varchar[],
    value578 varchar[],
    value579 varchar[],
    value580 varchar[],
    value581 varchar[],
    value582 varchar[],
    value583 varchar[],
    value584 varchar[],
    value585 varchar[],
    value586 varchar[],
    value587 varchar[],
    value588 varchar[],
    value589 varchar[],
    value590 varchar[],
    value591 varchar[],
    value592 varchar[],
    value593 varchar[],
    value594 varchar[],
    value595 varchar[],
    value596 varchar[],
    value597 varchar[],
    value598 varchar[],
    value599 varchar[],
    value600 varchar[],
    value601 varchar,
    value602 varchar,
    value603 varchar,
    value604 varchar,
    value605 varchar,
    value606 varchar,
    value607 varchar,
    value608 varchar,
    value609 varchar,
    value610 varchar,
    value611 varchar,
    value612 varchar,
    value613 varchar,
    value614 varchar,
    value615 varchar,
    value616 varchar,
    value617 varchar,
    value618 varchar,
    value619 varchar,
    value620 varchar,
    value621 varchar,
    value622 varchar,
    value623 varchar,
    value624 varchar,
    value625 varchar,
    value626 varchar,
    value627 varchar,
    value628 varchar,
    value629 varchar,
    value630 varchar,
    value631 varchar,
    value632 varchar,
    value633 varchar,
    value634 varchar,
    value635 varchar,
    value636 varchar,
    value637 varchar,
    value638 varchar,
    value639 varchar,
    value640 varchar,
    value641 varchar,
    value642 varchar,
    value643 varchar,
    value644 varchar,
    value645 varchar,
    value646 varchar,
    value647 varchar,
    value648 varchar,
    value649 varchar,
    value650 varchar,
    value651 varchar,
    value652 varchar,
    value653 varchar,
    value654 varchar,
    value655 varchar,
    value656 varchar,
    value657 varchar,
    value658 varchar,
    value659 varchar,
    value660 varchar,
    value661 varchar,
    value662 varchar,
    value663 varchar,
    value664 varchar,
    value665 varchar,
    value666 varchar,
    value667 varchar,
    value668 varchar,
    value669 varchar,
    value670 varchar,
    value671 varchar,
    value672 varchar,
    value673 varchar,
    value674 varchar,
    value675 varchar,
    value676 varchar,
    value677 varchar,
    value678 varchar,
    value679 varchar,
    value680 varchar,
    value681 varchar,
    value682 varchar,
    value683 varchar,
    value684 varchar,
    value685 varchar,
    value686 varchar,
    value687 varchar,
    value688 varchar,
    value689 varchar,
    value690 varchar,
    value691 varchar,
    value692 varchar,
    value693 varchar,
    value694 varchar,
    value695 varchar,
    value696 varchar,
    value697 varchar,
    value698 varchar,
    value699 varchar,
    value700 varchar,
    value701 varchar,
    value702 varchar,
    value703 varchar,
    value704 varchar,
    value705 varchar,
    value706 varchar,
    value707 varchar,
    value708 varchar,
    value709 varchar,
    value710 varchar,
    value711 varchar,
    value712 varchar,
    value713 varchar,
    value714 varchar,
    value715 varchar,
    value716 varchar,
    value717 varchar,
    value718 varchar,
    value719 varchar,
    value720 varchar,
    value721 varchar,
    value722 varchar,
    value723 varchar,
    value724 varchar,
    value725 varchar,
    value726 varchar,
    value727 varchar,
    value728 varchar,
    value729 varchar,
    value730 varchar,
    value731 varchar,
    value732 varchar,
    value733 varchar,
    value734 varchar,
    value735 varchar,
    value736 varchar,
    value737 varchar,
    value738 varchar,
    value739 varchar,
    value740 varchar,
    value741 varchar,
    value742 varchar,
    value743 varchar,
    value744 varchar,
    value745 varchar,
    value746 varchar,
    value747 varchar,
    value748 varchar,
    value749 varchar,
    value750 varchar,
    value751 varchar,
    value752 varchar,
    value753 varchar,
    value754 varchar,
    value755 varchar,
    value756 varchar,
    value757 varchar,
    value758 varchar,
    value759 varchar,
    value760 varchar,
    value761 varchar,
    value762 varchar,
    value763 varchar,
    value764 varchar,
    value765 varchar,
    value766 varchar,
    value767 varchar,
    value768 varchar,
    value769 varchar,
    value770 varchar,
    value771 varchar,
    value772 varchar,
    value773 varchar,
    value774 varchar,
    value775 varchar,
    value776 varchar,
    value777 varchar,
    value778 varchar,
    value779 varchar,
    value780 varchar,
    value781 varchar,
    value782 varchar,
    value783 varchar,
    value784 varchar,
    value785 varchar,
    value786 varchar,
    value787 varchar,
    value788 varchar,
    value789 varchar,
    value790 varchar,
    value791 varchar,
    value792 varchar,
    value793 varchar,
    value794 varchar,
    value795 varchar,
    value796 varchar,
    value797 varchar,
    value798 varchar,
    value799 varchar,
    value800 varchar,
    CONSTRAINT "biz_public_employee_ext_1_pkey" PRIMARY KEY ( "id", "tenant_id" )
);

DROP TRIGGER IF EXISTS x_audit_changes ON biz_public_employee_ext_1;

CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON biz_public_employee_ext_1 FOR EACH ROW EXECUTE PROCEDURE public.f_change_detail('id','tenant_id','object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON biz_public_employee_ext_1;

CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON biz_public_employee_ext_1 FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();

CREATE TABLE IF NOT EXISTS biz_public_employee_ext_0(
    "id" VARCHAR (64)  NOT NULL,
    "tenant_id" VARCHAR (16)  NOT NULL,
    "created_by" VARCHAR (32) ,
    "object_describe_api_name" VARCHAR (100) ,
    "data_own_department" VARCHAR (32) ,
    "data_own_organization" VARCHAR (32) ,
    "out_data_own_department" varchar(64) ,
    "out_data_own_organization" varchar(64) ,
    "last_modified_by" VARCHAR(50),
    "out_tenant_id" VARCHAR(50),
    "out_owner" VARCHAR(50),
    "owner" VARCHAR (128) ,
    "dimension_d1" varchar[],
    "dimension_d2" varchar[],
    "dimension_d3" varchar[],
    "data_auth_id" int4,
    "out_data_auth_id" int4,
    "data_auth_code" varchar(64),
    "out_data_auth_code" varchar(64),
    "sys_modified_time" bigint ,
    value1 numeric,
    value2 numeric,
    value3 numeric,
    value4 numeric,
    value5 numeric,
    value6 numeric,
    value7 numeric,
    value8 numeric,
    value9 numeric,
    value10 numeric,
    value11 numeric,
    value12 numeric,
    value13 numeric,
    value14 numeric,
    value15 numeric,
    value16 numeric,
    value17 numeric,
    value18 numeric,
    value19 numeric,
    value20 numeric,
    value21 numeric,
    value22 numeric,
    value23 numeric,
    value24 numeric,
    value25 numeric,
    value26 numeric,
    value27 numeric,
    value28 numeric,
    value29 numeric,
    value30 numeric,
    value31 numeric,
    value32 numeric,
    value33 numeric,
    value34 numeric,
    value35 numeric,
    value36 numeric,
    value37 numeric,
    value38 numeric,
    value39 numeric,
    value40 numeric,
    value41 numeric,
    value42 numeric,
    value43 numeric,
    value44 numeric,
    value45 numeric,
    value46 numeric,
    value47 numeric,
    value48 numeric,
    value49 numeric,
    value50 numeric,
    value51 numeric,
    value52 numeric,
    value53 numeric,
    value54 numeric,
    value55 numeric,
    value56 numeric,
    value57 numeric,
    value58 numeric,
    value59 numeric,
    value60 numeric,
    value61 numeric,
    value62 numeric,
    value63 numeric,
    value64 numeric,
    value65 numeric,
    value66 numeric,
    value67 numeric,
    value68 numeric,
    value69 numeric,
    value70 numeric,
    value71 numeric,
    value72 numeric,
    value73 numeric,
    value74 numeric,
    value75 numeric,
    value76 numeric,
    value77 numeric,
    value78 numeric,
    value79 numeric,
    value80 numeric,
    value81 numeric,
    value82 numeric,
    value83 numeric,
    value84 numeric,
    value85 numeric,
    value86 numeric,
    value87 numeric,
    value88 numeric,
    value89 numeric,
    value90 numeric,
    value91 numeric,
    value92 numeric,
    value93 numeric,
    value94 numeric,
    value95 numeric,
    value96 numeric,
    value97 numeric,
    value98 numeric,
    value99 numeric,
    value100 numeric,
    value101 numeric,
    value102 numeric,
    value103 numeric,
    value104 numeric,
    value105 numeric,
    value106 numeric,
    value107 numeric,
    value108 numeric,
    value109 numeric,
    value110 numeric,
    value111 numeric,
    value112 numeric,
    value113 numeric,
    value114 numeric,
    value115 numeric,
    value116 numeric,
    value117 numeric,
    value118 numeric,
    value119 numeric,
    value120 numeric,
    value121 numeric,
    value122 numeric,
    value123 numeric,
    value124 numeric,
    value125 numeric,
    value126 numeric,
    value127 numeric,
    value128 numeric,
    value129 numeric,
    value130 numeric,
    value131 numeric,
    value132 numeric,
    value133 numeric,
    value134 numeric,
    value135 numeric,
    value136 numeric,
    value137 numeric,
    value138 numeric,
    value139 numeric,
    value140 numeric,
    value141 numeric,
    value142 numeric,
    value143 numeric,
    value144 numeric,
    value145 numeric,
    value146 numeric,
    value147 numeric,
    value148 numeric,
    value149 numeric,
    value150 numeric,
    value151 numeric,
    value152 numeric,
    value153 numeric,
    value154 numeric,
    value155 numeric,
    value156 numeric,
    value157 numeric,
    value158 numeric,
    value159 numeric,
    value160 numeric,
    value161 numeric,
    value162 numeric,
    value163 numeric,
    value164 numeric,
    value165 numeric,
    value166 numeric,
    value167 numeric,
    value168 numeric,
    value169 numeric,
    value170 numeric,
    value171 numeric,
    value172 numeric,
    value173 numeric,
    value174 numeric,
    value175 numeric,
    value176 numeric,
    value177 numeric,
    value178 numeric,
    value179 numeric,
    value180 numeric,
    value181 numeric,
    value182 numeric,
    value183 numeric,
    value184 numeric,
    value185 numeric,
    value186 numeric,
    value187 numeric,
    value188 numeric,
    value189 numeric,
    value190 numeric,
    value191 numeric,
    value192 numeric,
    value193 numeric,
    value194 numeric,
    value195 numeric,
    value196 numeric,
    value197 numeric,
    value198 numeric,
    value199 numeric,
    value200 numeric,
    value201 bool,
    value202 bool,
    value203 bool,
    value204 bool,
    value205 bool,
    value206 bool,
    value207 bool,
    value208 bool,
    value209 bool,
    value210 bool,
    value211 bool,
    value212 bool,
    value213 bool,
    value214 bool,
    value215 bool,
    value216 bool,
    value217 bool,
    value218 bool,
    value219 bool,
    value220 bool,
    value221 bool,
    value222 bool,
    value223 bool,
    value224 bool,
    value225 bool,
    value226 bool,
    value227 bool,
    value228 bool,
    value229 bool,
    value230 bool,
    value231 bool,
    value232 bool,
    value233 bool,
    value234 bool,
    value235 bool,
    value236 bool,
    value237 bool,
    value238 bool,
    value239 bool,
    value240 bool,
    value241 bool,
    value242 bool,
    value243 bool,
    value244 bool,
    value245 bool,
    value246 bool,
    value247 bool,
    value248 bool,
    value249 bool,
    value250 bool,
    value251 bool,
    value252 bool,
    value253 bool,
    value254 bool,
    value255 bool,
    value256 bool,
    value257 bool,
    value258 bool,
    value259 bool,
    value260 bool,
    value261 bool,
    value262 bool,
    value263 bool,
    value264 bool,
    value265 bool,
    value266 bool,
    value267 bool,
    value268 bool,
    value269 bool,
    value270 bool,
    value271 bool,
    value272 bool,
    value273 bool,
    value274 bool,
    value275 bool,
    value276 bool,
    value277 bool,
    value278 bool,
    value279 bool,
    value280 bool,
    value281 bool,
    value282 bool,
    value283 bool,
    value284 bool,
    value285 bool,
    value286 bool,
    value287 bool,
    value288 bool,
    value289 bool,
    value290 bool,
    value291 bool,
    value292 bool,
    value293 bool,
    value294 bool,
    value295 bool,
    value296 bool,
    value297 bool,
    value298 bool,
    value299 bool,
    value300 bool,
    value301 bool,
    value302 bool,
    value303 bool,
    value304 bool,
    value305 bool,
    value306 bool,
    value307 bool,
    value308 bool,
    value309 bool,
    value310 bool,
    value311 bool,
    value312 bool,
    value313 bool,
    value314 bool,
    value315 bool,
    value316 bool,
    value317 bool,
    value318 bool,
    value319 bool,
    value320 bool,
    value321 bool,
    value322 bool,
    value323 bool,
    value324 bool,
    value325 bool,
    value326 bool,
    value327 bool,
    value328 bool,
    value329 bool,
    value330 bool,
    value331 bool,
    value332 bool,
    value333 bool,
    value334 bool,
    value335 bool,
    value336 bool,
    value337 bool,
    value338 bool,
    value339 bool,
    value340 bool,
    value341 bool,
    value342 bool,
    value343 bool,
    value344 bool,
    value345 bool,
    value346 bool,
    value347 bool,
    value348 bool,
    value349 bool,
    value350 bool,
    value351 bool,
    value352 bool,
    value353 bool,
    value354 bool,
    value355 bool,
    value356 bool,
    value357 bool,
    value358 bool,
    value359 bool,
    value360 bool,
    value361 bool,
    value362 bool,
    value363 bool,
    value364 bool,
    value365 bool,
    value366 bool,
    value367 bool,
    value368 bool,
    value369 bool,
    value370 bool,
    value371 bool,
    value372 bool,
    value373 bool,
    value374 bool,
    value375 bool,
    value376 bool,
    value377 bool,
    value378 bool,
    value379 bool,
    value380 bool,
    value381 bool,
    value382 bool,
    value383 bool,
    value384 bool,
    value385 bool,
    value386 bool,
    value387 bool,
    value388 bool,
    value389 bool,
    value390 bool,
    value391 bool,
    value392 bool,
    value393 bool,
    value394 bool,
    value395 bool,
    value396 bool,
    value397 bool,
    value398 bool,
    value399 bool,
    value400 bool,
    value401 varchar[],
    value402 varchar[],
    value403 varchar[],
    value404 varchar[],
    value405 varchar[],
    value406 varchar[],
    value407 varchar[],
    value408 varchar[],
    value409 varchar[],
    value410 varchar[],
    value411 varchar[],
    value412 varchar[],
    value413 varchar[],
    value414 varchar[],
    value415 varchar[],
    value416 varchar[],
    value417 varchar[],
    value418 varchar[],
    value419 varchar[],
    value420 varchar[],
    value421 varchar[],
    value422 varchar[],
    value423 varchar[],
    value424 varchar[],
    value425 varchar[],
    value426 varchar[],
    value427 varchar[],
    value428 varchar[],
    value429 varchar[],
    value430 varchar[],
    value431 varchar[],
    value432 varchar[],
    value433 varchar[],
    value434 varchar[],
    value435 varchar[],
    value436 varchar[],
    value437 varchar[],
    value438 varchar[],
    value439 varchar[],
    value440 varchar[],
    value441 varchar[],
    value442 varchar[],
    value443 varchar[],
    value444 varchar[],
    value445 varchar[],
    value446 varchar[],
    value447 varchar[],
    value448 varchar[],
    value449 varchar[],
    value450 varchar[],
    value451 varchar[],
    value452 varchar[],
    value453 varchar[],
    value454 varchar[],
    value455 varchar[],
    value456 varchar[],
    value457 varchar[],
    value458 varchar[],
    value459 varchar[],
    value460 varchar[],
    value461 varchar[],
    value462 varchar[],
    value463 varchar[],
    value464 varchar[],
    value465 varchar[],
    value466 varchar[],
    value467 varchar[],
    value468 varchar[],
    value469 varchar[],
    value470 varchar[],
    value471 varchar[],
    value472 varchar[],
    value473 varchar[],
    value474 varchar[],
    value475 varchar[],
    value476 varchar[],
    value477 varchar[],
    value478 varchar[],
    value479 varchar[],
    value480 varchar[],
    value481 varchar[],
    value482 varchar[],
    value483 varchar[],
    value484 varchar[],
    value485 varchar[],
    value486 varchar[],
    value487 varchar[],
    value488 varchar[],
    value489 varchar[],
    value490 varchar[],
    value491 varchar[],
    value492 varchar[],
    value493 varchar[],
    value494 varchar[],
    value495 varchar[],
    value496 varchar[],
    value497 varchar[],
    value498 varchar[],
    value499 varchar[],
    value500 varchar[],
    value501 varchar[],
    value502 varchar[],
    value503 varchar[],
    value504 varchar[],
    value505 varchar[],
    value506 varchar[],
    value507 varchar[],
    value508 varchar[],
    value509 varchar[],
    value510 varchar[],
    value511 varchar[],
    value512 varchar[],
    value513 varchar[],
    value514 varchar[],
    value515 varchar[],
    value516 varchar[],
    value517 varchar[],
    value518 varchar[],
    value519 varchar[],
    value520 varchar[],
    value521 varchar[],
    value522 varchar[],
    value523 varchar[],
    value524 varchar[],
    value525 varchar[],
    value526 varchar[],
    value527 varchar[],
    value528 varchar[],
    value529 varchar[],
    value530 varchar[],
    value531 varchar[],
    value532 varchar[],
    value533 varchar[],
    value534 varchar[],
    value535 varchar[],
    value536 varchar[],
    value537 varchar[],
    value538 varchar[],
    value539 varchar[],
    value540 varchar[],
    value541 varchar[],
    value542 varchar[],
    value543 varchar[],
    value544 varchar[],
    value545 varchar[],
    value546 varchar[],
    value547 varchar[],
    value548 varchar[],
    value549 varchar[],
    value550 varchar[],
    value551 varchar[],
    value552 varchar[],
    value553 varchar[],
    value554 varchar[],
    value555 varchar[],
    value556 varchar[],
    value557 varchar[],
    value558 varchar[],
    value559 varchar[],
    value560 varchar[],
    value561 varchar[],
    value562 varchar[],
    value563 varchar[],
    value564 varchar[],
    value565 varchar[],
    value566 varchar[],
    value567 varchar[],
    value568 varchar[],
    value569 varchar[],
    value570 varchar[],
    value571 varchar[],
    value572 varchar[],
    value573 varchar[],
    value574 varchar[],
    value575 varchar[],
    value576 varchar[],
    value577 varchar[],
    value578 varchar[],
    value579 varchar[],
    value580 varchar[],
    value581 varchar[],
    value582 varchar[],
    value583 varchar[],
    value584 varchar[],
    value585 varchar[],
    value586 varchar[],
    value587 varchar[],
    value588 varchar[],
    value589 varchar[],
    value590 varchar[],
    value591 varchar[],
    value592 varchar[],
    value593 varchar[],
    value594 varchar[],
    value595 varchar[],
    value596 varchar[],
    value597 varchar[],
    value598 varchar[],
    value599 varchar[],
    value600 varchar[],
    value601 varchar,
    value602 varchar,
    value603 varchar,
    value604 varchar,
    value605 varchar,
    value606 varchar,
    value607 varchar,
    value608 varchar,
    value609 varchar,
    value610 varchar,
    value611 varchar,
    value612 varchar,
    value613 varchar,
    value614 varchar,
    value615 varchar,
    value616 varchar,
    value617 varchar,
    value618 varchar,
    value619 varchar,
    value620 varchar,
    value621 varchar,
    value622 varchar,
    value623 varchar,
    value624 varchar,
    value625 varchar,
    value626 varchar,
    value627 varchar,
    value628 varchar,
    value629 varchar,
    value630 varchar,
    value631 varchar,
    value632 varchar,
    value633 varchar,
    value634 varchar,
    value635 varchar,
    value636 varchar,
    value637 varchar,
    value638 varchar,
    value639 varchar,
    value640 varchar,
    value641 varchar,
    value642 varchar,
    value643 varchar,
    value644 varchar,
    value645 varchar,
    value646 varchar,
    value647 varchar,
    value648 varchar,
    value649 varchar,
    value650 varchar,
    value651 varchar,
    value652 varchar,
    value653 varchar,
    value654 varchar,
    value655 varchar,
    value656 varchar,
    value657 varchar,
    value658 varchar,
    value659 varchar,
    value660 varchar,
    value661 varchar,
    value662 varchar,
    value663 varchar,
    value664 varchar,
    value665 varchar,
    value666 varchar,
    value667 varchar,
    value668 varchar,
    value669 varchar,
    value670 varchar,
    value671 varchar,
    value672 varchar,
    value673 varchar,
    value674 varchar,
    value675 varchar,
    value676 varchar,
    value677 varchar,
    value678 varchar,
    value679 varchar,
    value680 varchar,
    value681 varchar,
    value682 varchar,
    value683 varchar,
    value684 varchar,
    value685 varchar,
    value686 varchar,
    value687 varchar,
    value688 varchar,
    value689 varchar,
    value690 varchar,
    value691 varchar,
    value692 varchar,
    value693 varchar,
    value694 varchar,
    value695 varchar,
    value696 varchar,
    value697 varchar,
    value698 varchar,
    value699 varchar,
    value700 varchar,
    value701 varchar,
    value702 varchar,
    value703 varchar,
    value704 varchar,
    value705 varchar,
    value706 varchar,
    value707 varchar,
    value708 varchar,
    value709 varchar,
    value710 varchar,
    value711 varchar,
    value712 varchar,
    value713 varchar,
    value714 varchar,
    value715 varchar,
    value716 varchar,
    value717 varchar,
    value718 varchar,
    value719 varchar,
    value720 varchar,
    value721 varchar,
    value722 varchar,
    value723 varchar,
    value724 varchar,
    value725 varchar,
    value726 varchar,
    value727 varchar,
    value728 varchar,
    value729 varchar,
    value730 varchar,
    value731 varchar,
    value732 varchar,
    value733 varchar,
    value734 varchar,
    value735 varchar,
    value736 varchar,
    value737 varchar,
    value738 varchar,
    value739 varchar,
    value740 varchar,
    value741 varchar,
    value742 varchar,
    value743 varchar,
    value744 varchar,
    value745 varchar,
    value746 varchar,
    value747 varchar,
    value748 varchar,
    value749 varchar,
    value750 varchar,
    value751 varchar,
    value752 varchar,
    value753 varchar,
    value754 varchar,
    value755 varchar,
    value756 varchar,
    value757 varchar,
    value758 varchar,
    value759 varchar,
    value760 varchar,
    value761 varchar,
    value762 varchar,
    value763 varchar,
    value764 varchar,
    value765 varchar,
    value766 varchar,
    value767 varchar,
    value768 varchar,
    value769 varchar,
    value770 varchar,
    value771 varchar,
    value772 varchar,
    value773 varchar,
    value774 varchar,
    value775 varchar,
    value776 varchar,
    value777 varchar,
    value778 varchar,
    value779 varchar,
    value780 varchar,
    value781 varchar,
    value782 varchar,
    value783 varchar,
    value784 varchar,
    value785 varchar,
    value786 varchar,
    value787 varchar,
    value788 varchar,
    value789 varchar,
    value790 varchar,
    value791 varchar,
    value792 varchar,
    value793 varchar,
    value794 varchar,
    value795 varchar,
    value796 varchar,
    value797 varchar,
    value798 varchar,
    value799 varchar,
    value800 varchar,
    CONSTRAINT "biz_public_employee_ext_0_pkey" PRIMARY KEY ( "id", "tenant_id" )
);

DROP TRIGGER IF EXISTS x_audit_changes ON biz_public_employee_ext_0;

CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON biz_public_employee_ext_0 FOR EACH ROW EXECUTE PROCEDURE public.f_change_detail('id','tenant_id','object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON biz_public_employee_ext_0;

CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON biz_public_employee_ext_0 FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();