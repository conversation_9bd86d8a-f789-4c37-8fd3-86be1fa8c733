<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd">

    <bean id="springContextUtils" class="com.facishare.paas.metadata.util.SpringContextUtil"/>

    <bean id="relationChangeMQSender" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer">
        <constructor-arg index="0" value="fs-er-provider-notify-mq"/>
    </bean>

    <bean id="outOrganizationChangeMQSender" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer">
        <constructor-arg index="0" value="fs-er-out-organization-change-mq"/>
    </bean>


    <!-- 监听新服务器消息：172.17.41.36:9876;172.17.41.37:9876 -->
    <bean id="organizationEventProcessorNew" class="com.facishare.rest.account.mq.OrganizationEventProcessor"
          init-method="init"
          p:rocketMQConsumerConfigName="fs-er-organization-mq-notify-new">
    </bean>

    <bean id="paasLicense2EventProcessor" class="com.facishare.rest.account.mq.PaasLicense2EventProcessor"
          init-method="init"
          p:rocketMQConsumerConfigName="fs-er-paas-license-consumer">
    </bean>
    <bean id="wxServiceFansFollowEventProcessor" class="com.facishare.rest.account.mq.WxServiceFansFollowEventProcessor"
          init-method="init"
          p:rocketMQConsumerConfigName="fs-er-fans-follow-mq">
    </bean>
    <!--CRM微信用户客户变化更新客户互联关系 MQ事件消费者-->
    <bean id="CRMMetaDataProcessor" class="com.facishare.rest.account.mq.CRMMetaDataProcessor"
          init-method="init"
          depends-on="springContextUtils"
          p:rocketMQConsumerConfigName="fs-customer-link-account-mq">
    </bean>

    <bean id="paasWorkflowEventProcessor" class="com.facishare.rest.account.mq.PaasWorkflowEventProcessor"
          init-method="init"
          p:rocketMQConsumerConfigName="fs-er-paas-workflow-consumer">
    </bean>

    <!-- mq consumer config -->
    <bean id="interfaceRequestNotifyProcessor"
          class="com.facishare.rest.account.mqtest.RestWebInterfaceRequestNotifyProcessor"
          p:rocketMQConsumerConfigName="rocketmq-consumer.ini"
          p:consumerGroup="consumer.group"
          p:sectionNames="common,name_server_08,consumer_er_inner_big_data_test">
    </bean>


    <!-- 账号合并之后迁移数据权限-->
    <bean id="transferDataPermissionsProcess" class="com.facishare.rest.account.mq.TransferDataPermissionsProcess"
          p:rocketMQConsumerConfigName="rocketmq-consumer.ini"
          p:consumerGroup="consumer.group"
          p:sectionNames="common,name_server_08,consumer_account_merge_event">
    </bean>

    <!-- 数据迁移之后 结果通知 -->
    <bean id="transferDataPermissionMessageProcess"
          class="com.facishare.rest.account.mq.TransferDataPermissionMessageProcess"
          p:rocketMQConsumerConfigName="rocketmq-consumer.ini"
          p:consumerGroup="consumer.group"
          p:sectionNames="common,name_server_07,consumer_transfer_data_permission">
    </bean>


    <!--  互联用户同步roleCode 事件  -->
    <bean id="publicEmployeeSyncRoleProcess" class="com.facishare.rest.account.mq.PublicEmployeeSyncRoleProcess"
          p:rocketMQConsumerConfigName="rocketmq-consumer.ini"
          p:consumerGroup="consumer.group"
          p:sectionNames="common,name_server_01,consumer_sync_public_employee_roles">
    </bean>
    <!--    -->
    <bean id="relevantEventProcess" class="com.facishare.rest.account.mq.RelevantTeamEventProcess"
          p:rocketMQConsumerConfigName="rocketmq-consumer.ini"
          p:consumerGroup="consumer.group"
          p:sectionNames="common,name_server_02,er_relevant_team">
    </bean>

    <!--复制企业模板MQ事件消费者-->
    <bean id="copySandboxEventProcessor" class="com.facishare.rest.account.mq.CopySandboxEventProcessor"/>
    <!--全量沙盒数据复制MQ事件消费者-->
    <bean id="copyEnterpriseRelationEventProcessor"
          class="com.facishare.rest.account.mq.CopyEnterpriseRelationEventProcessor"/>
</beans>
