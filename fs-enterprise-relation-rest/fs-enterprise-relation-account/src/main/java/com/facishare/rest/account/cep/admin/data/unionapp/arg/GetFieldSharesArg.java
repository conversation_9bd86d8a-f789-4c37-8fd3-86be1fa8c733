package com.facishare.rest.account.cep.admin.data.unionapp.arg;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class GetFieldSharesArg implements Serializable {
    private String describeApiName;
    private Integer pageSize;
    private Integer pageNumber;
    private List<Integer> pageSizeOption;
    @JsonProperty("_isfilter")
    private Boolean _isfilter;
    @ApiModelProperty("4角色 5下游企业(全部下游企业-*********) 6下游企业组 :7 下游对接人(对接人需要穿 [{'outTenantId':'74164','outUserId':'4444'}] 结构),除了角色之外receivesWithType结构 Map<String,List<String>>")
    private Map<String, List<Object>> receivesWithType;
    @ApiModelProperty("权限类型 1只读 2读写")
    private Integer permission;
    @ApiModelProperty("1开启 0关闭 ")
    private Integer status;
    private String ruleName;
    @ApiModelProperty("是否仅查询外部,是->true")
    private Boolean outReceive = true;
}
