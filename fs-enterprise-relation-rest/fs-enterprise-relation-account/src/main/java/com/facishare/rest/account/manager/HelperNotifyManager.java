package com.facishare.rest.account.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.facishare.enterprise.common.enums.QXMessageEnums;
import com.facishare.enterprise.common.enums.RelationRelateApprovalType;
import com.facishare.enterprise.common.model.CloudConfigVo;
import com.facishare.enterprise.common.util.*;
import com.facishare.er.api.model.enums.RelationRelateApprovalOperateType;
import com.facishare.er.api.model.vo.RelationRelateApprovalVo;
import com.facishare.global.rest.dao.mongo.FxiaokeEmployeeAssociationEntityDao;
import com.facishare.global.rest.dao.mongo.FxiaokeEnterpriseAssociationEntityDao;
import com.facishare.global.rest.model.entity.FxiaokeEmployeeAssociationEntity;
import com.facishare.qixin.api.model.message.content.OTTemplateMessage;
import com.facishare.rest.account.dao.mongo.ApprovalDao;
import com.facishare.rest.account.dao.mongo.entity.Approval;
import com.facishare.rest.account.remote.FxiaokeAccountManager;
import com.facishare.rest.account.remote.QixinNotificationManager;
import com.facishare.rest.account.util.ConfigUtil;
import com.fxiaoke.model.*;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhangjh on 2017/5/25.
 * <p>
 * history:hardy 17.8.25加上对审批提醒的通知
 * <p>
 * 助手通知和审批提醒通知管理
 */
@Slf4j(topic = "helperNotifyManager")
@Component("helperNotifyManager")
public class HelperNotifyManager {
    @Autowired
    private QixinNotificationManager qixinNotificationManager;
//    private com.facishare.er.provider.remote.QixinNotificationManager newQiXinNotificationManager;
    @Autowired
    private FxiaokeAccountManager fxiaokeAccountManager;
    @Autowired
    private RelationRelateApprovalManager relationRelateApprovalManager;
    @Autowired
    private ApprovalDao approvalDaoOldManager;
    @Autowired
    private FxiaokeEmployeeAssociationEntityDao fxiaokeEmployeeAssociationEntityDao;
    @Autowired
    private EmployeeCardManager employeeCardManager;
    @Autowired
    private FxiaokeEnterpriseAssociationEntityDao fxiaokeEnterpriseAssociationEntityDao;
    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private CloudUtil cloudUtil;

    private final String titleColor = ConfigUtil.templateMsgTitleColor;
    private final String infoColor = ConfigUtil.templateMsgTitleColor;
    private final String buttonColor = ConfigUtil.templateMsgTitleColor;

    public void sendApplicantTimeOutNotification(Approval approval) {
        String titleContent = ConfigUtil.getUpAddDownEmployeeTimeOutNotifyUpApplicantTitleContent();
        String fistStr = ConfigUtil.getUpAddDownEmployeeTimeOutNotifyUpApplicantFirstContent();
        Map<String, String> infosMap = new LinkedHashMap<>(3);
        infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_315), approval.getFriendEmployeeName());
        infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_324), approval.getFriendEmployeeMobile());
        infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_325), I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_325));
        OTTemplateMessage templateMessage = buildTemplateMessage(titleContent, fistStr, infosMap, ConfigUtil.approvalDetailUrl + approval.get_id().toString());
        try {
            qixinNotificationManager.sendCrossHelperMessages(approval.getSourceEnterpriseAccount(), approval.getApplicantId(), templateMessage);
        } catch (Exception e) {
            log.warn("fail to send applicant expire notification,enterpriseAccount:{},applicantId:{},msg:{},", approval.getSourceEnterpriseAccount(), approval.getApplicantId(), templateMessage, e);
        }
    }

    /**
     * 向申请人发送审批超时过期通知（当前仅适用于上游设置下游对接人类型审批）
     */
    public void sendApplicantTimeOutNotification(RelationRelateApprovalVo approvalVo) {
        String titleContent = ConfigUtil.getUpAddDownEmployeeTimeOutNotifyUpApplicantTitleContent();
        String fistStr = ConfigUtil.getUpAddDownEmployeeTimeOutNotifyUpApplicantFirstContent();
        Map<String, String> infosMap = new LinkedHashMap<>(3);
        infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_315), approvalVo.getFriendEmployeeName());
        infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_324), approvalVo.getFriendEmployeeMobile());
        infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_325), I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_325));
        OTTemplateMessage templateMessage = buildTemplateMessage(titleContent, fistStr, infosMap, ConfigUtil.approvalDetailUrl + approvalVo.getId());
        try {
            qixinNotificationManager.sendCrossHelperMessages(approvalVo.getSourceEnterpriseAccount(), approvalVo.getApplicant().getApplicantId(), templateMessage);
        } catch (Exception e) {
            log.warn("fail to send applicant expire notification,enterpriseAccount:{},applicant:{},msg:{},", approvalVo.getSourceEnterpriseAccount(), approvalVo.getApplicant(), templateMessage, e);
        }
    }

    /**
     * 向审批接收方企业管理员发送审批提醒
     *
     * @param destEnterpriseAccount 审批接收方企业帐号
     * @param approvalId 审批单ID
     */

    public void sendNewReceiverAdminsApplyNotification(String destEnterpriseAccount, String approvalId) {
        TextCardMessage textCardMessage = new TextCardMessage();
        List<Integer> receiverAdmins = fxiaokeAccountManager.listRelationAdmins(destEnterpriseAccount);
        if (CollectionUtils.isEmpty(receiverAdmins)) {
            return;
        }
        int upstreamTenantId = eieaConverter.enterpriseAccountToId(destEnterpriseAccount);
        Approval approval = approvalDaoOldManager.getApprovalDetailById(upstreamTenantId, approvalId);

        if (approval.getApproveType().equals(RelationRelateApprovalType.UP_ADD_DOWN_EMPLOYEE.getType())) {
            return;
        }

        TextCardMessageHead textCardMessageHead = new TextCardMessageHead();
        TextCardMessageBody textCardMessageBody = new TextCardMessageBody();
        List<KeyValueItem> keyValueItems = new ArrayList<>();
        textCardMessageHead.setTime(new Date().getTime());

        KeyValueItem keyValueItem = new KeyValueItem();
        keyValueItem.setKeyElement(TextCardElement.builder().text(QXMessageEnums.ENTERPRISE_NAME.getContent()).textInfo(new InternationalItem(QXMessageEnums.ENTERPRISE_NAME.getKey())).build());
        keyValueItem.setValueElement(TextCardElement.builder().text(approval.getSourceEnterpriseName()).build());
        keyValueItems.add(keyValueItem);

        KeyValueItem keyValueItem2 = new KeyValueItem();
        keyValueItem2.setKeyElement(TextCardElement.builder().text(QXMessageEnums.APPLICATION_PEOPLE.getContent()).textInfo(new InternationalItem(QXMessageEnums.BUSINESS_TYPE.getKey())).build());
        keyValueItem2.setValueElement(TextCardElement.builder().text(approval.getApplicantName()).build());
        keyValueItems.add(keyValueItem2);

        String titleContent;
        RelationRelateApprovalType approvalTypeEnum = RelationRelateApprovalType.getByType(approval.getApproveType());
        switch (Objects.requireNonNull(approvalTypeEnum)) {
            case RELATION_APPLY :
                titleContent = String.format(ConfigUtil.getRelationApplyNotifyReceiverAdminTitleContent(), approval.getSourceEnterpriseName());
                textCardMessageHead.setTitleElement(TextCardElement.builder().text(titleContent).textInfo(InternationalItem.buildInternationalItem(QXMessageEnums.ENTERPRISE_APPLICATION_CONNECT.getKey(), Collections.singletonList(approval.getSourceEnterpriseName()))).build());
                keyValueItems.add(KeyValueItem.builder().
                        keyElement(TextCardElement.builder().text(QXMessageEnums.APPLICATION_BUSINESS.getContent()).textInfo(new InternationalItem(QXMessageEnums.APPLICATION_BUSINESS.getKey())).build())
                        .valueElement(TextCardElement.builder().text(ConfigUtil.getRelationApplyType()).build()).build());
                break;
            case EMPLOYEE_BUSINESS:
                titleContent = String.format(ConfigUtil.getEmployeeBusinessNotifyReceiverAdminTitleContent(), approval.getSourceEnterpriseName());
                textCardMessageHead.setTitleElement(TextCardElement.builder().text(titleContent).textInfo(InternationalItem.buildInternationalItem(QXMessageEnums.ER_PEOPLE_BUSINESS_APPLICATION.getKey(), Collections.singletonList(approval.getSourceEnterpriseName()))).build());
                keyValueItems.add(KeyValueItem.builder().
                        keyElement(TextCardElement.builder().text(QXMessageEnums.APPLICATION_BUSINESS.getContent()).textInfo(new InternationalItem(QXMessageEnums.APPLICATION_BUSINESS.getKey())).build())
                        .valueElement(TextCardElement.builder().text(ConfigUtil.getEmployeeBusinessType()).build()).build());
                break;
        }

        textCardMessageBody.setForm(keyValueItems);
        textCardMessage.setHead(textCardMessageHead);
        textCardMessage.setBody(textCardMessageBody);

        setTextCardMessage(textCardMessage, approval);
        log.info("===send template message,enterprise:{},user:{},msg:{}", approval.getSourceEnterpriseAccount(), approval.getApplicantId(), textCardMessage);
        try {
            qixinNotificationManager.sendCrossHelperMessages(destEnterpriseAccount, Collections.singletonList(approval.getApplicantId()), textCardMessage);
        } catch (Exception e) {
            log.warn("fail to send applicant approval result notification,enterpriseAccount:{},applicantId:{},msg:{},", approval.getSourceEnterpriseAccount(), approval.getApplicantId(),
                    textCardMessage, e);
        }
        try {
            //更新企业互联下 我的审批的session 的未读数和未审批数目。
            long unprocessedApprovalNum = relationRelateApprovalManager.getAllUnprocessedApprovalNum(destEnterpriseAccount);
            updateEaConnAdminsUnprocessApprovalNum(destEnterpriseAccount, unprocessedApprovalNum);
        } catch (Exception e) {
            log.warn("fail to send receiver admins apply notification,enterpriseAccount:{},admins:{},msg:{},", destEnterpriseAccount, receiverAdmins, e);
        }
    }


    public void sendReceiverAdminsApplyNotification(String destEnterpriseAccount, String approvalId) {
        //查询上游企业系统管理员
        List<Integer> receiverAdmins = fxiaokeAccountManager.listRelationAdmins(destEnterpriseAccount);
        if (CollectionUtils.isEmpty(receiverAdmins)) {
            return;
        }
        int upstreamTenantId = eieaConverter.enterpriseAccountToId(destEnterpriseAccount);
        Approval approval = approvalDaoOldManager.getApprovalDetailById(upstreamTenantId, approvalId);
        Integer approvalType = approval.getApproveType();
        String titleContent = null;
        Map<String, String> infosMap = new LinkedHashMap<>();
        infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERADMIN_EAOPERATIONCONTROLLER_166), approval.getSourceEnterpriseName());
        infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_229), approval.getApplicantName());
        if (approvalType.equals(RelationRelateApprovalType.RELATION_APPLY.getType())) {
            titleContent = String.format(ConfigUtil.getRelationApplyNotifyReceiverAdminTitleContent(), approval.getSourceEnterpriseName());
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_232), ConfigUtil.getRelationApplyType());
        } else if (approvalType.equals(RelationRelateApprovalType.EMPLOYEE_BUSINESS.getType())) {
            titleContent = String.format(ConfigUtil.getEmployeeBusinessNotifyReceiverAdminTitleContent(), approval.getSourceEnterpriseName());
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_232), ConfigUtil.getEmployeeBusinessType());
        } else if (approvalType.equals(RelationRelateApprovalType.UP_ADD_DOWN_EMPLOYEE.getType())) {
            /**上游设置下游对接人当前未涉及发送助手消息**/
        } else {
            List<String> downstreamEmployeeNames = new ArrayList<>();
            for (Integer friendEmployeeId : approval.getFriendEmployeeIds()) {
                String employeeName = fxiaokeAccountManager.getEmployeeName(approval.getSourceEnterpriseAccount(), friendEmployeeId);
                downstreamEmployeeNames.add(employeeName);
            }
            titleContent = String.format(ConfigUtil.getDownAddEmployeeApplyNotifyUpAdminTitleContent(), approval.getSourceEnterpriseName());
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_232), ConfigUtil.getDownAddEmployeeType());
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_315), StringUtil.combineWithSeparator(downstreamEmployeeNames, ", "));
        }
        OTTemplateMessage templateMessage = buildTemplateMessage(titleContent, null, infosMap, ConfigUtil.approvalDetailUrl + approvalId);

        try {
            //灰度控制，不在灰度名单上的企业，消息发到互联助手，在灰度名单上的，消息只发到 我的审批。
            if (approvalType.equals(RelationRelateApprovalType.UP_ADD_DOWN_EMPLOYEE.getType())) {
                //上游替下游添加对接人，审批方是下游，而且是短信审批。审批提醒是上游才有。
                return;
            }
            //更新企业互联下 我的审批的session 的未读数和未审批数目。
            qixinNotificationManager.sendCrossRemindingMessage(destEnterpriseAccount, receiverAdmins, titleContent);
            long unprocessedApprovalNum = relationRelateApprovalManager.getAllUnprocessedApprovalNum(destEnterpriseAccount);
            updateEaConnAdminsUnprocessApprovalNum(destEnterpriseAccount, unprocessedApprovalNum);
        } catch (Exception e) {
            log.warn("fail to send receiver admins apply notification,enterpriseAccount:{},admins:{},msg:{},", destEnterpriseAccount, receiverAdmins, templateMessage, e);
        }
    }

    /**
     * 更新企业互联管理员审批提醒session上的未处理审批数目
     *
     * @param destEnterpriseAccount 审批接收方企业帐号
     * @param unprocessApprovalNum 未处理审批数目
     */
    public void updateEaConnAdminsUnprocessApprovalNum(String destEnterpriseAccount, long unprocessApprovalNum) {
        //查询上游企业系统管理员
        List<Integer> receiverAdmins = fxiaokeAccountManager.listRelationAdmins(destEnterpriseAccount);
        if (CollectionUtils.isEmpty(receiverAdmins)) {
            return;
        }

        qixinNotificationManager.updateCrossRemindingUnprocessNum(destEnterpriseAccount, receiverAdmins, unprocessApprovalNum);
    }

    private OTTemplateMessage buildTemplateMessage(String titleStr, String firstStr, Map<String, String> infos, String btnUrl) {
        String msgTimeStr = DateUtil.dateFormat(new Date(), I18nUtil.get(I18nKeyEnum.EPI_MANAGER_HELPERNOTIFYMANAGER_62));
        OTTemplateMessage templateMessage = new OTTemplateMessage();
        if (!Strings.isNullOrEmpty(titleStr)) {
            OTTemplateMessage.Title title = new OTTemplateMessage.Title();
            title.color = ConfigUtil.templateMsgTitleColor;
            title.time = msgTimeStr;
            title.content = titleStr;
            templateMessage.title = title;
        }
        if (!Strings.isNullOrEmpty(firstStr)) {
            OTTemplateMessage.Frist first = new OTTemplateMessage.Frist();
            first.color = ConfigUtil.templateInfoValueColor;
            first.content = firstStr;
            templateMessage.first = first;
        }
        if (!CollectionUtils.isEmpty(infos)) {
            List<OTTemplateMessage.Info> infoList = new ArrayList<>(infos.size());
            infos.forEach((key, value) -> {
                OTTemplateMessage.Info info = new OTTemplateMessage.Info();
                info.color = ConfigUtil.templateInfoValueColor;
                info.label = key;
                info.value = value;
                infoList.add(info);
            });
            templateMessage.infos = infoList;
        }
        if (!Strings.isNullOrEmpty(btnUrl)) {
            OTTemplateMessage.Button button = new OTTemplateMessage.Button();
            button.color = ConfigUtil.templateButtonColor;
            button.title = I18nUtil.get(I18nKeyEnum.EIP_ERADMIN_MSGMANAGER_99);
            button.url = btnUrl;
            templateMessage.button = button;
        }
        return templateMessage;
    }

    public void sendApplicantApprovalResultNotification(Approval approval, RelationRelateApprovalOperateType operateType, String remark) {
        Integer approvalType = approval.getApproveType();
        boolean agreeApproval = operateType.equals(RelationRelateApprovalOperateType.AGREE_APPROVAL);
        String titleContent;
        String firstStr = null;
        Map<String, String> infosMap = new LinkedHashMap<>();
        if (approvalType.equals(RelationRelateApprovalType.RELATION_APPLY.getType())) {
            if (agreeApproval) {
                titleContent = ConfigUtil.getRelationApplyAgreeNotifyApplicantTitleContent();
            } else {
                titleContent = ConfigUtil.getRelationApplyRejectNotifyApplicantTitleContent();
            }
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERADMIN_EAOPERATIONCONTROLLER_166), approval.getDestEnterpriseName());
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_232), ConfigUtil.getRelationApplyType());
        } else if (approvalType.equals(RelationRelateApprovalType.EMPLOYEE_BUSINESS.getType())) {
            if (agreeApproval) {
                titleContent = ConfigUtil.getEmployeeBusinessAgreeNotifyApplicantTitleContent();
            } else {
                titleContent = ConfigUtil.getEmployeeBusinessRejectNotifyApplicantTitleContent();
            }
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERADMIN_EAOPERATIONCONTROLLER_166), approval.getDestEnterpriseName());
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_232), ConfigUtil.getEmployeeBusinessType());
            List<FxiaokeEmployeeAssociationEntity> srcFxiaokeEmps = fxiaokeEmployeeAssociationEntityDao.batchGetBySingleEa(approval.getSourceEnterpriseAccount(), approval.getFriendEmployeeIds());
            Long sourceOuterTenantId = fxiaokeEnterpriseAssociationEntityDao.getOuterTenantIdOrCreate(approval.getSourceEnterpriseAccount());
            List<Long> srcOuterUids = srcFxiaokeEmps.stream().map(FxiaokeEmployeeAssociationEntity::getOuterUid).collect(Collectors.toList());
            //开通对接人互联业务审批结果通告中需要展示目标对接人列表，形式 A、B、C等N人
            String publicEmployeeStr = employeeCardManager.getEmployeeNameListStr(sourceOuterTenantId, srcOuterUids, null);
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_315), publicEmployeeStr);
        } else if (approvalType.equals(RelationRelateApprovalType.UP_ADD_DOWN_EMPLOYEE.getType())) {
            titleContent = ConfigUtil.getUpAddDownEmployeeResultNotifyUpApplicantTitleContent();
            if (agreeApproval) {
                firstStr = ConfigUtil.getUpAddDownEmployeeAgreeNotifyUpApplicantFirstContent();
            } else {
                firstStr = ConfigUtil.getUpAddDownEmployeeRejectNotifyUpApplicantFirstContent();
            }
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_315), approval.getFriendEmployeeName());
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_324), approval.getFriendEmployeeMobile());
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_325),
                agreeApproval ? I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_325A) : I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_325A));
        } else {//下游添加我方对接人
            if (agreeApproval) {
                titleContent = ConfigUtil.getDownAddEmployeeAgreeNotifyDownApplicantTitleContent();
            } else {
                titleContent = ConfigUtil.getDownAddEmployeeRejectNotifyDownApplicantTitleContent();
            }
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERADMIN_EAOPERATIONCONTROLLER_166), approval.getDestEnterpriseName());
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_232), ConfigUtil.getDownAddEmployeeType());
            List<FxiaokeEmployeeAssociationEntity> srcFxiaokeEmps = fxiaokeEmployeeAssociationEntityDao.batchGetBySingleEa(approval.getSourceEnterpriseAccount(), approval.getFriendEmployeeIds());
            Long sourceOuterTenantId = fxiaokeEnterpriseAssociationEntityDao.getOuterTenantIdOrCreate(approval.getSourceEnterpriseAccount());
            List<Long> srcOuterUids = srcFxiaokeEmps.stream().map(FxiaokeEmployeeAssociationEntity::getOuterUid).collect(Collectors.toList());
            String publicEmployeeStr = employeeCardManager.getEmployeeNameListStr(sourceOuterTenantId, srcOuterUids, null);
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_315), publicEmployeeStr);
        }
        if (!agreeApproval) {
            infosMap.put(I18nUtil.get(I18nKeyEnum.EIP_ERPROVIDER_HELPERNOTIFYMANAGER_338), remark);
        }
        OTTemplateMessage templateMessage = buildTemplateMessage(titleContent, firstStr, infosMap, ConfigUtil.approvalDetailUrl + approval.get_id().toString());

        log.info("===send template message,enterprise:{},user:{},msg:{}", approval.getSourceEnterpriseAccount(), approval.getApplicantId(), templateMessage);
        try {
            qixinNotificationManager.sendCrossHelperMessages(approval.getSourceEnterpriseAccount(), approval.getApplicantId(), templateMessage);
        } catch (Exception e) {
            log.warn("fail to send applicant approval result notification,enterpriseAccount:{},applicantId:{},msg:{},", approval.getSourceEnterpriseAccount(), approval.getApplicantId(),
                templateMessage, e);
        }
    }

    public void sendApplicantApprovalResultNotificationNew(Approval approval, RelationRelateApprovalOperateType operateType, String remark) {
        TextCardMessage textCardMessage = null;
        RelationRelateApprovalType approvalTypeEnum = RelationRelateApprovalType.getByType(approval.getApproveType());

        switch (Objects.requireNonNull(approvalTypeEnum)) {
            case RELATION_APPLY :
                textCardMessage = getRelationApplyCard(approval, operateType, remark);
                break;
            case EMPLOYEE_BUSINESS:
                textCardMessage = getEmployeeBusiness(approval, operateType, remark);
                break;
            case UP_ADD_DOWN_EMPLOYEE:
                textCardMessage = getUpAddDownEmployee(approval, operateType, remark);
                break;
            case DOWN_ADD_SELF_EMPLOYEE:
                textCardMessage = getDownAddSelfEmployee(approval, operateType, remark);
                break;
        }

        setTextCardMessage(textCardMessage, approval);

        log.info("===send template message,enterprise:{},user:{},msg:{}", approval.getSourceEnterpriseAccount(), approval.getApplicantId(), textCardMessage);
        try {
            qixinNotificationManager.sendCrossHelperMessages(approval.getSourceEnterpriseAccount(), Collections.singletonList(approval.getApplicantId()), textCardMessage);
        } catch (Exception e) {
            log.warn("fail to send applicant approval result notification,enterpriseAccount:{},applicantId:{},msg:{},", approval.getSourceEnterpriseAccount(), approval.getApplicantId(),
                    textCardMessage, e);
        }
    }

    private TextCardMessage getRelationApplyCard(Approval approval, RelationRelateApprovalOperateType operateType, String remark) {
        TextCardMessage textCardMessage = new TextCardMessage();
        boolean agreeApproval = operateType.equals(RelationRelateApprovalOperateType.AGREE_APPROVAL);

        TextCardMessageHead textCardMessageHead = new TextCardMessageHead();
        TextCardMessageBody textCardMessageBody = new TextCardMessageBody();
        List<KeyValueItem> keyValueItems = new ArrayList<>();
        textCardMessageHead.setTime(new Date().getTime());

        QXMessageEnums titleEnum = agreeApproval ? QXMessageEnums.ER_APPROVE_AGREE : QXMessageEnums.ER_APPROVE_REJECT;

        textCardMessageHead.setTitleElement(TextCardElement.builder().textInfo(new InternationalItem(titleEnum.getKey())).text(titleEnum.getContent()).build());

        KeyValueItem keyValueItem = new KeyValueItem();
        keyValueItem.setKeyElement(TextCardElement.builder().text(QXMessageEnums.ENTERPRISE_NAME.getContent()).textInfo(new InternationalItem(QXMessageEnums.ENTERPRISE_NAME.getKey())).build());
        keyValueItem.setValueElement(TextCardElement.builder().text(approval.getDestEnterpriseName()).build());
        keyValueItems.add(keyValueItem);

        KeyValueItem keyValueItem2 = new KeyValueItem();
        keyValueItem2.setKeyElement(TextCardElement.builder().text(QXMessageEnums.BUSINESS_TYPE.getContent()).textInfo(new InternationalItem(QXMessageEnums.BUSINESS_TYPE.getKey())).build());
        keyValueItem2.setValueElement(TextCardElement.builder().text(ConfigUtil.getRelationApplyType()).build());
        keyValueItems.add(keyValueItem2);

        if (!agreeApproval) {
            KeyValueItem keyValueItem3 = new KeyValueItem();
            keyValueItem3.setKeyElement(TextCardElement.builder().text(QXMessageEnums.REJECT_REASON.getContent()).textInfo(new InternationalItem(QXMessageEnums.REJECT_REASON.getKey())).build());
            keyValueItem3.setValueElement(TextCardElement.builder().text(remark).build());
            keyValueItems.add(keyValueItem3);
        }

        textCardMessageBody.setForm(keyValueItems);

        textCardMessage.setHead(textCardMessageHead);
        textCardMessage.setBody(textCardMessageBody);

        return textCardMessage;
    }

    private TextCardMessage getEmployeeBusiness(Approval approval, RelationRelateApprovalOperateType operateType, String remark) {
        TextCardMessage textCardMessage = new TextCardMessage();
        boolean agreeApproval = operateType.equals(RelationRelateApprovalOperateType.AGREE_APPROVAL);

        TextCardMessageHead textCardMessageHead = new TextCardMessageHead();
        TextCardMessageBody textCardMessageBody = new TextCardMessageBody();
        List<KeyValueItem> keyValueItems = new ArrayList<>();
        textCardMessageHead.setTime(new Date().getTime());

        QXMessageEnums titleEnum = agreeApproval ? QXMessageEnums.ER_PEOPLE_APPROVE_AGREE : QXMessageEnums.ER_PEOPLE_APPROVE_REJECT;

        textCardMessageHead.setTitleElement(TextCardElement.builder().textInfo(new InternationalItem(titleEnum.getKey())).text(titleEnum.getContent()).build());

        KeyValueItem keyValueItem = new KeyValueItem();
        keyValueItem.setKeyElement(TextCardElement.builder().text(QXMessageEnums.ENTERPRISE_NAME.getContent()).textInfo(new InternationalItem(QXMessageEnums.ENTERPRISE_NAME.getKey())).build());
        keyValueItem.setValueElement(TextCardElement.builder().text(approval.getDestEnterpriseName()).build());
        keyValueItems.add(keyValueItem);

        KeyValueItem keyValueItem2 = new KeyValueItem();
        keyValueItem2.setKeyElement(TextCardElement.builder().text(QXMessageEnums.APPLICATION_BUSINESS.getContent()).textInfo(new InternationalItem(QXMessageEnums.APPLICATION_BUSINESS.getKey())).build());
        keyValueItem2.setValueElement(TextCardElement.builder().text(ConfigUtil.getEmployeeBusinessType()).build());
        keyValueItems.add(keyValueItem2);

        List<FxiaokeEmployeeAssociationEntity> srcFxiaokeEmps = fxiaokeEmployeeAssociationEntityDao.batchGetBySingleEa(approval.getSourceEnterpriseAccount(), approval.getFriendEmployeeIds());
        Long sourceOuterTenantId = fxiaokeEnterpriseAssociationEntityDao.getOuterTenantIdOrCreate(approval.getSourceEnterpriseAccount());
        List<Long> srcOuterUids = srcFxiaokeEmps.stream().map(FxiaokeEmployeeAssociationEntity::getOuterUid).collect(Collectors.toList());
        //开通对接人互联业务审批结果通告中需要展示目标对接人列表，形式 A、B、C等N人
        String publicEmployeeStr = employeeCardManager.getEmployeeNameListStr(sourceOuterTenantId, srcOuterUids, null);

        KeyValueItem keyValueItem3 = new KeyValueItem();
        keyValueItem3.setKeyElement(TextCardElement.builder().text(QXMessageEnums.ER_PEOPLE.getContent()).textInfo(new InternationalItem(QXMessageEnums.ER_PEOPLE.getKey())).build());
        keyValueItem3.setValueElement(TextCardElement.builder().text(publicEmployeeStr).build());
        keyValueItems.add(keyValueItem3);

        if (!agreeApproval) {
            KeyValueItem keyValueItem4 = new KeyValueItem();
            keyValueItem4.setKeyElement(TextCardElement.builder().text(QXMessageEnums.REJECT_REASON.getContent()).textInfo(new InternationalItem(QXMessageEnums.REJECT_REASON.getKey())).build());
            keyValueItem4.setValueElement(TextCardElement.builder().text(remark).build());
            keyValueItems.add(keyValueItem4);
        }

        textCardMessageBody.setForm(keyValueItems);

        textCardMessage.setHead(textCardMessageHead);
        textCardMessage.setBody(textCardMessageBody);

        return textCardMessage;
    }

    private TextCardMessage getUpAddDownEmployee(Approval approval, RelationRelateApprovalOperateType operateType, String remark) {
        TextCardMessage textCardMessage = new TextCardMessage();
        boolean agreeApproval = operateType.equals(RelationRelateApprovalOperateType.AGREE_APPROVAL);

        TextCardMessageHead textCardMessageHead = new TextCardMessageHead();
        TextCardMessageBody textCardMessageBody = new TextCardMessageBody();
        List<KeyValueItem> keyValueItems = new ArrayList<>();
        textCardMessageHead.setTime(new Date().getTime());


        textCardMessageHead.setTitleElement(TextCardElement.builder().textInfo(new InternationalItem(QXMessageEnums.PARTNER_ER_PEOPLE_APPROVE_RESULT.getKey())).text(QXMessageEnums.PARTNER_ER_PEOPLE_APPROVE_RESULT.getContent()).build());

        QXMessageEnums firstEnum = agreeApproval ? QXMessageEnums.PARTNER_ER_PEOPLE_APPROVE_AGREE : QXMessageEnums.PARTNER_ER_PEOPLE_APPROVE_REJECT;
        textCardMessageBody.setContentElement(TextCardElement.builder().text(firstEnum.getContent()).textInfo(new InternationalItem(firstEnum.getKey())).build());

        KeyValueItem keyValueItem = new KeyValueItem();
        keyValueItem.setKeyElement(TextCardElement.builder().text(QXMessageEnums.ER_PEOPLE.getContent()).textInfo(new InternationalItem(QXMessageEnums.ER_PEOPLE.getKey())).build());
        keyValueItem.setValueElement(TextCardElement.builder().text(approval.getFriendEmployeeName()).build());
        keyValueItems.add(keyValueItem);


        KeyValueItem keyValueItem2 = new KeyValueItem();
        keyValueItem2.setKeyElement(TextCardElement.builder().text(QXMessageEnums.PHONE.getContent()).textInfo(new InternationalItem(QXMessageEnums.PHONE.getKey())).build());
        keyValueItem2.setValueElement(TextCardElement.builder().text(approval.getFriendEmployeeMobile()).build());
        keyValueItems.add(keyValueItem2);

        QXMessageEnums result = agreeApproval ? QXMessageEnums.AGREES : QXMessageEnums.REJECT_REASON;

        KeyValueItem keyValueItem3 = new KeyValueItem();
        keyValueItem3.setKeyElement(TextCardElement.builder().text(QXMessageEnums.APPROVE_RESULT.getContent()).textInfo(new InternationalItem(QXMessageEnums.APPROVE_RESULT.getKey())).build());
        keyValueItem3.setValueElement(TextCardElement.builder().text(result.getContent()).textInfo(new InternationalItem(result.getKey())).build());
        keyValueItems.add(keyValueItem3);

        if (!agreeApproval) {
            KeyValueItem keyValueItem4 = new KeyValueItem();
            keyValueItem4.setKeyElement(TextCardElement.builder().text(QXMessageEnums.REJECT_REASON.getContent()).textInfo(new InternationalItem(QXMessageEnums.REJECT_REASON.getKey())).build());
            keyValueItem4.setValueElement(TextCardElement.builder().text(remark).build());
            keyValueItems.add(keyValueItem4);
        }

        textCardMessageBody.setForm(keyValueItems);

        textCardMessage.setHead(textCardMessageHead);
        textCardMessage.setBody(textCardMessageBody);

        return textCardMessage;
    }

    private TextCardMessage getDownAddSelfEmployee(Approval approval, RelationRelateApprovalOperateType operateType, String remark) {
        TextCardMessage textCardMessage = new TextCardMessage();
        boolean agreeApproval = operateType.equals(RelationRelateApprovalOperateType.AGREE_APPROVAL);

        TextCardMessageHead textCardMessageHead = new TextCardMessageHead();
        TextCardMessageBody textCardMessageBody = new TextCardMessageBody();
        List<KeyValueItem> keyValueItems = new ArrayList<>();
        textCardMessageHead.setTime(new Date().getTime());

        QXMessageEnums titleEnum = agreeApproval ? QXMessageEnums.ADD_ER_PEOPLE_AGREE : QXMessageEnums.ADD_ER_PEOPLE_REJECT;
        textCardMessageHead.setTitleElement(TextCardElement.builder().textInfo(new InternationalItem(titleEnum.getKey())).text(titleEnum.getContent()).build());

        KeyValueItem keyValueItem = new KeyValueItem();
        keyValueItem.setKeyElement(TextCardElement.builder().text(QXMessageEnums.ENTERPRISE_NAME.getContent()).textInfo(new InternationalItem(QXMessageEnums.ENTERPRISE_NAME.getKey())).build());
        keyValueItem.setValueElement(TextCardElement.builder().text(approval.getDestEnterpriseName()).build());
        keyValueItems.add(keyValueItem);


        KeyValueItem keyValueItem2 = new KeyValueItem();
        keyValueItem2.setKeyElement(TextCardElement.builder().text(QXMessageEnums.APPLICATION_BUSINESS.getContent()).textInfo(new InternationalItem(QXMessageEnums.APPLICATION_BUSINESS.getKey())).build());
        keyValueItem2.setValueElement(TextCardElement.builder().text(ConfigUtil.getEmployeeBusinessType()).build());
        keyValueItems.add(keyValueItem2);

        List<FxiaokeEmployeeAssociationEntity> srcFxiaokeEmps = fxiaokeEmployeeAssociationEntityDao.batchGetBySingleEa(approval.getSourceEnterpriseAccount(), approval.getFriendEmployeeIds());
        Long sourceOuterTenantId = fxiaokeEnterpriseAssociationEntityDao.getOuterTenantIdOrCreate(approval.getSourceEnterpriseAccount());
        List<Long> srcOuterUids = srcFxiaokeEmps.stream().map(FxiaokeEmployeeAssociationEntity::getOuterUid).collect(Collectors.toList());
        //开通对接人互联业务审批结果通告中需要展示目标对接人列表，形式 A、B、C等N人
        String publicEmployeeStr = employeeCardManager.getEmployeeNameListStr(sourceOuterTenantId, srcOuterUids, null);

        KeyValueItem keyValueItem3 = new KeyValueItem();
        keyValueItem3.setKeyElement(TextCardElement.builder().text(QXMessageEnums.ER_PEOPLE.getContent()).textInfo(new InternationalItem(QXMessageEnums.ER_PEOPLE.getKey())).build());
        keyValueItem3.setValueElement(TextCardElement.builder().text(publicEmployeeStr).build());
        keyValueItems.add(keyValueItem3);

        if (!agreeApproval) {
            KeyValueItem keyValueItem4 = new KeyValueItem();
            keyValueItem4.setKeyElement(TextCardElement.builder().text(QXMessageEnums.REJECT_REASON.getContent()).textInfo(new InternationalItem(QXMessageEnums.REJECT_REASON.getKey())).build());
            keyValueItem4.setValueElement(TextCardElement.builder().text(remark).build());
            keyValueItems.add(keyValueItem4);
        }

        textCardMessageBody.setForm(keyValueItems);

        textCardMessage.setHead(textCardMessageHead);
        textCardMessage.setBody(textCardMessageBody);

        return textCardMessage;
    }

    private void setTextCardMessage(TextCardMessage textCardMessage, Approval approval) {
        String url = ConfigUtil.approvalDetailUrl + approval.get_id().toString();

        CloudConfigVo cloudConfigVo = cloudUtil.getCloudConfig(approval.getSourceEnterpriseAccount()).getData();
        url = CloudUtil.convertCloudAppUrl(cloudConfigVo, null, url);

        textCardMessage.setFoot(TextCardMessageFoot.builder().footElement(
                TextCardElement.builder().color(buttonColor).text(QXMessageEnums.CHECK_DETAIL.getContent())
                        .textInfo(new InternationalItem(QXMessageEnums.CHECK_DETAIL.getKey())).build()).build());

        textCardMessage.setOutPlatformUrl(url);
        textCardMessage.setInnerPlatformMobileUrl(url);
        textCardMessage.setInnerPlatformWebUrl(url);

        if (textCardMessage.getHead() != null) {
            textCardMessage.getHead().getTitleElement().setColor(titleColor);
        }

        if (textCardMessage.getBody() != null){
            for (KeyValueItem keyValueItem : textCardMessage.getBody().getForm()) {
                keyValueItem.getKeyElement().setColor(infoColor);
                keyValueItem.getValueElement().setColor(infoColor);
            }

            if (textCardMessage.getBody().getContentElement() != null){
                textCardMessage.getBody().getContentElement().setColor(infoColor);
            }

            if (textCardMessage.getBody().getRemarkElement() != null){
                textCardMessage.getBody().getRemarkElement().setColor(infoColor);
            }
        }
    }
}