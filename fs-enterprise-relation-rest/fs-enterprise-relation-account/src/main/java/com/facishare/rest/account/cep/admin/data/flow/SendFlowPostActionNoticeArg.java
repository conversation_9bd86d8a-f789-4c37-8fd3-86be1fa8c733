package com.facishare.rest.account.cep.admin.data.flow;

import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.facishare.enterprise.common.model.paas.ObjectDataSimpleVo;
import com.facishare.enterprise.common.util.I18nUtil;
import com.facishare.rest.account.cep.admin.data.flow.cons.FlowNoticeForwardType;
import com.facishare.rest.account.cep.admin.data.flow.vo.FlowNoticeVo;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class SendFlowPostActionNoticeArg implements Serializable {
    private static final long serialVersionUID = 6281332513854981366L;
    /**
     * 消息ID
     */
    private String msgId;
    /**
     * 发送者企业ID
     */
    private Integer senderEi;
    /**
     * 接受者外部ID列表
     */
    private Set<Long> receiverIds;
    /**
     * 接收角色项，与接受者外部ID列表取并集
     */
    private RoleRecipient receiverRoles;
    /**
     * 外部通知
     */
    private FlowNoticeVo notice;
    /**
     * 消息渠道（互联应用标识）
     */
    private String channel;
    /**
     * 当前流程实例名称
     */
    private String flowName;
    /**
     * 当前流程实例ID
     */
    private String flowId;
    /**
     * 当前阶段名称
     */
    private String stageName;

    public void validate() {
        Preconditions.checkArgument(Objects.nonNull(senderEi), I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_SENDFLOWPOSTACTIONNOTICEARG_69));
        Preconditions.checkArgument(Objects.nonNull(receiverIds) || Objects.nonNull(receiverRoles), I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_SENDFLOWPOSTACTIONNOTICEARG_70));
        if (Objects.nonNull(receiverRoles) && Objects.isNull(receiverIds)) {
            receiverRoles.validate();
        }
        Preconditions.checkArgument(!Strings.isNullOrEmpty(flowName), I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_SENDFLOWPOSTACTIONNOTICEARG_74));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(channel), I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_SENDFLOWPOSTACTIONNOTICEARG_75));
        Preconditions.checkArgument(Objects.nonNull(notice), I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_SENDFLOWPOSTACTIONNOTICEARG_76));
        Preconditions.checkArgument(Objects.nonNull(notice.getForward()) && Objects.nonNull(notice.getForward().getType()) && FlowNoticeForwardType.isValidType(notice.getForward().getType()),
            I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_SENDFLOWPOSTACTIONNOTICEARG_77));
        if (!notice.getForward().getType().equals(FlowNoticeForwardType.no_forward.getType())) {
            Preconditions.checkArgument(!Strings.isNullOrEmpty(notice.getForward().getObjectApiName()) && !Strings.isNullOrEmpty(notice.getForward().getObjectId()),
                I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_SENDFLOWPOSTACTIONNOTICEARG_79));
        }
    }

    @Setter
    @Getter
    @ToString
    public class RoleRecipient implements Serializable {
        private static final long serialVersionUID = -4915296747652767952L;
        private List<ObjectDataSimpleVo> downObjects;  //下游企业匹配项
        private List<String> roleIds; //外部角色列表

        public void validate() {
            Preconditions.checkArgument(Objects.nonNull(downObjects) && Objects.nonNull(roleIds), I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_SENDFLOWPOSTACTIONNOTICEARG_92));
        }
    }
}
