package com.facishare.rest.account.outrest;

import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.EnterpriseRelationObjConstant;
import com.facishare.enterprise.common.constant.GlobalRoleConstants;
import com.facishare.enterprise.common.constant.IdentityType;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.constant.RegisterType;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.model.OuterUserOuterRoleVo;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.I18nUtil;
import com.facishare.enterprise.common.util.I18nUtil;
import com.facishare.enterprise.common.util.OffsetUtil;
import com.facishare.er.api.model.ERAccountActiveConfigData;
import com.facishare.er.api.model.data.UserRoleData;
import com.facishare.er.api.model.result.UserRoleDataPageResult;
import com.facishare.global.rest.dao.pg.EmployeeCardEntityDao;
import com.facishare.global.rest.model.entity.EmployeeCardEntity;
import com.facishare.linkapp.api.service.LinkAppOuterRoleService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.rest.account.action.EnterpriseRelationAddAction;
import com.facishare.rest.account.action.PublicEmployeeAddAction;
import com.facishare.rest.account.cep.admin.data.linkapp.data.PaasFieldApiNameAndLabelData;
import com.facishare.rest.account.common.RelationOuterDataPrivilegeEnum;
import com.facishare.rest.account.manager.ActionCallManager;
import com.facishare.rest.account.remote.OuterRoleRemoteBaseManager;
import com.facishare.rest.account.remote.RemoteManager;
import com.facishare.rest.account.remote.data.ReferenceMapperObjectFieldOutPermissionConfig;
import com.facishare.rest.account.service.ERAccountActiveConfigService;
import com.facishare.rest.account.service.EnterpriseRelationCopyService;
import com.facishare.rest.account.service.OuterUserRoleService;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.data.Wheres;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ObjectDataCreateResult;
import com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.enterpriserelation.objrest.common.OffsetArg;
import com.fxiaoke.enterpriserelation.objrest.result.AddActionResult;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 互联数据修复工具
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/18 14:24
 **/
@Slf4j
@RestController
@RequestMapping("/dataFixTool")
public class DataFixToolsController {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private ActionCallManager actionCallManager;
    @Autowired
    private OuterUserRoleService outerUserRoleService;
    @Autowired
    private OuterRoleRemoteBaseManager outerRoleRemoteBaseManager;
    @Autowired
    private LinkAppOuterRoleService linkAppOuterRoleService;
    @Autowired
    private EnterpriseRelationCopyService enterpriseRelationCopyService;
    @Autowired
    private ERAccountActiveConfigService erAccountActiveConfigService;
    @Autowired
    private RemoteManager remoteManager;
    @Autowired
    private EmployeeCardEntityDao employeeCardEntityDaoRemoteManager;
    public static final String API_NAME_ACCOUNT = "AccountObj";
    public static final String API_NAME_CONTACT = "ContactObj";
    public static final String COPY_ALL_Config = "all";
    public static final String COPY_LinkAppConfig = "LinkAppConfig";
    public static final String COPY_LinkAppData = "LinkAppData";
    public static final String COPY_RelationConfig = "RelationConfig";
    public static final String COPY_AccountActiveConfig = "AccountActiveConfig";
    public static final String COPY_OuterRoleData = "OuterRoleData";

    /**
     * 检查并修复客户数据
     * 1、检查客户联系人是否创建成功
     * 2、检查联系人是否关联互联用户
     * 3、检查联系人对应的互联用户是否创建成功
     * 4、检查客户对应的互联企业是否创建成功
     *
     * @return java.util.List<com.facishare.rest.account.outrest.DataFixToolsController.ObjDataFixResult>
     * @since 22:24 2023/11/18
     **/
    @PostMapping("/checkAndFixAccountObjData")
    public List<ObjDataFixResult> checkAndFixAccountObjData(@RequestBody AccountDataFixArg arg) {
        List<ObjDataFixResult> results = Lists.newArrayList();
        if (CollectionUtils.isEmpty(arg.getEas()) || CollectionUtils.isEmpty(arg.getFilterType())) {
            log.info("checkAndFixAccountObjData-params error!");
            return results;
        }
        Integer batchSize = arg.getBatchSize() == null ? 500 : arg.getBatchSize();
        List<String> outerRoleIds = arg.getOuterRoleIds();
        Boolean isExecute = arg.getExecute();
        Boolean isTestMode = arg.getIsTestMode();
        arg.getEas().forEach(upstreamEa -> {
            Integer upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
            log.info("checkAndFixAccountObjData-upstreamEa:{}, ei:{}", upstreamEa, upstreamEi);
            OffsetUtil.offsetFunction(batchSize, (offset, limit) -> {
                // 查询企业客户数据
                List<ObjectData> accountObjs = queryAccountObjDataList(upstreamEi, limit, offset, arg.getFilterType());
                if (CollectionUtils.isNotEmpty(accountObjs)) {
                    accountObjs.forEach(accountObj -> {
                        String accountId = accountObj.getId();
                        String accountName = accountObj.getName();
                        String accountNo = accountObj.getString("account_no");
                        String contactMobile = accountObj.getString("tel");
                        String contactName = accountObj.getString("contacts");
                        String contactType = accountObj.getString("field_2m89p__c");
                        String bindOuterTenantId = accountObj.getString("enterpriserelation_id"); //绑定互联企业
                        String bindOuterUserId = null;
                        Integer contactOwner = accountObj.getOwner();
                        log.info("ProcessAccount->start,id:{},name:{},accNo:{},mobile:{},contact:{}", accountId, accountName, accountNo, contactMobile, contactName);
                        if (StringUtils.isEmpty(contactMobile)) {
                            log.info("checkAndFixAccountObjData-AccountObj-upstreamEa:{},accountId:{},accountName:{},accountMobile:{}", upstreamEa, accountId, accountName, contactMobile);
                            return;
                        }
                        if (StringUtils.isEmpty(contactName)) {
                            contactName = accountName;
                        }
                        // 1、检测客户联系人是否存在
                        ObjectData contactObj = queryContactIsExists(upstreamEi, contactMobile, contactName, accountId);
                        String contactId = null;
                        if (contactObj == null) {
                            // 1.1、创建客户联系人
                            if (BooleanUtils.isTrue(isExecute) && StringUtils.isNotEmpty(contactName)) {
                                contactObj = createContactObjData(upstreamEi, accountId, contactName, contactMobile, contactType, contactOwner);
                            } else {
                                log.info("checkAndFixAccountObjData->contactObj is not exists! upEi:{},accountId:{},accountName:{},moblie:{}", upstreamEi, accountId, contactName, contactMobile);
                            }
                        }
                        if (contactObj != null) {
                            contactId = contactObj.getId();
                            bindOuterUserId = contactObj.getString("publicemployee_id");
                        }
                        // 2、检测客户是否关联互联企业
                        if (StringUtils.isEmpty(bindOuterTenantId) && StringUtils.isNotEmpty(accountName)) {
                            String enterpriseName = StringUtils.isEmpty(accountNo) ? accountName : (accountName + "(" + accountNo + ")");
                            ObjectData enterpriseRelationObj = queryEnterpriseRelationIsExists(upstreamEi, enterpriseName, null);
                            if (enterpriseRelationObj == null) {
                                //2.1、创建互联企业
                                if (BooleanUtils.isTrue(isExecute)) {
                                    AddActionResult relationObj = createEnterpriseRelationObj(upstreamEi, accountId, enterpriseName, arg.getTemplateEa(), arg.getInitFsAccount());
                                    if (relationObj != null && relationObj.getObjectData() != null && !relationObj.getObjectData().isEmpty()) {
                                        enterpriseRelationObj = ObjectData.convert(relationObj.getObjectData());
                                    }
                                } else {
                                    log.info("checkAndFixAccountObjData->enterpriseRelationObj is not exists! upEi:{},accountId:{},name:{}", upstreamEi, accountId, enterpriseName);
                                }
                            } else {
                                // 2.2、更新互联企业关联的客户ID字段
                                String mapperAccountId = enterpriseRelationObj.getString("mapper_account_id");
                                if (StringUtils.isEmpty(mapperAccountId)) {
                                    updateEnterpriseRelationObjData(upstreamEi, enterpriseRelationObj.getId(), accountId);
                                }
                            }
                            if (enterpriseRelationObj != null && !enterpriseRelationObj.isEmpty()) {
                                bindOuterTenantId = enterpriseRelationObj.getId();
                                //2.3、更新客户绑定互联企业字段
                                if (BooleanUtils.isTrue(isExecute)) {
                                    updateAccountObjData(upstreamEi, accountId, bindOuterTenantId);
                                } else {
                                    log.info("checkAndFixAccountObjData->accountObj is not bind enterprise! upEi:{},accountId:{},outerTenantId:{}", upstreamEi, accountId, bindOuterTenantId);
                                }
                            }
                        }

                        if (StringUtils.isNotEmpty(bindOuterTenantId)) {
                            ObjectData publicEmployeeObj = queryPublicEmployeeIsExists(upstreamEi, bindOuterTenantId, contactMobile);
                            // 3、检查互联用户是否创建
                            if (publicEmployeeObj == null) {
                                // 3.1、创建互联用户
                                if (BooleanUtils.isTrue(isExecute) && StringUtils.isNotEmpty(contactName)) {
                                    AddActionResult employeeObjData = createPublicEmployeeObjData(upstreamEi, contactId, contactName, contactMobile, bindOuterTenantId, outerRoleIds,
                                        arg.getInitFsAccount());
                                    if (employeeObjData != null && employeeObjData.getObjectData() != null && !employeeObjData.getObjectData().isEmpty()) {
                                        publicEmployeeObj = ObjectData.convert(employeeObjData.getObjectData());
                                    }
                                } else {
                                    log.info("checkAndFixAccountObjData->publicEmployeeObj is not exists! upEi:{},outerTenantId:{},mobile:{},name:{}", upstreamEi, bindOuterTenantId, contactMobile,
                                        contactName);
                                }
                            } else {
                                // 3.2、更新互联用户关联联系人字段
                                String mapperContactId = publicEmployeeObj.getString("contract_id");
                                if (StringUtils.isEmpty(mapperContactId)) {
                                    updatePublicEmployeeObjData(upstreamEi, publicEmployeeObj.getId(), null, contactId);
                                }
                            }
                            // 4、检测联系人是否关联互联用户
                            if (publicEmployeeObj != null && !publicEmployeeObj.isEmpty()) {
                                // 联系人未绑定或绑定错误的互联用户ID
                                if (StringUtils.isEmpty(bindOuterUserId) || !bindOuterUserId.equals(publicEmployeeObj.getId())) {
                                    bindOuterUserId = publicEmployeeObj.getId();
                                    // 4.1、更新联系人关联互联用户状态
                                    if (BooleanUtils.isTrue(isExecute)) {
                                        updateContactObjData(upstreamEi, contactId, bindOuterUserId);
                                    } else {
                                        log.info("checkAndFixAccountObjData->contactObj is not bind publicEmployee! upEi:{},contactId:{},outerUserId:{}", upstreamEi, contactId, bindOuterUserId);
                                    }
                                }
                            }
                            // 5、检查互联用户是否设置互联角色
                            if (publicEmployeeObj != null && !publicEmployeeObj.isEmpty()) {
                                String outerUserId = publicEmployeeObj.getId();
                                String bindContactId = null;
                                if (publicEmployeeObj.containsKey(PublicEmployeeObjConstant.CONTRACT_ID)) {
                                    bindContactId = publicEmployeeObj.getString(PublicEmployeeObjConstant.CONTRACT_ID);
                                }
                                List<String> bindRoleIds = queryPublicEmployeeHasRoles(upstreamEa, Long.valueOf(outerUserId), outerRoleIds);
                                log.info("checkAndFixAccountObjData->publicEmployeeObj, upEi:{},outerUserId:{},roleIds:{},contactId:{}", upstreamEi, outerUserId, bindRoleIds, bindContactId);
                                if (CollectionUtils.isEmpty(bindRoleIds) || StringUtils.isEmpty(bindContactId)) {
                                    if (BooleanUtils.isTrue(isExecute)) {
                                        updatePublicEmployeeObjData(upstreamEi, outerUserId, outerRoleIds, contactId);
                                    } else {
                                        log.info("checkAndFixAccountObjData->publicEmployeeObj is not roleIds! upEi:{},roleIds:{},contactId:{},outerUserId:{}", upstreamEi, outerRoleIds, contactId,
                                            contactMobile, outerUserId);
                                    }
                                }
                            }
                        }
                    });
                }
                if (isTestMode) {
                    log.info("checkAndFixAccountObjData->testMode exit!");
                    return 0;
                }
                return CollectionUtils.isEmpty(accountObjs) ? 0 : accountObjs.size();
            });
        });
        return results;
    }

    /**
     * 更新互联企业联系的客户ID
     *
     * @param upstreamEi 上游企业
     * @param objId 互联企业ID
     * @param accountId 客户ID
     * @return 更新结果
     */
    private Boolean updateEnterpriseRelationObjData(Integer upstreamEi, String objId, String accountId) {
        if (upstreamEi == null || StringUtils.isEmpty(objId) || StringUtils.isEmpty(accountId)) {
            return false;
        }
        try {
            User user = User.systemUser(upstreamEi + "");
            Map<String, Object> objectMap = Maps.newHashMap();
            objectMap.put(PublicEmployeeObjConstant.ID, objId);
            objectMap.put(PublicEmployeeObjConstant.OUTER_TENANT_ID, objId);
            objectMap.put(EnterpriseRelationObjConstant.MAPPER_ACCOUNT_ID, accountId);
            BaseObjectSaveAction.Result triggerResult = actionCallManager.triggerEnterpriseRelationEditAction(user, ObjectDataDocument.of(objectMap), true);
            if (triggerResult.getObjectData() != null) {
                EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(triggerResult.getObjectData().toObjectData());
                log.info("fixObjData-updateEnterpriseRelationObjData-success! enterpriseRelationObj:{}", enterpriseRelationObj);
                return true;
            }
        } catch (Exception ex) {
            log.info("checkAndFixAccountObjData-updateEnterpriseRelationObjData-fail! upEi:{},outerTenantId:{},accountId:{}", upstreamEi, objId, accountId);
        }
        return false;
    }

    /**
     * 查询互联用户授权的角色
     *
     * @param upstreamEa 上游企业Ea
     * @param outerUid 互联用户ID
     * @return 互联角色集合
     */
    private List<String> queryPublicEmployeeHasRoles(String upstreamEa, Long outerUid, List<String> checkOuterRoleIds) {
        try {
            com.facishare.enterprise.common.result.Result<List<OuterUserOuterRoleVo>> result = outerUserRoleService.listOuterUserOuterRolesByOuterUids(upstreamEa, Lists.newArrayList(outerUid));
            if (result.isSuccess() && result.getData() != null && result.getData().size() > 0) {
                // 过滤基础角色
                List<String> hasRoles = result.getData().stream()
                    .filter(role -> (!GlobalRoleConstants.ENTERPRISE_ROLE_CODE.equals(role.getRoleId()) && !GlobalRoleConstants.PERSONAL_ROLE_CODE.equals(role.getRoleId())))
                    .map(role -> role.getRoleId()).collect(Collectors.toList());
                // 过滤出待添加的角色
                if (CollectionUtils.isNotEmpty(checkOuterRoleIds) && CollectionUtils.isNotEmpty(hasRoles)) {
                    return hasRoles.stream().filter(roleId -> checkOuterRoleIds.contains(roleId)).collect(Collectors.toList());
                }
                return hasRoles;
            }
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-queryPublicEmployeeHasRoles-fail! upEa:{},outerUid:{}, error:{}", upstreamEa, outerUid, ex);
        }
        return null;
    }

    /**
     * 分页查询上游企业的所有客户信息
     *
     * @param upstreamEi 上游企业EI
     * @param limit 分页大小
     * @param offset 偏移位置
     * @return java.util.List<com.fxiaoke.crmrestapi.common.data.ObjectData>
     * @since 23:50 2023/11/18
     **/
    private List<ObjectData> queryAccountObjDataList(Integer upstreamEi, Integer limit, Integer offset, List<String> recordType) {
        try {
            List<Filter> filters = Lists.newArrayList();
            Filter filter = new Filter();
            filter.setFieldName("record_type"); // 业务类型
            filter.setOperator("EQ"); // 等于
            filter.setFieldValues(recordType);
            filters.add(filter);
            List<ObjectData> objectData = queryObjectDataList(upstreamEi, API_NAME_ACCOUNT, limit, offset, filters);
            return objectData;
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-queryAccountObjDataList-fail!", ex);
        }
        return null;
    }

    /**
     * 更新客户对象数据
     *
     * @param upstreamEi 上游企业EI
     * @param accountId 客户ID
     * @param bindOuterTenantId 更新客户字段数据
     * @return void
     * @since 18:42 2023/11/18
     **/
    private void updateAccountObjData(Integer upstreamEi, String accountId, String bindOuterTenantId) {
        if (upstreamEi == null || StringUtils.isEmpty(accountId) || StringUtils.isEmpty(bindOuterTenantId)) {
            log.info("fixObjData-updateAccountObjData-fail! upEi:{},accountId:{},bindOuterTenantId:{}", upstreamEi, accountId, bindOuterTenantId);
            return;
        }
        try {
            HeaderObj headerObj = HeaderObj.newInstance(upstreamEi, -10000);
            Map<String, Object> updateObjData = Maps.newHashMap();
            updateObjData.put("enterpriserelation_id", bindOuterTenantId);
            updateObjData.put("enterpriserelation_type", 1);
            ObjectData objectData = ObjectData.convert(updateObjData);
            Result<ObjectData> result = objectDataService.updateObjectData(headerObj, API_NAME_ACCOUNT, accountId, objectData);
            if (result.isSuccess()) {
                log.info("fixObjData-updateAccountObjData-success! upEi:{},accountId:{},updata:{}", upstreamEi, accountId, updateObjData);
            }
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-updateAccountObjData-fail!", ex);
        }
    }

    @PostMapping("/checkAndFixContactObjData")
    public List<ObjDataFixResult> checkAndFixContactObjData() {
        List<ObjDataFixResult> results = Lists.newArrayList();
        return results;
    }

    /**
     * 更新联系人对象数据
     *
     * @param upstreamEi 上游企业ID
     * @param contactId 联系人ID
     * @param bindOuterUserId 联系人更新数据
     * @return void
     * @since 19:02 2023/11/18
     **/
    private void updateContactObjData(Integer upstreamEi, String contactId, String bindOuterUserId) {
        if (upstreamEi == null || StringUtils.isEmpty(contactId) || StringUtils.isEmpty(bindOuterUserId)) {
            log.info("fixObjData-updateContactObjData-fail! upEi:{},contactId:{},bindOuterUserId:{}", upstreamEi, contactId, bindOuterUserId);
            return;
        }
        try {
            Map<String, Object> updateObjData = Maps.newHashMap();
            updateObjData.put("publicemployee_id", bindOuterUserId);
            updateObjData.put("publicemployee_type", 1);
            HeaderObj headerObj = HeaderObj.newInstance(upstreamEi, -10000);
            ObjectData objectData = ObjectData.convert(updateObjData);
            Result<ObjectData> result = objectDataService.updateObjectData(headerObj, API_NAME_CONTACT, contactId, objectData);
            if (result.isSuccess()) {
                log.info("fixObjData-updateContactObjData-success! upEi:{},contactId:{},updata:{}", upstreamEi, contactId, updateObjData);
            }
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-updateContactObjData-fail!", ex);
        }
    }

    /**
     * 查询客户联系人是否存在
     *
     * @param upstreamEi 上游企业EI
     * @param mobile 客户联系人手机号
     * @param name 客户联系人名称
     * @return com.fxiaoke.crmrestapi.common.data.ObjectData
     * @since 17:05 2023/11/18
     **/
    private ObjectData queryContactIsExists(Integer upstreamEi, String mobile, String name, String accountId) {
        try {
            if (StringUtils.isEmpty(mobile) && StringUtils.isEmpty(name)) {
                return null;
            }
            List<Filter> filters = Lists.newArrayList();
            if (StringUtils.isNotEmpty(mobile)) {
                Filter filter = new Filter();
                filter.setFieldName("mobile1");
                filter.setOperator("EQ");
                filter.setFieldValues(Lists.newArrayList(mobile));
                filters.add(filter);
            }
            if (StringUtils.isNotEmpty(name)) {
                Filter filter = new Filter();
                filter.setFieldName("name");
                filter.setOperator("EQ");
                filter.setFieldValues(Lists.newArrayList(name));
                filters.add(filter);
            }
            if (StringUtils.isNotEmpty(accountId)) {
                Filter filter = new Filter();
                filter.setFieldName("account_id");
                filter.setOperator("EQ");
                filter.setFieldValues(Lists.newArrayList(accountId));
                filters.add(filter);
            }
            List<ObjectData> objectDatas = queryObjectDataList(upstreamEi, API_NAME_CONTACT, 1, 0, filters);
            if (CollectionUtils.isNotEmpty(objectDatas)) {
                return objectDatas.get(0);
            }
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-queryContactIsExists-fail!", ex);
        }
        return null;
    }

    /**
     * 创建联系人
     *
     * @param upstreamEi 上游企业EI
     * @param accountObjId 客户ID
     * @param name 联系人名称
     * @param mobile 联系人电话
     * @param recordType 联系人业务类型
     * @return com.fxiaoke.crmrestapi.common.result.Result<java.lang.String>
     * @since 17:13 2023/11/18
     **/
    public ObjectData createContactObjData(Integer upstreamEi, String accountObjId, String name, String mobile, String recordType, Integer ownerId) {
        try {
            HeaderObj headerObj = HeaderObj.newInstance(upstreamEi, -10000);
            Map<String, Object> dataMap = Maps.newHashMap();
            if (StringUtils.isEmpty(recordType)) {
                recordType = "default__c";
            }
            Map<String, String> personType = Maps.of("default__c", I18nUtil.get(I18nKeyEnum.EPI_OUTREST_DATAFIXTOOLSCONTROLLER_482), "dealer__c", I18nUtil.get(I18nKeyEnum.EPI_OUTREST_DATAFIXTOOLSCONTROLLER_482_1), "secondary_dealer__c", I18nUtil.get(I18nKeyEnum.EPI_OUTREST_DATAFIXTOOLSCONTROLLER_482_2));
            dataMap.put("account_id", accountObjId); // 关联客户ID
            dataMap.put("name", name);
            dataMap.put("tel", mobile);
            dataMap.put("mobile1", mobile);
            dataMap.put("field_2m89p__c", personType.get(recordType)); // 联系人类型
            dataMap.put("field_0Ko9g__c", "1"); // 门店订货账号-开通
            dataMap.put("field_czQW3__c", ""); // 客户类型-店主
            dataMap.put("field_zOh2N__c", "Y"); // 是否系统创建
            dataMap.put("record_type", "default__c");
            dataMap.put("owner", Lists.newArrayList(ownerId + ""));
            ObjectData contactObjData = ObjectData.convert(dataMap);
            Result<ObjectDataCreateResult> result = objectDataService.create(headerObj, API_NAME_CONTACT, false, true, false, contactObjData);
            log.info("fixObjData-createContactObjData-success! ei:{},accountId:{},name:{},mobile:{},recordType:{}", upstreamEi, accountObjId, name, mobile, recordType);
            return result.isSuccess() ? result.getData().getObjectData() : null;
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-createContactObjData-fail! ei:{},accountId:{},name:{},mobile:{},recordType:{}, errMsg:{}", upstreamEi, accountObjId, name, mobile, recordType,
                ex.getMessage());
            return null;
        }
    }

    @PostMapping("/checkAndFixEnterpriseRelationObjData")
    public List<ObjDataFixResult> checkAndFixEnterpriseRelationObjData() {
        List<ObjDataFixResult> results = Lists.newArrayList();
        return results;
    }

    /**
     * 查询客户关联的互联企业是否存在
     *
     * @param upstreamEi 上游企业EI
     * @param accountName 客户名称
     * @return com.fxiaoke.crmrestapi.common.data.ObjectData
     * @since 18:29 2023/11/18
     **/
    public ObjectData queryEnterpriseRelationIsExists(Integer upstreamEi, String accountName, String accountId) {
        try {
            List<Filter> filters = Lists.newArrayList();
            Filter filter = new Filter();
            filter.setFieldName("name");
            filter.setOperator("EQ");
            // 客户名称
            filter.setFieldValues(Lists.newArrayList(accountName));
            filters.add(filter);
            if (StringUtils.isNotEmpty(accountId)) {
                // 客户Id
                Filter filterAccId = new Filter();
                filterAccId.setFieldName("mapper_account_id");
                filterAccId.setOperator("EQ");
                filterAccId.setFieldValues(Lists.newArrayList(accountId));
                filters.add(filterAccId);
            }
            List<ObjectData> objectDatas = queryObjectDataList(upstreamEi, EnterpriseRelationObjConstant.API_NAME, 1, 0, filters);
            if (CollectionUtils.isNotEmpty(objectDatas)) {
                return objectDatas.get(0);
            }
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-queryEnterpriseRelationIsExists-fail! upstreamEi:{},accountName:{}, errMsg:{}", upstreamEi, accountName, ex.getMessage());
        }
        return null;
    }

    /**
     * 创建互联企业数据
     *
     * @param upstreamEi 上游企业EI
     * @return void
     * @since 18:53 2023/11/18
     **/
    public AddActionResult createEnterpriseRelationObj(Integer upstreamEi, String accountId, String accountName, String templateEa, Boolean initFsAccount) {
        if (upstreamEi == null || StringUtils.isEmpty(accountId) || StringUtils.isEmpty(accountName)) {
            log.warn("createEnterpriseRelationObj-fail! upEi:{},accountId:{},accountName:{}, errMsg:{}", upstreamEi, accountId, accountName, "param is empty");
            return null;
        }
        try {
            Map<String, Object> enterpriseData = Maps.newHashMap();
            enterpriseData.put(EnterpriseRelationObjConstant.MAPPER_ACCOUNT_ID, accountId);
            enterpriseData.put(EnterpriseRelationObjConstant.NAME, accountName);
            if (StringUtils.isNotBlank(templateEa)) {
                enterpriseData.put(EnterpriseRelationObjConstant.TEMPLATE_EA, templateEa);
            }
            if (BooleanUtils.isTrue(initFsAccount)) {
                enterpriseData.put(EnterpriseRelationObjConstant.CRM_OPEN_STATUS, EnterpriseRelationObjConstant.CrmOpenStatus.OPENING);
            }
            enterpriseData.put(EnterpriseRelationObjConstant.REGISTER_TYPE, RegisterType.ADMIN_REGISTER.getType());
            enterpriseData.put(EnterpriseRelationObjConstant.RECORD_TYPE, EnterpriseRelationObjConstant.toRecordType(IdentityType.CORP.getType()));
            User user = User.systemUser(upstreamEi + "");
            EnterpriseRelationAddAction.Result actionResult = actionCallManager.triggerEnterpriseRelationAddAction(user, ObjectDataDocument.of(enterpriseData));
            AddActionResult result = new AddActionResult();
            result.setObjectData(actionResult.getObjectData());
            result.setIsDuplicate(actionResult.getIsDuplicate());
            log.info("fixObjData-createEnterpriseRelationObj-success!upEi:{},enterpriseData:{}", upstreamEi, enterpriseData);
            return result;
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-createEnterpriseRelationObj-fail! upEi:{},accountId:{},accountName:{}, errMsg:{}", upstreamEi, accountId, accountName, ex);
        }
        return null;
    }

    @PostMapping("/checkAndFixPublicEmployeeObjData")
    public List<ObjDataFixResult> checkAndFixPublicEmployeeObjData() {
        List<ObjDataFixResult> results = Lists.newArrayList();
        return results;
    }

    @PostMapping("/checkAndFixEmployeeCardData")
    public List<ObjDataFixResult> checkAndFixEmployeeCardData(@RequestBody PublicEmployeeMainRoleFixArg arg) {
        List<ObjDataFixResult> results = Lists.newArrayList();
        if (CollectionUtils.isEmpty(arg.getEas()) || CollectionUtils.isEmpty(arg.getOuterRoleIds())) {
            log.info("checkAndFixEmployeeCardData-params error!,args:{}", arg);
            return results;
        }
        Integer batchSize = arg.getBatchSize() == null ? 500 : arg.getBatchSize();
        Boolean isExecute = arg.getExecute();
        Boolean isTestMode = arg.getIsTestMode();
        arg.getEas().forEach(upstreamEa -> {
            Integer upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
            log.info("checkAndFixEmployeeCardData-upstreamEa:{}, ei:{}", upstreamEa, upstreamEi);
            ObjDataFixResult fixResult = new ObjDataFixResult(upstreamEi + "", upstreamEa, "checkAndFixEmployeeCardData", 0, 0);
            OffsetUtil.offsetFunction(batchSize, (offset, limit) -> {
                // 查询有租户互联企业
                List<EnterpriseRelationObj> enterpriseRelationObjs = queryEnterpriseRelation(upstreamEi, offset, limit);
                if (CollectionUtils.isNotEmpty(enterpriseRelationObjs)) {
                    enterpriseRelationObjs.stream().filter(enterpriseRelationObj -> !StringUtils.isEmpty(enterpriseRelationObj.getEnterpriseAccount())).forEach(enterpriseRelationObj -> {
                        String downEnterpriseAccount = enterpriseRelationObj.getEnterpriseAccount();
                        Long outerTenantId = enterpriseRelationObj.getOuterTenantId();
                        OffsetUtil.offsetFunction(batchSize, (offset2, limit2) -> {
                            // 分页查询员工ID不为空的互联用户数据
                            List<PublicEmployeeObj> publicEmployeeData = queryPublicEmployeeIsNotEmptyEmployeeId(upstreamEi, outerTenantId, offset2, limit2);
                            if (!CollectionUtils.isEmpty(publicEmployeeData)) {
                                publicEmployeeData.stream().filter(x -> !StringUtils.isEmpty(x.getEmployeeId())).forEach(publicEmployee -> {
                                    Long downOuterTenantId = publicEmployee.getOuterTenantId();
                                    Long outerUserId = publicEmployee.getOuterUserId();
                                    String employeeId = publicEmployee.getEmployeeId();
                                    try {
                                        EmployeeCardEntity employeeCardEntity = employeeCardEntityDaoRemoteManager.getByOuterId(outerUserId);
                                        if (isExecute) {
                                            // 修复员工卡片表数据
                                            if (employeeCardEntity != null) {
                                                if (employeeCardEntity.getEmployeeId() == null || employeeCardEntity.getOuterTenantId() == null || !employeeCardEntity.getOuterTenantId()
                                                    .equals(downOuterTenantId)) {
                                                    employeeCardEntityDaoRemoteManager
                                                        .updateTenantIdEmployeeIdByOuterUid(outerUserId, outerTenantId, downEnterpriseAccount, Integer.valueOf(employeeId));
                                                    fixResult.setSuccess(fixResult.getSuccess() + 1); // 测试模式
                                                }
                                            } else {
                                                employeeCardEntityDaoRemoteManager
                                                    .createCardByEaEmployeeIdAndOuterTenantIdOuterUid(outerUserId, downOuterTenantId, downEnterpriseAccount, Integer.valueOf(employeeId));
                                                fixResult.setSuccess(fixResult.getSuccess() + 1); // 测试模式
                                            }
                                        }
                                    } catch (Exception ex) {
                                        log.warn("updateTenantIdEmployeeIdByOuterUid-fail! upstreamEa:{}, outerTenantId:{}, outerUserId:{},employeeId:{}, ex:{}", upstreamEa, outerTenantId,
                                            outerUserId, employeeId, ex);
                                        fixResult.setFail(fixResult.getFail() + 1);
                                    }
                                });
                            }
                            if (isTestMode) {
                                return 0;
                            }
                            return publicEmployeeData.size();
                        });
                    });
                    return enterpriseRelationObjs.size();
                }
                return 0;
            });

            results.add(fixResult);
        });
        return results;
    }

    private List<EnterpriseRelationObj> queryEnterpriseRelation(Integer ei, Integer offset, Integer limit) {
        List<EnterpriseRelationObj> dataList = Lists.newArrayList();
        List<Filter> filters = Lists.newArrayList();
        Filter filterLifeStatus = new Filter();
        filterLifeStatus.setFieldName("life_status"); // 生命状态
        filterLifeStatus.setOperator("EQ"); // 等于
        filterLifeStatus.setFieldValues(Lists.newArrayList("normal")); // 正常
        filters.add(filterLifeStatus);
        Filter filterHasFsAccount = new Filter();
        filterHasFsAccount.setFieldName("has_fs_account"); // crm账号
        filterHasFsAccount.setOperator("EQ"); // 等于
        filterHasFsAccount.setFieldValues(Lists.newArrayList("true")); // 正常
        filters.add(filterHasFsAccount);
        List<ObjectData> queryResult = queryObjectDataList(ei, EnterpriseRelationObjConstant.API_NAME, limit, offset, filters);
        if (CollectionUtils.isNotEmpty(queryResult)) {
            queryResult.forEach(obj -> dataList.add(EnterpriseRelationObj.newInstance(obj)));
        }
        return dataList;
    }

    /**
     * 查询员工ID不为空的互联用户
     */
    @NotNull
    private List<PublicEmployeeObj> queryPublicEmployeeIsNotEmptyEmployeeId(Integer upstreamEi, Long outerTenantId, Integer offset, Integer limit) {
        List<Filter> filters = Lists.newArrayList();
        Filter filterLifeStatus = new Filter();
        filterLifeStatus.setFieldName("life_status"); // 生命状态
        filterLifeStatus.setOperator("EQ"); // 等于
        filterLifeStatus.setFieldValues(Lists.newArrayList("normal")); // 正常
        filters.add(filterLifeStatus);

        Filter filterOuterTenantId = new Filter();
        filterOuterTenantId.setFieldName("outer_tenant_id"); // 互联企业
        filterOuterTenantId.setOperator("EQ"); // 等于
        filterOuterTenantId.setFieldValues(Lists.newArrayList(outerTenantId + ""));
        filters.add(filterOuterTenantId);

        Filter filterEmployeeId = new Filter();
        filterEmployeeId.setFieldName("employee_id"); // 员工ID
        filterEmployeeId.setOperator("ISN"); // 不为空
        filterEmployeeId.setFieldValues(Lists.newArrayList());
        filters.add(filterEmployeeId);
        List<PublicEmployeeObj> publicEmployeeData = queryPublicEmployeesByFilters(upstreamEi, filters, offset, limit);
        return publicEmployeeData;
    }

    /**
     * 查询某个企业下的所有互联用户数据
     *
     * @param ei 上游企业ei
     * @param filters 互联用户过滤条件
     * @param offset 偏移位置
     * @param limit 分页大小
     * @return 查询结果
     */
    private List<PublicEmployeeObj> queryPublicEmployeesByFilters(Integer ei, List<Filter> filters, Integer offset, Integer limit) {
        List<PublicEmployeeObj> dataList = Lists.newArrayList();
        List<ObjectData> queryResult = queryObjectDataList(ei, PublicEmployeeObjConstant.API_NAME, limit, offset, filters);
        if (CollectionUtils.isNotEmpty(queryResult)) {
            queryResult.forEach(obj -> dataList.add(PublicEmployeeObj.newInstance(obj)));
        }
        return dataList;
    }

    /**
     * 使用联系人手机号码查询互联用户是否创建
     *
     * @param upstreamEi 上游企业EI
     * @param outerTenantId 互联企业ID
     * @param mobile 联系人手机号码
     * @return com.fxiaoke.crmrestapi.common.data.ObjectData
     * @since 18:54 2023/11/18
     **/
    private ObjectData queryPublicEmployeeIsExists(Integer upstreamEi, String outerTenantId, String mobile) {
        try {
            List<Filter> filters = Lists.newArrayList();
            Filter filterMobile = new Filter();
            filterMobile.setFieldName("mobile");
            filterMobile.setOperator("EQ");
            filterMobile.setFieldValues(Lists.newArrayList(mobile));

            Filter filterOuterTenantId = new Filter();
            filterOuterTenantId.setFieldName("outer_tenant_id");
            filterOuterTenantId.setOperator("EQ");
            filterOuterTenantId.setFieldValues(Lists.newArrayList(outerTenantId));
            filters.add(filterMobile);
            filters.add(filterOuterTenantId);
            List<ObjectData> objectDatas = queryObjectDataList(upstreamEi, PublicEmployeeObjConstant.API_NAME, 1, 0, filters);
            if (CollectionUtils.isNotEmpty(objectDatas)) {
                return objectDatas.get(0);
            }
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-queryPublicEmployeeIsExists-fail!", ex);
        }
        return null;
    }

    /**
     * 查询指定ID互联用户信息
     *
     * @param upstreamEi 上游 企业EI
     * @param outerUids 互联用户ID
     * @return 互联用户列表
     */
    private List<ObjectData> queryPublicEmployeeByIds(Integer upstreamEi, Collection<String> outerUids) {
        try {
            List<Filter> filters = Lists.newArrayList();
            Filter filterUids = new Filter();
            filterUids.setFieldName(PublicEmployeeObjConstant.OUTER_UID);
            filterUids.setOperator("IN");
            filterUids.setFieldValues(Lists.newArrayList(outerUids));

            Filter filterType = new Filter();
            filterType.setFieldName(PublicEmployeeObjConstant.TYPE);
            filterType.setOperator("EQ");
            filterType.setFieldValues(Lists.newArrayList("1"));
            filters.add(filterUids);
            filters.add(filterType);
            List<ObjectData> objectDatas = queryObjectDataList(upstreamEi, PublicEmployeeObjConstant.API_NAME, outerUids.size(), 0, filters);
            if (CollectionUtils.isNotEmpty(objectDatas)) {
                return objectDatas;
            }
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-queryPublicEmployeeByIds-fail!", ex);
        }
        return Lists.newArrayList();
    }

    /**
     * 创建互联用户对象数据
     *
     * @param upstreamEi 上游企业EI
     * @return void
     * @since 18:52 2023/11/18
     **/
    public AddActionResult createPublicEmployeeObjData(Integer upstreamEi, String contactId, String contactName, String contactMobile, String outerTenantId, List<String> outerRoleIds,
        Boolean hasFsAccount) {
        if (upstreamEi == null || StringUtils.isEmpty(contactId) || StringUtils.isEmpty(contactName) || StringUtils.isEmpty(contactMobile) || StringUtils.isEmpty(outerTenantId)) {
            log.warn("createPublicEmployeeObjData-fail! upEi:{},contactId:{},outerTenantId:{},name:{},mobile:{}, errMsg:{}", upstreamEi, contactId, outerTenantId, contactName, contactMobile,
                "param is empty");
            return null;
        }
        try {
            User user = User.systemUser(upstreamEi + "");

            Map<String, Object> objectMap = Maps.newHashMap();
            objectMap.put(PublicEmployeeObjConstant.NAME, contactName);
            objectMap.put(PublicEmployeeObjConstant.MOBILE, contactMobile);
            objectMap.put(PublicEmployeeObjConstant.OUTER_TENANT_ID, outerTenantId);
            objectMap.put(PublicEmployeeObjConstant.CONTRACT_ID, contactId);
            objectMap.put(PublicEmployeeObjConstant.OUTER_ROLE_IDS, outerRoleIds);

            Map<String, Object> attributes = Maps.newHashMap();
            if (hasFsAccount) {
                attributes.put(EnterpriseRelationObjConstant.HAS_FS_ACCOUNT, true);
            }
            PublicEmployeeAddAction.Result actionResult = actionCallManager.triggerPublicEmployeeAddAction(user, ObjectDataDocument.of(objectMap), attributes);
            AddActionResult result = new AddActionResult();
            result.setObjectData(actionResult.getObjectData());
            result.setIsDuplicate(actionResult.getIsDuplicate());
            log.info("fixObjData-createPublicEmployeeObjData-success! outerTenantId:{},contactId:{},name:{},mobile:{}", outerTenantId, contactId, contactName, contactMobile);
            return result;
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-createPublicEmployeeObjData-fail! upEi:{},contactId:{},outerTenantId:{},name:{},mobile:{}, errMsg:{}", upstreamEi, contactId, outerTenantId,
                contactName, contactMobile, ex.getMessage());
        }
        return null;
    }

    /**
     * 更新互联用户关联的角色和联系人ID
     *
     * @param upstreamEi 上游企业ID
     * @param roleIds 角色ID
     * @param contactId 联系人ID
     * @return 更新后的互联应用
     */
    public Boolean updatePublicEmployeeObjData(Integer upstreamEi, String objId, List<String> roleIds, String contactId) {
        if (upstreamEi == null || StringUtils.isEmpty(objId)) {
            return null;
        }
        if (CollectionUtils.isEmpty(roleIds) && StringUtils.isEmpty(contactId)) {
            return null;
        }
        try {
            User user = User.systemUser(upstreamEi + "");
            Map<String, Object> objectMap = Maps.newHashMap();
            objectMap.put(PublicEmployeeObjConstant.ID, objId);
            objectMap.put(PublicEmployeeObjConstant.OUTER_UID, objId);
            if (CollectionUtils.isNotEmpty(roleIds)) {
                objectMap.put(PublicEmployeeObjConstant.OUTER_ROLE_IDS, roleIds);
            }
            if (StringUtils.isNotEmpty(contactId)) {
                objectMap.put(PublicEmployeeObjConstant.CONTRACT_ID, contactId);
            }
            BaseObjectSaveAction.Result triggerResult = actionCallManager.triggerPublicEmployeeEditAction(user, ObjectDataDocument.of(objectMap), true);
            if (triggerResult.getObjectData() != null) {
                PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(triggerResult.getObjectData().toObjectData());
                log.info("fixObjData-updatePublicEmployeeObjData-success! publicEmployeeObj:{}", publicEmployeeObj);
                return true;
            }
        } catch (Exception ex) {
            log.info("checkAndFixAccountObjData-updatePublicEmployeeObjData-fail! upEi:{},contactId:{},roleIds:{}", upstreamEi, contactId, roleIds);
        }
        return false;
    }

    /**
     * 分页查询对象数据
     *
     * @return java.util.List<com.fxiaoke.crmrestapi.common.data.ObjectData>
     * @since 15:56 2023/11/18
     **/
    public List<ObjectData> queryObjectDataList(Integer upstreamEi, String apiName, Integer limit, Integer offset, List<Filter> filters) {
        try {
            SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
            queryTemplate.setLimit(limit);
            queryTemplate.setOffset(offset);
            queryTemplate.setNeedReturnCountNum(false);
            queryTemplate.setDataRolePermission(false);
            Wheres wheres = new Wheres();
            wheres.addFilter("include_describe", Lists.newArrayList("false"), "EQ");
            wheres.addFilter("include_layout", Lists.newArrayList("false"), "EQ");
            queryTemplate.setWheres(Lists.newArrayList(wheres));
            queryTemplate.setFilters(filters);
            HeaderObj headerObj = HeaderObj.newInstance(upstreamEi, 1000);
            QueryBySearchTemplateResult result = objectDataService.queryBySearchTemplate(headerObj, apiName, queryTemplate).getData();
            if (result.getQueryResult().getData() != null && result.getQueryResult().getData().size() > 0) {
                return result.getQueryResult().getData();
            }
        } catch (Exception ex) {
            log.warn("checkAndFixAccountObjData-queryObjectDataList-fail!", ex);
        }
        return null;
    }

    /**
     * 查询对象数据
     *
     * @param upstreamEi 上游企业EI
     * @param apiName 对象名称
     * @param id 对象ID
     * @return com.fxiaoke.crmrestapi.common.data.ObjectData
     * @since 19:18 2023/11/18
     **/
    public ObjectData queryObjectData(Integer upstreamEi, String apiName, String id) {
        HeaderObj headerObj = HeaderObj.newInstance(upstreamEi, 1000);
        Result<ObjectDataGetByIdResult> result = objectDataService.getById(headerObj, apiName, id, false);
        return result.isSuccess() ? result.getData().getObjectData() : null;
    }

    /**
     * 复制模板企业配置到下游企业
     *
     * @param arg 复制参数
     * @return java.util.Map<java.lang.String, java.lang.Boolean>
     * @since 19:18 2023/11/18
     **/
    @PostMapping("/copyTemplateConfigToDownstream")
    public Map<String, Map<String, Boolean>> copyTemplateConfigToDownstream(@RequestBody CopyTemplateConfigToDownstreamArg arg) {
        if (arg.getTemplateEa() == null || arg.getToEas() == null || arg.getToEas().size() == 0 || arg.getConfigNames() == null) {
            return null;
        }
        Map<String, Map<String, Boolean>> copyResultMap = new HashMap<>();
        try {
            String templateEa = arg.getTemplateEa();
            List<String> toEas = arg.getToEas();
            List<String> configNames = arg.getConfigNames();
            for (String downstreamEa : toEas) {
                Map<String, Boolean> copyResult = new HashMap<>();
                if (configNames.contains(COPY_ALL_Config) || configNames.contains(COPY_OuterRoleData)) {
                    // 复制“互联角色”相关数据
                    boolean result = enterpriseRelationCopyService.copyEnterpriseOuterRoleData(templateEa, downstreamEa);
                    copyResult.put(COPY_OuterRoleData, result);
                }
                if (configNames.contains(COPY_ALL_Config) || configNames.contains(COPY_LinkAppData)) {
                    // 复制“互联应用”相关数据
                    boolean result = enterpriseRelationCopyService.copyEnterpriseLinkAppData(templateEa, downstreamEa);
                    copyResult.put(COPY_LinkAppData, result);
                }
                if (configNames.contains(COPY_ALL_Config) || configNames.contains(COPY_LinkAppConfig)) {
                    // 复制“互联应用”配置数据
                    boolean result = enterpriseRelationCopyService.copyEnterpriseLinkAppConfigData(templateEa, downstreamEa);
                    copyResult.put(COPY_LinkAppConfig, result);
                }
                if (configNames.contains(COPY_ALL_Config) || configNames.contains(COPY_RelationConfig)) {
                    // 复制“互联设置”配置数据
                    boolean result = enterpriseRelationCopyService.copyEnterpriseRelationConfigData(templateEa, downstreamEa);
                    copyResult.put(COPY_RelationConfig, result);
                }
                if (configNames.contains(COPY_ALL_Config) || configNames.contains(COPY_AccountActiveConfig)) {
                    // 复制“互联账号自注册”配置数据
                    boolean result = enterpriseRelationCopyService.copyLoginRegisterConfigData(templateEa, downstreamEa);
                    copyResult.put(COPY_AccountActiveConfig, result);
                }
                copyResultMap.put(downstreamEa, copyResult);
                log.info("copyTemplateConfigToDownstream-success! templateEa:{},downstreamEa:{},copyResult:{}", templateEa, downstreamEa, copyResult);
            }
        } catch (Exception ex) {
            log.warn("copyTemplateConfigToDownstream-fail!", ex);
        }
        return copyResultMap;
    }

    /**
     * 修复蒙牛互联用户主角色缺失问题
     *
     * @param arg 修复数据参数
     * @return 修复结果
     */
    @PostMapping("/checkAndFixPublicEmployeeMainRole")
    public List<ObjDataFixResult> checkAndFixPublicEmployeeMainRole(@RequestBody PublicEmployeeMainRoleFixArg arg) {
        List<ObjDataFixResult> results = Lists.newArrayList();
        if (CollectionUtils.isEmpty(arg.getEas()) || CollectionUtils.isEmpty(arg.getOuterRoleIds())) {
            log.info("checkAndFixPublicEmployeeMainRole-params error!,args:{}", arg);
            return results;
        }
        Integer batchSize = arg.getBatchSize() == null ? 500 : arg.getBatchSize();
        List<String> outerRoleIds = arg.getOuterRoleIds();
        Boolean updateMainRoleIfExists = arg.getUpdateMainRoleIfExists();
        Boolean isExecute = arg.getExecute();
        Boolean isTestMode = arg.getIsTestMode();
        arg.getEas().forEach(upstreamEa -> {
            Integer upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
            log.info("checkAndFixPublicEmployeeMainRole-upstreamEa:{}, ei:{}", upstreamEa, upstreamEi);
            ObjDataFixResult fixResult = new ObjDataFixResult(upstreamEi + "", upstreamEa, "userAppRoleMainAssigns", 0, 0);
            OffsetUtil.offsetFunction(batchSize, (offset, limit) -> {
                List<UserRoleData> userRoleData = queryPublicEmployeeByRoleIds(upstreamEi, outerRoleIds, offset, limit);
                if (!CollectionUtils.isEmpty(userRoleData)) {
                    Collection<String> outerUids = userRoleData.stream().map(x -> x.getUserId()).collect(Collectors.toSet()); // 所有互联用户
                    List<ObjectData> dataList = queryPublicEmployeeByIds(upstreamEi, outerUids);
                    Collection<String> validUids = dataList.stream().map(x -> x.getId()).collect(Collectors.toSet()); // 未停用互联用户
                    userRoleData.stream().filter(x -> !StringUtils.isEmpty(x.getUserId()) && validUids.contains(x.getUserId())).forEach(userRole -> {
                        if (!StringUtils.isEmpty(userRole.getRoleCode()) && outerRoleIds.contains(userRole.getRoleCode()) && !userRole.getMajorRole()) {
                            Boolean hasUpdateMainRole = userRole.getMajorRole();
                            if (isExecute) {
                                // todo: 更新互联用户的主角色
                                String mainRoleId = userRole.getRoleCode();
                                Collection<Long> downOuterUids = Collections.singletonList(Long.valueOf(userRole.getUserId()));
                                try {
                                    hasUpdateMainRole = linkAppOuterRoleService.batchUpdateAppRoleMainAssigns(upstreamEa, mainRoleId, downOuterUids, BooleanUtils.isTrue(updateMainRoleIfExists))
                                        .isSuccess();
                                } catch (Exception ex) {
                                    log.warn("batchUpdateAppRoleMainAssigns-fail! upstreamEa:{}, mainRoleId:{}, downOuterUids:{}, ex:{}", upstreamEa, mainRoleId, downOuterUids, ex);
                                    hasUpdateMainRole = false;
                                }
                                if (hasUpdateMainRole) {
                                    fixResult.setSuccess(fixResult.getSuccess() + 1);
                                } else {
                                    fixResult.setFail(fixResult.getFail() + 1);
                                }
                            } else {
                                fixResult.setSuccess(fixResult.getSuccess() + 1); // 测试模式
                            }
                            log.info("checkAndFixPublicEmployeeMainRole-userRole:{}, majorRole:{}", userRole, hasUpdateMainRole);
                        }
                    });
                }
                if (isTestMode) {
                    return 0;
                }
                return userRoleData.size();
            });
            results.add(fixResult);
        });
        return results;
    }

    /**
     * 分页查询授权指定角色的互联用户
     *
     * @param fsEi 上游企业EI
     * @param roleIds 互联角色ID
     * @param offset 偏移
     * @param limit 分页大小
     * @return 返回数据
     */
    private List<UserRoleData> queryPublicEmployeeByRoleIds(Integer fsEi, List<String> roleIds, Integer offset, Integer limit) {
        try {
            OffsetArg offsetArg = new OffsetArg();
            offsetArg.setOffset(offset);
            offsetArg.setLimit(limit);
            UserRoleDataPageResult userRoleDataPageResult = outerRoleRemoteBaseManager.queryOuterUserByUpsteramEiAndOuterRoleIds(fsEi, roleIds, offsetArg);
            List<UserRoleData> userRoleData = userRoleDataPageResult.getOuterRoleDatas();
            return userRoleData;
        } catch (Exception ex) {
            log.warn("queryPublicEmployeeByRoleIds-fail!", ex);
            return new ArrayList<>();
        }
    }

    /**
     * 复制模板企业的互联应用-外部数据权限-外部团队数据权限
     *
     * @param arg 复制参数
     */
    @PostMapping("/syncOuterRelatedTeamDataPermissionConfig")
    public List<ObjDataFixResult> syncOuterRelatedTeamDataPermissionConfig(@RequestBody CopyOuterRelatedTeamDataPermissionConfigArg arg) {
        List<ObjDataFixResult> results = Lists.newArrayList();
        if (StringUtils.isEmpty(arg.getFromEa()) || CollectionUtils.isEmpty(arg.getToEas()) || CollectionUtils.isEmpty(arg.getObjApiNames())) {
            log.info("syncOuterRelatedTeamDataPermissionConfig-params error!,args:{}", arg);
            return results;
        }
        Integer tenantId = eieaConverter.enterpriseAccountToId(arg.getFromEa());
        List<OutRelatedTeamDataPermissionConfig> outRelatedTeamDataPermissionConfigs = Lists.newArrayList();
        for (String objApiName : arg.getObjApiNames()) {
            OutRelatedTeamDataPermissionConfig outRelatedTeamDataPermissionConfig = getOutRelatedTeamDataPermissionConfig(tenantId, objApiName);
            if (outRelatedTeamDataPermissionConfig != null) {
                outRelatedTeamDataPermissionConfigs.add(outRelatedTeamDataPermissionConfig);
            }
        }
        if (CollectionUtils.isNotEmpty(outRelatedTeamDataPermissionConfigs)) {
            for (String toEa : arg.getToEas()) {
                Integer targetTenantId = eieaConverter.enterpriseAccountToId(toEa);
                for (OutRelatedTeamDataPermissionConfig config : outRelatedTeamDataPermissionConfigs) {
                    if (arg.getExecute()) {
                        updateOutRelatedTeamDataPermissionConfig(targetTenantId, config);
                    }
                    log.info("syncOuterRelatedTeamDataPermissionConfig-targetTenantId:{} ,config:{}", targetTenantId, config);
                }
                if (arg.getIsTestMode()) {
                    break;
                }
            }
        }
        return results;
    }

    /**
     * 查询对象外部相关团队数据权限配置
     *
     * @param apiName 对象名称
     */
    private OutRelatedTeamDataPermissionConfig getOutRelatedTeamDataPermissionConfig(Integer tenantId, String apiName) {
        try {
            OutRelatedTeamDataPermissionConfig res = new OutRelatedTeamDataPermissionConfig();
            // 查出对象所有字段描述
            ReferenceMapperObjectFieldOutPermissionConfig referenceMapperObjectFieldDescribe = remoteManager.getReferenceMapperObjectFieldDescribe(tenantId, apiName);
            if (referenceMapperObjectFieldDescribe == null) {
                return null;
            }
            List<PaasFieldApiNameAndLabelData> referenceAccountObjOutTeamFieldData = referenceMapperObjectFieldDescribe.getReferenceAccountObjOuterTeamFieldDatas();
            List<PaasFieldApiNameAndLabelData> referencePartnerObjOutTeamFieldData = referenceMapperObjectFieldDescribe.getReferencePartnerObjOuterTeamFieldDatas();
            res.setRelationOuterDataPrivilege(FieldDescribeExt.RELATION_NOT_RELATED);
            if (!referenceAccountObjOutTeamFieldData.isEmpty()) {
                res.setRelationOuterDataPrivilege(referenceAccountObjOutTeamFieldData.get(0).getRelationOuterDataPrivilege());
            }
            if (!referencePartnerObjOutTeamFieldData.isEmpty()) {
                res.setRelationOuterDataPrivilege(referencePartnerObjOutTeamFieldData.get(0).getRelationOuterDataPrivilege());
            }
            res.setObjectApiName(apiName);
            res.setOutOwnerObjectReferenceAccountFieldApiName(referenceAccountObjOutTeamFieldData.stream().map(PaasFieldApiNameAndLabelData::getFieldApiName).collect(Collectors.toList()));
            res.setOutOwnerObjectReferencePartnerFieldApiName(referencePartnerObjOutTeamFieldData.stream().map(PaasFieldApiNameAndLabelData::getFieldApiName).collect(Collectors.toList()));
            return res;
        } catch (Exception ex) {
            log.warn("getOutRelatedTeamDataPermissionConfig-fail!", ex);
            return null;
        }
    }

    /**
     * 更新企业指定对象的外部团队数据权限配置
     *
     * @param tenantId 目标企业EI
     * @param arg 对象外部团队数据权限配置
     */
    private void updateOutRelatedTeamDataPermissionConfig(Integer tenantId, OutRelatedTeamDataPermissionConfig arg) {
        // 修改该对象的这个字段描述的外部数据权限属性
        if (StringUtils.isBlank(arg.getObjectApiName()) || StringUtils.isBlank(arg.getRelationOuterDataPrivilege())) {
            return;
        }
        RelationOuterDataPrivilegeEnum relationOuterDataPrivilegeEnum = RelationOuterDataPrivilegeEnum.getByType(arg.getRelationOuterDataPrivilege());
        if (relationOuterDataPrivilegeEnum == null) {
            return;
        }
        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        ObjectDescribe objectDescribe = remoteManager.getObjectDescribe(ea, arg.getObjectApiName());
        if (objectDescribe == null || objectDescribe.getApiName() == null) {
            return;
        }
        // 查出已经存在的
        ReferenceMapperObjectFieldOutPermissionConfig referenceMapperObjectFieldDescribe = ReferenceMapperObjectFieldOutPermissionConfig.newInstance(objectDescribe);
        Set<PaasFieldApiNameAndLabelData> currentUpdateFieldApiNameDatas = new HashSet<>();
        currentUpdateFieldApiNameDatas.addAll(referenceMapperObjectFieldDescribe.getReferenceAccountObjOuterTeamFieldDatas());
        currentUpdateFieldApiNameDatas.addAll(referenceMapperObjectFieldDescribe.getReferencePartnerObjOuterTeamFieldDatas());

        Set<String> finalUpdateFieldApiNames = new HashSet<>();
        finalUpdateFieldApiNames.addAll(arg.getOutOwnerObjectReferencePartnerFieldApiName());
        finalUpdateFieldApiNames.addAll(arg.getOutOwnerObjectReferenceAccountFieldApiName());
        Set<PaasFieldApiNameAndLabelData> finalUpdateFieldApiNameDatas = referenceMapperObjectFieldDescribe.getByFieldApiName(finalUpdateFieldApiNames);
        for (PaasFieldApiNameAndLabelData finalUpdateFieldApiNameData : finalUpdateFieldApiNameDatas) {
            finalUpdateFieldApiNameData.setRelationOuterDataPrivilege(arg.getRelationOuterDataPrivilege());
        }
        // 计算需要删除的
        List<PaasFieldApiNameAndLabelData> toRemoveFieldApiNameDatas = currentUpdateFieldApiNameDatas.stream().filter(x -> !finalUpdateFieldApiNames.contains(x.getFieldApiName()))
            .collect(Collectors.toList());
        // 计算需要修改的
        List<PaasFieldApiNameAndLabelData> toUpdateFieldApiNameDatas = finalUpdateFieldApiNameDatas.stream().filter(obj -> !currentUpdateFieldApiNameDatas.contains(obj)).collect(Collectors.toList());
        if (!org.springframework.util.CollectionUtils.isEmpty(toRemoveFieldApiNameDatas)) {
            for (PaasFieldApiNameAndLabelData toRemoveFieldApiNameData : toRemoveFieldApiNameDatas) {
                try {
                    remoteManager.updateObjectReferenceOutDataPermissionConfig(tenantId, objectDescribe, toRemoveFieldApiNameData.getFieldApiName(), FieldDescribeExt.RELATION_NOT_RELATED);
                } catch (Exception ex) {
                    log.warn("updateOutRelatedTeamDataPermissionConfig-fail1!", ex);
                }
            }
        }
        // 更新字段对象外部团队数据权限
        if (!org.springframework.util.CollectionUtils.isEmpty(toUpdateFieldApiNameDatas)) {
            for (PaasFieldApiNameAndLabelData toUpdateFieldApiNameData : toUpdateFieldApiNameDatas) {
                try {
                    remoteManager.updateObjectReferenceOutDataPermissionConfig(tenantId, objectDescribe, toUpdateFieldApiNameData.getFieldApiName(), arg.getRelationOuterDataPrivilege());
                } catch (Exception ex) {
                    log.warn("updateOutRelatedTeamDataPermissionConfig-fail2!", ex);
                }
            }
        }
    }

    /**
     * 复制模板企业“自注册配置”到下游企业（蒙牛）
     *
     * @param arg 复制参数
     * @return 复制结果
     */
    @PostMapping("/syncERAccountActiveConfig")
    public List<ObjDataFixResult> syncERAccountActiveConfig(@RequestBody UpdateERAccountActiveConfigArg arg) {
        if (StringUtils.isBlank(arg.getLinkType()) || arg.getLoginIdentityType() == null || StringUtils.isBlank(arg.getMapperApiName()) || CollectionUtils.isEmpty(arg.getEas())) {
            log.info("syncERAccountActiveConfig-fail! arg={}", arg);
            return Lists.newArrayList();
        }
        List<ObjDataFixResult> result = Lists.newArrayList();
        ERAccountActiveConfigData activeConfigTemplate = null;
        if (StringUtils.isNotEmpty(arg.getTemplateEa())) {
            activeConfigTemplate = erAccountActiveConfigService.getERAccountActiveConfig(arg.getTemplateEa(), arg.getLoginIdentityType(), arg.getMapperApiName()).getData();
        }
        for (String upEa : arg.getEas()) {
            Integer tenantId = eieaConverter.enterpriseAccountToId(upEa);
            ERAccountActiveConfigData activeConfig = erAccountActiveConfigService.getERAccountActiveConfig(upEa, arg.getLoginIdentityType(), arg.getMapperApiName()).getData();
            if (activeConfig != null) {
                if (arg.getContactRequire() != null) {
                    activeConfig.setContactRequire(arg.getContactRequire());
                }
                if (arg.getAccountRequire() != null) {
                    activeConfig.setAccountRequire(arg.getAccountRequire());
                }
                if (CollectionUtils.isNotEmpty(arg.getOuterRoleIds())) {
                    activeConfig.setOuterRoleIds(arg.getOuterRoleIds());
                }
                if (StringUtils.isNotEmpty(arg.getContactDetailLayoutApiName())) {
                    activeConfig.setContactDetailLayoutApiName(arg.getContactDetailLayoutApiName());
                }
                if (StringUtils.isNotEmpty(arg.getAccountDetailLayoutApiName())) {
                    activeConfig.setAccountDetailLayoutApiName(arg.getAccountDetailLayoutApiName());
                }
                if (StringUtils.isNotEmpty(arg.getAccountRecordType())) {
                    activeConfig.setAccountRecordType(arg.getAccountRecordType());
                }
                if (StringUtils.isNotEmpty(arg.getContactRecordType())) {
                    activeConfig.setContactRecordType(arg.getContactRecordType());
                }
            } else if (activeConfigTemplate != null) {
                activeConfig = activeConfigTemplate;
            }
            if (activeConfig != null && arg.getExecute()) {
                boolean success = erAccountActiveConfigService.updateERAccountActiveConfig(upEa, activeConfig).isSuccess();
                if (success) {
                    result.add(new ObjDataFixResult(tenantId + "", upEa, "updateERAccountActiveConfig", 1, 0));
                } else {
                    result.add(new ObjDataFixResult(tenantId + "", upEa, "updateERAccountActiveConfig", 0, 1));
                }
            }
            log.info("syncERAccountActiveConfig-success! upEa:{},activeConfig:{}", upEa, activeConfig);
            if (arg.getIsTestMode()) {
                break;
            }
        }
        return result;
    }

    @Data
    public static class UpdateERAccountActiveConfigArg extends ObjDataFixArg {
        private String templateEa;
        private String linkType;
        private Integer loginIdentityType;
        private String mapperApiName;
        private List<String> outerRoleIds;
        private Boolean contactRequire;
        private Boolean accountRequire;
        private String contactDetailLayoutApiName;
        private String accountDetailLayoutApiName;
        private String contactRecordType;
        private String accountRecordType;
    }

    @Data
    public static class OutRelatedTeamDataPermissionConfig implements Serializable {
        private String objectApiName;
        private String relationOuterDataPrivilege;
        private List<String> outOwnerObjectReferenceAccountFieldApiName;
        private List<String> outOwnerObjectReferencePartnerFieldApiName;
    }

    @Data
    public static class CopyOuterRelatedTeamDataPermissionConfigArg extends ObjDataFixArg {
        private String fromEa;
        private List<String> toEas;
        private List<String> objApiNames;
    }

    @Data
    public static class CopyTemplateConfigToDownstreamArg implements Serializable {
        /**
         * 模板企业Ea
         */
        private String templateEa;
        /**
         * 目标企业Ea
         */
        private List<String> toEas;
        /**
         * 配置名称
         */
        private List<String> configNames;
        /**
         * 是否执行
         */
        protected Boolean execute;
    }

    @Data
    public static class ObjDataFixArg implements Serializable {
        protected List<String> eas;
        protected Boolean initAll;
        protected Boolean execute;
        protected Integer batchSize = 100;
        protected Boolean isTestMode = true;
    }

    @Data
    public static class AccountDataFixArg extends ObjDataFixArg {
        private String templateEa;
        private Boolean initFsAccount = false;
        private List<String> outerRoleIds;
        private List<String> filterType = Lists.newArrayList();
    }

    @Data
    public static class PublicEmployeeMainRoleFixArg extends ObjDataFixArg {
        private List<String> outerRoleIds;
        private Boolean updateMainRoleIfExists = false;
    }

    @Data
    @AllArgsConstructor
    public static class ObjDataFixResult implements Serializable {
        private String upstreamEi;
        private String upstreamEa;
        private String apiName;
        private Integer success = 0;
        private Integer fail = 0;
    }
}