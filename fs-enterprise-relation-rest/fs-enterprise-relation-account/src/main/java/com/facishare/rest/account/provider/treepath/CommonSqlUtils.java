package com.facishare.rest.account.provider.treepath;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class CommonSqlUtils {
    private CommonSqlUtils() {
        throw new IllegalStateException("Utility class");
    }

    private static final ICommonSqlService commonSqlService = SpringUtil.getContext().getBean(CommonSqlServiceImpl.class);

    public static void addWhereParam(List<WhereParam> whereParamList, String columnName, CommonSqlOperator op, List<Object> values){
        WhereParam whereParam = getWhereParam(columnName, op, values);
        whereParamList.add(whereParam);
    }

    public static WhereParam getWhereParam (String columnName, CommonSqlOperator op, List<Object> values) {
        WhereParam whereParam = new WhereParam();
        whereParam.setColumn(columnName);
        whereParam.setOperator(op);
        whereParam.setValue(values);
        return  whereParam;
    }
    @NotNull
    public static ActionContext getActionContext(String tenantId, String userId) {
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(tenantId);
        actionContext.setUserId(userId);
        actionContext.setDbType("pg");
        actionContext.setAllowUpdateInvalid(true);
        actionContext.put("not_validate", true);
        actionContext.setPrivilegeCheck(false);
        return actionContext;
    }

    private static ActionContext getActionContext(String tenantId) {
        return getActionContext(tenantId, User.SUPPER_ADMIN_USER_ID);
    }

    public static int updateData(String tenantId, String tableName, Map<String, Object> dataMap, List<WhereParam> whereParams) throws MetadataServiceException {
        ActionContext actionContext = getActionContext(tenantId);
        return commonSqlService.update(tableName, dataMap, whereParams, actionContext);
    }
}
