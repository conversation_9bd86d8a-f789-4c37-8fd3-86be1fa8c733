package com.facishare.rest.account.cep.admin.objcontroller;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.PublicEmployeeTypeEnum;
import com.facishare.enterprise.common.enums.PublicEmployeeObjTypeOption;
import com.facishare.enterprise.common.model.OuterAccountVo;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.util.OffsetUtil;
import com.facishare.er.api.model.result.EmployeeCardResult;
import com.facishare.er.api.model.vo.Page;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.rest.account.manager.ErObjPaasApiBusServiceManager;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.enterpriserelation.objrest.arg.GetObjectDataArg;
import com.fxiaoke.enterpriserelation.objrest.arg.GetRelationOwnerArg;
import com.fxiaoke.enterpriserelation.objrest.arg.ListPublicEmployeeObjByConditionArg;
import com.fxiaoke.enterpriserelation.objrest.arg.ListRelationOwnersArg;
import com.fxiaoke.enterpriserelation.objrest.arg.OrderByData;
import com.fxiaoke.enterpriserelation.objrest.common.OffsetArg;
import com.fxiaoke.enterpriserelation.objrest.result.ListByConditionResult;
import com.fxiaoke.enterpriserelation.objrest.service.PublicEmployeeObjService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: jiangxch
 * @date: 2022/5/24 15:04
 */
@Slf4j
@Service("objPublicEmployeeService")
public class ObjPublicEmployeeService {
    private PublicEmployeeObjService publicEmployeeObjService = ErObjPaasApiBusServiceManager.getPublicEmployeeObjService();
    @Autowired
    private EIEAConverter eieaConverter;

    public PublicEmployeeObj getPublicEmployeeObj(String upstreamEa, Integer employeeId, Long downstreamOuterUid) {
        int upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(upstreamTenantId + "", "-10000");
        GetObjectDataArg objectDataArg = new GetObjectDataArg();
        objectDataArg.setId(downstreamOuterUid);
        Map<String, Object> publicEmployeeObjectMap = publicEmployeeObjService.getObjectData(serviceContext, objectDataArg).getObjectData();
        return PublicEmployeeObj.newInstance(publicEmployeeObjectMap);
    }

    public PublicEmployeeObj getByOuterTenantIdAndOuterUid(String upstreamEa, Integer employeeId, Long downstreamOuterTenantId, Long downstreamOuterUid) {
        int upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(upstreamTenantId + "", employeeId + "");
        ListPublicEmployeeObjByConditionArg conditionArg = ListPublicEmployeeObjByConditionArg.newStartType();
        conditionArg.setOuterUids(Lists.newArrayList(downstreamOuterUid));
        conditionArg.setOuterTenantIds(Lists.newArrayList(downstreamOuterTenantId));
        ListByConditionResult listByConditionResult = publicEmployeeObjService.listByCondition(serviceContext, conditionArg);
        List<Map<String, Object>> publicEmployeeObjectDatas = listByConditionResult.getObjectDatas();
        if (CollectionUtils.isEmpty(publicEmployeeObjectDatas)) {
            return null;
        }
        return PublicEmployeeObj.newInstance(publicEmployeeObjectDatas.get(0));
    }

    public PublicEmployeeObj getByContractIdAndIdentityType(String upstreamEa, Integer employeeId, String contractId, Integer identityType) {
        return getByContractIdAndIdentityType(upstreamEa, employeeId, contractId, identityType, false);
    }

    public PublicEmployeeObj getByContractIdAndIdentityType(String upstreamEa, Integer employeeId, String contractId, Integer identityType, boolean includeInvalid) {
        int upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(upstreamTenantId + "", employeeId + "");

        ListPublicEmployeeObjByConditionArg conditionArg = new ListPublicEmployeeObjByConditionArg();
        conditionArg.setIdentityType(identityType);
        conditionArg.setContractIds(Lists.newArrayList(contractId));
        conditionArg.setOrderByData(OrderByData.createTimeAsc());
        conditionArg.setIncludeInvalid(includeInvalid);
        List<Map<String, Object>> objectDatas = publicEmployeeObjService.listByCondition(serviceContext, conditionArg).getObjectDatas();
        if (CollectionUtils.isEmpty(objectDatas)) {
            return null;
        }

        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newInstance(objectDatas);
        if (publicEmployeeObjs.size() > 1) {
            for (PublicEmployeeObj publicEmployeeObj : publicEmployeeObjs) {
                if (PublicEmployeeTypeEnum.TYPE_IS_PUBLIC_EMPLOYEE.getType() == publicEmployeeObj.getType()) {
                    return publicEmployeeObj;
                }
            }
        }
        return publicEmployeeObjs.get(0);
    }

    public Map<Long, PublicEmployeeObj> listPublicEmployeeObjMap(String upstreamEa, Integer employeeId, List<Long> downstreamOuterUids) {
        Map<Long, PublicEmployeeObj> publicEmployeeObjMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(downstreamOuterUids)) {
            return publicEmployeeObjMap;
        }
        ListPublicEmployeeObjByConditionArg conditionArg = new ListPublicEmployeeObjByConditionArg();
        conditionArg.setOuterUids(downstreamOuterUids);
        List<PublicEmployeeObj> publicEmployeeObjs = listByCondition(upstreamEa, employeeId, conditionArg).getDataList();
        if (!CollectionUtils.isEmpty(publicEmployeeObjs)) {
            publicEmployeeObjMap = publicEmployeeObjs.stream().collect(Collectors.toMap(PublicEmployeeObj::getOuterUid, x -> x));
        }
        return publicEmployeeObjMap;
    }

    public Result<List<Long>> listAllDownstreamUids(String upstreamEa, Integer identityType) {
        ListPublicEmployeeObjByConditionArg conditionArg = new ListPublicEmployeeObjByConditionArg();
        conditionArg.setTypes(Lists.newArrayList(PublicEmployeeObjTypeOption.NORMAL.getType()));
        conditionArg.setIdentityType(identityType);
        conditionArg.setOrderByData(OrderByData.createTimeAsc());
        List<PublicEmployeeObj> publicEmployeeObjs = listAllPublicEmployeeObjsByCondition(upstreamEa, SuperUserConstants.USER_ID, conditionArg);
        List<Long> res = publicEmployeeObjs.stream().map(PublicEmployeeObj::getOuterUid).collect(Collectors.toList());
        return Result.newSuccess(res);
    }

    public Result<List<Long>> listDownstreamUids(String upstreamEa, Integer identityType, Integer offset, Integer limit) {
        ListPublicEmployeeObjByConditionArg conditionArg = new ListPublicEmployeeObjByConditionArg();
        conditionArg.setTypes(Lists.newArrayList(PublicEmployeeObjTypeOption.NORMAL.getType()));
        conditionArg.setIdentityType(identityType);
        conditionArg.setOrderByData(OrderByData.createTimeAsc());
        conditionArg.setLimit(limit);
        conditionArg.setOffset(offset);
        List<PublicEmployeeObj> publicEmployeeObjs = listAllPublicEmployeeObjsByCondition(upstreamEa, SuperUserConstants.USER_ID, conditionArg);
        List<Long> res = publicEmployeeObjs.stream().map(PublicEmployeeObj::getOuterUid).collect(Collectors.toList());
        return Result.newSuccess(res);
    }

    public Page<PublicEmployeeObj> listByCondition(String upstreamEa, Integer employeeId, ListPublicEmployeeObjByConditionArg conditionArg) {
        int upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(upstreamTenantId + "", employeeId + "");
        Page<PublicEmployeeObj> objectPage = listByCondition(serviceContext, conditionArg);
        return objectPage;
    }

    public Page<PublicEmployeeObj> listByCondition(ServiceContext serviceContext, ListPublicEmployeeObjByConditionArg conditionArg) {
        ListByConditionResult listByConditionResult = publicEmployeeObjService.listByCondition(serviceContext, conditionArg);
        List<Map<String, Object>> publicEmployeeObjectDatas = listByConditionResult.getObjectDatas();
        Page<PublicEmployeeObj> objectPage = new Page<>();
        objectPage.setTotal(Long.valueOf(listByConditionResult.getTotal()));
        objectPage.setDataList(Lists.newArrayList());
        if (publicEmployeeObjectDatas != null) {
            objectPage.setDataList(publicEmployeeObjectDatas.stream().map(PublicEmployeeObj::newInstance).collect(Collectors.toList()));
        }
        return objectPage;
    }

    public List<Long> listDownstreamUidsByCondition(String upstreamEa, Integer identityType, OffsetArg offsetArg) {
        ListPublicEmployeeObjByConditionArg conditionArg = new ListPublicEmployeeObjByConditionArg();
        conditionArg.setTypes(Lists.newArrayList(PublicEmployeeObjTypeOption.NORMAL.getType()));
        conditionArg.setIdentityType(identityType);
        conditionArg.setOrderByData(OrderByData.createTimeAsc());
        conditionArg.setLimit(offsetArg.getLimit());
        conditionArg.setOffset(offsetArg.getOffset());
        List<PublicEmployeeObj> publicEmployeeObjs = listByCondition(upstreamEa, SuperUserConstants.USER_ID, conditionArg).getDataList();
        return publicEmployeeObjs.stream().map(PublicEmployeeObj::getOuterUid).collect(Collectors.toList());
    }

    public List<Long> listAllDownstreamUidsByDownstreamOuterTenantIds(String upstreamEa, Set<Long> downstreamOuterTenantIds) {
        return listAllDownstreamUidsByDownstreamOuterTenantIds(upstreamEa, null, downstreamOuterTenantIds);
    }

    public List<Long> listAllDownstreamUidsByDownstreamOuterTenantIds(String upstreamEa, Integer identityType, Set<Long> downstreamOuterTenantIds) {
        ListPublicEmployeeObjByConditionArg conditionArg = new ListPublicEmployeeObjByConditionArg();
        conditionArg.setOuterTenantIds(new ArrayList<>(downstreamOuterTenantIds));
        if (identityType != null) {
            conditionArg.setIdentityType(identityType);
        }
        conditionArg.setTypes(Lists.newArrayList(PublicEmployeeObjTypeOption.NORMAL.getType()));
        conditionArg.setOrderByData(OrderByData.createTimeAsc());
        List<PublicEmployeeObj> publicEmployeeObjs = listAllPublicEmployeeObjsByCondition(upstreamEa, SuperUserConstants.USER_ID, conditionArg);
        return publicEmployeeObjs.stream().map(PublicEmployeeObj::getOuterUid).collect(Collectors.toList());
    }

    public Result<List<EmployeeCardResult>> listEmployeeCardResultByOuterUids(String upstreamEa, Integer employeeId, Collection<Long> outerUids) {
        if (CollectionUtils.isEmpty(outerUids)) {
            return Result.newSuccess(new ArrayList<>());
        }
        ListPublicEmployeeObjByConditionArg conditionArg = new ListPublicEmployeeObjByConditionArg();
        conditionArg.setTypes(Lists.newArrayList(PublicEmployeeObjTypeOption.NORMAL.getType()));
        conditionArg.setOrderByData(OrderByData.createTimeAsc());
        conditionArg.setOuterUids(new ArrayList<>(outerUids));
        List<PublicEmployeeObj> publicEmployeeObjs = listAllPublicEmployeeObjsByCondition(upstreamEa, SuperUserConstants.USER_ID, conditionArg);
        List<EmployeeCardResult> collect = publicEmployeeObjs.stream().map(EmployeeCardResult::of).collect(Collectors.toList());
        return Result.newSuccess(collect);
    }

    public PublicEmployeeObj getRelationOwner(String upstreamEa, Integer employeeId, Long downstreamOuterTenantId) {
        int upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(upstreamTenantId + "", employeeId + "");
        GetRelationOwnerArg arg = new GetRelationOwnerArg();
        arg.setDownstreamOuterUid(downstreamOuterTenantId);
        Map<String, Object> relationOwner = publicEmployeeObjService.getRelationOwner(serviceContext, arg).getRelationOwner();
        return PublicEmployeeObj.newInstance(relationOwner);
    }

    public List<PublicEmployeeObj> listRelationOwners(String upstreamEa, Integer employeeId, List<Long> downstreamOuterTenantIds) {
        int upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(upstreamTenantId + "", employeeId + "");
        ListRelationOwnersArg arg = new ListRelationOwnersArg();
        arg.setDownstreamOuterTenantIds(downstreamOuterTenantIds);
        List<Map<String, Object>> relationOwners = publicEmployeeObjService.listRelationOwners(serviceContext, arg).getRelationOwners();
        return PublicEmployeeObj.newInstance(relationOwners);
    }

    public List<PublicEmployeeObj> listAllPublicEmployeeObjsByCondition(String upstreamEa, Integer employeeId, ListPublicEmployeeObjByConditionArg conditionArg) {
        List<PublicEmployeeObj> res = new ArrayList<>();
        OffsetUtil.offsetFunction(1000, (offset, limit) -> {
            conditionArg.setOffset(offset);
            conditionArg.setLimit(limit);
            conditionArg.setOrderByData(OrderByData.createTimeAsc());
            List<PublicEmployeeObj> publicEmployeeObjs = listByCondition(upstreamEa, employeeId, conditionArg).getDataList();
            res.addAll(publicEmployeeObjs);
            return publicEmployeeObjs.size();
        });
        return res;
    }

    public Map<String, List<OuterAccountVo>> listAllOuterAccountByCondition(String upstreamEa, Integer employeeId, ListPublicEmployeeObjByConditionArg conditionArg) {
        Map<String, List<OuterAccountVo>> res = new HashMap<>();
        OffsetUtil.offsetFunction(500, (offset, limit) -> {
            conditionArg.setOffset(offset);
            conditionArg.setLimit(limit);
            conditionArg.setOrderByData(OrderByData.createTimeAsc());
            List<PublicEmployeeObj> publicEmployeeObjs = listByCondition(upstreamEa, employeeId, conditionArg).getDataList();
            if (CollectionUtils.isNotEmpty(publicEmployeeObjs)) {
                for (PublicEmployeeObj publicEmployeeObj : publicEmployeeObjs) {
                    String employeeObjName = publicEmployeeObj.getName();

                    OuterAccountVo outerAccountVo = new OuterAccountVo();
                    outerAccountVo.setOuterTenantId(publicEmployeeObj.getOuterTenantId());
                    outerAccountVo.setOuterUid(publicEmployeeObj.getOuterUid());
                    List<OuterAccountVo> outerAccountVos = res.computeIfAbsent(employeeObjName, k -> new ArrayList<>());
                    outerAccountVos.add(outerAccountVo);
                }
            }
            return publicEmployeeObjs.size();
        });
        return res;
    }

    public Long count(String upstreamEa, Integer employeeId, ListPublicEmployeeObjByConditionArg arg) {
        arg.setFindExplicitTotalNum(true);
        arg.setOffset(0);
        arg.setLimit(1);
        return listByCondition(upstreamEa, employeeId, arg).getTotal();
    }
}
