package com.facishare.rest.account.manager.publicobject;

import com.facishare.crm.openapi.Utils;
import com.facishare.enterprise.common.constant.EnterpriseRelationObjConstant;
import com.facishare.enterprise.common.constant.ErDepartmentObjConstant;
import com.facishare.enterprise.common.constant.OrganizationalModel;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.util.FsGrayReleaseUtil;
import com.facishare.enterprise.common.util.StringUtil;
import com.facishare.er.api.model.EnterpriseRelation;
import com.facishare.global.rest.dao.mongo.FxiaokeEmployeeAssociationEntityDao;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.rest.account.action.EnterpriseRelationChangeOwnerAction;
import com.facishare.rest.account.common.GroupControlInfo;
import com.facishare.rest.account.dao.EnterpriseRelationDao;
import com.facishare.rest.account.dao.PublicEmployeeDao;
import com.facishare.rest.account.manager.ActionCallManager;
import com.facishare.rest.account.manager.ErOrgManager;
import com.facishare.rest.account.mq.event.Body;
import com.facishare.rest.account.mq.event.CrmEvent;
import com.facishare.rest.account.mq.event.CrmEventData;
import com.facishare.rest.account.remote.CrmManager;
import com.facishare.rest.account.remote.PaasTenantGroupManager;
import com.google.common.collect.Lists;
import com.mysql.cj.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.antlr.v4.runtime.atn.SemanticContext;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnterpriseRelationPublicObjectManager {
    @Autowired
    private PaasTenantGroupManager paasTenantGroupManager;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private PublicEmployeeDao publicEmployeeDao;
    @Autowired
    private EnterpriseRelationDao enterpriseRelationDao;
    @Autowired
    private CrmManager crmManager;
    @Autowired
    private ActionCallManager actionCallManager;
    @Autowired
    private FxiaokeEmployeeAssociationEntityDao fxiaokeEmployeeAssociationEntityDaoOldManager;
    @Autowired
    private ErOrgManager erOrgManager;

    /**
     * 如果升级公共对象默认情况下创建的数据是公共数据，如果没给参数，纠正可见范围为私有数据
     *
     * @param objectData
     */
    public void resetDownstreamTenantIdIfNecessary(User user, ObjectDataDocument objectData) {
        if (ObjectUtils.isEmpty(objectData)) {
            return;
        }

        IObjectDescribe describe = serviceFacade.findObjectWithoutCopy(user.getTenantId(), EnterpriseRelationObjConstant.API_NAME);
        if (!describe.isPublicObject()) {
            return;
        }

        if (!objectData.containsKey(IObjectData.DOWNSTREAM_TENANT_ID)) {
            objectData.put(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PRIVATE_DATA);
            objectData.put(IObjectData.DOWNSTREAM_TENANT_ID, com.beust.jcommander.internal.Lists.newArrayList(describe.getTenantId()));
        }
    }

    public void processErOrganizationalPublicEnterpriseRelationCreate(String tenantId, List<Long> enterpriseRelationIds) {
        User user = User.systemUser(tenantId);
        try {
            IObjectDescribe objectDescribe = serviceFacade.findObjectWithoutCopy(tenantId, EnterpriseRelationObjConstant.API_NAME);
            if (!objectDescribe.isPublicObject()) {
                return;
            }

            GroupControlInfo orgManagerGroupItControlInfo = erOrgManager.getGroupItControlInfo(objectDescribe.getUpstreamTenantId());
            if (!orgManagerGroupItControlInfo.isEnable()) {
                return;
            }


            log.info("processEnterpriseRelationCreateSyncMapperObjectAndOwner {} {} ", user.getTenantId(), enterpriseRelationIds);
            List<IObjectData> enterpriseRelationObjData = enterpriseRelationDao.batchGetByIds(user, enterpriseRelationIds);
            if (ObjectUtils.isEmpty(enterpriseRelationObjData)) {
                return;
            }

            List<EnterpriseRelationObj> enterpriseRelationObj = enterpriseRelationObjData.stream().map(EnterpriseRelationObj::newInstance).filter(it -> ObjectUtils.isNotEmpty(it.getOrgManageType())).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(enterpriseRelationObj)) {
                return;
            }

            OrganizationalModel organizationalModel = OrganizationalModel.of(orgManagerGroupItControlInfo.getOrganizationalModel());

            for (EnterpriseRelationObj relationObj : enterpriseRelationObj) {
                String mapperObjectId = organizationalModel == OrganizationalModel.GROUP ?
                        relationObj.getMapperPartnerId() :
                        relationObj.getMapperAccountId();
                if (ObjectUtils.isNotEmpty(mapperObjectId)) {
                    continue;
                }

                String owner = getRelationOwnerInnerEmployee(user, relationObj.getOuterTenantId());
                if (ObjectUtils.isNotEmpty(owner) && ObjectUtils.isEmpty(relationObj.getOwner())) {
                    try {
                        List<StandardChangeOwnerAction.ChangeOwnerData> changeOwnerDataList = new ArrayList<>();
                        StandardChangeOwnerAction.ChangeOwnerData changeOwnerData = new StandardChangeOwnerAction.ChangeOwnerData();
                        changeOwnerData.setObjectDataId(relationObj.getId());
                        changeOwnerData.setOwnerId(Lists.newArrayList(owner));
                        changeOwnerDataList.add(changeOwnerData);

                        StandardChangeOwnerAction.Arg changeOwnerArg = new StandardChangeOwnerAction.Arg();
                        changeOwnerArg.setData(changeOwnerDataList);
                        actionCallManager.triggerAction(user, EnterpriseRelationObjConstant.API_NAME, ObjectAction.CHANGE_OWNER.getActionCode(), changeOwnerArg, null, null);
                    } catch (Exception e) {
                        log.warn("change owner error {} {} {}", user.getTenantId(), relationObj.getId(), owner, e);
                    }
                }

                if (organizationalModel == OrganizationalModel.GROUP && FsGrayReleaseUtil.groupErOrgIgnorePartner(objectDescribe.getUpstreamTenantId())) {
                    return;
                }

                String createObjectApiName = organizationalModel == OrganizationalModel.GROUP ?
                        Utils.PARTNER_API_NAME :
                        Utils.ACCOUNT_API_NAME;
                String updateApiField = organizationalModel == OrganizationalModel.GROUP ?
                        EnterpriseRelationObjConstant.MAPPER_PARTNER_ID :
                        EnterpriseRelationObjConstant.MAPPER_ACCOUNT_ID;

                try {
                    String partnerOrAccountId = crmManager.createSimpleObjectIfNecessary(tenantId, createObjectApiName, relationObj.getName(), owner);
                    if (ObjectUtils.isNotEmpty(partnerOrAccountId)) {
                        ObjectDataDocument data = new ObjectDataDocument();
                        data.put(IObjectData.TENANT_ID, tenantId);
                        data.put(IObjectData.DESCRIBE_API_NAME, EnterpriseRelationObjConstant.API_NAME);
                        data.put(updateApiField, partnerOrAccountId);
                        data.put(IObjectData.ID, relationObj.getId());
                        actionCallManager.triggerEditAction(user, data, EnterpriseRelationObjConstant.API_NAME, true);
                    }
                } catch (Exception e) {
                    log.warn("", e);
                }
            }

        } catch (Exception e) {
            log.warn("processErOrganizationalPublicEnterpriseRelationCreate error ", e);
        }
    }


    /**
     * 针对公共对象,非本企业创建的，初始化默认企业组
     *
     * @param obj
     */
    public void initPublicObjectDefaultPaasTenantGroup(User user, List<EnterpriseRelationObj> obj) {
        try {
            IObjectDescribe objectDescribe = serviceFacade.findObjectWithoutCopy(user.getTenantId(), EnterpriseRelationObjConstant.API_NAME);
            if (!objectDescribe.isPublicObject()) {
                return;
            }
            //当前数据是N企业创建的，加入默认企业组
            for (EnterpriseRelationObj enterpriseRelationObj : obj) {
                String createEnterprise = enterpriseRelationObj.getCreateEnterprise();
                if (ObjectUtils.isNotEmpty(createEnterprise) && !Objects.equals(createEnterprise, user.getTenantId())) {
                    paasTenantGroupManager.initDefaultPaasTenantGroupIfNecessary(enterpriseRelationObj);
                }
            }
        } catch (Exception e) {
            log.warn("", e);
        }
    }


    private String getRelationOwnerInnerEmployee(User user, Long outerTenantId) {
        PublicEmployeeObj relationOwner = publicEmployeeDao.getRelationOwner(user, outerTenantId);
        if (relationOwner == null) {
            log.warn("not find relation owner  {} {}", user.getTenantId(), outerTenantId);
            return null;
        }
        Integer innerEmployeeId = fxiaokeEmployeeAssociationEntityDaoOldManager.getEmployeeIdByOuterUid(relationOwner.getOuterUid());
        if (innerEmployeeId == null) {
            log.warn("not find inner employee id  {} ", relationOwner.getOuterTenantId());
            return null;
        }
        return innerEmployeeId.toString();
    }
}
