package com.facishare.rest.account.dao.mongo;

import com.facishare.er.api.model.enums.UpstreamOpenedEnterpriseStatusConstant;
import com.facishare.rest.account.dao.data.Page;
import com.facishare.rest.account.dao.mongo.entity.UpstreamOpenedEnterpriseRecordEntity;
import com.google.common.collect.Lists;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

@Slf4j
@Repository
public class UpstreamOpenedEnterpriseRecordEntityDao extends BaseDao<UpstreamOpenedEnterpriseRecordEntity> {
    private static final String TENANT_ID_FIELD_NAME = "tenantId";

    public List<UpstreamOpenedEnterpriseRecordEntity> listByUpstreamEa(Integer tenantId, String upstreamEa) {
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("upstreamEa").equal(upstreamEa);
        query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        query.field("status").notEqual(UpstreamOpenedEnterpriseStatusConstant.DISASSOCIATE);
        return query.asList();
    }

    public String createNew(Integer tenantId, UpstreamOpenedEnterpriseRecordEntity entity) {
        entity.setTenantId(tenantId);
        return datastore.save(entity).getId().toString();
    }

    public void create(Integer tenantId, UpstreamOpenedEnterpriseRecordEntity entity) {
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setTenantId(tenantId);
        datastore.save(entity);
    }


    /**
     * @param upstreamEa 必填
     * @param downstreamEas 可为null
     */
    public Page<UpstreamOpenedEnterpriseRecordEntity> listByEaAndPageAndAssociateStatus(Integer tenantId, String upstreamEa, Collection<String> downstreamEas, Integer pageNumber, Integer pageSize) {
        if (StringUtils.isEmpty(upstreamEa) && CollectionUtils.isEmpty(downstreamEas)) {
            return null;
        }
        return this.listByEaAndPageAndNotEqStatus(tenantId, upstreamEa, downstreamEas, UpstreamOpenedEnterpriseStatusConstant.DISASSOCIATE, pageNumber, pageSize);
    }

    public Page<UpstreamOpenedEnterpriseRecordEntity> listByEaAndPage(Integer tenantId, String upstreamEa, Collection<String> downstreamEas, Integer pageNumber, Integer pageSize) {
        if (StringUtils.isEmpty(upstreamEa) && CollectionUtils.isEmpty(downstreamEas)) {
            return null;
        }
        return this.listByEaAndPageAndNotEqStatus(tenantId, upstreamEa, downstreamEas, null, pageNumber, pageSize);
    }

    public Page<UpstreamOpenedEnterpriseRecordEntity> listByEaAndPageAndNotEqStatus(Integer tenantId, String upstreamEa, Collection<String> downstreamEas, Integer notEqualStatus, Integer pageNumber,
        Integer pageSize) {
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("upstreamEa").equal(upstreamEa);
        query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        if (notEqualStatus != null) {
            query.field("status").notEqual(notEqualStatus);
        }
        if (!CollectionUtils.isEmpty(downstreamEas)) {
            query.field("downstreamEa").in(downstreamEas);
        }
        long total = query.countAll(); //必须在分页前获取总数
        if (pageNumber != null && pageSize != null && pageNumber > 0 && pageSize > 0) {
            query.offset(pageSize * (pageNumber - 1));
            query.limit(pageSize);
        }
        // 默认时间倒序
        query.order("-UT");
        List<UpstreamOpenedEnterpriseRecordEntity> upstreamOpenedEnterpriseRecordEntities = query.asList();
        Page<UpstreamOpenedEnterpriseRecordEntity> result = new Page<>();
        result.setDataList(upstreamOpenedEnterpriseRecordEntities);
        result.setTotal(total);
        return result;
    }


    public void updateStatus(Integer tenantId, String upstreamEa, Collection<String> downstreamEas, Integer status) {
        if (StringUtils.isEmpty(upstreamEa) || CollectionUtils.isEmpty(downstreamEas)) {
            return;
        }
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("upstreamEa").equal(upstreamEa);
        query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        query.field("downstreamEa").in(downstreamEas);
        UpdateOperations<UpstreamOpenedEnterpriseRecordEntity> update = datastore.createUpdateOperations(UpstreamOpenedEnterpriseRecordEntity.class);
        update.set("status", status);
        datastore.update(query, update, false);
    }


    public List<UpstreamOpenedEnterpriseRecordEntity> listByDownstreamEaAndModuleCode(Integer tenantId, String downstreamEa, String moduleCode) {
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("downstreamEa").equal(downstreamEa);
        query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        query.field("downstreamModuleCode").equal(moduleCode);
        return query.asList();
    }

    public void updateOrderIdsByDownstreamEaAndModuleCode(Integer tenantId, String id, List<String> orderIds) {
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("_id").equal(new ObjectId(id));
        query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        UpdateOperations<UpstreamOpenedEnterpriseRecordEntity> update = datastore.createUpdateOperations(UpstreamOpenedEnterpriseRecordEntity.class);
        update.set("orderIds", orderIds);
        datastore.update(query, update, false);
    }

    public void updateEndTime(Integer tenantId, String upstreamEa, String downstreamEa, Long endTime) {
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("upstreamEa").equal(upstreamEa);
        query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        query.field("downstreamEa").equal(downstreamEa);
        UpdateOperations<UpstreamOpenedEnterpriseRecordEntity> update = datastore.createUpdateOperations(UpstreamOpenedEnterpriseRecordEntity.class);
        update.set("endTime", new Date(endTime));
        update.set("UT", new Date());
        datastore.update(query, update, false);
    }

    public void updateOrderIds(Integer tenantId, String upstreamEa, String downstreamEa, List<String> orderIds) {
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("upstreamEa").equal(upstreamEa);
        query.field("downstreamEa").equal(downstreamEa);
        query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        UpdateOperations<UpstreamOpenedEnterpriseRecordEntity> update = datastore.createUpdateOperations(UpstreamOpenedEnterpriseRecordEntity.class);
        update.set("orderIds", orderIds);
        datastore.update(query, update, false);
    }

    /**
     * 删除开通记录
     *
     * @param upstreamEa 上游企业账号
     * @param downstreamOuterTenantId 下游企业外部ID
     */
    public void deleteByUpstreamEaAndDownstreamOuterTenantId(Integer tenantId, String upstreamEa, Long downstreamOuterTenantId) {
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("upstreamEa").equal(upstreamEa);
        query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        query.field("downstreamOuterTenantId").equal(downstreamOuterTenantId);
        datastore.delete(query);
    }

    /**
     * 更新Crm版本续费状态
     *
     * @param id 下游企业开通记录ID
     * @param status 续费状态：1-成功，2失败
     * @param orderId 上游企业续费订单ID
     * @param orderData 下游企业Crm续费订单
     * @param result 续费失败原因
     */
    public void updateRenewStatus(Integer tenantId, String id, Integer status, String orderId, Object orderData, String result) {
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("_id").equal(new ObjectId(id));
        query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        UpdateOperations<UpstreamOpenedEnterpriseRecordEntity> update = datastore.createUpdateOperations(UpstreamOpenedEnterpriseRecordEntity.class);
        update.set("lastRenewStatus", status);
        update.set("lastRenewOrderId", orderId);
        update.set("lastRenewOrderData", orderData);
        update.set("lastRenewResult", result);
        update.set("lastRenewTime", new Date());
        datastore.update(query, update, false);
    }

    /**
     * 查询某个上游企业下需要续期的下游企业列表
     *
     * @param upstreamEa 上游企业Ea
     * @param downstreamEa 下游企业Ea
     * @return 需要续期的企业开通记录
     */
    public List<UpstreamOpenedEnterpriseRecordEntity> listByUpstreamEaAndDownstreamEa(Integer tenantId, String upstreamEa, String downstreamEa) {
        if (StringUtils.isEmpty(upstreamEa)) {
            return Lists.newArrayList();
        }
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("upstreamEa").equal(upstreamEa);
        query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        if (StringUtils.isNotEmpty(downstreamEa)) {
            query.field("downstreamEa").equal(downstreamEa);
        }
        query.field("status").equal(1);
        return query.asList();
    }

    /**
     * 分页查询出所有即将过期或已过期有记录
     *
     * @param status 互联状态：1-互联，2-解除互联
     * @param enableTime 即将到期时间
     * @param expiredTime 已过期时间
     * @param pageNumber 页码
     * @param pageSize 分页大小
     * @return 记录列表
     */
    public Page<UpstreamOpenedEnterpriseRecordEntity> pageListExpiredRecordByStatusAndTime(Integer status, Long enableTime, Long expiredTime, Integer pageNumber, Integer pageSize) {
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        //query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        if (status != null) {
            query.field("status").equal(status);
        }
        if (expiredTime != null) {
            Date startTime = new Date(System.currentTimeMillis() - expiredTime);
            query.field("endTime").greaterThanOrEq(startTime);
        }
        if (enableTime != null) {
            Date endTime = new Date(System.currentTimeMillis() + enableTime);
            query.field("endTime").lessThanOrEq(endTime);
        }
        long total = query.countAll(); //必须在分页前获取总数
        if (pageNumber != null && pageSize != null && pageNumber > 0 && pageSize > 0) {
            query.offset(pageSize * (pageNumber - 1));
            query.limit(pageSize);
        }
        // 默认时间倒序
        query.order("-UT");
        List<UpstreamOpenedEnterpriseRecordEntity> upstreamOpenedEnterpriseRecordEntities = query.asList();
        Page<UpstreamOpenedEnterpriseRecordEntity> result = new Page<>();
        result.setDataList(upstreamOpenedEnterpriseRecordEntities);
        result.setTotal(total);
        return result;
    }

    /**
     * 获取上游企业下指定模块下，指定状态的最后一次续费记录
     * @param tenantId 上游企业ID
     * @param upstreamEa 上游企业Ea
     * @param upstreamModuleCode 上游企业CRM模块
     * @param downModuleCode 下游企业CRM模块
     * @return 续费记录
     */
    public UpstreamOpenedEnterpriseRecordEntity getLastRenewOrderRecord(Integer tenantId, String upstreamEa, String upstreamModuleCode, String downModuleCode) {
        Query<UpstreamOpenedEnterpriseRecordEntity> query = datastore.createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("upstreamEa").equal(upstreamEa);
        query.field(TENANT_ID_FIELD_NAME).equal(tenantId);
        query.field("upstreamModuleCode").equal(upstreamModuleCode);
        query.field("downstreamModuleCode").equal(downModuleCode);
        query.field("status").equal(1);
        query.order("-lastRenewTime,-endTime");
        query.offset(0).limit(1);
        return query.get();
    }

    public void delByUpstreamEa(String upstreamEa) {
        Query<UpstreamOpenedEnterpriseRecordEntity> query = createQuery(UpstreamOpenedEnterpriseRecordEntity.class);
        query.field("upstreamEa").equal(upstreamEa);
        datastore.delete(query);
    }
}
