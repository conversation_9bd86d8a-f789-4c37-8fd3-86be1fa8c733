package com.facishare.rest.account.cep.admin.data.EnterpriseRelation.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel
public class GetDownstreamUsageAnalysisDetailsArg implements Serializable {
    @ApiModelProperty("下游外部租户id")
    private long downstreamOuterTenantId;
    @ApiModelProperty("时间类型，1本周，2上周，3本月，4上月")
    private int timeType;
    private int pageNumber;
    private int pageSize;
}
