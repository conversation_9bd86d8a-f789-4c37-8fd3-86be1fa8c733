package com.facishare.rest.account.manager.publicobject;

import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.global.rest.dao.mongo.FxiaokeEmployeeAssociationEntityDao;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.rest.account.dao.EnterpriseRelationDao;
import com.facishare.rest.account.dao.PublicEmployeeDao;
import com.facishare.rest.account.manager.ActionCallManager;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentObjConstant;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PublicEmployeePublicObjectManager {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private PublicEmployeeDao publicEmployeeDao;
    @Autowired
    private ActionCallManager actionCallManager;
    @Autowired
    private FxiaokeEmployeeAssociationEntityDao fxiaokeEmployeeAssociationEntityDaoOldManager;
    @Autowired
    private EnterpriseRelationDao enterpriseRelationDao;


    /**
     * 如果升级公共对象默认情况下创建的数据是公共数据，如果没给参数，纠正可见范围为私有数据
     *
     * @param objectData
     */
    public void resetDownstreamTenantIdIfNecessary(User user, ObjectDataDocument objectData) {
        if (ObjectUtils.isEmpty(objectData)) {
            return;
        }

        IObjectDescribe describe = serviceFacade.findObjectWithoutCopy(user.getTenantId(), PublicEmployeeObjConstant.API_NAME);
        if (!describe.isPublicObject()) {
            return;
        }

        if (!objectData.containsKey(IObjectData.DOWNSTREAM_TENANT_ID)) {
            objectData.put(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PRIVATE_DATA);
            objectData.put(IObjectData.DOWNSTREAM_TENANT_ID, com.beust.jcommander.internal.Lists.newArrayList(describe.getTenantId()));
        }
    }

    public void processErOrganizationalPublicEmployeeCreate(String tenantId, List<Long> createPublicEmployeeIds) {

        IObjectDescribe describe = serviceFacade.findObjectWithoutCopy(tenantId, PublicEmployeeObjConstant.API_NAME);
        if (!describe.isPublicObject()) {
            return;
        }

        User user = User.systemUser(tenantId);
        List<IObjectData> objectDataList = publicEmployeeDao.batchGetByIds(user, createPublicEmployeeIds);
        if (ObjectUtils.isEmpty(objectDataList)) {
            return;
        }

        List<PublicEmployeeObj> publicEmployeeObjList = objectDataList.stream().map(PublicEmployeeObj::newInstance).filter(it -> ObjectUtils.isNotEmpty(it.getOrgManageType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(publicEmployeeObjList)) {
            return;
        }

        List<PublicEmployeeObj> needSyncDownstreamTenantIdEmployee = publicEmployeeObjList.stream().filter(it -> isCurrentTenantCreateObject(it, describe, tenantId)).collect(Collectors.toList());
        if (ObjectUtils.isNotEmpty(needSyncDownstreamTenantIdEmployee)) {
            syncEnterpriseRelationDownstreamTenantId(user, needSyncDownstreamTenantIdEmployee);
        }

        List<PublicEmployeeObj> needSyncOwnerEmployee = publicEmployeeObjList.stream().filter(it -> ObjectUtils.isEmpty(it.getOwner())).collect(Collectors.toList());
        if (ObjectUtils.isNotEmpty(needSyncOwnerEmployee)) {
            syncPublicEmployeeRelationOwner(user, needSyncOwnerEmployee);
        }
    }


    private void syncEnterpriseRelationDownstreamTenantId(User user, List<PublicEmployeeObj> needSyncDownstreamTenantIdEmployee) {
        try {
            List<Long> outerTenantIds = needSyncDownstreamTenantIdEmployee.stream().map(PublicEmployeeObj::getOuterTenantId).distinct().collect(Collectors.toList());
            if (ObjectUtils.isEmpty(outerTenantIds)) {
                return;
            }
            List<IObjectData> enterpriseRelationObjectDataList = enterpriseRelationDao.batchGetByIds(user, outerTenantIds);
            if (ObjectUtils.isEmpty(enterpriseRelationObjectDataList)) {
                return;
            }
            Map<Long, EnterpriseRelationObj> outerTenantId2Enterprise = enterpriseRelationObjectDataList.stream().map(EnterpriseRelationObj::newInstance).collect(Collectors.toMap(EnterpriseRelationObj::getOuterTenantId, Function.identity(), (x, y) -> x));

            List<IObjectData> updateDataList = new ArrayList<>();
            for (PublicEmployeeObj publicEmployeeObj : needSyncDownstreamTenantIdEmployee) {
                EnterpriseRelationObj enterpriseRelationObj = outerTenantId2Enterprise.get(publicEmployeeObj.getOuterTenantId());
                if (enterpriseRelationObj == null) {
                    continue;
                }

                IObjectData data = new com.facishare.paas.metadata.impl.ObjectData();
                data.setId(publicEmployeeObj.getId());
                data.setDescribeApiName(PublicEmployeeObjConstant.API_NAME);
                data.set(IObjectData.PUBLIC_DATA_TYPE, enterpriseRelationObj.get(IObjectData.PUBLIC_DATA_TYPE));
                data.set(IObjectData.DOWNSTREAM_TENANT_ID, enterpriseRelationObj.get(IObjectData.DOWNSTREAM_TENANT_ID));
                updateDataList.add(data);
            }

            if (ObjectUtils.isNotEmpty(updateDataList)) {
                serviceFacade.batchUpdateByFields(user, updateDataList, Lists.newArrayList(IObjectData.PUBLIC_DATA_TYPE, IObjectData.DOWNSTREAM_TENANT_ID));
            }
        } catch (Exception e) {
            log.warn("syncEnterpriseRelationDownstreamTenantId error ", e);
        }
    }

    private void syncPublicEmployeeRelationOwner(User user, List<PublicEmployeeObj> needSyncOwnerEmployee) {
        try {
            Map<Long, List<PublicEmployeeObj>> outerTenantId2Employee = needSyncOwnerEmployee.stream().collect(Collectors.groupingBy(PublicEmployeeObj::getOuterTenantId));
            for (Map.Entry<Long, List<PublicEmployeeObj>> outerTenantId2PublicEmployee : outerTenantId2Employee.entrySet()) {
                PublicEmployeeObj relationOwner = publicEmployeeDao.getRelationOwner(user, outerTenantId2PublicEmployee.getKey());
                if (relationOwner == null) {
                    log.warn("not find relation owner  {} {}", user.getTenantId(), outerTenantId2PublicEmployee.getKey());
                    continue;
                }
                Integer innerEmployeeId = fxiaokeEmployeeAssociationEntityDaoOldManager.getEmployeeIdByOuterUid(relationOwner.getOuterUid());
                if (innerEmployeeId == null) {
                    log.warn("not find inner employee id  {} ", relationOwner.getOuterTenantId());
                    continue;
                }

                List<StandardChangeOwnerAction.ChangeOwnerData> changeOwnerDataList = new ArrayList<>();
                for (PublicEmployeeObj publicEmployeeObj : outerTenantId2PublicEmployee.getValue()) {
                    StandardChangeOwnerAction.ChangeOwnerData changeOwnerData = new StandardChangeOwnerAction.ChangeOwnerData();
                    changeOwnerData.setObjectDataId(publicEmployeeObj.getId());
                    changeOwnerData.setOwnerId(Lists.newArrayList(innerEmployeeId.toString()));
                    changeOwnerDataList.add(changeOwnerData);
                }

                StandardChangeOwnerAction.Arg changeOwnerArg = new StandardChangeOwnerAction.Arg();
                changeOwnerArg.setData(changeOwnerDataList);
                actionCallManager.triggerAction(user, PublicEmployeeObjConstant.API_NAME, ObjectAction.CHANGE_OWNER.getActionCode(), changeOwnerArg, null, null);
            }
        } catch (Exception e) {
            log.warn("syncPublicEmployeeRelationOwner error  {}", user.getTenantId(), e);
        }
    }

    private boolean isCurrentTenantCreateObject(PublicEmployeeObj publicEmployeeObj, IObjectDescribe describe, String tenantId) {
        String createEnterprise = publicEmployeeObj.getCreateEnterprise();

        //1 企业开启公共对象的历史数据，创建企业为空
        if (ObjectUtils.isEmpty(createEnterprise)) {
            createEnterprise = describe.getUpstreamTenantId();
        }
        //如果发送消息的企业 是当前企业则为 当前企业创建，而不是可见范围修改 散发的元数据消息
        return Objects.equals(createEnterprise, tenantId);
    }

}
