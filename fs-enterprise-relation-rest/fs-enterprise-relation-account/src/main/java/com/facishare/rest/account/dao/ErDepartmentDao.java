package com.facishare.rest.account.dao;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.ErDepartmentObjConstant;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.exception.RelationException;
import com.facishare.enterprise.common.model.ErDepartmentObj;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.report.EsLogSendUtil;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.enterprise.common.util.ListUtil;
import com.facishare.enterprise.common.util.OffsetUtil;
import com.facishare.enterprise.common.util.StringUtil;
import com.facishare.er.api.util.BeanUtil;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IDataRightsParameter;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.rest.account.ServiceContextHolder;
import com.facishare.rest.account.dao.data.EnterpriseRelationQueryArg;
import com.facishare.rest.account.dao.data.ErDepartmentQueryArg;
import com.facishare.rest.account.provider.treepath.SearchUtil;
import com.facishare.rest.account.remote.FxiaokeAccountManager;
import com.facishare.rest.account.util.PatternBuilder;
import com.fxiaoke.crmrestapi.common.contants.DataRightPermissionScenTypeEnum;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentTypeEnum;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ErDepartmentDao extends BaseObjDao {
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private FxiaokeAccountManager fxiaokeAccountManager;

    public ErDepartmentObj getEnterpriseTypeErDepByOuterTenantId(User user, Long outerTenantId) {
        return getEnterpriseTypeErDepByOuterTenantId(user, Lists.newArrayList(outerTenantId)).get(outerTenantId);
    }

    public Map<Long, ErDepartmentObj> getEnterpriseTypeErDepByOuterTenantId(User user, List<Long> outerTenantIds) {
        Map<Long, ErDepartmentObj> res = new HashMap<>();
        if (outerTenantIds == null || outerTenantIds.isEmpty()) {
            return res;
        }
        ErDepartmentQueryArg erDepartmentQueryArg = new ErDepartmentQueryArg();
        erDepartmentQueryArg.setUseDataPermission(false);
        erDepartmentQueryArg.setErDepTypes(Lists.newArrayList(ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType()));
        erDepartmentQueryArg.setOuterTenantIds(outerTenantIds);
        QueryResult<IObjectData> query = this.getQuery(user, erDepartmentQueryArg);
        if (CollectionUtils.isEmpty(query.getData())) {
            return res;
        }
        List<ErDepartmentObj> erDepartmentObjs = ErDepartmentObj.newObjInstance(query.getData());
        Map<Long, List<ErDepartmentObj>> outerTenantIdsObjs = erDepartmentObjs.stream().collect(Collectors.groupingBy(ErDepartmentObj::getOuterTenantId, Collectors.mapping(x -> x, Collectors.toList())));

        for (Long outerTenantId : outerTenantIds) {
            List<ErDepartmentObj> objs = outerTenantIdsObjs.get(outerTenantId);
            if (CollectionUtils.isEmpty(objs)) {
                res.put(outerTenantId, null);
                continue;
            }
            objs.sort((obj1, obj2) -> {
                String treePath1 = obj1.getTreePath();
                String treePath2 = obj2.getTreePath();

                if (treePath1 == null && treePath2 == null) {
                    return 0;
                } else if (treePath1 == null) {
                    return 1;
                } else if (treePath2 == null) {
                    return -1;
                } else {
                    return Integer.compare(treePath2.length(), treePath1.length());
                }
            });
            res.put(outerTenantId, objs.get(0));
        }
        return res;
    }

    public ErDepartmentObj getErDepartmentByAccountDepartmentId(User user, String accountDepartmentId) {
        ErDepartmentQueryArg erDepartmentQueryArg = new ErDepartmentQueryArg();
        erDepartmentQueryArg.setUseDataPermission(false);
        erDepartmentQueryArg.setAccountDepartmentId(accountDepartmentId);
        QueryResult<IObjectData> query = this.getQuery(user, erDepartmentQueryArg);
        if (CollectionUtils.isEmpty(query.getData())) {
            return null;
        }
        if (query.getData().size() > 1) {
            log.warn("getEnterpriseTypeErDepByOuterTenantId query.getData().size() > 1");
        }
        return ErDepartmentObj.newInstance(query.getData().get(0));
    }

    public Long getManagerIdByEnterpriseRelationType(User user, Long outerTenantId) {
        ErDepartmentQueryArg erDepartmentQueryArg = new ErDepartmentQueryArg();
        erDepartmentQueryArg.setErDepTypes(Collections.singletonList(ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType()));
        erDepartmentQueryArg.setOuterTenantIds(Collections.singletonList(outerTenantId));

        QueryResult<IObjectData> result = this.getQuery(user, erDepartmentQueryArg);
        if (result.getData() != null && result.getData().size() > 0) {
            List<ErDepartmentObj> erDepartmentObjList = ErDepartmentObj.newObjInstance(result.getData());
            for (ErDepartmentObj erDepartmentObj : erDepartmentObjList) {
                if (erDepartmentObj.getManagerId() != null) {
                    return erDepartmentObj.getManagerId();
                }
            }
        }

        return null;
    }

    @SneakyThrows
    public ErDepartmentObj getById(User user, String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        List<ErDepartmentObj> erDepartmentIds = getByDataLifeStatus(user, Lists.newArrayList(id), false, false);
        if (ObjectUtils.isEmpty(erDepartmentIds)) {
            return null;
        }
        return erDepartmentIds.get(0);
    }

    public List<ErDepartmentObj> getByIds(User user, List<String> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<ErDepartmentObj> erDepartmentIds = getByDataLifeStatus(user, ids, false, false);
        return erDepartmentIds;
    }


    public List<ErDepartmentObj> getByIds(User user, List<String> ids, List<String> selectFields) {
        if (ObjectUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }

        if (ObjectUtils.isEmpty(selectFields)) {
            return getByDataLifeStatus(user, ids, false, false);
        }

        ErDepartmentQueryArg erDepartmentQueryArg = new ErDepartmentQueryArg();
        erDepartmentQueryArg.setIds(ids);
        erDepartmentQueryArg.setOffset(0);
        erDepartmentQueryArg.setLimit(ids.size());
        erDepartmentQueryArg.setSelectFields(selectFields);
        QueryResult<IObjectData> query = getQuery(user, erDepartmentQueryArg);
        if (ObjectUtils.isEmpty(query.getData())) {
            return Lists.newArrayList();
        }

        return query.getData().stream().map(ErDepartmentObj::newInstance).collect(Collectors.toList());
    }

    public List<ErDepartmentObj> findObjectDataByIdsIncludeDeleted(User user, Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        if (ids.size() > 2000) {
            throw new RelationException(ResultCode.BATCH_SIZE_LIMIT_ERROR);
        }
        List<String> idsList = ids.stream().distinct().collect(Collectors.toList());
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(user, idsList, ErDepartmentObjConstant.API_NAME);
        if (objectDataList == null) {
            return new ArrayList<>();
        }
        return ErDepartmentObj.newObjInstance(objectDataList);
    }

    @SneakyThrows
    public List<ErDepartmentObj> getByDataLifeStatus(User user, List<String> ids, Boolean includeInvalid, Boolean includeDeleted) {
        IActionContext actionContextExt = ActionContextExt.of(user).setSkipRelevantTeam(true).setCalculateQuote(true).getContext();
        actionContextExt.put(ActionContextKey.SEARCH_DATA_NEED_DEEP_QUOTE, Boolean.TRUE);
        List<IObjectData> iObjectDatas = null;
        if (Boolean.TRUE.equals(includeDeleted)) {
            iObjectDatas = objectDataService.findByIdsIncludeDeleted(ids, user.getTenantId(), ErDepartmentObjConstant.API_NAME, actionContextExt);
        } else if (Boolean.TRUE.equals(includeInvalid)) {
            iObjectDatas = objectDataService.findByIdsIncludeInvalid(ids, user.getTenantId(), ErDepartmentObjConstant.API_NAME, actionContextExt);
        } else {
            iObjectDatas = objectDataService.findByIds(ids, user.getTenantId(), ErDepartmentObjConstant.API_NAME, actionContextExt);
        }

        List<ErDepartmentObj> result = CollectionUtils.emptyIfNull(iObjectDatas).stream().map(ErDepartmentObj::newInstance).collect(Collectors.toList());
        return result;
    }


    private SearchTemplateQuery buildSearchQuery(ErDepartmentQueryArg arg) {
        PatternBuilder patternBuilder = PatternBuilder.newBuilder();
        List<IFilter> filters = new ArrayList<>(3);
        if (arg.getOwnerOuterUid() != null) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.MANAGER_ID);
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(arg.getOwnerOuterUid() + ""));
            filters.add(filter);
            patternBuilder.and();
        }
        if (arg.getAssistOuterUid() != null) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.ASSISTANT_IDS);
            filter.setOperator(Operator.IN);
            filter.setFieldValues(Lists.newArrayList(arg.getAssistOuterUid() + ""));
            filters.add(filter);
            patternBuilder.and();
        }
        if (ObjectUtils.isNotEmpty(arg.getOwnerOrAssistOutUids())) {
            List<String> ownerOrAssistOutUids = arg.getOwnerOrAssistOutUids()
                    .stream().map(String::valueOf)
                    .collect(Collectors.toList());
            PatternBuilder patternOrBuilder = patternBuilder.newSubBuilder();
            Filter assistantFilter = new Filter();
            assistantFilter.setFieldName(ErDepartmentObjConstant.ASSISTANT_IDS);
            assistantFilter.setOperator(Operator.IN);
            assistantFilter.setFieldValues(ownerOrAssistOutUids);
            filters.add(assistantFilter);
            patternOrBuilder.or();
            Filter ownerFilter = new Filter();
            ownerFilter.setFieldName(ErDepartmentObjConstant.MANAGER_ID);
            ownerFilter.setOperator(Operator.IN);
            ownerFilter.setFieldValues(Lists.newArrayList(ownerOrAssistOutUids));
            filters.add(ownerFilter);
            patternOrBuilder.or();
            patternBuilder.addSubPattern(patternOrBuilder.build());
        }

        if (arg.getErDepStatus() != null) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.STATUS);
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(arg.getErDepStatus() + ""));
            filters.add(filter);
            patternBuilder.and();
        }
        if (StringUtils.isNotBlank(arg.getAccountDepartmentId())) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.ACCOUNT_DEPARTMENT_ID);
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(arg.getAccountDepartmentId() + ""));
            filters.add(filter);
            patternBuilder.and();
        }
        if (CollectionUtils.isNotEmpty(arg.getErDepTypes())) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.DEPARTMENT_TYPE);
            filter.setOperator(Operator.IN);
            filter.setFieldValues(ListUtil.toStringList(arg.getErDepTypes()));
            filters.add(filter);
            patternBuilder.and();
        }

        if (CollectionUtils.isNotEmpty(arg.getParentIds())) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.PARENT_ID);
            filter.setOperator(Operator.IN);
            filter.setFieldValues(arg.getParentIds());
            filters.add(filter);
            patternBuilder.and();
        }
        if (CollectionUtils.isNotEmpty(arg.getIds())) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.ID);
            filter.setOperator(Operator.IN);
            filter.setFieldValues(arg.getIds());
            filters.add(filter);
            patternBuilder.and();
        }
        if (StringUtils.isNotBlank(arg.getName())) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.NAME);
            filter.setOperator(Operator.LIKE);
            filter.setFieldValues(Lists.newArrayList(arg.getName()));
            filters.add(filter);
            patternBuilder.and();
        }

        if (CollectionUtils.isNotEmpty(arg.getNames())) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.NAME);
            filter.setOperator(Operator.IN);
            filter.setFieldValues(arg.getNames());
            filters.add(filter);
            patternBuilder.and();
        }
        if (BooleanUtils.isTrue(arg.isIncludeInvalid())){
            Filter filter = new Filter();
            filter.setFieldName(IObjectData.IS_DELETED);
            filter.setOperator(Operator.IN);
            filter.setFieldValues(Lists.newArrayList(DELETE_STATUS.NORMAL.string(),DELETE_STATUS.INVALID.string()));
            filters.add(filter);
            patternBuilder.and();
        }

        if (CollectionUtils.isNotEmpty(arg.getParentIdsWithCascade())) {
            String matchValue = String.format("*.%s.*", Joiner.on("|").join(arg.getParentIdsWithCascade()));
            Filter filter = SearchUtil.filter(ErDepartmentObjConstant.TREE_PATH, Operator.MATCH, matchValue);
            filters.add(filter);
            patternBuilder.and();
        }

        if (CollectionUtils.isNotEmpty(arg.getOuterTenantIds())) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.OUTER_TENANT_ID);
            filter.setOperator(Operator.IN);
            filter.setFieldValues(ListUtil.toStringList(arg.getOuterTenantIds()));
            filters.add(filter);
            patternBuilder.and();
        }
        if (ObjectUtils.isNotEmpty(arg.getOrgManageTypes())) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.ORG_MANAGE_TYPE);
            filter.setOperator(Operator.HASANYOF);
            filter.setFieldValues(arg.getOrgManageTypes());
            filters.add(filter);
            patternBuilder.and();
        }
        if (ObjectUtils.isNotEmpty(arg.getOuterTenantDeptIds())) {
            Filter filter = new Filter();
            filter.setFieldName(ErDepartmentObjConstant.OUTER_TENANT_DEPT_ID);
            filter.setOperator(Operator.IN);
            filter.setFieldValues(arg.getOuterTenantDeptIds());
            filters.add(filter);
            patternBuilder.and();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        if (arg.getAscErOrder() != null) {
            OrderBy orderBy = new OrderBy();
            orderBy.setFieldName(ErDepartmentObjConstant.ER_ORDER);
            orderBy.setIsAsc(arg.getAscErOrder());
            query.setOrders(Lists.newArrayList(orderBy));
        }
        if (BooleanUtils.isNotTrue(arg.getUseDataPermission())) {
            query.setPermissionType(0);
        } else {
            IDataRightsParameter dataRightsParameter = new DataRightsParameter();
            dataRightsParameter.setSceneType(DataRightPermissionScenTypeEnum.ALL.getValue());
            query.setDataRightsParameter(dataRightsParameter);
            query.setPermissionType(1);
        }
        if (arg.getLimit() != null) {
            query.setLimit(arg.getLimit());
        } else {
            query.setLimit(1000);
        }
        if (arg.getOffset() != null) {
            query.setOffset(arg.getOffset());
        }
        query.addFilters(filters);
        query.setPattern(patternBuilder.build());
        if (arg.getFindExplicitTotalNum() != null) {
            query.setFindExplicitTotalNum(arg.getFindExplicitTotalNum());
        }
        if (BooleanUtils.isNotFalse(arg.getSearchFromDb())) {
            query.setSearchSource("db");
        }
        log.info("pattern={}, filter size={},query={}", query.getPattern(), query.getFilters().size(), query);
        return query;
    }

    public QueryResult<IObjectData> getQuery(User user, ErDepartmentQueryArg arg) {
        QueryResult<IObjectData> result = null;

        if (arg.getLimit() != null && arg.getLimit() > 1000) {
            QueryResult<IObjectData> queryResult = new QueryResult<>();
            List<IObjectData> list = new ArrayList<>();
            OffsetUtil.splitLimitFunction(arg.getLimit(), arg.getOffset(), 1000, (offset, limit) -> {
                ErDepartmentQueryArg subArg = BeanUtil.copy(arg, ErDepartmentQueryArg.class);
                subArg.setOffset(offset);
                subArg.setLimit(limit);
                QueryResult<IObjectData> subQueryResult = doGetQuery(user, subArg);
                List<IObjectData> subList = subQueryResult.getData();
                list.addAll(subList);
                queryResult.setTotalNumber(subQueryResult.getTotalNumber());
                return subList.size();
            });
            queryResult.setData(list);
            if (list.size() > 1000) {
                try {
                    String extInfo = String.format("request out of limit: null, record size: %d", list.size());
                    EsLogSendUtil.sendBizLogToEs("fs-er-biz", "fs-er-rest-api-query", user.getTenantId(), user.getTenantId(), JsonUtil.toJson(arg), ServiceContextHolder.getUrl(), extInfo,
                            ServiceContextHolder.getPeerName(), list.size() + "");
                } catch (Exception ex) {
                    log.info("OutLimitQuery:PublicEmployeeDao-->getQuery({},{},{}),size={}", user.getTenantIdInt(), user.getUserIdInt(), arg, list.size());
                }
            }
            result = queryResult;
        } else {
            result = doGetQuery(user, arg);
            if (arg.getLimit() == null && result.getTotalNumber() > 1000) {
                try {
                    String extInfo = String.format("request out of limit: null, record size: %d", result.getData().size());
                    EsLogSendUtil.sendBizLogToEs("fs-er-biz", "fs-er-rest-api-query2", user.getTenantId(), user.getTenantId(), JsonUtil.toJson(arg), ServiceContextHolder.getUrl(), extInfo,
                            ServiceContextHolder.getPeerName(), result.getData().size() + "");
                } catch (Exception ex) {
                    log.info("OutLimitQuery2:PublicEmployeeDao-->getQuery({},{},{})", user.getTenantIdInt(), user.getUserIdInt(), arg);
                }
            }
        }
        if (BooleanUtils.isTrue(arg.getFetchParentName())) {
            List<IObjectData> iObjectDatas = result.getData();
            List<Map<String, Object>> collect = iObjectDatas.stream().map(ObjectDataDocument::of).collect(Collectors.toList());
            Set<String> parentIds = new HashSet<>();
            for (Map<String, Object> objectMap : collect) {
                ErDepartmentObj obj = ErDepartmentObj.newInstance(objectMap);
                parentIds.add(obj.getParentId());
            }
            Map<String, String> idNames = serviceFacade.findNameByIds(User.systemUser(user.getTenantId()), ErDepartmentObjConstant.API_NAME, Lists.newArrayList(parentIds));
            for (Map<String, Object> objectMap : collect) {
                ErDepartmentObj obj = ErDepartmentObj.newInstance(objectMap);
                String parentName = idNames.get(obj.getParentId());
                if (StringUtils.isNotEmpty(parentName)) {
                    obj.set(ErDepartmentObjConstant.PARENT_ID_NAME, parentName);
                }
            }
        }
        return result;
    }

    public QueryResult<IObjectData> doGetQuery(User user, ErDepartmentQueryArg arg) {
        try {
            if (fxiaokeAccountManager.isAdmin(user)) {
                arg.setUseDataPermission(false);
            }
            SearchTemplateQuery query = buildSearchQuery(arg);
            if (BooleanUtils.isTrue(arg.getUseDataPermission())) {
                IActionContext actionContextExt = ActionContextExt.of(user).setSkipRelevantTeam(true).setCalculateQuote(true).getContext();
                actionContextExt.put(ActionContextKey.SEARCH_DATA_NEED_DEEP_QUOTE, Boolean.TRUE);
                buildSearchAttribute(actionContextExt, arg);

                if (ObjectUtils.isEmpty(arg.getSelectFields())) {
                    return serviceFacade.findBySearchQuery(actionContextExt, ErDepartmentObjConstant.API_NAME, query);
                }
                return serviceFacade.findBySearchTemplateQueryWithFields(actionContextExt, ErDepartmentObjConstant.API_NAME, query, arg.getSelectFields());
            }

            IActionContext actionContextExt = ActionContextExt.of(User.systemUser(user.getTenantId())).setSkipRelevantTeam(true).getContext();
            actionContextExt.put(ActionContextKey.SEARCH_DATA_NEED_DEEP_QUOTE, Boolean.TRUE);
            buildSearchAttribute(actionContextExt, arg);
            if (ObjectUtils.isEmpty(arg.getSelectFields())) {
                return serviceFacade.findBySearchQuery(actionContextExt, ErDepartmentObjConstant.API_NAME, query);
            }
            return serviceFacade.findBySearchTemplateQueryWithFields(actionContextExt, ErDepartmentObjConstant.API_NAME, query, arg.getSelectFields());
        } catch (ObjectDefNotFoundError ex) {
            log.info("ObjectDefNotFoundError  PublicEmployeeObj,user={},arg={}", user, arg);
            QueryResult<IObjectData> res = new QueryResult<>();
            res.setData(Lists.newArrayList());
            res.setTotalNumber(0);
            return res;
        }
    }

    public void updatePublicEmployeeObjsMainErDep(User user, List<Long> updateMainErDepartmentOuterUids, String id) {
        if (CollectionUtils.isEmpty(updateMainErDepartmentOuterUids) || StringUtils.isBlank(id)) {
            return;
        }
        List<IObjectData> dataList = new ArrayList<>();
        for (Long updateMainErDepartmentOuterUid : updateMainErDepartmentOuterUids) {
            PublicEmployeeObj publicEmployeeObj = new PublicEmployeeObj();
            publicEmployeeObj.set(PublicEmployeeObjConstant.ID, updateMainErDepartmentOuterUid + "");
            publicEmployeeObj.set(Tenantable.TENANT_ID, user.getTenantIdInt() + "");
            publicEmployeeObj.set(IObjectData.DESCRIBE_API_NAME, PublicEmployeeObjConstant.API_NAME);
            publicEmployeeObj.set(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT, Lists.newArrayList(id));
            dataList.add(publicEmployeeObj);
        }

        List<String> validFieldApiNames = ObjectDataDocument.ofList(dataList).stream().flatMap(dataDocument -> dataDocument.keySet().stream()).distinct().collect(Collectors.toList());
        serviceFacade.batchUpdateByFields(user, dataList, validFieldApiNames);
    }

    private void buildSearchAttribute(IActionContext context, ErDepartmentQueryArg arg) {
        if (arg.isIgnoreBaseVisibleRange()) {
            context.put(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, true);
        }
    }
}
