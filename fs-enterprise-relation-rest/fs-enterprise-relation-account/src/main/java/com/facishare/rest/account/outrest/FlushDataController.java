package com.facishare.rest.account.outrest;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.annotation.IgnoreI18nFile;
import com.facishare.enterprise.common.constant.*;
import com.facishare.enterprise.common.enums.LoginRegisterOptionEnum;
import com.facishare.enterprise.common.enums.LoginRegisterStatusEnum;
import com.facishare.enterprise.common.exception.RelationException;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.model.LoginRegisterConfigVO;
import com.facishare.enterprise.common.model.LoginRegisterLayoutConfigVo;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.model.paas.AddDescribeFieldData;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.CloudUtil;
import com.facishare.enterprise.common.util.CommonPoolUtil;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.enterprise.common.util.OffsetUtil;
import com.facishare.er.api.model.ERAccountActiveConfigData;
import com.facishare.er.api.model.RegisterERAccountActiveConfigData;
import com.facishare.er.api.model.arg.CreateLoginRegisterConfigArg;
import com.facishare.er.api.model.arg.EnterpriseDimensionsFlushDbArg;
import com.facishare.er.api.model.arg.ListLoginRegisterConfigsArg;
import com.facishare.er.api.model.arg.UpdateLoginRegisterConfigStatusArg;
import com.facishare.er.api.model.result.ListLoginRegisterConfigsResult;
import com.facishare.er.api.model.vo.EnterpriseCrmRelationSettingVo;
import com.facishare.global.rest.arg.*;
import com.facishare.global.rest.common.HeaderObj;
import com.facishare.global.rest.dao.mongo.EnterpriseMetaDataEntityDao;
import com.facishare.global.rest.dao.mongo.FxiaokeEmployeeAssociationEntityDao;
import com.facishare.global.rest.dao.mongo.FxiaokeEnterpriseAssociationEntityDao;
import com.facishare.global.rest.dao.pg.EmployeeCardEntityDao;
import com.facishare.global.rest.dao.pg.EnterpriseCardEntityDao;
import com.facishare.global.rest.data.UpstreamPublicEmployeeVos;
import com.facishare.global.rest.model.entity.*;
import com.facishare.global.rest.result.ListUpstreamPublicEmployeesResult;
import com.facishare.global.rest.service.GlobalService;
import com.facishare.linkapp.api.service.HttpUpstreamService;
import com.facishare.organization.api.model.employee.EmployeeOption;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.entity.metadata.Unique;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.DataMapper;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.UniqueMapper;
import com.facishare.paas.metadata.impl.AbstractDocumentBasedDBRecord;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.facishare.rest.account.dao.EnterpriseRelationDao;
import com.facishare.rest.account.dao.data.EnterpriseRelationQueryArg;
import com.facishare.rest.account.dao.mongo.EnterpriseLinkWxServiceMetaDataDao;
import com.facishare.rest.account.dao.mongo.entity.EnterpriseLinkWxServiceMetaData;
import com.facishare.rest.account.manager.*;
import com.facishare.rest.account.mq.OrganizationEventProcessor;
import com.facishare.rest.account.old.objproxy.EnterpriseRelationObjProxyServiceOldManager;
import com.facishare.rest.account.outrest.arg.*;
import com.facishare.rest.account.outrest.arg.flush.FlushBusinessOptimzeCrmDataArg;
import com.facishare.rest.account.outrest.arg.flush.FlushEnterpriseItControlArg;
import com.facishare.rest.account.outrest.arg.flush.FlushPhoneCodeTemplateIdAndVariableArg;
import com.facishare.rest.account.outrest.data.LicenseLowerThanUpstreamData;
import com.facishare.rest.account.outrest.data.LicenseLowerThanUpstreamData.EaDownstreamOpenLicenseData;
import com.facishare.rest.account.outrest.data.MultiOpenIdAccountData;
import com.facishare.rest.account.remote.FxiaokeAccountManager;
import com.facishare.rest.account.remote.OuterRoleRemoteBaseManager;
import com.facishare.rest.account.remote.PaasLicenseManager;
import com.facishare.rest.account.remote.RemoteManager;
import com.facishare.rest.account.remote.data.DownstreamOpenLicenseData;
import com.facishare.rest.account.service.*;
import com.facishare.rest.account.service.flush.licenseupgrade.EnterpriseLicenseUpgradeService;
import com.facishare.rest.account.service.flush.service.FlashCommonService;
import com.facishare.rest.account.service.flush.service.TenantService;
import com.facishare.rest.account.service.flush.wechat.WechatDataFlushService;
import com.facishare.rest.account.util.PatternBuilder;
import com.facishare.uc.api.model.enterprise.arg.BatchGetEnterpriseRunStatusArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetEnterpriseRunStatusResult;
import com.facishare.uc.api.model.fscore.RunStatus;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.arg.AddTeamMemberArg;
import com.fxiaoke.crmrestapi.arg.AddTeamMemberArg.OuterUser;
import com.fxiaoke.crmrestapi.arg.AddTeamMemberNewArg;
import com.fxiaoke.crmrestapi.arg.AddTeamMemberNewArg.TeamMemberInfos;
import com.fxiaoke.crmrestapi.arg.ChangePartnerOwnerArg;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.common.contants.*;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants;
import com.fxiaoke.crmrestapi.result.BatchObjectDataResult;
import com.fxiaoke.crmrestapi.result.ListObjectDescribeResult;
import com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.enterpriserelation.objrest.arg.*;
import com.fxiaoke.enterpriserelation.objrest.common.EnterpriseRelationTypeEnum;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentObjConstant;
import com.fxiaoke.enterpriserelation.objrest.common.IdentityTypeEnum;
import com.fxiaoke.enterpriserelation.objrest.result.ListByConditionResult;
import com.fxiaoke.enterpriserelation.objrest.result.UpstreamPublicEmployeeData;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.fxiaoke.enterpriserelation.objrest.service.ErBindThirdAccountObjService;
import com.fxiaoke.enterpriserelation.objrest.service.PublicEmployeeObjService;
import com.fxiaoke.enterpriserelation.objrest.util.PaasServiceContextSupport;
import com.fxiaoke.otherrestapi.syncservice.constant.ResultCodeEnum;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.atomic.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import io.swagger.models.auth.In;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据刷库脚本
 *
 * @IgnoreI18nFile
 */
@Slf4j
@RestController
@RequestMapping("/flushData")
public class FlushDataController {
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private GlobalService globalService;
    @Autowired
    private EnterpriseRelationManager enterpriseRelationManager;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    private PublicEmployeeObjService publicEmployeeObjCrossCloudService = ErObjPaasApiBusServiceManager.getPublicEmployeeObjService();
    @Autowired
    private EnterpriseLicenseUpgradeService enterpriseLicenseUpgradeService;
    @Autowired
    private WechatDataFlushService wechatDataFlushService;
    @Autowired
    private EnterpriseMetaDataEntityDao enterpriseMetaDataEntityDaoRemoteManager;
    @Autowired
    private EnterpriseRelationObjProxyServiceOldManager enterpriseRelationObjServiceRemote;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private FxiaokeEnterpriseAssociationEntityDao fxiaokeEnterpriseAssociationEntityDaoRemoteManager;
    @Autowired
    private EnterpriseCardEntityDao enterpriseCardEntityDaoRemoteManager;
    @Autowired
    private HttpUpstreamService httpUpstreamService;
    @Autowired
    private EmployeeCardEntityDao employeeCardEntityDaoRemoteManager;
    @Autowired
    private FxiaokeEmployeeAssociationEntityDao fxiaokeEmployeeAssociationEntityDaoRemoteManager;
    @Autowired
    private HttpUpstreamService upstreamService;
    @Autowired
    private EIEAConverter eieaConverter;
    //@Autowired
    private OrganizationEventProcessor organizationEventProcessor;
    @Autowired
    private FxiaokeAccountManager fxiaokeAccountManager;
    @Autowired
    private FxiaokeEmployeeAssociationEntityDao fxiaokeEmployeeAssociationEntityDaoOldManager;
    @Autowired
    private EnterpriseMetaDataManager enterpriseMetaDataManager;
    @Autowired
    private RemoteManager remoteManager;
    @Autowired
    private RecordTypeService recordTypeService;
    @Autowired
    private PaasObjectDescribeManager paasObjectDescribeManager;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    private EnterpriseRelationObjService enterpriseRelationObjService = ErObjPaasApiBusServiceManager.getEnterpriseRelationObjService();
    private PublicEmployeeObjService publicEmployeeObjService = ErObjPaasApiBusServiceManager.getPublicEmployeeObjService();
    private ErBindThirdAccountObjService erBindThirdAccountObjService = ErObjPaasApiBusServiceManager.getErBindThirdAccountObjService();
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private OuterRoleRemoteBaseManager outerRoleRemoteBaseManager;
    @Autowired
    private PublicEmployeeManager publicEmployeeManager;
    @Autowired
    private PaasLicenseManager paasLicenseManager;
    @Autowired
    private EnterpriseLicenseService enterpriseLicenseService;
    @Autowired
    private ERAccountActiveConfigService erAccountActiveConfigService;
    @Autowired
    private LoginRegisterConfigService loginRegisterConfigService;
    @Autowired
    private EnterpriseLinkWxServiceMetaDataDao enterpriseLinkWxServiceMetaDataDaoOldManager;
    @Autowired
    private EnterpriseRelationDao enterpriseRelationDao;
    @Autowired
    private CloudUtil cloudUtil;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private ObjectDataServiceImpl objectDataServiceImpl;
    @Autowired
    private UniqueMapper uniqueMapper;
    @Autowired
    private DataMapper dataMapper;
    @Autowired
    private FlashCommonService flashCommonService;
    @Autowired
    @Qualifier("jedisSupport")
    private MergeJedisCmd jedis;
    private volatile static RateLimiter rateLimiter;
    @Autowired
    private ObjectDescribeServiceImpl baseObjectDescribeService;

    private static final String FIND_DEPARTMENT_CODE_SQL = "SELECT id,tenant_id,department_code from biz_er_department where tenant_id = '%s' and is_deleted in ('0','1') AND department_code IS NOT NULL ORDER BY ID ASC LIMIT %s ";

    private static final String DELETE_UNIQUE_FIELD_SQL = "DELETE FROM mt_unique WHERE (tenant_id,unique_id) IN (select tenant_id,unique_id FROM mt_unique WHERE tenant_id ='%s' AND describe_api_name = 'ErDepartmentObj' AND field_name = 'department_code' LIMIT %s)   ";

    private static final String UPDATE_EMPTY_DEPARTMENT_CODE_SQL = "UPDATE biz_er_department SET department_code = null WHERE (id,tenant_id) IN (select id,tenant_id from biz_er_department where tenant_id = '%s' AND department_code = '--' ORDER BY ID ASC LIMIT %s )";

    private static final String FIND_DEPARTMENT_CODE_SQL_2 = "SELECT id,tenant_id,department_code from biz_er_department where tenant_id = '%s' and is_deleted in ('0','1') AND department_code IS NOT NULL AND ID > '%s' ORDER BY ID ASC LIMIT %s ";

    private static final String UPDATE_FIELD_SQL = "UPDATE mt_field set is_unique = TRUE WHERE tenant_id = '%s' AND field_id = '%s'";

    private static final String SELECT_ENTERPRISE_RELATION_START_TIME_IS_NULL_SQL = "SELECT id,create_time,last_modified_time,relation_type FROM biz_enterprise_relation WHERE (id,tenant_id) IN (select id,tenant_id from biz_enterprise_relation where tenant_id = '%s' and is_deleted in ('0','1') AND start_time IS NULL ORDER BY ID ASC LIMIT %s )";

    private static final String SELECT_ENTERPRISE_RELATION_START_TIME_IS_NULL_SQL_2 = "SELECT id,create_time,last_modified_time,relation_type FROM biz_enterprise_relation WHERE (id,tenant_id) IN (select id,tenant_id from biz_enterprise_relation where tenant_id = '%s' and is_deleted in ('0','1') AND start_time IS NULL AND ID > '%s' ORDER BY ID ASC LIMIT %s )";


    private static final String SELECT_PUBLIC_EMPLOYEE_START_TIME_IS_NULL_SQL = "SELECT id,create_time,last_modified_time,type FROM biz_public_employee WHERE (id,tenant_id) IN (select id,tenant_id from biz_public_employee where tenant_id = '%s' and is_deleted in ('0','1') AND start_time IS NULL  ORDER BY ID ASC LIMIT %s)";

    private static final String SELECT_PUBLIC_EMPLOYEE_START_TIME_IS_NULL_SQL_2 = "SELECT id,create_time,last_modified_time,type FROM biz_public_employee WHERE (id,tenant_id) IN (select id,tenant_id from biz_public_employee where tenant_id = '%s' and is_deleted in ('0','1') AND start_time IS NULL AND ID > '%s' ORDER BY ID ASC LIMIT %s )";

    private static final String UPDATE_ENTERPRISE_RELATION_START_STOP_TIME_SQL = "UPDATE biz_enterprise_relation SET start_time = %s,stop_time = %s where tenant_id = '%s' and id = '%s' ";
    private static final String UPDATE_PUBLIC_EMPLOYEE_START_STOP_TIME_SQL = "UPDATE biz_public_employee SET start_time = %s,stop_time = %s where tenant_id = '%s' and id = '%s' ";

    static {
        ConfigFactory.getConfig("fs-er-gray", config -> {
            rateLimiter = RateLimiter.create(config.getDouble("migrate.rate", 400));
            log.info("rateLimiter:{}", rateLimiter.getRate());
        });
    }

    @RequestMapping(value = "/redis", method = RequestMethod.GET)
    public Result redis(@RequestParam("key") String key, @RequestParam("opt") String opt, @RequestParam("value") String value) {
        if (opt.equals("del")) {
            return Result.newSuccess(jedis.del(key));
        } else if (opt.equals("set")) {
            return Result.newSuccess(jedis.set(key, value));
        } else if (opt.equals("get")) {
            return Result.newSuccess(jedis.get(key));
        } else if (opt.equals("exists")) {
            return Result.newSuccess(jedis.exists(key));
        } else if (opt.equals("ttl")) {
            return Result.newSuccess(jedis.ttl(key));
        }
        return Result.newSuccess("not opt");
    }

    @PostMapping("/flushPhoneCodeTemplateAndVariable")
    public Result<Boolean> flushPhoneCodeTemplateAndVariable(@RequestBody FlushPhoneCodeTemplateIdAndVariableArg arg) {
        try {
            enterpriseMetaDataManager.updatePhoneCodeTemplateIdAndVariable(arg.getEa(), arg.getPhoneCodeTemplateId(), arg.getPhoneCodeVariable());
            return Result.newSuccess(true);
        } catch (Throwable throwable) {
            log.warn("", throwable);
        }

        return Result.newSuccess(false);
    }

    @PostMapping("/clearEnterpriseRelationAfterDelete")
    public Result<Boolean> clearEnterpriseRelationAfterDelete(@RequestBody ClearEnterpriseRelationAfterStopArg arg) {
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            arg.setEas(listAllUpstreamEas());
        }
        if (CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newSuccess(false);
        }

        // 过滤停用的用企业
        BatchGetEnterpriseRunStatusArg runStatusArg = new BatchGetEnterpriseRunStatusArg();
        runStatusArg.setEnterpriseAccounts(arg.getEas());
        BatchGetEnterpriseRunStatusResult runStatusResult = enterpriseEditionService.batchGetEnterpriseRunStatus(runStatusArg);
        if (MapUtils.isEmpty(runStatusResult.getEaRunStatus())) {
            log.warn("clearEnterpriseRelationAfterStop -> batchGetEnterpriseRunStatus failed! result:{}", runStatusResult);
            return Result.newSuccess(false);
        }
        Set<Integer> delRunStatusSet = Sets.newHashSet(RunStatus.RUN_STATUS_DELETE);
        List<String> delEas = runStatusResult.getEaRunStatus().entrySet().stream().filter(x -> delRunStatusSet.contains(x.getValue())).map(Entry::getKey).collect(Collectors.toList());
        log.info("clearEnterpriseRelationAfterStop -> total:{}, stopped:{}, filtered stopped eas:{}", arg.getEas().size(), delEas.size(), delEas);
        if (CollectionUtils.isEmpty(delEas)) {
            return Result.newSuccess(false);
        }

        // 清理停用企业互联账号相关数据
        if (BooleanUtils.isTrue(arg.getExecute())) {
            boolean success = enterpriseRelationManager.clearEnterpriseRelationAfterDelete(delEas);
            boolean result = enterpriseRelationManager.clearLinkAppAssociationData(delEas);
            log.info("clearEnterpriseRelationAfterDelete：{}", result);
            return Result.newSuccess(success);
        }
        return Result.newSuccess(false);
    }

    /**
     * 清理已被停用的企业关联的互联应用相关数据
     *
     * @param arg 清理目标参数
     * @return 清理结果
     */
    @PostMapping("/clearLinkAppAssociationDataAfterEnterpriseDeleted")
    public Result<Boolean> clearLinkAppAssociationDataAfterEnterpriseDeleted(@RequestBody ClearEnterpriseRelationAfterStopArg arg) {
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            List<String> eas = upstreamService.listUpstreamEnterpriseLinkAppRelationByStatus(1).getData();
            arg.setEas(eas);
        }
        if (CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newSuccess(false);
        }
        // 过滤停用的用企业
        BatchGetEnterpriseRunStatusArg runStatusArg = new BatchGetEnterpriseRunStatusArg();
        runStatusArg.setEnterpriseAccounts(arg.getEas());
        BatchGetEnterpriseRunStatusResult runStatusResult = enterpriseEditionService.batchGetEnterpriseRunStatus(runStatusArg);
        if (MapUtils.isEmpty(runStatusResult.getEaRunStatus())) {
            log.warn("clearLinkAppAssociationDataAfterEnterpriseDeleted -> batchGetEnterpriseRunStatus failed! result:{}", runStatusResult);
            return Result.newSuccess(false);
        }
        if (CollectionUtils.isEmpty(arg.getEaStatus())) {
            arg.setEaStatus(Lists.newArrayList(RunStatus.RUN_STATUS_DELETE));
        }
        List<String> delEas = runStatusResult.getEaRunStatus().entrySet().stream().filter(x -> arg.getEaStatus().contains(x.getValue())).map(Entry::getKey).collect(Collectors.toList());
        log.info("clearLinkAppAssociationDataAfterEnterpriseDeleted -> total:{}, stopped:{}, filtered stopped eas:{}", arg.getEas().size(), delEas.size(), delEas);
        if (CollectionUtils.isEmpty(delEas)) {
            return Result.newSuccess(false);
        }

        // 清理停用企业关联互联应用相关数据
        if (BooleanUtils.isTrue(arg.getExecute())) {
            boolean result = enterpriseRelationManager.clearLinkAppAssociationData(delEas);
            log.info("clearLinkAppAssociationDataAfterEnterpriseDeleted：{}", result);
            return Result.newSuccess(result);
        }
        return Result.newSuccess(false);
    }

    /**
     * 修复互联用户outer_uid字段异常数据（金杯电工股份有限公司：726805）
     *
     * @param arg 修改数据条件
     * @return 修复结果
     */
    @PostMapping("/fixPublicEmployeeOuterUidField")
    public Result<Boolean> fixPublicEmployeeOuterUidField(@RequestBody FixPublicEmployeeObjDataArg arg) {
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            List<String> eas = listAllUpstreamEas();
            arg.setEas(eas);
        }
        if (CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newSuccess(false);
        }
        List<String> normalEas = Lists.newArrayList();
        OffsetUtil.offsetFunction(100, (offset, limit) -> {
            // 过滤停用的用企业
            BatchGetEnterpriseRunStatusArg runStatusArg = new BatchGetEnterpriseRunStatusArg();
            runStatusArg.setEnterpriseAccounts(normalEas.subList(offset, offset + limit));
            BatchGetEnterpriseRunStatusResult runStatusResult = enterpriseEditionService.batchGetEnterpriseRunStatus(runStatusArg);
            if (MapUtils.isEmpty(runStatusResult.getEaRunStatus())) {
                log.warn("fixPublicEmployeeOuterUidField -> batchGetEnterpriseRunStatus failed! result:{}", runStatusResult);
                return 0;
            }
            List<String> Eas = runStatusResult.getEaRunStatus().entrySet().stream().filter(x -> x.getValue().equals(RunStatus.RUN_STATUS_NORMAL)).map(Entry::getKey).collect(Collectors.toList());
            normalEas.addAll(Eas);
            return runStatusResult.getEaRunStatus().size();
        });

        for (String ea : normalEas) {
            int tenantId = eieaConverter.enterpriseAccountToId(ea);
            OffsetUtil.offsetFunction(1000, (offset, limit) -> {
                com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(tenantId, 1000);
                SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
                queryTemplate.setLimit(limit);
                queryTemplate.setOffset(offset);
                queryTemplate.setNeedReturnCountNum(false);
                queryTemplate.setDataRolePermission(false);
                QueryBySearchTemplateResult result = objectDataService.queryBySearchTemplate(headerObj, PublicEmployeeObjConstant.API_NAME, queryTemplate).getData();
                if (result.getQueryResult().getData() != null) {
                    List<ObjectData> publicEmployeeList = result.getQueryResult().getData();
                    for (ObjectData publicEmployeeObj : publicEmployeeList) {
                        String outerUid = publicEmployeeObj.getString(PublicEmployeeObjConstant.OUTER_UID);
                        if (StringUtils.isEmpty(outerUid) || !outerUid.matches("^\\d+$")) {
                            log.info("fixPublicEmployeeOuterUidField ->upstreamEa:{},publicEmployeeId:{}", tenantId, publicEmployeeObj.getId());
                            if (BooleanUtils.isTrue(arg.getExecute())) {
                                publicEmployeeObj.put(PublicEmployeeObjConstant.OUTER_UID, publicEmployeeObj.getId());
                                com.fxiaoke.crmrestapi.common.result.Result<ObjectData> result1 = objectDataService.updateObjectData(headerObj, PublicEmployeeObjConstant.API_NAME, publicEmployeeObj.getId(), publicEmployeeObj);
                                log.info("fixPublicEmployeeOuterUidField ->upstreamEa:{},publicEmployeeId:{},result:{}", tenantId, publicEmployeeObj.getId(), result1.getMessage());
                            }
                        }
                    }
                }
                return result.getQueryResult().getTotalNumber();
            });
        }

        return Result.newSuccess(false);
    }

    @PostMapping("/fixDownstreamEmployeeInfoUpdateSyncEmployeeCard")
    public Result<Boolean> fixDownstreamEmployeeInfoUpdateSyncEmployeeCard(@RequestBody FixPublicEmployeeObjDataArg arg) {
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            List<String> eas = listAllUpstreamEas();
            arg.setEas(eas);
        }
        if (CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newSuccess(false);
        }
        for (String ea : arg.getEas()) {
            int tenantId = eieaConverter.enterpriseAccountToId(ea);
            try {
                List<EmployeeOption> allEmployeeOption = fxiaokeAccountManager.getAllEmployeeOption(tenantId);
                for (EmployeeOption employeeOption : allEmployeeOption) {
                    try {
                        Integer employeeId = employeeOption.getId();
                        FxiaokeEmployeeAssociationEntity fsEmp = fxiaokeEmployeeAssociationEntityDaoOldManager.getByFixaokeNotCreate(ea, employeeId);
                        if (fsEmp == null) {
                            continue;
                        }
                        organizationEventProcessor.updateEmployeeCard(ea, employeeId, fsEmp.getOuterUid(), fsEmp.getOuterTenantId());
                        DownstreamOuterUidArg downstreamOuterUidArg = new DownstreamOuterUidArg();
                        downstreamOuterUidArg.setDownstreamOuterUid(fsEmp.getOuterUid());
                        List<String> upstreamEas = globalService.listAllUpstreamEasByOuterUid(HeaderObj.newInstance(0), downstreamOuterUidArg).getData();
                        if (CollectionUtils.isNotEmpty(upstreamEas)) {
                            for (String upstreamEa : upstreamEas) {
                                Integer upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
                                EnterpriseCrmRelationSettingVo settingVo = enterpriseMetaDataManager.getEnterpriseCrmRelationSetting(upstreamEa);
                                // 企业默认开启
                                if (settingVo != null && BooleanUtils.isFalse(settingVo.getDownstreamFsAccountInfoSyncEnterpriseRelationInfoSwitch())) {
                                    continue;
                                }
                                ServiceContext serviceContext = publicEmployeeObjCrossCloudService.createServiceContext(upstreamTenantId + "", "-10000");
                                if (employeeOption.getStatus().equals(EmployeeEntityStatus.STOP.getValue())) {
                                    PublicEmployeeStopActionArg stopArg = new PublicEmployeeStopActionArg();
                                    stopArg.setDownstreamOuterUid(fsEmp.getOuterUid());
                                    publicEmployeeObjCrossCloudService.stopAction(serviceContext, stopArg);
                                } else {
                                    publicEmployeeManager.checkAndUpdateEmployeeInfo(upstreamTenantId, fsEmp.getOuterUid());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.warn("下游员工update event 更新卡片表失败,[event:{}]", employeeOption, e);
                    }
                }
            } catch (Exception e) {
                log.warn("fxiaokeAccountManager.getAllEmployeeOption ex", e);
            }
        }
        return Result.newSuccess(false);
    }

    @PostMapping("/fixCardFsIds")
    public void fixCardFsIds(@RequestBody ClearEnterpriseRelationAfterStopArg arg) {
        int tenantId = eieaConverter.enterpriseAccountToId(arg.getEas().get(0));
        OffsetUtil.offsetFunction(1000, (offset, limit) -> {
            com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(tenantId, 1000);
            SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
            queryTemplate.setLimit(limit);
            queryTemplate.setOffset(offset);
            queryTemplate.setNeedReturnCountNum(false);
            queryTemplate.setDataRolePermission(false);
            QueryBySearchTemplateResult result = objectDataService.queryBySearchTemplate(headerObj, EnterpriseRelationObjConstant.API_NAME, queryTemplate).getData();
            if (result.getQueryResult().getData() != null) {
                List<ObjectData> enterpriseList = result.getQueryResult().getData();
                for (ObjectData objectData : enterpriseList) {
                    EnterpriseRelationObj obj = EnterpriseRelationObj.newInstance(objectData);
                    if (StringUtils.isNotEmpty(obj.getEnterpriseAccount())) {
                        EnterpriseCardEntity enterpriseCardEntity = enterpriseCardEntityDaoRemoteManager.getByOutTenantId(obj.getOuterTenantId());
                        if (enterpriseCardEntity.getTenantId() == null) {
                            try {
                                if (arg.getExecute() != null && arg.getExecute()) {
                                    enterpriseCardEntityDaoRemoteManager.updateTenantIdByOuterTenantId(obj.getOuterTenantId(), obj.getEnterpriseAccount());
                                } else {
                                    log.info("fix:{}", enterpriseCardEntity);
                                }
                            } catch (Exception ex) {
                                log.warn("enterpriseCardEntityDaoRemoteManager.updateTenantIdByOuterTenantId error", ex);
                            }
                        }
                        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(tenantId + "", "-10000");
                        ListPublicEmployeeObjByConditionArg publicEmployeeObjByConditionArg = ListPublicEmployeeObjByConditionArg.newStartType();
                        publicEmployeeObjByConditionArg.setOuterTenantIds(Lists.newArrayList(obj.getOuterTenantId()));
                        publicEmployeeObjByConditionArg.setUseDataPermission(false);
                        publicEmployeeObjByConditionArg.setLimit(1000);
                        publicEmployeeObjByConditionArg.setOffset(0);
                        ListByConditionResult listByConditionResult = publicEmployeeObjService.listByCondition(serviceContext, publicEmployeeObjByConditionArg);
                        for (Map<String, Object> data : listByConditionResult.getObjectDatas()) {
                            PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(data);
                            EmployeeCardEntity employeeCardEntity = employeeCardEntityDaoRemoteManager.getByOuterId(publicEmployeeObj.getOuterUid());
                            if (employeeCardEntity.getEmployeeId() == null) {
                                try {
                                    if (arg.getExecute() != null && arg.getExecute()) {
                                        employeeCardEntityDaoRemoteManager.updateTenantIdEmployeeIdByOuterUid(publicEmployeeObj.getOuterUid(), publicEmployeeObj.getOuterTenantId(), obj.getEnterpriseAccount(), StringUtils.isNotEmpty(publicEmployeeObj.getEmployeeId()) ? Integer.parseInt(publicEmployeeObj.getEmployeeId()) : null);
                                    } else {
                                        log.info("fix:{}", employeeCardEntity);
                                    }
                                } catch (Exception ex) {
                                    log.warn("employeeCardEntityDaoRemoteManager.updateTenantIdEmployeeIdByOuterUid error", ex);
                                }
                            }
                        }
                    }
                }
            }
            return result.getQueryResult().getData().size();
        });
    }

    /**
     * 修复互联用户创建成功但是内部员工创建失败数据
     */
    @PostMapping("/fixPublicEmployeeCreatedSuccessButEmployeeCreateFail")
    public void fixPublicEmployeeCreatedSuccessButEmployeeCreateFail(@RequestBody ClearEnterpriseRelationAfterStopArg arg) {
        int tenantId = eieaConverter.enterpriseAccountToId(arg.getEas().get(0));
        int batchSize = arg.getBatchSize() != null ? arg.getBatchSize() : 1000;
        boolean isFixAllData = arg.getIsFixAll() != null && arg.getIsFixAll();
        OffsetUtil.offsetFunction(batchSize, (offset, limit) -> {
            com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(tenantId, 1000);
            SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
            queryTemplate.setLimit(limit);
            queryTemplate.setOffset(offset);
            queryTemplate.setNeedReturnCountNum(false);
            queryTemplate.setDataRolePermission(false);
            List<Filter> filters = Lists.newArrayList();
            // 过滤employee_id为空
            Filter filterEmployeeId = new Filter();
            filterEmployeeId.setFieldName("employee_id");
            filterEmployeeId.setOperator(FilterOperatorEnum.IS.getValue());
            filterEmployeeId.setFieldValues(Lists.newArrayList());
            filters.add(filterEmployeeId);
            if (!isFixAllData) {
                // 过滤”正常“状态
                Filter filterType = new Filter();
                filterType.setFieldName("type");
                filterType.setOperator(FilterOperatorEnum.EQ.getValue());
                filterType.setFieldValues(Lists.newArrayList("1"));
                filters.add(filterType);
            }
            queryTemplate.setFilters(filters);
            QueryBySearchTemplateResult result = objectDataService.queryBySearchTemplate(headerObj, PublicEmployeeObjConstant.API_NAME, queryTemplate).getData();
            if (result.getQueryResult() != null && result.getQueryResult().getData() != null) {
                List<ObjectData> publicEmployees = result.getQueryResult().getData();
                for (ObjectData data : publicEmployees) {
                    PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(data);
                    Integer employeeId = StringUtils.isNotEmpty(publicEmployeeObj.getEmployeeId()) ? Integer.parseInt(publicEmployeeObj.getEmployeeId()) : null;
                    if (employeeId == null) {
                        ObjectDataGetByIdResult enterpriseData = objectDataService.getById(headerObj, EnterpriseRelationObjConstant.API_NAME, publicEmployeeObj.getOuterTenantId() + "", false).getData();
                        if (enterpriseData != null && enterpriseData.getObjectData() != null) {
                            EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(enterpriseData.getObjectData());
                            String downEa = enterpriseRelationObj.getEnterpriseAccount();
                            if (StringUtils.isNotEmpty(downEa) && enterpriseRelationObj.getCrmOpenStatus().equals("2") && arg.getExecute() != null && arg.getExecute()) {
                                try {
                                    employeeId = publicEmployeeManager.createOrStartFsAccount(User.systemUser(publicEmployeeObj.getTenantId()), downEa, publicEmployeeObj.getOuterTenantId(), publicEmployeeObj.getOuterUid(), publicEmployeeObj.getMobile(), publicEmployeeObj.getLoginEmail(), publicEmployeeObj.getName(), null);
                                } catch (Exception ex) {
                                    log.warn("publicEmployeeManager.createOrStartFsAccount error", ex);
                                    continue;
                                }
                            }
                        }
                        log.info("fix-employeeId is empty! fixed outerUid={},outerTenantId={},tenantId={},employeeId={}", publicEmployeeObj.getOuterUid(), publicEmployeeObj.getOuterTenantId(), tenantId, employeeId);
                    }
                }
                return result.getQueryResult().getData().size();
            }
            return 0;
        });
    }

    /**
     * 对比fsAccount绑定表数据是否确实 如果有缺失进行修复
     */
    @PostMapping("/compareAndFixFsAccountData")
    public Result compareAndFixFsAccountData(@RequestBody ClearEnterpriseRelationAfterStopArg arg) {
        // ea映射表总数
        AtomicInteger enterpriseAssociationNum = new AtomicInteger(0);
        // 卡片表缺失数据数量
        AtomicInteger enterpriseCardMissNum = new AtomicInteger(0);
        // 卡片表需要update数据数量
        AtomicInteger enterpriseCardUpdateNum = new AtomicInteger(0);
        // 卡片表需要update数据数量
        AtomicInteger enterpriseObjUpdateNum = new AtomicInteger(0);

        OffsetUtil.offsetFunction(arg.getBatchSize(), (offset, limit) -> {
            List<FxiaokeEnterpriseAssociationEntity> enterpriseAssociationEntityList = fxiaokeEnterpriseAssociationEntityDaoRemoteManager.listAllAssociationByPage(offset / limit + 1, limit);
            if (enterpriseAssociationEntityList != null) {
                try {
                    // 对比卡片表数据并且修复
                    compareEnterpriseCardAndFixData(enterpriseCardMissNum, enterpriseCardUpdateNum, enterpriseAssociationEntityList, BooleanUtils.isTrue(arg.getExecute()));
                } catch (Exception e) {
                    log.warn("mongo move compareEnterpriseCardAndFixData异常", e);
                }
                try {
                    // 对比对象化表数据并且修复
                    compareEnterpriseObjAndFixData(enterpriseObjUpdateNum, enterpriseAssociationEntityList, BooleanUtils.isTrue(arg.getExecute()));
                } catch (Exception e) {
                    log.warn("mongo move compareEnterpriseObjAndFixData异常", e);
                }
            }
            int num = enterpriseAssociationEntityList == null ? 0 : enterpriseAssociationEntityList.size();
            enterpriseAssociationNum.addAndGet(num);
            return num;
        });
        log.info("mongo move 对比修复ea绑定表(总数={})、企业卡片表(缺失数据数={},修改数据数={})、互联企业对象数据完成(修改数据数={}),是否执行={}", enterpriseAssociationNum.get(), enterpriseCardMissNum.get(), enterpriseCardUpdateNum.get(), enterpriseObjUpdateNum.get(), BooleanUtils.isTrue(arg.getExecute()));

        // ea映射表总数
        AtomicInteger employeeAssociationNum = new AtomicInteger(0);
        // 卡片表缺失数据数量
        AtomicInteger employeeCardMissNum = new AtomicInteger(0);
        // 卡片表需要update数据数量
        AtomicInteger employeeCardUpdateNum = new AtomicInteger(0);
        // 卡片表需要update数据数量
        AtomicInteger employeeObjUpdateNum = new AtomicInteger(0);
        OffsetUtil.offsetFunction(arg.getBatchSize(), (offset, limit) -> {
            List<FxiaokeEmployeeAssociationEntity> employeeAssociationEntityList = fxiaokeEmployeeAssociationEntityDaoRemoteManager.listAllAssociationByPage(offset / limit + 1, limit);
            if (employeeAssociationEntityList != null) {
                try {
                    // 对比卡片表数据并且修复
                    compareEmployeeCardAndFixData(employeeCardMissNum, employeeCardUpdateNum, employeeAssociationEntityList, BooleanUtils.isTrue(arg.getExecute()));
                } catch (Exception e) {
                    log.warn("mongo move compareEnterpriseCardAndFixData异常", e);
                }
                try {
                    // 对比对象化表数据并且修复
                    comparePublicEmployeeObjAndFixData(employeeObjUpdateNum, employeeAssociationEntityList, BooleanUtils.isTrue(arg.getExecute()));
                } catch (Exception e) {
                    log.warn("mongo move compareEnterpriseObjAndFixData异常", e);
                }
            }
            int num = employeeAssociationEntityList == null ? 0 : employeeAssociationEntityList.size();
            employeeAssociationNum.addAndGet(num);
            return num;
        });
        log.info("mongo move 对比修复employee绑定表(总数={})、用户卡片表(缺失数据数={},修改数据数={})、互联用户对象数据完成(修改数据数={}),是否执行={}", employeeAssociationNum.get(), employeeCardMissNum.get(), employeeCardUpdateNum.get(), employeeObjUpdateNum.get(), BooleanUtils.isTrue(arg.getExecute()));
        return Result.newSuccess(false);
    }

    private void comparePublicEmployeeObjAndFixData(AtomicInteger employeeObjUpdateNum, List<FxiaokeEmployeeAssociationEntity> employeeAssociationEntityList, boolean execute) {
        for (FxiaokeEmployeeAssociationEntity fxiaokeEmployeeAssociationEntity : employeeAssociationEntityList) {
            Long outerTenantId = fxiaokeEmployeeAssociationEntity.getOuterTenantId();
            String ea = fxiaokeEmployeeAssociationEntity.getEa();
            Integer employeeId = fxiaokeEmployeeAssociationEntity.getEmployeeId();
            Long outerUid = fxiaokeEmployeeAssociationEntity.getOuterUid();
            DownstreamOuterUidArg downstreamOuterTenantIdArg = new DownstreamOuterUidArg();
            downstreamOuterTenantIdArg.setDownstreamOuterUid(outerUid);
            List<String> upstreamEas = globalService.listAllUpstreamEasByOuterUid(HeaderObj.newInstance(), downstreamOuterTenantIdArg).getData();

            if (upstreamEas != null && !upstreamEas.isEmpty()) {
                for (String upstreamEa : upstreamEas) {
                    int upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
                    ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(upstreamTenantId + "", "-10000");

                    // 创建互联用户
                    GetObjectDataArg getObjectDataArg = GetObjectDataArg.newIncludeInvalidArg(outerUid);
                    Map<String, Object> publicEmployeeObjectData = publicEmployeeObjService.getObjectData(serviceContext, getObjectDataArg).getObjectData();
                    if (publicEmployeeObjectData == null) {
                        continue;
                    }
                    PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(publicEmployeeObjectData);
                    if (StringUtils.isNotBlank(publicEmployeeObj.getEmployeeId()) && !publicEmployeeObj.getEmployeeId().equals(employeeId + "")) {
                        log.warn("mongo move 互联用户对象数据有employeeId数据但是与employeeId绑定表数据不一致,对象化={},Employee绑定表={}", publicEmployeeObj, fxiaokeEmployeeAssociationEntity);
                        continue;
                    }

                    if (StringUtils.isBlank(publicEmployeeObj.getEmployeeId())) {
                        employeeObjUpdateNum.getAndIncrement();
                        if (execute) {
                            EditActionArg arg = new EditActionArg();
                            Map<String, Object> objectData = new HashMap<>();
                            objectData.put(PublicEmployeeObjConstant.ID, outerUid + "");
                            objectData.put(PublicEmployeeObjConstant.EMPLOYEE_ID, employeeId);
                            arg.setObjectData(objectData);
                            arg.setIncrementUpdate(true);
                            publicEmployeeObjService.editAction(serviceContext, arg);
                        }
                    }
                }
            }
        }
    }

    private void compareEmployeeCardAndFixData(AtomicInteger employeeCardMissNum, AtomicInteger employeeCardUpdateNum, List<FxiaokeEmployeeAssociationEntity> employeeAssociationEntityList, boolean execute) {
        List<Long> outerUids = employeeAssociationEntityList.stream().map(FxiaokeEmployeeAssociationEntity::getOuterUid).collect(Collectors.toList());
        Map<Long, EmployeeCardEntity> outerUidEmployeeCardEntityMap = employeeCardEntityDaoRemoteManager.batchGetByOuterIds(outerUids).stream().collect(Collectors.toMap(EmployeeCardEntity::getOuterUid, Function.identity()));
        for (FxiaokeEmployeeAssociationEntity fxiaokeEmployeeAssociationEntity : employeeAssociationEntityList) {
            Long outerTenantId = fxiaokeEmployeeAssociationEntity.getOuterTenantId();
            String ea = fxiaokeEmployeeAssociationEntity.getEa();
            Long outerUid = fxiaokeEmployeeAssociationEntity.getOuterUid();
            Integer employeeId = fxiaokeEmployeeAssociationEntity.getEmployeeId();
            Integer tenantId = eieaConverter.enterpriseAccountToId(ea);

            EmployeeCardEntity employeeCardEntity = outerUidEmployeeCardEntityMap.get(outerUid);
            if (employeeCardEntity == null) {
                employeeCardMissNum.incrementAndGet();
                if (execute) {
                    // 新建卡片表数据
                    employeeCardEntityDaoRemoteManager.createCardByEaEmployeeIdAndOuterTenantIdOuterUid(outerUid, outerTenantId, ea, employeeId);
                }
                continue;
            }
            if (!tenantId.equals(employeeCardEntity.getTenantId()) || !employeeId.equals(employeeCardEntity.getEmployeeId())) {
                employeeCardUpdateNum.incrementAndGet();
                if (execute) {
                    // 修改卡片表数据
                    employeeCardEntityDaoRemoteManager.updateTenantIdEmployeeIdByOuterUid(outerUid, outerTenantId, ea, employeeId);
                }
            }
        }
    }

    private void compareEnterpriseObjAndFixData(AtomicInteger enterpriseObjUpdateNum, List<FxiaokeEnterpriseAssociationEntity> enterpriseAssociationEntityList, boolean execute) {
        for (FxiaokeEnterpriseAssociationEntity fxiaokeEnterpriseAssociationEntity : enterpriseAssociationEntityList) {
            Long outerTenantId = fxiaokeEnterpriseAssociationEntity.getOuterTenantId();
            String ea = fxiaokeEnterpriseAssociationEntity.getEa();
            DownstreamOuterTenantIdArg downstreamOuterTenantIdArg = new DownstreamOuterTenantIdArg();
            downstreamOuterTenantIdArg.setDownstreamOuterTenantId(outerTenantId);
            List<String> upstreamEas = globalService.listAllUpstreamEasByDownstreamOuterTenantId(HeaderObj.newInstance(), downstreamOuterTenantIdArg).getData();

            if (upstreamEas != null && !upstreamEas.isEmpty()) {
                for (String upstreamEa : upstreamEas) {
                    int upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
                    ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(upstreamTenantId + "", "-10000");

                    // 创建互联用户
                    Map<String, Object> enterpriseRelationObjectData = enterpriseRelationObjService.getObjectData(serviceContext, outerTenantId).getObjectData();
                    if (enterpriseRelationObjectData == null) {
                        continue;
                    }
                    EnterpriseRelationObj relationObj = EnterpriseRelationObj.newInstance(enterpriseRelationObjectData);
                    if (StringUtils.isNotBlank(relationObj.getEnterpriseAccount()) && !relationObj.getEnterpriseAccount().equals(ea)) {
                        log.warn("mongo move 互联企业对象数据有ea数据但是与ea绑定表数据不一致,对象化={},ea绑定表={}", relationObj, fxiaokeEnterpriseAssociationEntity);
                        continue;
                    }

                    if (StringUtils.isBlank(relationObj.getEnterpriseAccount())) {
                        enterpriseObjUpdateNum.getAndIncrement();
                        if (execute) {
                            Map<String, Object> objectDataMap = new HashMap<>(2);
                            objectDataMap.put(EnterpriseRelationObjConstant.ID, outerTenantId);
                            objectDataMap.put(EnterpriseRelationObjConstant.ENTERPRISE_ACCOUNT, ea);
                            Integer ei = eieaConverter.enterpriseAccountToId(ea);
                            objectDataMap.put(EnterpriseRelationObjConstant.ENTERPRISE_ID, ei);
                            EditActionArg arg = new EditActionArg();
                            arg.setIncrementUpdate(Boolean.TRUE);
                            arg.setObjectData(objectDataMap);
                            enterpriseRelationObjService.editAction(serviceContext, arg);
                        }
                    }
                }
            }
        }
    }

    private void compareEnterpriseCardAndFixData(AtomicInteger enterpriseCardMissNum, AtomicInteger enterpriseCardUpdateNum, List<FxiaokeEnterpriseAssociationEntity> enterpriseAssociationEntityList, boolean execute) {
        List<Long> enterpriseAssociationOuterTenantIds = enterpriseAssociationEntityList.stream().map(FxiaokeEnterpriseAssociationEntity::getOuterTenantId).collect(Collectors.toList());
        Map<Long, EnterpriseCardEntity> outerTenantIdEnterpriseCardEntityMap = enterpriseCardEntityDaoRemoteManager.batchGetNotCreate(enterpriseAssociationOuterTenantIds).stream().collect(Collectors.toMap(EnterpriseCardEntity::getOuterTenantId, Function.identity()));
        for (FxiaokeEnterpriseAssociationEntity fxiaokeEnterpriseAssociationEntity : enterpriseAssociationEntityList) {
            Long outerTenantId = fxiaokeEnterpriseAssociationEntity.getOuterTenantId();
            String ea = fxiaokeEnterpriseAssociationEntity.getEa();
            Integer tenantId = eieaConverter.enterpriseAccountToId(ea);

            EnterpriseCardEntity enterpriseCardEntity = outerTenantIdEnterpriseCardEntityMap.get(outerTenantId);
            if (enterpriseCardEntity == null) {
                enterpriseCardMissNum.incrementAndGet();
                if (execute) {
                    // 新建卡片表数据
                    enterpriseCardEntityDaoRemoteManager.createCardByEaAndOuterTenantId(ea, outerTenantId);
                }
                continue;
            }
            if (!tenantId.equals(enterpriseCardEntity.getTenantId())) {
                enterpriseCardUpdateNum.incrementAndGet();
                if (execute) {
                    // 修改卡片表数据
                    enterpriseCardEntityDaoRemoteManager.updateTenantIdByOuterTenantId(outerTenantId, ea);
                }
            }
        }
    }

    private List<String> listAllUpstreamEasUseLinkAppDao() {
        List<String> allUpstreamEas = Lists.newArrayList();
        OffsetUtil.offsetFunction(100000, (offset, limit) -> {
            List<String> upstreamEas = httpUpstreamService.listUpstreamEasByPage(offset, limit).getData();
            allUpstreamEas.addAll(upstreamEas);
            return upstreamEas.size();
        });
        return allUpstreamEas;
    }

    private List<String> listAllUpstreamEas() {
        List<String> allUpstreamEas = Lists.newArrayList();
        OffsetUtil.offsetFunction(1000, (offset, limit) -> {
            PageListArg pageListArg = new PageListArg();
            pageListArg.setOffset(offset);
            pageListArg.setLimit(limit);
            com.facishare.global.rest.result.Result<List<String>> upstreamEasResult = globalService.listAllUpstreamEas(HeaderObj.newInstance(), pageListArg);
            if (!upstreamEasResult.isSuccess()) {
                log.warn("listAllUpstreamEas failed! result:{}", upstreamEasResult);
                allUpstreamEas.clear();
                return 0;
            }
            List<String> upstreamEas = upstreamEasResult.getData();
            if (CollectionUtils.isEmpty(upstreamEas)) {
                return 0;
            }
            allUpstreamEas.addAll(upstreamEas);
            return upstreamEas.size();
        });
        return allUpstreamEas;
    }

    /**
     * 第一步：生成历史互联产品包订单数据升级文件（excel）
     *
     * @param historyOrderDataFileName 历史订单数据文件地址
     * @return 订单升级文件数据
     */
    @RequestMapping(value = "/EnterpriseLicense/upgradeOldSaleOrderData", method = {RequestMethod.GET, RequestMethod.POST})
    public Result<Boolean> upgradeOldSaleOrderData(@RequestParam("fileName") String historyOrderDataFileName, @RequestBody List<String> upstreamEas) {
        try {
            CommonPoolUtil.execute(() -> {
                enterpriseLicenseUpgradeService.upgradeOldSaleOrderData(historyOrderDataFileName, upstreamEas);
            });
        } catch (Exception e) {
            log.warn("upgradeOldSaleOrderData：{}", e);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), e.getMessage());
        }
        return Result.newSuccess(Boolean.TRUE);
    }

    /**
     * 为无互联产品包订单的企业升级产品包配额数据
     *
     * @param upstreamEas 需要升级新产品包的企业名单
     * @return 订单数据生成结果
     */
    @RequestMapping(value = "/EnterpriseLicense/upgradeEnterpriseLicenseOrderForNotHasOldOrderEa", method = RequestMethod.POST)
    public Result<Boolean> upgradeEnterpriseLicenseOrderForNotHasOldOrderEa(@RequestBody List<String> upstreamEas) {
        try {
            CommonPoolUtil.execute(() -> {
                enterpriseLicenseUpgradeService.upgradeEnterpriseLicenseOrderForNotHasOldOrderEa(upstreamEas);
            });
        } catch (Exception e) {
            log.warn("upgradeEnterpriseLicenseOrderForNotHasOldOrderEa：{}", e);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), e.getMessage());
        }
        return Result.newSuccess(Boolean.TRUE);
    }

    /**
     * 第二步：检查企业历史License数据升级结果
     */
    @RequestMapping(value = "/EnterpriseLicense/checkEnterpriseLicenceUpgradeData", method = RequestMethod.POST)
    public Result<Boolean> checkEnterpriseLicenceUpgradeData(@RequestBody List<String> upstreamEas) {
        try {
            CommonPoolUtil.execute(() -> {
                enterpriseLicenseUpgradeService.checkUpstreamEnterpriseOpenedLicenseQuotaData(upstreamEas);
            });
        } catch (Exception e) {
            log.warn("checkEnterpriseLicenceUpgradeData：{}", e);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), e.getMessage());
        }
        return Result.newSuccess(Boolean.TRUE);
    }

    /**
     * 第三步：生成白名单企业购买“下游精简版配额包【5人】”下游配额包订单数据
     */
    @RequestMapping(value = "/EnterpriseLicense/createOldWhiteListEnterpriseSalesOrderData", method = RequestMethod.GET)
    public Result<Boolean> createOldWhiteListEnterpriseSalesOrderData() {
        try {
            CommonPoolUtil.execute(() -> {
                enterpriseLicenseUpgradeService.createOldWhiteListEnterpriseSalesOrderData();
            });
        } catch (Exception e) {
            log.warn("createOldWhiteListEnterpriseSalesOrderData：{}", e);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), e.getMessage());
        }
        return Result.newSuccess(Boolean.TRUE);
    }

    /**
     * 第四步：生成白名单企业开通的有租户下游企业产品包“协同账号1年”升级订单数据
     */
    @RequestMapping(value = "/EnterpriseLicense/createOldWhiteListUpstreamOpenedDownstreamCrmOrderData", method = RequestMethod.GET)
    public Result<Boolean> createOldWhiteListUpstreamOpenedDownstreamCrmOrderData() {
        try {
            CommonPoolUtil.execute(() -> {
                enterpriseLicenseUpgradeService.createOldWhiteListUpstreamOpenedDownstreamCrmOrderData();
            });
        } catch (Exception e) {
            log.warn("createOldWhiteListUpstreamOpenedDownstreamCrmOrderData：{}", e);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), e.getMessage());
        }
        return Result.newSuccess(Boolean.TRUE);
    }

    /**
     * 第五步：刷所有白名单企业历史开通有租户下游记录(以前开通时未保存开通记录)
     */
    @RequestMapping(value = "/EnterpriseLicense/createOldWhiteListUpstreamOpenedDownstreamCrmRecord", method = RequestMethod.GET)
    public Result<Boolean> createOldWhiteListUpstreamOpenedDownstreamCrmRecord() {
        try {
            CommonPoolUtil.execute(() -> {
                enterpriseLicenseUpgradeService.createOldWhiteListUpstreamOpenedDownstreamCrmRecord();
            });
        } catch (Exception e) {
            log.warn("createOldWhiteListUpstreamOpenedDownstreamCrmRecord：{}", e);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), e.getMessage());
        }
        return Result.newSuccess(Boolean.TRUE);
    }

    /**
     * 手动给指定企业开通指定产品包license配额
     *
     * @param arg 开通参数
     */
    @RequestMapping(value = "/EnterpriseLicense/flushEnterpriseLicenseData", method = RequestMethod.POST)
    public Result<Boolean> flushEnterpriseLicenseData(@RequestBody FlushEnterpriseLicenseArg arg) {
        try {
            enterpriseLicenseUpgradeService.flushErLicenseQuotaData(arg.getProductCode(), arg.getEaList(), arg.getResourceNum());
        } catch (InterruptedException e) {
            log.warn("flushEnterpriseLicenseData：{}", e);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), e.getMessage());
        }
        return Result.newSuccess(Boolean.TRUE);
    }

    /**
     * 查询企业运行环境信息
     *
     * @param upstreamEas 企业账号列表
     * @return 企业运行环境信息
     */
    @RequestMapping(value = "/EnterpriseLicense/queryEnterpriseRunEnvInfo", method = RequestMethod.POST)
    public Result<Boolean> queryEnterpriseRunEnvInfo(@RequestBody List<String> upstreamEas) {
        try {
            enterpriseLicenseUpgradeService.queryEnterpriseRunEnvInfo(upstreamEas);
        } catch (Exception e) {
            log.warn("flushEnterpriseLicenseData：{}", e);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), e.getMessage());
        }
        return Result.newSuccess(Boolean.TRUE);
    }

    /**
     * 解除指定上游企业开通的所有下游有租户企业绑定的tenantId(新华三紫光云数据迁移使用)
     *
     * @param upstreamEa 上游企业账号
     * @return 返回结果
     */
    @RequestMapping(value = "/EnterpriseRelation/removeDownstreamEnterpriseRelationBindTenantId", method = RequestMethod.POST)
    public Result<Boolean> removeDownstreamEnterpriseRelationBindTenantId(@RequestParam("upstreamEa") String upstreamEa) {
        return Result.newSuccess(Boolean.TRUE);
    }

    /**
     * 导出指定上游企业开通的有租户下游企业开通的新旧TenantId映射关系
     *
     * @param upstreamEa 上游企业账号
     * @return 返回结果
     */
    @RequestMapping(value = "/EnterpriseRelation/exportDownstreamEnterpriseRelationTenantIdMaps", method = RequestMethod.POST)
    public Result<List<Map<Integer, Integer>>> exportDownstreamEnterpriseRelationTenantIdMaps(@RequestParam("upstreamEa") String upstreamEa) {
        return Result.newSuccess();
    }

    @RequestMapping(value = "/countOrder")
    public Result<List<Map<Integer, Integer>>> countOrder(@RequestParam("upstreamEas") String upstreamEas) {
        String[] upstreamEasArray = upstreamEas.split(",");
        enterpriseLicenseUpgradeService.countOrder(upstreamEasArray);
        return Result.newSuccess();
    }

    @Data
    public static class FlushEnterpriseLicenseArg implements Serializable {
        private String productCode;
        private List<String> eaList;
        private Integer resourceNum;
    }

    /**
     * 导出绑定了多个openId的互联账号
     */
    @PostMapping(value = "/listMultiOpenIdAccount")
    public Result<List<MultiOpenIdAccountData>> listMultiOpenIdAccount(@RequestBody ListMultiOpenIdAccountArg arg) {
        return wechatDataFlushService.listMultiOpenIdAccount(arg);
    }

    /**
     * 批量解绑openId
     */
    @PostMapping(value = "/batchUnBindOpenId")
    public Result<Void> batchUnBindOpenId(@RequestBody BatchUnBindOpenIdArg arg) {
        if (StringUtils.isEmpty(arg.getWxAppId()) || CollectionUtils.isEmpty(arg.getUpstreamWxAccountAssociationDatas())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (BooleanUtils.isFalse(arg.getExecute())) {
            return Result.newSuccess();
        }
        return wechatDataFlushService.batchUnBindOpenId(arg);
    }

    @PostMapping(value = "/migrateMongoDB")
    public Result<Void> migrateMongoDB(@RequestBody MigrateMongoDBArg arg) {
        //migrateMongoDBService.migrate(arg.getEas(), arg.getEntityNames(), arg.getIsMigrateAllEntities(), arg.getExec());
        return Result.newSuccess();
    }

    @PostMapping(value = "/flushEnterpriseItControl")
    public Result<Void> flushEnterpriseItControl(@RequestBody FlushEnterpriseItControlArg arg) {
        List<String> eas = arg.getEas();
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            eas = listAllUpstreamEasUseLinkAppDao();
        }
        if (CollectionUtils.isEmpty(eas)) {
            return Result.newResult(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.EA_IS_NULL.getDescription());
        }
        for (String upstreamEa : eas) {
            int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
            remoteManager.openCrmObjVisibleScopeWithGray(upstreamEa, Lists.newArrayList(ErDepartmentObjConstant.API_NAME)); //支持幂等操作
            recordTypeService.addCropRecordTypeForCrmManager(tenantId + "", ErDepartmentObjConstant.API_NAME);
            paasObjectDescribeManager.initCrmObjectDefineForEnterpriseItControl(upstreamEa);
        }
        return Result.newSuccess();
    }

    @PostMapping(value = "/initManageModeIsNullEnterpriseRelationObjs")
    public Result<Void> initEnterpriseRelationObjsManageModeIsNull(@RequestBody InitEnterpriseRelationObjsManageModeIsNullArg arg) {
        List<String> eas = arg.getEas();
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = listAllUpstreamEas();
        }
        if (CollectionUtils.isEmpty(eas)) {
            return Result.newSuccess();
        }
        for (String upstreamEa : eas) {
            int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
            ServiceContext serviceContext = enterpriseRelationObjServiceRemote.createServiceContext(tenantId + "", "-10000");
            Map<String, String> contextMap = PaasServiceContextSupport.serviceContext2HeaderMap(serviceContext);
            ListEnterpriseRelationObjByConditionArg conditionArg = new ListEnterpriseRelationObjByConditionArg();
            conditionArg.setHasFsAccount(BooleanUtils.isFalse(arg.getIncludeNoFsAccount()));
            List<Map<String, Object>> objectDatas = enterpriseRelationObjServiceRemote.listByCondition(contextMap, conditionArg).getObjectDatas();
            if (CollectionUtils.isEmpty(objectDatas)) {
                continue;
            }
            List<ObjectData> objectDatasToUpdate = Lists.newArrayList();
            List<EnterpriseRelationObj> enterpriseRelationObjs = EnterpriseRelationObj.newInstance(objectDatas);
            List<String> downstreamEas = enterpriseRelationObjs.stream().filter(x -> !StringUtils.isEmpty(x.getEnterpriseAccount())).map(EnterpriseRelationObj::getEnterpriseAccount).collect(Collectors.toList());
            List<EnterpriseMetaDataEntity> entities = enterpriseMetaDataEntityDaoRemoteManager.listMetasByAccounts(downstreamEas);
            if (CollectionUtils.isEmpty(entities)) {
                log.warn("initEnterpriseRelationObjsManageModeIsNull -> downstreamEas no meta data, downstreamEas={}", downstreamEas);
                continue;
            }
            // metaData表manageMode=null的情况应该是点过互联设置，然后导致metaData表有数据，
            // 其实企业本身没有买互联license(买了license会直接初始化manageMode)，
            // 所以他只能和上游通过邀请码建立互联关系。邀请码建立的互联关系都是企业代管
            Map<String, Integer> eaManageModeMap = entities.stream().collect(Collectors.toMap(EnterpriseMetaDataEntity::getEnterpriseAccount, x -> x.getManageMode() == null ? AccountManageMode.ASSIST_MANAGE.getType() : x.getManageMode()));
            for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationObjs) {
                if (enterpriseRelationObj.getManageMode() != null) {
                    continue;
                }
                if (StringUtils.isEmpty(enterpriseRelationObj.getEnterpriseAccount())) {
                    continue;
                }
                ObjectData objectData = new ObjectData();
                objectData.put(EnterpriseRelationObjConstant.ID, enterpriseRelationObj.getOuterTenantId() + "");
                objectData.put(EnterpriseRelationObjConstant.MANAGE_MODE, eaManageModeMap.get(enterpriseRelationObj.getEnterpriseAccount()));
                objectDatasToUpdate.add(objectData);
            }
            if (!CollectionUtils.isEmpty(objectDatasToUpdate) && BooleanUtils.isTrue(arg.getExec())) {
                com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(tenantId, -10000);
                objectDataService.batchIncrementUpdate(headerObj, EnterpriseRelationObjConstant.API_NAME, objectDatasToUpdate);
            }
        }
        return Result.newSuccess();
    }

    @PostMapping("/flushEnterpriseMetaData")
    public Result<Void> flushEnterpriseMetaData(@RequestBody EnterpriseDimensionsFlushDbArg arg) {
        List<String> upstreamNormalEas = arg.getEaList();
        for (String upstreamNormalEa : upstreamNormalEas) {
            enterpriseMetaDataEntityDaoRemoteManager.updateAddDataPrivilege(upstreamNormalEa, arg.getForceFlush());
        }
        return Result.newSuccess();
    }

    @PostMapping("/flushOuterRelativeTeamAndOwner")
    public Result<Void> flushOuterRelativeTeamAndOwner(@RequestBody EnterpriseDimensionsFlushDbArg arg) {
        String upstreamEa = arg.getEas();
        // 查出所有互联企业
        List<EnterpriseRelationObj> enterpriseRelationObjs = new ArrayList<>();
        List<PublicEmployeeObj> publicEmployeeObjList = new ArrayList<>();
        List<ObjectData> allNewEnterpriseRelationObj = queryAllBySearchTemplate(upstreamEa, EnterpriseRelationObjConstant.API_NAME, null);
        allNewEnterpriseRelationObj.forEach(e -> {
            EnterpriseRelationObj obj = EnterpriseRelationObj.newInstance(e);
            if (!obj.isDeleted()) {
                enterpriseRelationObjs.add(obj);
            }
        });
        List<ObjectData> allPublicEmployeeObj = queryAllBySearchTemplate(upstreamEa, PublicEmployeeObjConstant.API_NAME, null);
        allPublicEmployeeObj.forEach(e -> {
            PublicEmployeeObj obj = PublicEmployeeObj.newInstance(e);
            if (!obj.isDeleted()) {
                publicEmployeeObjList.add(obj);
            }
        });
        log.info("互联企业总数：[{}]，互联用户总数：[{}]", enterpriseRelationObjs.size(), publicEmployeeObjList.size());
        Map<Long, List<PublicEmployeeObj>> outerTenantIdWithEmployees = publicEmployeeObjList.stream().collect(Collectors.groupingBy(PublicEmployeeObj::getOuterTenantId));
        // 处理客户数据
        int successSize = 0;
        Set<String> set = new HashSet<>(enterpriseRelationObjs.size());
        for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationObjs) {
            String mapperAccountId = enterpriseRelationObj.getMapperAccountId();
            if (StringUtil.isAllBlank(mapperAccountId)) {
                continue;
            }
            com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(Integer.valueOf(enterpriseRelationObj.getTenantId()), SuperUserConstants.USER_ID);
            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdResult> accountResult = objectDataService.getById(headerObj, AccountFieldContants.API_NAME, mapperAccountId, Boolean.FALSE);
            if (!accountResult.isSuccess()) {
                log.info("企业[{}]的客户数据[{}]刷新失败", enterpriseRelationObj.getEnterpriseAccount(), mapperAccountId);
                continue;
            }
            ObjectDataGetByIdResult data = accountResult.getData();
            if (data == null || data.getObjectData() == null) {
                continue;
            }
            set.add(mapperAccountId);
            Long outerTenantId = enterpriseRelationObj.getOuterTenantId();
            List<PublicEmployeeObj> publicEmployeeObjs = outerTenantIdWithEmployees.get(outerTenantId);
            if (org.springframework.util.CollectionUtils.isEmpty(publicEmployeeObjs)) {
                continue;
            }
            ObjectData objectData = data.getObjectData();
            List<AddTeamMemberArg.OuterUser> accountOutUsers = new ArrayList<>(publicEmployeeObjs.size());
            Long outOwnerId = null;
            for (PublicEmployeeObj publicEmployeeObj : publicEmployeeObjs) {
                Boolean isRelationOwner = publicEmployeeObj.getRelationOwner();
                Object outOwner = objectData.get(AccountFieldContants.OUT_OWNER);
                if (isRelationOwner != null && isRelationOwner && (outOwner == null || ((List) outOwner).isEmpty())) {
                    outOwnerId = publicEmployeeObj.getOuterUid();
                } else {
                    AddTeamMemberArg.OuterUser outerUser = new OuterUser();
                    outerUser.setOutTenantId(String.valueOf(publicEmployeeObj.getOuterTenantId()));
                    outerUser.setUserId(publicEmployeeObj.getOuterUid().toString());
                    accountOutUsers.add(outerUser);
                }
            }
            changePartnerOwner(headerObj, AccountFieldContants.API_NAME, Arrays.asList(mapperAccountId), outOwnerId, String.valueOf(enterpriseRelationObj.getOuterTenantId()));
            addEnterpriseTeamMember(headerObj, AccountFieldContants.API_NAME, Arrays.asList(mapperAccountId), outerTenantId);
            successSize++;
        }
        log.info("客户数据共有[{}]条，其中[{}]条处理成功", set.size(), successSize);
        // 处理合作伙伴数据
        successSize = 0;
        set = new HashSet<>(enterpriseRelationObjs.size());
        for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationObjs) {
            String mapperPartnerId = enterpriseRelationObj.getMapperPartnerId();
            if (StringUtil.isAllBlank(mapperPartnerId)) {
                continue;
            }
            com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(Integer.valueOf(enterpriseRelationObj.getTenantId()), SuperUserConstants.USER_ID);
            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdResult> partnerResult = objectDataService.getById(headerObj, PartnerFieldContants.API_NAME, mapperPartnerId, Boolean.FALSE);
            if (!partnerResult.isSuccess()) {
                log.info("企业[{}}]的以下合作伙伴数据[{}}]刷新失败", enterpriseRelationObj.getEnterpriseAccount(), mapperPartnerId);
                continue;
            }
            ObjectDataGetByIdResult data = partnerResult.getData();
            if (data == null || data.getObjectData() == null) {
                continue;
            }
            set.add(mapperPartnerId);
            Long outerTenantId = enterpriseRelationObj.getOuterTenantId();
            List<PublicEmployeeObj> publicEmployeeObjs = outerTenantIdWithEmployees.get(outerTenantId);
            if (org.springframework.util.CollectionUtils.isEmpty(publicEmployeeObjs)) {
                continue;
            }
            ObjectData objectData = data.getObjectData();
            List<AddTeamMemberArg.OuterUser> partnerOutUsers = new ArrayList<>(publicEmployeeObjs.size());
            Long outOwnerId = null;
            for (PublicEmployeeObj publicEmployeeObj : publicEmployeeObjs) {
                Boolean isRelationOwner = publicEmployeeObj.getRelationOwner();
                Object outOwner = objectData.get(AccountFieldContants.OUT_OWNER);
                if (isRelationOwner != null && isRelationOwner && (outOwner == null || ((List) outOwner).isEmpty())) {
                    outOwnerId = publicEmployeeObj.getOuterUid();
                } else {
                    AddTeamMemberArg.OuterUser outerUser = new OuterUser();
                    outerUser.setOutTenantId(String.valueOf(publicEmployeeObj.getOuterTenantId()));
                    outerUser.setUserId(publicEmployeeObj.getOuterUid().toString());
                    partnerOutUsers.add(outerUser);
                }
            }
            changePartnerOwner(headerObj, PartnerFieldContants.API_NAME, Arrays.asList(mapperPartnerId), outOwnerId, String.valueOf(enterpriseRelationObj.getOuterTenantId()));
            addEnterpriseTeamMember(headerObj, PartnerFieldContants.API_NAME, Arrays.asList(mapperPartnerId), outerTenantId);
            successSize++;
        }
        log.info("合作伙伴共有[{}]条，其中[{}]条处理成功", set.size(), successSize);
        // 处理联系人数据
        set = new HashSet<>(publicEmployeeObjList.size());
        successSize = 0;
        for (PublicEmployeeObj publicEmployeeObj : publicEmployeeObjList) {
            String contractId = publicEmployeeObj.getContractId();
            if (StringUtil.isBlank(contractId)) {
                continue;
            }
            com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(Integer.valueOf(publicEmployeeObj.getTenantId()), SuperUserConstants.USER_ID);
            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdResult> contactResult = objectDataService.getById(headerObj, ContactFieldContants.API_NAME, contractId, Boolean.FALSE);
            if (!contactResult.isSuccess()) {
                log.info("互联企业的[{}]互联用户[{}}]，[{}}]的以下合作伙伴数据[{}]刷新失败", publicEmployeeObj.getOuterTenantId(), publicEmployeeObj.getId(), publicEmployeeObj.getName(), contractId);
                continue;
            }
            ObjectDataGetByIdResult data = contactResult.getData();
            if (data == null || data.getObjectData() == null) {
                continue;
            }
            set.add(contractId);
            ObjectData contactData = data.getObjectData();
            Object outOwner = contactData.get(ContactFieldContants.OUT_OWNER);
            Boolean isRelationOwner = publicEmployeeObj.getRelationOwner();
            if (isRelationOwner != null && isRelationOwner && (outOwner == null || ((List) outOwner).isEmpty())) {
                changePartnerOwner(headerObj, ContactFieldContants.API_NAME, Arrays.asList(contractId), publicEmployeeObj.getOuterUid(), String.valueOf(publicEmployeeObj.getOuterTenantId()));
            } else {
                AddTeamMemberArg.OuterUser outerUser = new OuterUser();
                outerUser.setUserId(publicEmployeeObj.getOuterUid().toString());
                outerUser.setOutTenantId(String.valueOf(publicEmployeeObj.getOuterTenantId()));
                addTeamMember(headerObj, ContactFieldContants.API_NAME, Arrays.asList(contractId), Arrays.asList(outerUser), "0");
            }
            successSize++;
        }
        log.info("联系人共有[{}]条，其中[{}]条处理成功", set.size(), successSize);
        return Result.newSuccess();
    }

    public List<ObjectData> queryAllBySearchTemplate(String upstreamEa, String apiName, String id) {
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(eieaConverter.enterpriseAccountToId(upstreamEa), -10000);
        List<ObjectData> allObjectData = new ArrayList<>();
        SearchTemplateQuery query = new SearchTemplateQuery();
        Filter filter = new Filter();
        filter.setFieldName(DBRecord.IS_DELETED);
        filter.setOperator(OperatorContants.IN);
        List<String> deleteStatusValues = Arrays.stream(DELETE_STATUS.values()).map(x -> String.valueOf(x.getValue())).collect(Collectors.toList());
        filter.setFieldValues(deleteStatusValues);
        query.getFilters().add(filter);

        if (StringUtils.isNotBlank(id)) {
            Filter idFilter = new Filter();
            idFilter.setFieldName(DBRecord.ID);
            idFilter.setOperator(OperatorContants.EQ);
            idFilter.setFieldValues(Lists.newArrayList(id));
            query.getFilters().add(idFilter);
        }

        OffsetUtil.offsetFunction(1000, (offset, limit) -> {
            query.setOffset(offset);
            query.setLimit(limit);
            query.setPermissionType(0);
            com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> searchTemplateResult = objectDataService.queryBySearchTemplate(headerObj, apiName, query);
            List<ObjectData> objectDatas = searchTemplateResult.getData().getQueryResult().getData();
            allObjectData.addAll(objectDatas);
            return objectDatas.size();
        });
        return allObjectData;
    }

    private void addEnterpriseTeamMember(com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj, String apiName, List<String> dataIds, Long outerTenantId) {
        if (outerTenantId == null) {
            return;
        }
        AddTeamMemberNewArg.TeamMemberInfos teamMemberInfos = new TeamMemberInfos();
        List<AddTeamMemberNewArg.OuterUser> outTeamMemberEmployees = new ArrayList<>();
        AddTeamMemberNewArg.OuterUser outerUser = new AddTeamMemberNewArg.OuterUser();
        outerUser.setOutTenantId(outerTenantId + "");
        outerUser.setUserId(outerTenantId + "");
        outTeamMemberEmployees.add(outerUser);
        // 普通成员
        teamMemberInfos.setTeamMemberRoleList(Lists.newArrayList("4"));
        // 读写
        teamMemberInfos.setTeamMemberPermissionType("1");
        teamMemberInfos.setOutTeamMemberEmployee(outTeamMemberEmployees);
        teamMemberInfos.setTeamMemberType("5");
        AddTeamMemberNewArg arg = new AddTeamMemberNewArg();
        arg.setDataIDs(dataIds);
        arg.setTeamMemberInfos(Lists.newArrayList(teamMemberInfos));
        try {
            metadataActionService.addTeamMemberNew(headerObj, apiName, arg);
        } catch (Exception e) {
            log.warn("", e);
        }
    }

    private void addTeamMember(com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj, String apiName, List<String> dataIds, List<OuterUser> outTeamMemberEmployee, String teamMemberType) {
        if (org.springframework.util.CollectionUtils.isEmpty(outTeamMemberEmployee)) {
            return;
        }
        OffsetUtil.splitFunction(outTeamMemberEmployee, 100, (list) -> {
            AddTeamMemberArg arg = new AddTeamMemberArg();
            // 普通成员
            arg.setTeamMemberRoleList(Lists.newArrayList("4"));
            // 读写
            arg.setTeamMemberPermissionType("2");
            arg.setDataIDs(dataIds);
            arg.setOutTeamMemberEmployee(list);
            arg.setIgnoreSendingRemind(true);
            try {
                metadataActionService.addTeamMember(headerObj, apiName, arg);
            } catch (Exception e) {
                log.warn("", e);
            }
            return dataIds;
        });
    }

    private void changePartnerOwner(com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj, String apiName, List<String> objectId, Long outUid, String outTenantId) {
        if (outUid == null) {
            log.info("企业[{}]传进来的负责人为null", outTenantId);
            return;
        }
        ChangePartnerOwnerArg arg = new ChangePartnerOwnerArg();
        arg.setDataIds(objectId);
        arg.setOwnerId(String.valueOf(outUid));
        arg.setOutTenantId(outTenantId);
        // 原负责人变成普通成员 “2”；移除相关团队“1”
        arg.setOldOwnerStrategy("2");
        // 团队成员的权限，1只读，2读写
        arg.setOldOwnerTeamMemberPermissionType("1");
        try {
            metadataActionService.changePartnerOwner(headerObj, apiName, arg);
        } catch (Exception e) {
            log.warn("", e);
        }
    }

    @PostMapping(value = "/flushBusinessOptimzeCrmData")
    public Result<List<String>> flushBusinessOptimzeCrmData(@RequestBody FlushBusinessOptimzeCrmDataArg arg) {
        List<String> errEas = new ArrayList<>();
        List<String> eas = arg.getEas();
        if (CollectionUtils.isEmpty(eas)) {
            return Result.newResult(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.EA_IS_NULL.getDescription());
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            eas = listAllUpstreamEas();
        }
        List<String> exEas = new ArrayList<>();
        List<String> noFieldEas = new ArrayList<>();
        for (String upstreamEa : eas) {
            // 判断联系人描述是会否有互联用户id字段
            boolean hasField = hasPublicEployeeIdField(upstreamEa, exEas, noFieldEas);
            if (BooleanUtils.isTrue(arg.getExec()) && !hasField) {
                Result<Void> voidResult = doFlushBusinessOptimzeCrmData(upstreamEa);
                if (!voidResult.isSuccess()) {
                    errEas.add(upstreamEa);
                }
            }
        }
        log.warn("flushBusinessOptimzeCrmData 停用或者描述不存在ea={}", exEas);
        log.warn("flushBusinessOptimzeCrmData 没有字段eea={}", noFieldEas);
        log.warn("flushBusinessOptimzeCrmData 化数据不成功 ea={}", errEas);
        log.warn("flushBusinessOptimzeCrmData ea size={}", eas.size());
        if (errEas.isEmpty()) {
            return Result.newSuccess();
        } else {
            return Result.newSuccess(errEas);
        }
    }

    @PostMapping(value = "/checkErBusinessFieldExists")
    public Result<List<Map<String, Object>>> checkErBusinessFieldExists(@RequestBody BaseFlushArg arg) {
        List<String> eas = arg.getEas();
        if (!BooleanUtils.isTrue(arg.getIsInitAll()) && CollectionUtils.isEmpty(eas)) {
            return Result.newResult(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.EA_IS_NULL.getDescription());
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            eas = listAllUpstreamEas();
        }
        List<Map<String, Object>> resMapList = Lists.newArrayList();
        for (String ea : eas) {
            try {
                Map<String, Object> resMap = null;

                int tenantId = eieaConverter.enterpriseAccountToId(ea);
                Set<String> checkApiNameSet = Sets.newHashSet(AccountFieldContants.API_NAME, ContactFieldContants.API_NAME, PartnerFieldContants.API_NAME);
                for (AddDescribeFieldData addDescribeFieldData : PaasObjectDescribeManager.businessOperatorObjectAddFieldsMap.get(PaasObjectDescribeManager.initBusiness)) {
                    if (!checkApiNameSet.contains(addDescribeFieldData.getDescribeApiName())) {
                        continue;
                    }
                    if (PartnerFieldContants.API_NAME.equals(addDescribeFieldData.getDescribeApiName())) {
                        Boolean openPartner = remoteManager.partnerStatusIsOpen(ea);
                        if (!Boolean.TRUE.equals(openPartner)) {
                            continue;
                        }
                    }
                    boolean hasExitsFieldForCrmObject = paasObjectDescribeManager.hasExitsFieldForCrmObject(tenantId, addDescribeFieldData.getDescribeApiName(), addDescribeFieldData.getFieldApiName());
                    if (!hasExitsFieldForCrmObject || Boolean.TRUE.equals(addDescribeFieldData.getHasUpdateField())) {
                        if (resMap == null) {
                            resMap = Maps.newHashMap();
                            resMap.put("upstreamEa", ea);
                        }
                        Map<String, Object> resMap2 = Maps.newHashMap();
                        resMap2.put("api_name", addDescribeFieldData.getDescribeApiName());
                        resMap2.put("missing_field", addDescribeFieldData.getFieldApiName());
                        resMap.put("check_result", resMap2);
                    }
                }
                if (resMap != null) {
                    resMapList.add(resMap);
                }
            } catch (Exception e) {
                log.warn("checkErBusinessFieldExists error, ea={}", ea, e);
            }
        }
        return Result.newSuccess(resMapList);
    }

    @PostMapping(value = "/initErObjectDescribe")
    public Result<List<String>> initErObjectDescribe(@RequestBody BaseFlushArg arg) {
        List<String> errEas = new ArrayList<>();
        List<String> eas = arg.getEas();
        if (CollectionUtils.isEmpty(eas)) {
            return Result.newResult(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.EA_IS_NULL.getDescription());
        }
        BatchGetEnterpriseRunStatusArg batchGetEnterpriseRunStatusArg = new BatchGetEnterpriseRunStatusArg();
        batchGetEnterpriseRunStatusArg.setEnterpriseAccounts(eas);
        BatchGetEnterpriseRunStatusResult batchGetEnterpriseRunStatusResult = enterpriseEditionService.batchGetEnterpriseRunStatus(batchGetEnterpriseRunStatusArg);
        Map<String, Integer> eaRunStatusMap = batchGetEnterpriseRunStatusResult.getEaRunStatus();
        if (MapUtils.isEmpty(eaRunStatusMap)) {
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), ResultCode.FIND_ENTERPRISE_STATUS_FAIL.getDescription());
        }
        for (String upstreamEa : eas) {
            // 企业是否停用
            Integer runtStatus = eaRunStatusMap.get(upstreamEa);
            if (runtStatus == null || runtStatus != RunStatus.RUN_STATUS_NORMAL) {
                log.info("initErObjectDescribe fail, ea={} run status={}", upstreamEa, runtStatus);
                errEas.add(upstreamEa);
                continue;
            }
            // 是否开通互联企业对象
            Boolean quotaEverExists = enterpriseMetaDataManager.validateQuotaEverExist(upstreamEa);
            if (!quotaEverExists) {
                log.info("initErObjectDescribe fail, ea={} quotaEverExists={}", upstreamEa, quotaEverExists);
                errEas.add(upstreamEa);
                continue;
            }
            if (BooleanUtils.isTrue(arg.getExec())) {
                try {
                    int upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
                    ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(upstreamEi + "", "-10000");
                    com.fxiaoke.enterpriserelation.objrest.result.Result result = enterpriseRelationObjService.initErObjectDescribe(serviceContext, false);
                    if (!result.isSuccess()) {
                        errEas.add(upstreamEa);
                    }
                } catch (Exception e) {
                    errEas.add(upstreamEa);
                    log.warn("initErObjectDescribe error, upstreamEa={}", upstreamEa, e);
                }
            }
        }
        log.warn("initErObjectDescribe 刷数据不成功 ea={}", errEas);
        log.warn("initErObjectDescribe ea size={}", eas.size());
        return Result.newSuccess(errEas);
    }

    @PostMapping(value = "/flushPublicEmployeeObjEmployeeId")
    public Result<List<String>> flushPublicEmployeeObjEmployeeId(@RequestBody BaseFlushArg arg) {
        List<String> errEas = new ArrayList<>();
        List<String> eas = arg.getEas();
        if (CollectionUtils.isEmpty(eas)) {
            return Result.newResult(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.EA_IS_NULL.getDescription());
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            eas = listAllUpstreamEas();
        }
        List<String> exEas = new ArrayList<>();
        List<String> eaNotValid = new ArrayList<>();
        for (String upstreamEa : eas) {
            int ei;
            try {
                ei = eieaConverter.enterpriseAccountToId(upstreamEa);
            } catch (Exception e) {
                eaNotValid.add(upstreamEa);
                continue;
            }
            // 判断联系人描述是会否有互联用户id字段
            ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(ei + "", SuperUserConstants.USER_ID + "");
            OffsetUtil.offsetFunction(50, (offset, limit) -> {
                ListPublicEmployeeObjByConditionArg listByConditionArg = new ListPublicEmployeeObjByConditionArg();
                listByConditionArg.setIncludeInvalid(false);
                listByConditionArg.setHasFsAccount(true);
                listByConditionArg.setUseDataPermission(false);
                listByConditionArg.setIdentityType(IdentityTypeEnum.CORP.getType());
                listByConditionArg.setOffset(offset);
                listByConditionArg.setLimit(limit);

                try {
                    List<Map<String, Object>> objectDatas = publicEmployeeObjService.listByCondition(serviceContext, listByConditionArg).getObjectDatas();
                    if (CollectionUtils.isEmpty(objectDatas)) {
                        return 0;
                    }
                    FixPublicEmployeeObjEmployeeIdDataArg fixPublicEmployeeObjEmployeeIdDataArg = new FixPublicEmployeeObjEmployeeIdDataArg();
                    fixPublicEmployeeObjEmployeeIdDataArg.setObjectDatas(objectDatas);
                    com.fxiaoke.enterpriserelation.objrest.result.Result result = publicEmployeeObjService.fixPublicEmployeeObjEmployeeIdData(serviceContext, fixPublicEmployeeObjEmployeeIdDataArg);
                    if (!result.isSuccess()) {
                        errEas.add(upstreamEa);
                    }
                    return objectDatas.size();
                } catch (Exception e) {
                    // 调用对象化接口失败
                    exEas.add(upstreamEa);
                    log.warn("调用对象化接口publicEmployeeObjService.listByCondition失败", e);
                    return 0;
                }
            });
        }
        log.warn("flushBusinessOptimzeCrmData exEas={}", exEas);
        log.warn("flushBusinessOptimzeCrmData eaNotValid={}", eaNotValid);
        log.warn("flushBusinessOptimzeCrmData errEas ea={}", errEas);
        log.warn("flushBusinessOptimzeCrmData eas size={}", eas.size());

        if (errEas.isEmpty()) {
            return Result.newSuccess();
        } else {
            return Result.newSuccess(errEas);
        }
    }

    @PostMapping(value = "/flushUpstreamPublicEmployee")
    public Result<Object> flushUpstreamPublicEmployee(@RequestBody BaseFlushArg arg) {
        List<String> eas = arg.getEas();
        if (CollectionUtils.isEmpty(eas)) {
            return Result.newResult(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.EA_IS_NULL.getDescription());
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            eas = listAllUpstreamEas();
        }
        List<String> eaNotValid = new ArrayList<>();
        List<String> errorEas = new ArrayList<>();
        List<String> exEas = new ArrayList<>();
        for (String upstreamEa : eas) {
            int ei;
            try {
                ei = eieaConverter.enterpriseAccountToId(upstreamEa);
            } catch (Exception e) {
                eaNotValid.add(upstreamEa);
                continue;
            }
            // 只需要对比有租户的相关团队
            OffsetUtil.offsetFunction(500, (offset, limit) -> {
                ListEnterpriseRelationObjByConditionArg listEnterpriseRelationObjByConditionArg = new ListEnterpriseRelationObjByConditionArg();
                listEnterpriseRelationObjByConditionArg.setUseDataPermission(false);
                listEnterpriseRelationObjByConditionArg.setHasFsAccount(true);
                listEnterpriseRelationObjByConditionArg.setLimit(limit);
                listEnterpriseRelationObjByConditionArg.setOffset(offset);

                ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(ei + "", SuperUserConstants.USER_ID + "");
                try {
                    ListByConditionResult listByConditionResult = enterpriseRelationObjService.listByCondition(serviceContext, listEnterpriseRelationObjByConditionArg);
                    List<EnterpriseRelationObj> objList = EnterpriseRelationObj.newInstance(listByConditionResult.getObjectDatas());
                    List<Long> outerTenantIds = objList.stream().map(EnterpriseRelationObj::getOuterTenantId).collect(Collectors.toList());

                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(outerTenantIds)) {
                        ListUpstreamPublicEmployeesByPageArg listAllUpstreamPublicEmployeesArg = new ListUpstreamPublicEmployeesByPageArg();
                        listAllUpstreamPublicEmployeesArg.setDownstreamOuterTenantIds(outerTenantIds);
                        int queryLimit = 10000;
                        listAllUpstreamPublicEmployeesArg.setOffset(0);
                        listAllUpstreamPublicEmployeesArg.setLimit(queryLimit);

                        List<UpstreamPublicEmployeeData> upstreamPublicEmployeeDataList = enterpriseRelationObjService.listUpstreamPublicEmployeesByPage(serviceContext, listAllUpstreamPublicEmployeesArg).getDatas();
                        if (!CollectionUtils.isEmpty(upstreamPublicEmployeeDataList)) {
                            if (upstreamPublicEmployeeDataList.size() >= queryLimit) {
                                log.warn("enterpriseRelationObjService.listUpstreamPublicEmployeesByPage 获取的相关团队达到最大数量1000 可能数据没有查找完全 ea={},outerTeanntIds={}", upstreamEa, outerTenantIds);
                            }

                            // 根据downstreamOuterTenantId进行分组 插入数据
                            Map<Long, List<UpstreamPublicEmployeeData>> downstreamOuterTenantIdUpstreamPublicEmployeesMap = upstreamPublicEmployeeDataList.stream().collect(Collectors.groupingBy(UpstreamPublicEmployeeData::getDownstreamOuterTenantId, Collectors.mapping(Function.identity(), Collectors.toList())));

                            for (Entry<Long, List<UpstreamPublicEmployeeData>> entry : downstreamOuterTenantIdUpstreamPublicEmployeesMap.entrySet()) {
                                List<Integer> employeeIds = entry.getValue().stream().map(UpstreamPublicEmployeeData::getUpstreamEmployeeId).distinct().collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(employeeIds)) {
                                    continue;
                                }
                                BatchUpsertUpstreamPublicEmployeeArg batchUpsertUpstreamPublicEmployeeArg = new BatchUpsertUpstreamPublicEmployeeArg();
                                batchUpsertUpstreamPublicEmployeeArg.setUpstreamTenantId(ei);
                                batchUpsertUpstreamPublicEmployeeArg.setUpstreamUserIds(employeeIds);
                                batchUpsertUpstreamPublicEmployeeArg.setDownstreamOuterTenantId(entry.getKey());
                                batchUpsertUpstreamPublicEmployeeArg.setType(UpstreamPublicTypeContant.NORMAL_TYPE);
                                if (BooleanUtils.isTrue(arg.getExec())) {
                                    com.facishare.global.rest.result.Result result = globalService.batchUpsertUpstreamPublicEmployee(HeaderObj.newInstance(), batchUpsertUpstreamPublicEmployeeArg);
                                    if (!result.isSuccess()) {
                                        errorEas.add(upstreamEa);
                                        log.warn("globalService.batchUpsertUpstreamPublicEmployee error res={}", result);
                                        return 0;
                                    }
                                }
                            }
                        }
                    }
                    return objList.size();
                } catch (Exception e) {
                    log.warn("接口调用异常 跳过企业 ea={}", upstreamEa, e);
                    exEas.add(upstreamEa);
                    return 0;
                }
            });
        }
        log.warn("flushUpstreamPublicEmployee eaNotValid={}", eaNotValid);
        log.warn("flushUpstreamPublicEmployee errorEas={}", errorEas);
        log.warn("flushUpstreamPublicEmployee exEas={}", exEas);

        if (errorEas.isEmpty() && exEas.isEmpty()) {
            return Result.newSuccess();
        } else {
            errorEas.addAll(exEas);
            return Result.newSuccess(errorEas);
        }
    }

    @PostMapping(value = "/compareAndFixUpstreamPublicEmployee")
    public Result<Object> compareAndFixUpstreamPublicEmployee(@RequestBody BaseFlushArg arg) {
        List<String> eas = arg.getEas();
        if (CollectionUtils.isEmpty(eas)) {
            return Result.newResult(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.EA_IS_NULL.getDescription());
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            eas = listAllUpstreamEas();
        }
        List<String> eaNotValid = new ArrayList<>();
        List<String> errorEas = new ArrayList<>();
        List<String> exEas = new ArrayList<>();
        for (String upstreamEa : eas) {
            AtomicInteger oldNum = new AtomicInteger(0);
            AtomicInteger newNum = new AtomicInteger(0);
            int ei;
            try {
                ei = eieaConverter.enterpriseAccountToId(upstreamEa);
            } catch (Exception e) {
                eaNotValid.add(upstreamEa);
                continue;
            }
            // 只需要对比有租户的相关团队
            OffsetUtil.offsetFunction(500, (offset, limit) -> {
                ListEnterpriseRelationObjByConditionArg listEnterpriseRelationObjByConditionArg = new ListEnterpriseRelationObjByConditionArg();
                listEnterpriseRelationObjByConditionArg.setUseDataPermission(false);
                listEnterpriseRelationObjByConditionArg.setHasFsAccount(true);
                listEnterpriseRelationObjByConditionArg.setLimit(limit);
                listEnterpriseRelationObjByConditionArg.setOffset(offset);

                ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(ei + "", SuperUserConstants.USER_ID + "");
                try {
                    ListByConditionResult listByConditionResult = enterpriseRelationObjService.listByCondition(serviceContext, listEnterpriseRelationObjByConditionArg);
                    List<EnterpriseRelationObj> objList = EnterpriseRelationObj.newInstance(listByConditionResult.getObjectDatas());
                    List<Long> outerTenantIds = objList.stream().map(EnterpriseRelationObj::getOuterTenantId).collect(Collectors.toList());

                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(outerTenantIds)) {
                        ListUpstreamPublicEmployeesByPageArg listAllUpstreamPublicEmployeesArg = new ListUpstreamPublicEmployeesByPageArg();
                        listAllUpstreamPublicEmployeesArg.setDownstreamOuterTenantIds(outerTenantIds);
                        int queryLimit = 10000;
                        listAllUpstreamPublicEmployeesArg.setOffset(0);
                        listAllUpstreamPublicEmployeesArg.setLimit(queryLimit);

                        List<UpstreamPublicEmployeeData> oldUpstreamPublicEmployeeDataList = enterpriseRelationObjService.listUpstreamPublicEmployeesByPage(serviceContext, listAllUpstreamPublicEmployeesArg).getDatas();
                        if (oldUpstreamPublicEmployeeDataList.size() >= queryLimit) {
                            log.warn("enterpriseRelationObjService.listUpstreamPublicEmployeesByPage 获取的相关团队达到最大数量1000 可能数据没有查找完全 ea={},outerTeanntIds={}", upstreamEa, outerTenantIds);
                        }
                        oldNum.addAndGet(oldUpstreamPublicEmployeeDataList.size());
                        List<UpstreamPublicEmployeeVos> newUpstreamPublicEmployeeVos = new ArrayList<>();
                        OffsetUtil.offsetFunction(500, (globalOffset, globalLimit) -> {
                            ListUpstreamPublicEmployeesArg listUpstreamPublicEmployeesArg = new ListUpstreamPublicEmployeesArg();
                            listUpstreamPublicEmployeesArg.setPageSize(globalLimit);
                            listUpstreamPublicEmployeesArg.setPageNumber((globalOffset / globalLimit) + 1);
                            listUpstreamPublicEmployeesArg.setSearchText(null);
                            listUpstreamPublicEmployeesArg.setUpstreamTenantId(ei);
                            listUpstreamPublicEmployeesArg.setDownstreamOuterTenantIds(outerTenantIds);

                            com.facishare.global.rest.result.Result<ListUpstreamPublicEmployeesResult> listUpstreamPublicEmployeesResultResult = globalService.listUpstreamPublicEmployees(HeaderObj.newInstance(ei), listUpstreamPublicEmployeesArg);
                            if (!listUpstreamPublicEmployeesResultResult.isSuccess()) {
                                errorEas.add(upstreamEa);
                                log.warn("globalService.listUpstreamPublicEmployees res={}", listUpstreamPublicEmployeesResultResult);
                                return 0;
                            }
                            newUpstreamPublicEmployeeVos.addAll(listUpstreamPublicEmployeesResultResult.getData().getUpstreamPublicEmployeeVos());
                            return listUpstreamPublicEmployeesResultResult.getData().getUpstreamPublicEmployeeVos().size();
                        });
                        newNum.addAndGet(newUpstreamPublicEmployeeVos.size());
                        Boolean success = doCompareAndFixUpstreamPublicEmployee(oldUpstreamPublicEmployeeDataList, newUpstreamPublicEmployeeVos, ei, arg);
                        if (!success) {
                            errorEas.add(upstreamEa);
                            return 0;
                        }
                    }
                    return objList.size();
                } catch (Exception e) {
                    log.warn("接口调用异常 跳过企业 ea={}", upstreamEa, e);
                    exEas.add(upstreamEa);
                    return 0;
                }
            });
            log.warn("compareAndFixUpstreamPublicEmployee 对比数据 ei={},ea={},oldNum={},newNum={}", ei, upstreamEa, oldNum.get(), newNum.get());
        }
        log.warn("compareAndFixUpstreamPublicEmployee eaNotValid={}", eaNotValid);
        log.warn("compareAndFixUpstreamPublicEmployee errorEas={}", errorEas);
        log.warn("compareAndFixUpstreamPublicEmployee exEas={}", exEas);

        if (errorEas.isEmpty() && exEas.isEmpty()) {
            return Result.newSuccess();
        } else {
            errorEas.addAll(exEas);
            return Result.newSuccess(errorEas);
        }
    }

    private Boolean doCompareAndFixUpstreamPublicEmployee(List<UpstreamPublicEmployeeData> oldUpstreamPublicEmployeeDataList, List<UpstreamPublicEmployeeVos> newUpstreamPublicEmployeeVos, int ei, BaseFlushArg arg) {
        if (CollectionUtils.isEmpty(oldUpstreamPublicEmployeeDataList) && CollectionUtils.isEmpty(newUpstreamPublicEmployeeVos)) {
            return true;
        }
        List<UpstreamPublicEmployeeData> toDeleteUpstreamPublicEmployees = new ArrayList<>();
        List<UpstreamPublicEmployeeData> toAddUpstreamPublicEmployees = new ArrayList<>();

        if (CollectionUtils.isEmpty(oldUpstreamPublicEmployeeDataList)) {
            toDeleteUpstreamPublicEmployees = newUpstreamPublicEmployeeVos.stream().map(x -> {
                UpstreamPublicEmployeeData data = new UpstreamPublicEmployeeData();
                data.setUpstreamTenantId(ei);
                data.setUpstreamEmployeeId(x.getEmployeeId());
                data.setDownstreamOuterTenantId(x.getDownstreamOuterTenantId());
                return data;
            }).collect(Collectors.toList());
        } else if (CollectionUtils.isEmpty(newUpstreamPublicEmployeeVos)) {
            toAddUpstreamPublicEmployees = oldUpstreamPublicEmployeeDataList;
        } else {
            Map<Long, List<UpstreamPublicEmployeeData>> oldDownstreamOuterTenantIdUpstreamPublicEmployeesMap = oldUpstreamPublicEmployeeDataList.stream().collect(Collectors.groupingBy(UpstreamPublicEmployeeData::getDownstreamOuterTenantId, Collectors.mapping(Function.identity(), Collectors.toList())));

            Map<Long, List<UpstreamPublicEmployeeData>> newDownstreamOuterTenantIdUpstreamPublicEmployeesMap = newUpstreamPublicEmployeeVos.stream().map(x -> {
                UpstreamPublicEmployeeData data = new UpstreamPublicEmployeeData();
                data.setUpstreamTenantId(ei);
                data.setUpstreamEmployeeId(x.getEmployeeId());
                data.setDownstreamOuterTenantId(x.getDownstreamOuterTenantId());
                return data;
            }).collect(Collectors.groupingBy(UpstreamPublicEmployeeData::getDownstreamOuterTenantId, Collectors.mapping(Function.identity(), Collectors.toList())));

            Set<Long> allOuterTenantIds = new HashSet<>();
            allOuterTenantIds.addAll(oldDownstreamOuterTenantIdUpstreamPublicEmployeesMap.keySet());
            allOuterTenantIds.addAll(newDownstreamOuterTenantIdUpstreamPublicEmployeesMap.keySet());

            for (Long outerTenantId : allOuterTenantIds) {
                Set<Integer> oldEmployeeIds = oldDownstreamOuterTenantIdUpstreamPublicEmployeesMap.getOrDefault(outerTenantId, new ArrayList<>()).stream().map(UpstreamPublicEmployeeData::getUpstreamEmployeeId).distinct().collect(Collectors.toSet());
                Set<Integer> newEmployeeIds = newDownstreamOuterTenantIdUpstreamPublicEmployeesMap.getOrDefault(outerTenantId, new ArrayList<>()).stream().map(UpstreamPublicEmployeeData::getUpstreamEmployeeId).distinct().collect(Collectors.toSet());

                List<UpstreamPublicEmployeeData> toAdd = Sets.difference(oldEmployeeIds, newEmployeeIds).stream().map(x -> {
                    UpstreamPublicEmployeeData data = new UpstreamPublicEmployeeData();
                    data.setUpstreamTenantId(ei);
                    data.setUpstreamEmployeeId(x);
                    data.setDownstreamOuterTenantId(outerTenantId);
                    return data;
                }).collect(Collectors.toList());

                List<UpstreamPublicEmployeeData> toDelete = Sets.difference(newEmployeeIds, oldEmployeeIds).stream().map(x -> {
                    UpstreamPublicEmployeeData data = new UpstreamPublicEmployeeData();
                    data.setUpstreamTenantId(ei);
                    data.setUpstreamEmployeeId(x);
                    data.setDownstreamOuterTenantId(outerTenantId);
                    return data;
                }).collect(Collectors.toList());

                toAddUpstreamPublicEmployees.addAll(toAdd);
                toDeleteUpstreamPublicEmployees.addAll(toDelete);
            }
        }
        log.info("doCompareAndFixUpstreamPublicEmployee ei={} toAdd={} toDelete={}", ei, toAddUpstreamPublicEmployees, toDeleteUpstreamPublicEmployees);
        if (BooleanUtils.isTrue(arg.getExec())) {
            try {
                this.batchUpsertUpstreamPublicEmployee(toAddUpstreamPublicEmployees, ei, UpstreamPublicTypeContant.NORMAL_TYPE);
                this.batchUpsertUpstreamPublicEmployee(toDeleteUpstreamPublicEmployees, ei, UpstreamPublicTypeContant.DELETE_TYPE);
            } catch (Exception e) {
                return false;
            }
        }
        return true;
    }

    private void batchUpsertUpstreamPublicEmployee(List<UpstreamPublicEmployeeData> upstreamPublicEmployeeDataList, int ei, Integer type) {
        if (CollectionUtils.isEmpty(upstreamPublicEmployeeDataList)) {
            return;
        }
        // 根据downstreamOuterTenantId进行分组 插入数据
        Map<Long, List<UpstreamPublicEmployeeData>> downstreamOuterTenantIdUpstreamPublicEmployeesMap = upstreamPublicEmployeeDataList.stream().collect(Collectors.groupingBy(UpstreamPublicEmployeeData::getDownstreamOuterTenantId, Collectors.mapping(Function.identity(), Collectors.toList())));

        for (Entry<Long, List<UpstreamPublicEmployeeData>> entry : downstreamOuterTenantIdUpstreamPublicEmployeesMap.entrySet()) {
            List<Integer> employeeIds = entry.getValue().stream().map(UpstreamPublicEmployeeData::getUpstreamEmployeeId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(employeeIds)) {
                continue;
            }
            BatchUpsertUpstreamPublicEmployeeArg batchUpsertUpstreamPublicEmployeeArg = new BatchUpsertUpstreamPublicEmployeeArg();
            batchUpsertUpstreamPublicEmployeeArg.setUpstreamTenantId(ei);
            batchUpsertUpstreamPublicEmployeeArg.setUpstreamUserIds(employeeIds);
            batchUpsertUpstreamPublicEmployeeArg.setDownstreamOuterTenantId(entry.getKey());
            batchUpsertUpstreamPublicEmployeeArg.setType(type);
            try {
                com.facishare.global.rest.result.Result result = globalService.batchUpsertUpstreamPublicEmployee(HeaderObj.newInstance(ei), batchUpsertUpstreamPublicEmployeeArg);
                if (!result.isSuccess()) {
                    log.warn("globalService.batchUpsertUpstreamPublicEmployee res={}", result);
                }
            } catch (Exception e) {
                log.warn("", e);
                throw e;
            }
        }
    }

    private boolean hasPublicEployeeIdField(String ea, List<String> exEas, List<String> nofieldsEas) {
        try {
            int tenantId = eieaConverter.enterpriseAccountToId(ea);
            com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(tenantId, -10000);
            com.fxiaoke.crmrestapi.common.result.Result<ListObjectDescribeResult> listResult = objectDescribeService.list(headerObj, true, Lists.newArrayList(ContactFieldContants.API_NAME, PublicEmployeeObjConstant.API_NAME));
            if (!listResult.isSuccess()) {//企业停用 或则对象不存在
                exEas.add(ea);
                return true;
            }
            ListObjectDescribeResult data = listResult.getData();
            if (data == null) {
                exEas.add(ea);
                return true;
            }
            List<ObjectDescribe> describe = data.getDescribe();
            if (describe.size() != 2) {
                exEas.add(ea);
                return true;
            }
            for (ObjectDescribe objectDescribe : describe) {
                if (objectDescribe.getApiName().equals(ContactFieldContants.API_NAME)) {
                    HashMap<String, FieldDescribe> fields = objectDescribe.getFields();
                    if (fields.containsKey(ContactFieldContants.PUBLICEMPLOYEE_ID)) {
                        return true;
                    }
                }
            }
            nofieldsEas.add(ea);
            return false;
        } catch (Exception e) {
            log.warn("", e);
            nofieldsEas.add(ea);
            return true;
        }
    }

    public Result<Void> doFlushBusinessOptimzeCrmData(String ea) {
        try {
            ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(eieaConverter.enterpriseAccountToId(ea) + "", SuperUserConstants.USER_ID + "");
            com.fxiaoke.enterpriserelation.objrest.result.Result result = enterpriseRelationObjService.initErBusiness(serviceContext, true);
            if (!result.isSuccess()) {
                return Result.newError(result.getErrCode(), result.getErrMessage());
            }
            this.doFlushBusinessOptimzeData(ea, EnterpriseRelationObjConstant.API_NAME);
            this.doFlushBusinessOptimzeData(ea, PublicEmployeeObjConstant.API_NAME);
        } catch (Exception e) {
            log.warn("", e);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        return Result.newSuccess();
    }

    private void doFlushBusinessOptimzeData(String upstreamEa, String apiName) {
        AtomicLong readNum = new AtomicLong();
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(eieaConverter.enterpriseAccountToId(upstreamEa), -10000);
        SearchTemplateQuery query = new SearchTemplateQuery();
        Filter filter = new Filter();
        filter.setFieldName(DBRecord.IS_DELETED);
        filter.setOperator(OperatorContants.IN);
        List<String> deleteStatusValues = Lists.newArrayList(DELETE_STATUS.NORMAL, DELETE_STATUS.INVALID).stream().map(x -> String.valueOf(x.getValue())).collect(Collectors.toList());
        filter.setFieldValues(deleteStatusValues);
        query.getFilters().add(filter);
        SearchTemplateQueryOrderBy order = new SearchTemplateQueryOrderBy(DBRecord.CREATE_TIME, true);
        query.getOrders().add(order);

        OffsetUtil.offsetFunction(1000, (offset, limit) -> {
            query.setOffset(offset);
            query.setLimit(limit);
            query.setPermissionType(0);
            com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> searchTemplateResult = null;
            try {
                searchTemplateResult = objectDataService.queryBySearchTemplate(headerObj, apiName, query);
            } catch (Exception e) {
                log.warn("queryBySearchTemplate,apiName=" + apiName, e);
                return 0;
            }
            List<ObjectData> objectDatas = searchTemplateResult.getData().getQueryResult().getData();
            readNum.addAndGet(objectDatas.size());
            try {
                this.flushMapperObjectData(upstreamEa, apiName, objectDatas);
            } catch (Exception e) {
                log.warn("flushMapperObjectData,apiName=" + apiName, e);
            }
            return objectDatas.size();
        });
        log.info("doFlushBusinessOptimzeData,apiName={},size={}", apiName, readNum.get());
    }

    /**
     * 给客户数据的xx字段添加
     *
     * @param mapperApiName 客户、联系人、合作伙伴对象apiName
     * @param objectDatas   互联企业、互联用户对象数据
     */
    private Result<Void> flushMapperObjectData(String upstreamEa, String mapperApiName, List<ObjectData> objectDatas) {
        List<ObjectData> updateObjectDatas = new ArrayList<>();
        List<ObjectData> updatePartnerDatas = new ArrayList<>();
        String updateMapperApiName = null;
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(objectDatas)) {
            for (ObjectData objectData : objectDatas) {
                if (StringUtils.isBlank(objectData.getId())) {
                    log.info("StringUtils.isBlank(objectData.getId())=true,objectData={}", objectData);
                    continue;
                }
                if (EnterpriseRelationObjConstant.API_NAME.equals(mapperApiName)) {
                    if (!StringUtils.isBlank(objectData.getString(EnterpriseRelationObjConstant.MAPPER_ACCOUNT_ID))) {
                        updateMapperApiName = AccountFieldContants.API_NAME;
                        ObjectData updateObjectData = new ObjectData(AccountFieldContants.API_NAME);
                        updateObjectData.put(AccountFieldContants.ID, objectData.getString(EnterpriseRelationObjConstant.MAPPER_ACCOUNT_ID));
                        updateObjectData.put(AccountFieldContants.ENTERPRISERELATION_ID, objectData.getId());
                        updateObjectData.put(AccountFieldContants.ENTERPRISERELATION_TYPE, objectData.get(EnterpriseRelationObjConstant.RELATION_TYPE));
                        updateObjectDatas.add(updateObjectData);
                    }
                    if (!StringUtils.isBlank(objectData.getString(EnterpriseRelationObjConstant.MAPPER_PARTNER_ID))) {
                        ObjectData updateObjectData = new ObjectData(PartnerFieldContants.API_NAME);
                        updateObjectData.put(PartnerFieldContants.ID, objectData.getString(EnterpriseRelationObjConstant.MAPPER_PARTNER_ID));
                        updateObjectData.put(PartnerFieldContants.ENTERPRISERELATION_ID, objectData.getId());
                        updateObjectData.put(PartnerFieldContants.ENTERPRISERELATION_TYPE, objectData.get(EnterpriseRelationObjConstant.RELATION_TYPE));
                        updatePartnerDatas.add(updateObjectData);
                    }
                } else if (PublicEmployeeObjConstant.API_NAME.equals(mapperApiName)) {
                    if (!StringUtils.isBlank(objectData.getString(PublicEmployeeObjConstant.CONTRACT_ID))) {
                        updateMapperApiName = ContactFieldContants.API_NAME;
                        ObjectData updateObjectData = new ObjectData(ContactFieldContants.API_NAME);
                        updateObjectData.put(ContactFieldContants.ID, objectData.getString(PublicEmployeeObjConstant.CONTRACT_ID));
                        updateObjectData.put(ContactFieldContants.PUBLICEMPLOYEE_ID, objectData.getId());
                        updateObjectData.put(ContactFieldContants.PUBLICEMPLOYEE_TYPE, objectData.get(PublicEmployeeObjConstant.TYPE));
                        updateObjectData.put(ContactFieldContants.PUBLICEMPLOYEE_ACTIVE_STATE, PublicEmployeeObj.toActiveState(new com.facishare.paas.metadata.impl.ObjectData(objectData)));
                        updateObjectDatas.add(updateObjectData);
                    }
                } else {
                    throw new RuntimeException("not match apiName=" + mapperApiName);
                }
            }
        }
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(updateObjectDatas)) {
            this.batchUpdatePassObjWithRateLimit(upstreamEa, updateMapperApiName, updateObjectDatas);
        }
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(updatePartnerDatas)) {
            this.batchUpdatePassObjWithRateLimit(upstreamEa, PartnerFieldContants.API_NAME, updatePartnerDatas);
        }
        return Result.newSuccess();
    }

    public Result<Void> batchUpdatePassObjWithRateLimit(String upstreamEa, String apiName, List<ObjectData> objectDatas) {
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(objectDatas)) {
            return Result.newSuccess();
        }
        rateLimiter.acquire(1);
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(upstreamEa), SuperUserConstants.USER_ID);
        OffsetUtil.splitFunction(objectDatas, 500, (datas) -> {
            com.fxiaoke.crmrestapi.common.result.Result<BatchObjectDataResult> res = objectDataService.batchIncrementUpdateWithSpecialTime(headerObj, apiName, true, datas);
            if (res == null || !res.isSuccess()) {
                log.warn("[retry {}] batch update {} error,res={}", upstreamEa, apiName, JsonUtil.toJson(res));
                throw new RelationException(ResultCode.PARAMS_ERROR.getErrorCode(), "batch update error");
            }
            return Lists.newArrayList();
        });
        return Result.newSuccess();
    }

    @PostMapping(value = "/flushLayout")
    public Result<Object> flushLayout(@RequestBody BaseFlushArg arg) {
        List<String> eas = arg.getEas();
        if (CollectionUtils.isEmpty(eas)) {
            return Result.newResult(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.EA_IS_NULL.getDescription());
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            eas = listAllUpstreamEas();
        }
        List<String> eaNotValid = new ArrayList<>();
        List<String> errorEas = new ArrayList<>();
        List<String> exEas = new ArrayList<>();
        for (String upstreamEa : eas) {
            int ei;
            try {
                ei = eieaConverter.enterpriseAccountToId(upstreamEa);
            } catch (Exception e) {
                eaNotValid.add(upstreamEa);
                continue;
            }
            try {
                ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(ei + "", SuperUserConstants.USER_ID + "");
                com.fxiaoke.enterpriserelation.objrest.result.Result<Void> res = enterpriseRelationObjService.flushEnterpriseRelationLayout(serviceContext, NoArg.instance);
                if (!res.isSuccess()) {
                    log.warn("接口调用异常 跳过企业 ea={}", upstreamEa);
                    errorEas.add(upstreamEa);
                }
            } catch (Exception e) {
                log.warn("接口调用异常 跳过企业 ea={}", upstreamEa);
                exEas.add(upstreamEa);
            }
        }
        log.warn("flushLayout eaNotValid={}", eaNotValid);
        log.warn("flushLayout errorEas={}", errorEas);
        log.warn("flushLayout exEas={}", exEas);

        if (errorEas.isEmpty() && exEas.isEmpty()) {
            return Result.newSuccess();
        } else {
            errorEas.addAll(exEas);
            return Result.newSuccess(errorEas);
        }
    }

    @PostMapping(value = "/deleteDeletedPublicEmployeeOuterRole")
    public Result<Object> deleteDeletedPublicEmployeeOuterRole(@RequestBody BaseFlushArg arg) {
        List<String> eas = arg.getEas();
        if (CollectionUtils.isEmpty(eas)) {
            return Result.newResult(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.EA_IS_NULL.getDescription());
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            eas = listAllUpstreamEas();
        }
        List<String> eaNotValid = new ArrayList<>();
        List<String> errorEas = new ArrayList<>();
        List<String> exEas = new ArrayList<>();
        for (String upstreamEa : eas) {
            int ei;
            try {
                ei = eieaConverter.enterpriseAccountToId(upstreamEa);
            } catch (Exception e) {
                eaNotValid.add(upstreamEa);
                continue;
            }
            AtomicInteger noValidNum = new AtomicInteger();
            // 只需要对比有租户的相关团队
            OffsetUtil.offsetFunction(500, (offset, limit) -> {
                PatternBuilder patternBuilder = PatternBuilder.newBuilder();
                com.facishare.paas.metadata.impl.search.SearchTemplateQuery query = new com.facishare.paas.metadata.impl.search.SearchTemplateQuery();

                List<IFilter> filters = new ArrayList<>(3);
                com.facishare.paas.metadata.impl.search.Filter filter = new com.facishare.paas.metadata.impl.search.Filter();
                filter.setFieldName(DBRecord.IS_DELETED);
                filter.setOperator(Operator.IN);
                filter.setFieldValues(Lists.newArrayList(DELETE_STATUS.DELETE.getValue() + "", DELETE_STATUS.INVALID.getValue() + "", DELETE_STATUS.DATA_UTIL_DELETE.getValue() + ""));
                filters.add(filter);
                patternBuilder.and();
                query.setPermissionType(0);
                query.setLimit(limit);
                query.setOffset(offset);
                query.addFilters(filters);
                query.setPattern(patternBuilder.build());
                query.setSearchSource("db");

                try {
                    IActionContext actionContextExt = ActionContextExt.of(User.systemUser(ei + "")).setSkipRelevantTeam(true).getContext();
                    QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContextExt, PublicEmployeeObjConstant.API_NAME, query);

                    if (CollectionUtils.isNotEmpty(queryResult.getData())) {
                        List<IObjectData> data = queryResult.getData();
                        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newObjInstance(data);
                        List<Long> outerUidList = publicEmployeeObjs.stream().map(PublicEmployeeObj::getOuterUid).collect(Collectors.toList());
                        List<PublicEmployeeObj> normalPublicEmployeeObjs = filterNormalPublicEmployeeObjs(ei, outerUidList);
                        Set<Long> normalOuterUidSet = normalPublicEmployeeObjs.stream().map(PublicEmployeeObj::getOuterUid).collect(Collectors.toSet());
                        log.info("deleteDeletedPublicEmployeeOuterRole -> ea={}, normalOuterUidSet={}", upstreamEa, normalOuterUidSet);
                        Map<Long, List<Long>> outerTenantIdOuterUidsMap = publicEmployeeObjs.stream().filter(x -> !normalOuterUidSet.contains(x.getOuterUid())).collect(Collectors.groupingBy(PublicEmployeeObj::getOuterTenantId, Collectors.mapping(PublicEmployeeObj::getOuterUid, Collectors.toList())));
                        log.info("deleteDeletedPublicEmployeeOuterRole -> ea={}, outerTenantIdOuterUidsMap={}", upstreamEa, outerTenantIdOuterUidsMap);
                        if (!CollectionUtils.isEmpty(outerTenantIdOuterUidsMap.entrySet()) && BooleanUtils.isTrue(arg.getExec())) {
                            try {
                                for (Entry<Long, List<Long>> longListEntry : outerTenantIdOuterUidsMap.entrySet()) {
                                    Long outerTenantId = longListEntry.getKey();
                                    List<Long> outerUids = longListEntry.getValue();
                                    if (CollectionUtils.isNotEmpty(outerUids)) {
                                        rateLimiter.acquire(1);
                                        outerRoleRemoteBaseManager.cleanUserRoles(ei, outerTenantId, outerUids);
                                    }
                                }
                            } catch (Exception e) {
                                errorEas.add(upstreamEa);
                            }
                        }
                    }
                    noValidNum.addAndGet(queryResult.getData().size());
                    return queryResult.getData().size();
                } catch (Exception e) {
                    log.warn("接口调用异常 跳过企业 ea={}", upstreamEa, e);
                    exEas.add(upstreamEa);
                    return 0;
                }
            });
            log.warn("deleteDeletedPublicEmployeeOuterRole ea={},stopNum={}", upstreamEa, noValidNum.get());
        }
        log.warn("deleteDeletedPublicEmployeeOuterRole eaNotValid={}", eaNotValid);
        log.warn("deleteDeletedPublicEmployeeOuterRole errorEas={}", errorEas);
        log.warn("deleteDeletedPublicEmployeeOuterRole exEas={}", exEas);

        if (errorEas.isEmpty() && exEas.isEmpty()) {
            return Result.newSuccess();
        } else {
            errorEas.addAll(exEas);
            return Result.newSuccess(errorEas);
        }
    }

    private List<PublicEmployeeObj> filterNormalPublicEmployeeObjs(int ei, List<Long> outerUids) {
        if (CollectionUtils.isEmpty(outerUids)) {
            return Lists.newArrayList();
        }

        PatternBuilder patternBuilder = PatternBuilder.newBuilder();
        com.facishare.paas.metadata.impl.search.SearchTemplateQuery query = new com.facishare.paas.metadata.impl.search.SearchTemplateQuery();

        List<IFilter> filters = new ArrayList<>(3);
        com.facishare.paas.metadata.impl.search.Filter filter = new com.facishare.paas.metadata.impl.search.Filter();
        filter.setFieldName(DBRecord.IS_DELETED);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList(DELETE_STATUS.NORMAL.getValue() + ""));
        filters.add(filter);
        patternBuilder.and();

        List<String> outerUidStrList = outerUids.stream().map(String::valueOf).collect(Collectors.toList());
        com.facishare.paas.metadata.impl.search.Filter filter2 = new com.facishare.paas.metadata.impl.search.Filter();
        filter2.setFieldName(PublicEmployeeObjConstant.OUTER_UID);
        filter2.setOperator(Operator.IN);
        filter2.setFieldValues(outerUidStrList);
        filters.add(filter2);
        patternBuilder.and();

        query.setPermissionType(0);
        query.setLimit(outerUids.size());
        query.setOffset(0);
        query.addFilters(filters);
        query.setPattern(patternBuilder.build());
        query.setSearchSource("db");

        IActionContext actionContextExt = ActionContextExt.of(User.systemUser(ei + "")).setSkipRelevantTeam(true).getContext();
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContextExt, PublicEmployeeObjConstant.API_NAME, query);
        List<IObjectData> datas = queryResult.getData();
        if (CollectionUtils.isEmpty(datas)) {
            return Lists.newArrayList();
        }
        return PublicEmployeeObj.newObjInstance(datas);
    }

    /**
     * 开通多版本经销商License的企业列表
     */
    @PostMapping(value = "/listMultiModuleCodesEas")
    public Result<Map<String, List<String>>> listMultiModuleCodesEas() {
        Map<String, List<String>> resultMap = Maps.newHashMap();
        List<String> eas = listAllUpstreamEas();
        for (String ea : eas) {
            int tenantId = eieaConverter.enterpriseAccountToId(ea);
            List<String> moduleCodes = paasLicenseManager.listDownstreamOpenModuleCodes(tenantId);
            if (!CollectionUtils.isEmpty(moduleCodes) && moduleCodes.size() > 1) {
                resultMap.put(ea, moduleCodes);
            }
        }
        return Result.newSuccess(resultMap);
    }

    /**
     * 下游有租户互联企业中，当前License级别低于上游优先级最高License的企业ea
     */
    @PostMapping(value = "/listLicenseLowerThanUpstreamDownstreamEas")
    public Result<List<LicenseLowerThanUpstreamData>> listLicenseLowerThanUpstreamDownstreamEas() {
        List<LicenseLowerThanUpstreamData> resultDatas = Lists.newArrayList();
        List<String> eas = listAllUpstreamEas();
        for (String ea : eas) {
            int tenantId = eieaConverter.enterpriseAccountToId(ea);
            List<String> allDownstreamEas = Lists.newArrayList();
            ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(tenantId + "", "-10000");
            ListEnterpriseRelationObjByConditionArg conditionArg = new ListEnterpriseRelationObjByConditionArg();
            conditionArg.setHasFsAccount(true);
            OffsetUtil.offsetFunction(1000, (offset, limit) -> {
                conditionArg.setOffset(offset);
                conditionArg.setLimit(limit);
                ListByConditionResult conditionResult = enterpriseRelationObjService.listByCondition(serviceContext, conditionArg);
                if (CollectionUtils.isEmpty(conditionResult.getObjectDatas())) {
                    return 0;
                }
                List<EnterpriseRelationObj> enterpriseRelationObjs = EnterpriseRelationObj.newInstance(conditionResult.getObjectDatas());
                List<String> downstreamEas = enterpriseRelationObjs.stream().map(EnterpriseRelationObj::getEnterpriseAccount).collect(Collectors.toList());
                allDownstreamEas.addAll(downstreamEas);
                return conditionResult.getObjectDatas().size();
            });

            if (CollectionUtils.isEmpty(allDownstreamEas)) {
                continue;
            }

            DownstreamOpenLicenseData upstreamCurrentDownstreamOpenLicenseData = getCurrentDownstreamOpenLicenseData(ea);
            LicenseLowerThanUpstreamData licenseLowerThanUpstreamData = new LicenseLowerThanUpstreamData();
            licenseLowerThanUpstreamData.setUpstreamEa(ea);
            licenseLowerThanUpstreamData.setUpstreamLicenseData(upstreamCurrentDownstreamOpenLicenseData);
            licenseLowerThanUpstreamData.setDownstreamLicenseDatas(Lists.newArrayList());

            for (String downstreamEa : allDownstreamEas) {
                DownstreamOpenLicenseData currentDownstreamOpenLicenseData = getCurrentDownstreamOpenLicenseData(downstreamEa);
                if (currentDownstreamOpenLicenseData == null) {
                    continue;
                }
                if (currentDownstreamOpenLicenseData.getPriority() < upstreamCurrentDownstreamOpenLicenseData.getPriority()) {
                    EaDownstreamOpenLicenseData downstreamLicenseData = new EaDownstreamOpenLicenseData();
                    downstreamLicenseData.setDownstreamEa(downstreamEa);
                    downstreamLicenseData.setDownstreamLicenseData(currentDownstreamOpenLicenseData);
                    licenseLowerThanUpstreamData.getDownstreamLicenseDatas().add(downstreamLicenseData);
                }
            }
            if (!CollectionUtils.isEmpty(licenseLowerThanUpstreamData.getDownstreamLicenseDatas())) {
                resultDatas.add(licenseLowerThanUpstreamData);
            }
        }
        return Result.newSuccess(resultDatas);
    }

    private DownstreamOpenLicenseData getCurrentDownstreamOpenLicenseData(String ea) {
        LinkedHashMap<String, DownstreamOpenLicenseData> downstreamOpenLicenseDataMap = paasLicenseManager.listDownstreamOpenLicenseDatas(ea);
        if (MapUtils.isEmpty(downstreamOpenLicenseDataMap)) {
            return null;
        }
        String upstreamFirstCode = new ArrayList<>(downstreamOpenLicenseDataMap.keySet()).get(0);
        return downstreamOpenLicenseDataMap.get(upstreamFirstCode);
    }

    @PostMapping(value = "/listUsedTenantGroupTeamMemberEas")
    public Result<List<String>> listUsedTenantGroupTeamMemberEas() {
        List<String> result = Lists.newArrayList();
        List<String> eas = listAllUpstreamEas();
        List<String> failEas = Lists.newArrayList();
        for (String ea : eas) {
            int tenantId = eieaConverter.enterpriseAccountToId(ea);
            OffsetUtil.offsetFunction(1000, (offset, limit) -> {
                int size = 0;
                try {
                    SearchTemplateQuery query = new SearchTemplateQuery();
                    query.setDataRolePermission(false);
                    query.setOffset(offset);
                    query.setLimit(limit);

                    FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
                    findByQueryV3Arg.setDescribeApiName(EnterpriseRelationObjConstant.API_NAME);
                    findByQueryV3Arg.setSelectFields(Lists.newArrayList(EnterpriseRelationObjConstant.ID, "relevant_team"));
                    findByQueryV3Arg.setIncludeInvalid(true);
                    findByQueryV3Arg.setIncludeRelevantTeam(true);
                    findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(query));

                    com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> queryResult = objectDataService.queryBySearchTemplate(com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(tenantId, -10000), EnterpriseRelationObjConstant.API_NAME, query);
                    List<ObjectData> dataList = queryResult.getData().getQueryResult().getData();

                    for (ObjectData objectData : dataList) {
                        List<Map<String, Object>> relevantTeam = (List<Map<String, Object>>) objectData.get("relevant_team");
                        if (CollectionUtils.isEmpty(relevantTeam)) {
                            continue;
                        }
                        for (Map<String, Object> objectMap : relevantTeam) {
                            Integer teamMemberType = Integer.valueOf((String) objectMap.get("teamMemberType"));
                            if (teamMemberType != null && teamMemberType == 6) {// 企业组
                                result.add(ea);
                                return 0;
                            }
                        }
                    }
                    size = dataList.size();
                } catch (Exception e) {
                    failEas.add(ea);
                    log.warn("listUsedTenantGroupTeamMemberEas error. ea={}", ea, e);
                    return 0;
                }
                return size;
            });
        }
        log.info("listUsedTenantGroupTeamMemberEas finish, fail eas={}", failEas);
        return Result.newSuccess(result);
    }

    @PostMapping(value = "/flushErObjDescribe")
    public Result<Object> flushErObjDescribe(@RequestBody BaseFlushArg arg) {
        List<String> eas = arg.getEas();
        if (CollectionUtils.isEmpty(eas)) {
            return Result.newResult(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.EA_IS_NULL.getDescription());
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            eas = listAllUpstreamEas();
        }
        List<String> eaNotValid = new ArrayList<>();
        List<String> errorEas = new ArrayList<>();
        List<String> exEas = new ArrayList<>();
        for (String upstreamEa : eas) {
            int ei;
            try {
                ei = eieaConverter.enterpriseAccountToId(upstreamEa);
            } catch (Exception e) {
                eaNotValid.add(upstreamEa);
                continue;
            }
            try {
                ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(ei + "", SuperUserConstants.USER_ID + "");
                FlushLoginEmailFeatureDescribeArg flushLoginEmailFeatureDescribeArg = new FlushLoginEmailFeatureDescribeArg();
                flushLoginEmailFeatureDescribeArg.setEis(Lists.newArrayList(ei));
                flushLoginEmailFeatureDescribeArg.setAddObjectFieldKey(arg.getAddObjectFieldKey());
                com.fxiaoke.enterpriserelation.objrest.result.Result result = enterpriseRelationObjService.flushErObjDescribe(serviceContext, flushLoginEmailFeatureDescribeArg);
                if (result.getErrCode() != ResultCode.SUCCESS.getErrorCode()) {
                    exEas.add(upstreamEa);
                }
            } catch (Exception e) {
                log.warn("接口调用异常 跳过企业 ea={}", upstreamEa);
                exEas.add(upstreamEa);
            }
        }
        log.warn("flushLayout eaNotValid={}", eaNotValid);
        log.warn("flushLayout errorEas={}", errorEas);
        log.warn("flushLayout exEas={}", exEas);

        if (errorEas.isEmpty() && exEas.isEmpty()) {
            return Result.newSuccess();
        } else {
            errorEas.addAll(exEas);
            return Result.newSuccess(errorEas);
        }
    }

    /**
     * 给历史企业开通“ErBindThirdAccountObj”对象
     */
    @PostMapping(value = "/flushErBindThirdAccountObj")
    public Result<Map<String, Boolean>> flushErBindThirdAccountObj(@RequestBody BaseFlushArg arg) {
        if (BooleanUtils.isFalse(arg.getIsInitAll()) && CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR.getErrCode(), ResultCode.SPECIFY_INTERCONNECTED_ENTERPRISE.getDescription() + "【ErBindThirdAccountObj】");
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            arg.setEas(listAllUpstreamEas());
        }

        Map<String, Boolean> result = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(arg.getEas())) {
            arg.getEas().forEach(ea -> {
                int ei;
                try {
                    ei = eieaConverter.enterpriseAccountToId(ea);
                } catch (Exception e) {
                    result.put(ea, false);
                    return;
                }
                try {
                    if (BooleanUtils.isTrue(arg.getExec())) {
                        ServiceContext serviceContext = erBindThirdAccountObjService.createServiceContext(ei + "", SuperUserConstants.USER_ID + "");
                        boolean isSuccess = erBindThirdAccountObjService.openErBindThirdAccountObj(serviceContext, NoArg.instance).isSuccess();
                        result.put(ea, isSuccess);
                    } else {
                        result.put(ea, true);
                    }
                } catch (Exception e) {
                    log.warn("flushErBindThirdAccountObj error. ea={}", ea, e);
                    result.put(ea, false);
                }
            });
            log.info("flushErBindThirdAccountObj finish, result={}", result);
        }
        return Result.newSuccess(result);
    }

    @PostMapping(value = "/fixAccountObjIsErEnterpriseStatus")
    public Result<Void> fixAccountObjIsErEnterpriseStatus(@RequestBody BaseFlushArg arg) {
        if (CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        for (String upstreamEa : arg.getEas()) {
            int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
            com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(tenantId, -10000);

            /*List<String> downstreamEas = Lists.newArrayList();
            OffsetUtil.offsetFunction(1000, (offset, limit) -> {
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.setPermissionType(0);
                query.addFilter(EnterpriseRelationObjConstant.HAS_FS_ACCOUNT, Lists.newArrayList("true"), FilterOperatorEnum.EQ);
                query.setOffset(offset);
                query.setLimit(limit);
                com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> result = objectDataService.queryBySearchTemplate(headerObj, EnterpriseRelationObjConstant.API_NAME, query);
                if (!result.isSuccess()) {
                    log.warn("fixAccountObjIsErEnterpriseStatus list downstream ea error. result={}", result);
                    return 0;
                }
                List<ObjectData> datas = result.getData().getQueryResult().getData();
                List<EnterpriseRelationObj> enterpriseRelationObjs = EnterpriseRelationObj.newInstance(ObjectData.convert2MapList(datas));
                List<String> eas = enterpriseRelationObjs.stream().filter(x -> !StringUtils.isEmpty(x.getEnterpriseAccount())).map(EnterpriseRelationObj::getEnterpriseAccount).collect(Collectors.toList());
                downstreamEas.addAll(eas);
                return datas.size();
            });*/

            EnterpriseMetaDataEntity enterpriseMetaData = enterpriseMetaDataManager.getEnterpriseMetaData(upstreamEa);
            String erVisitorBindAccountId = enterpriseMetaData.getErVisitorBindAccountId();

            Map<String, List<ObjectData>> updateEnterpriseRelationIdFailedDatas = Maps.newHashMap();
            Map<String, List<ObjectData>> updateIsErEnterpriseStatusFailedDatas = Maps.newHashMap();

            OffsetUtil.offsetFunction(1000, (offset, limit) -> {
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.setPermissionType(0);
                query.addFilter("is_er_enterprise", Lists.newArrayList("true"), FilterOperatorEnum.EQ);
                query.setOffset(offset);
                query.setLimit(limit);
                com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> result = objectDataService.queryBySearchTemplate(headerObj, AccountFieldContants.API_NAME, query);
                if (!result.isSuccess()) {
                    log.warn("fixAccountObjIsErEnterpriseStatus error. query account obj fail, result={}", result);
                    return 0;
                }
                QueryBySearchTemplateResult.QueryResult queryResult = result.getData().getQueryResult();
                List<ObjectData> accountObjDatas = queryResult.getData();
                List<String> accountIds = accountObjDatas.stream().map(ObjectData::getId).collect(Collectors.toList());

                SearchTemplateQuery query2 = new SearchTemplateQuery();
                query2.setPermissionType(0);
                query2.addFilter(EnterpriseRelationObjConstant.MAPPER_ACCOUNT_ID, accountIds, FilterOperatorEnum.IN);
                query2.setOffset(offset);
                query2.setLimit(limit);
                com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> result2 = objectDataService.queryBySearchTemplate(headerObj, EnterpriseRelationObjConstant.API_NAME, query2);
                if (!result2.isSuccess()) {
                    return accountObjDatas.size();
                }

                // 关联互联企业的客户
                List<ObjectData> enterpriseRelationObjectDatas = result2.getData().getQueryResult().getData();
                List<EnterpriseRelationObj> enterpriseRelationObjs = EnterpriseRelationObj.newInstance(ObjectData.convert2MapList(enterpriseRelationObjectDatas));
                List<ObjectData> accountObjsToUpdateEnterpriseRelationId = Lists.newArrayList();
                Map<String, String> accountIdOuterTenantIdMap = enterpriseRelationObjs.stream().filter(x -> x.getRelationType() == EnterpriseRelationTypeEnum.RELIVE.getType()).collect(Collectors.toMap(EnterpriseRelationObj::getMapperAccountId, AbstractDocumentBasedDBRecord::getId));
                if (!MapUtils.isEmpty(accountIdOuterTenantIdMap)) {
                    for (Entry<String, String> entry : accountIdOuterTenantIdMap.entrySet()) {
                        ObjectData objectData = new ObjectData();
                        objectData.put("_id", entry.getKey());
                        objectData.put("is_er_enterprise", false);
                        accountObjsToUpdateEnterpriseRelationId.add(objectData);
                    }
                    log.info("fixAccountObjIsErEnterpriseStatus -> accountObjsToUpdateEnterpriseRelationId={}", accountObjsToUpdateEnterpriseRelationId);
                    if (!CollectionUtils.isEmpty(accountObjsToUpdateEnterpriseRelationId) && BooleanUtils.isTrue(arg.getExec())) {
                        com.fxiaoke.crmrestapi.common.result.Result<BatchObjectDataResult> batchObjectDataResultResult = objectDataService.batchIncrementUpdate(headerObj, AccountFieldContants.API_NAME, accountObjsToUpdateEnterpriseRelationId);
                        if (!batchObjectDataResultResult.isSuccess()) {
                            log.warn("fixAccountObjIsErEnterpriseStatus -> accountObjsToUpdateEnterpriseRelationId fail. accountObjsToUpdateEnterpriseRelationId={}, result={}", accountObjsToUpdateEnterpriseRelationId, batchObjectDataResultResult);
                            List<ObjectData> orDefault = updateEnterpriseRelationIdFailedDatas.getOrDefault(upstreamEa, Lists.newArrayList());
                            orDefault.addAll(accountObjsToUpdateEnterpriseRelationId);
                            updateEnterpriseRelationIdFailedDatas.put(upstreamEa, orDefault);
                        }
                    }
                }

                // 沒有关联互联企业的客户
                accountIds.removeAll(accountIdOuterTenantIdMap.keySet());
                if (!StringUtils.isEmpty(erVisitorBindAccountId)) {
                    accountIds.remove(erVisitorBindAccountId);
                }
                List<ObjectData> accountObjsToUpdateIsErEnterpriseStatus = Lists.newArrayList();
                for (String accountId : accountIds) {
                    ObjectData objectData = new ObjectData();
                    objectData.put("_id", accountId);
                    objectData.put("is_er_enterprise", false);
                    accountObjsToUpdateIsErEnterpriseStatus.add(objectData);
                }
                log.info("fixAccountObjIsErEnterpriseStatus -> accountObjsToUpdateIsErEnterpriseStatus={}", accountObjsToUpdateIsErEnterpriseStatus);
                if (!CollectionUtils.isEmpty(accountObjsToUpdateIsErEnterpriseStatus) && BooleanUtils.isTrue(arg.getExec())) {
                    com.fxiaoke.crmrestapi.common.result.Result<BatchObjectDataResult> batchObjectDataResultResult = objectDataService.batchIncrementUpdate(headerObj, AccountFieldContants.API_NAME, accountObjsToUpdateIsErEnterpriseStatus);
                    if (!batchObjectDataResultResult.isSuccess()) {
                        log.warn("fixAccountObjIsErEnterpriseStatus -> accountObjsToUpdateIsErEnterpriseStatus fail. accountObjsToUpdateIsErEnterpriseStatus={}, result={}", accountObjsToUpdateIsErEnterpriseStatus, batchObjectDataResultResult);
                        List<ObjectData> orDefault = updateIsErEnterpriseStatusFailedDatas.getOrDefault(upstreamEa, Lists.newArrayList());
                        orDefault.addAll(accountObjsToUpdateIsErEnterpriseStatus);
                        updateIsErEnterpriseStatusFailedDatas.put(upstreamEa, orDefault);
                    }
                }
                return accountObjDatas.size();
            });
            log.info("fixAccountObjIsErEnterpriseStatus -> finish, updateEnterpriseRelationIdFailedDatas={}", updateEnterpriseRelationIdFailedDatas);
            log.info("fixAccountObjIsErEnterpriseStatus -> finish, updateIsErEnterpriseStatusFailedDatas={}", updateIsErEnterpriseStatusFailedDatas);
        }
        return Result.newSuccess();
    }

    @PostMapping(value = "/initSystemLayoutRule")
    public Result<Map<String, Object>> initSystemLayoutRule(@RequestBody BaseFlushArg arg) {
        if (!BooleanUtils.isTrue(arg.getIsInitAll()) && CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newSuccess();
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            arg.setEas(listAllUpstreamEas());
        }
        List<String> failedEas = Lists.newArrayList();
        for (String upstreamEa : arg.getEas()) {
            try {
                // 预制布局规则
                if (BooleanUtils.isTrue(arg.getExec())) {
                    boolean success = paasObjectDescribeManager.initErLayoutRule(upstreamEa);
                    if (!success) {
                        log.warn("initSystemLayoutRule -> createLayoutRule fail. upstreamEa={}", upstreamEa);
                        failedEas.add(upstreamEa);
                    }
                }
            } catch (Exception e) {
                log.warn("initSystemLayoutRule error. upstreamEa={}", upstreamEa, e);
                failedEas.add(upstreamEa);
            }
        }
        Map<String, Object> resMap = Maps.newHashMap();
        resMap.put("failedEas", failedEas);
        return Result.newSuccess(resMap);
    }

    @PostMapping(value = "/initLoginRegisterConfigNew")
    public Result<Map<String, Object>> initLoginRegisterConfigNew(@RequestBody BaseFlushArg arg) {
        if (!BooleanUtils.isTrue(arg.getIsInitAll()) && CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newSuccess();
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            arg.setEas(listAllUpstreamEas());
        }
        List<String> failedEas = Lists.newArrayList();
        for (String upstreamEa : arg.getEas()) {
            try {
                int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
                loginRegisterConfigService.initLoginRegisterConfigData(String.valueOf(tenantId));
            } catch (Throwable throwable) {
                log.warn("", throwable);
                failedEas.add(upstreamEa);
            }
        }
        Map<String, Object> resMap = Maps.newHashMap();
        resMap.put("failedEas", failedEas);
        return Result.newSuccess(resMap);
    }

    @PostMapping(value = "/initLoginRegisterConfig")
    public Result<Map<String, Object>> initLoginRegisterConfig(@RequestBody BaseFlushArg arg) {
        if (!BooleanUtils.isTrue(arg.getIsInitAll()) && CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newSuccess();
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            arg.setEas(listAllUpstreamEas());
        }
        List<String> failedEas = Lists.newArrayList();
        for (String upstreamEa : arg.getEas()) {
            try {
                Result<RegisterERAccountActiveConfigData> accountActiveConfigsResult = erAccountActiveConfigService.getRegisterERAccountActiveConfigs(upstreamEa);
                if (!accountActiveConfigsResult.isSuccess()) {
                    log.warn("initLoginRegisterConfig fail, upstreamEa={}, accountActiveConfigsResult={}", upstreamEa, accountActiveConfigsResult);
                    failedEas.add(upstreamEa);
                    continue;
                }

                int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
                RegisterERAccountActiveConfigData data = accountActiveConfigsResult.getData();

                // 先检查这个配置有没有创建过
                ListLoginRegisterConfigsArg listLoginRegisterConfigsArg = new ListLoginRegisterConfigsArg();
                listLoginRegisterConfigsArg.setLinkAppId("all");
                Result<ListLoginRegisterConfigsResult> loginRegisterConfigsResult = loginRegisterConfigService.listLoginRegisterConfigs(String.valueOf(tenantId), listLoginRegisterConfigsArg);
                if (!loginRegisterConfigsResult.isSuccess()) {
                    log.warn("listLoginRegisterConfigs fail, upstreamEa={}, loginRegisterConfigsResult={}", upstreamEa, loginRegisterConfigsResult);
                    failedEas.add(upstreamEa);
                    continue;
                }
                List<LoginRegisterConfigVO> loginRegisterConfigVOS = loginRegisterConfigsResult.getData().getDataList();
                // 企业-客户
                if (!isConfigCreated(loginRegisterConfigVOS, "all", "AccountObj", IdentityTypeEnum.CORP.getType())) {
                    boolean success = createLoginRegisterConfig(tenantId, data.getCorpERAccountActiveConfigData());
                    if (!success) {
                        failedEas.add(upstreamEa);
                    }
                }

                // 企业-合作伙伴
                if (!isConfigCreated(loginRegisterConfigVOS, "all", "PartnerObj", IdentityTypeEnum.CORP.getType())) {
                    boolean success = createLoginRegisterConfig(tenantId, data.getCorpERPartnerActiveConfigData());
                    if (!success) {
                        failedEas.add(upstreamEa);
                    }
                }

                // 个人-客户
                if (!isConfigCreated(loginRegisterConfigVOS, "all", "AccountObj", IdentityTypeEnum.PERSONAL.getType())) {
                    boolean success = createLoginRegisterConfig(tenantId, data.getPersonnalERAccountActiveConfigData());
                    if (!success) {
                        failedEas.add(upstreamEa);
                    }
                }

            } catch (Exception e) {
                failedEas.add(upstreamEa);
                log.warn("initLoginRegisterConfig error. upstreamEa={}", upstreamEa, e);
            }
        }
        Map<String, Object> resMap = Maps.newHashMap();
        resMap.put("failedEas", failedEas);
        return Result.newSuccess(resMap);
    }

    private boolean isConfigCreated(List<LoginRegisterConfigVO> configVOS, String linkAppId, String mapperApiName, int identityType) {
        if (CollectionUtils.isEmpty(configVOS)) {
            return false;
        }
        List<LoginRegisterConfigVO> matchConfigs = configVOS.stream().filter(x -> linkAppId.equals(x.getLinkAppId())).filter(x -> mapperApiName.equals(x.getMapperObjectApiName())).filter(x -> identityType == x.getIdentityType()).collect(Collectors.toList());
        return !CollectionUtils.isEmpty(matchConfigs);
    }

    private boolean createLoginRegisterConfig(int tenantId, ERAccountActiveConfigData data) {
        CreateLoginRegisterConfigArg corpAccountConfigArg = toCreateLoginRegisterConfigArg(tenantId, data);
        Result<String> createResult = loginRegisterConfigService.createLoginRegisterConfig(String.valueOf(tenantId), corpAccountConfigArg);
        if (!createResult.isSuccess()) {
            log.warn("createLoginRegisterConfig fail, tenantId={}, createResult={}", tenantId, createResult);
            return false;
        }
        if (data.isOpenRegiestOrCrmCreate()) {
            UpdateLoginRegisterConfigStatusArg statusArg = new UpdateLoginRegisterConfigStatusArg();
            statusArg.setId(createResult.getData());
            statusArg.setStatus(LoginRegisterStatusEnum.STARTED.getCode());
            Result<Void> startResult = loginRegisterConfigService.updateLoginRegisterConfigStatus(String.valueOf(tenantId), statusArg);
            if (!startResult.isSuccess()) {
                log.warn("updateLoginRegisterConfigStatus fail, tenantId={}, startResult={}", tenantId, startResult);
                return false;
            }
        }
        return true;
    }

    private CreateLoginRegisterConfigArg toCreateLoginRegisterConfigArg(int tenantId, ERAccountActiveConfigData data) {
        CreateLoginRegisterConfigArg createLoginRegisterConfigArg = new CreateLoginRegisterConfigArg();
        createLoginRegisterConfigArg.setLinkAppId("all");
        createLoginRegisterConfigArg.setMapperApiName(data.getMapperApiName());
        createLoginRegisterConfigArg.setIdentityType(data.getIdentityType());
        createLoginRegisterConfigArg.setOuterRoleIds(data.getOuterRoleIds());
        Integer owner = 1000;
        if (data.isOpenRegiestOrCrmCreate()) {
            createLoginRegisterConfigArg.setLoginRegisterLayoutConfig(toLoginRegisterLayoutConfigVo(tenantId, data));
        } else {
            String ea = eieaConverter.enterpriseIdToAccount(tenantId);
            List<Integer> relationAdmins = fxiaokeAccountManager.listRelationAdmins(ea);
            if (!CollectionUtils.isEmpty(relationAdmins)) {
                owner = relationAdmins.get(0);
            } else {
                List<Integer> crmAdmins = fxiaokeAccountManager.listCrmAdmin(tenantId);
                if (!CollectionUtils.isEmpty(crmAdmins)) {
                    owner = crmAdmins.get(0);
                }
            }
        }
        if ("AccountObj".equals(data.getMapperApiName()) && data.getIdentityType() == IdentityTypeEnum.CORP.getType()) {
            createLoginRegisterConfigArg.setName("全部企业（客户）应用通用配置");
            List<Integer> optionValues = Lists.newArrayList();
            // 自注册
            if (data.isOpenRegiestCreate()) {
                optionValues.add(LoginRegisterOptionEnum.ACCOUNT_AND_CONTACT_SELF.getCode());
            }
            // 业务员注册
            if (data.isOpenCrmCreate()) {
                optionValues.add(LoginRegisterOptionEnum.ACCOUNT_AND_CONTACT_EXISTS.getCode());
            }
            // 默认配置
            if (CollectionUtils.isEmpty(optionValues)) {
                optionValues.add(LoginRegisterOptionEnum.ACCOUNT_AND_CONTACT_SELF.getCode());
                optionValues.add(LoginRegisterOptionEnum.ACCOUNT_AND_CONTACT_EXISTS.getCode());
            }
            createLoginRegisterConfigArg.setLoginRegisterOptionValues(optionValues);
            if (!data.isOpenRegiestOrCrmCreate()) {
                createLoginRegisterConfigArg.setLoginRegisterLayoutConfig(toDefaultAccountLoginRegisterLayoutConfigVo(tenantId, data, owner));
            }
        }
        if ("PartnerObj".equals(data.getMapperApiName()) && data.getIdentityType() == IdentityTypeEnum.CORP.getType()) {
            createLoginRegisterConfigArg.setName("全部企业（合作伙伴）应用通用配置");
            List<Integer> optionValues = Lists.newArrayList();
            // 自注册
            if (data.isOpenRegiestCreate()) {
                optionValues.add(LoginRegisterOptionEnum.PARTNER_AND_CONTACT_SELF.getCode());
            }
            // 业务员注册
            if (data.isOpenCrmCreate()) {
                optionValues.add(LoginRegisterOptionEnum.PARTNER_AND_CONTACT_EXISTS.getCode());
            }
            if (CollectionUtils.isEmpty(optionValues)) {
                optionValues.add(LoginRegisterOptionEnum.PARTNER_AND_CONTACT_SELF.getCode());
                optionValues.add(LoginRegisterOptionEnum.PARTNER_AND_CONTACT_EXISTS.getCode());
            }
            createLoginRegisterConfigArg.setLoginRegisterOptionValues(optionValues);
            if (!data.isOpenRegiestOrCrmCreate()) {
                createLoginRegisterConfigArg.setLoginRegisterLayoutConfig(toDefaultPartnerLoginRegisterLayoutConfigVo(tenantId, data, owner));
            }
        }
        if ("AccountObj".equals(data.getMapperApiName()) && data.getIdentityType() == IdentityTypeEnum.PERSONAL.getType()) {
            createLoginRegisterConfigArg.setName("全部个人（客户）应用通用配置");
            List<Integer> optionValues = Lists.newArrayList();
            // 自注册
            optionValues.add(LoginRegisterOptionEnum.ACCOUNT_AND_CONTACT_SELF.getCode());
            // 业务员注册
            optionValues.add(LoginRegisterOptionEnum.ACCOUNT_AND_CONTACT_EXISTS.getCode());
            createLoginRegisterConfigArg.setLoginRegisterOptionValues(optionValues);
            if (!data.isOpenRegiestOrCrmCreate()) {
                createLoginRegisterConfigArg.setLoginRegisterLayoutConfig(toDefaultAccountLoginRegisterLayoutConfigVo(tenantId, data, owner));
            }
        }
        return createLoginRegisterConfigArg;
    }

    private LoginRegisterLayoutConfigVo toLoginRegisterLayoutConfigVo(int tenantId, ERAccountActiveConfigData data) {
        LoginRegisterLayoutConfigVo layoutConfigVo = new LoginRegisterLayoutConfigVo();
        layoutConfigVo.setTenantId(String.valueOf(tenantId));
        layoutConfigVo.setOwners(data.getOwners());
        layoutConfigVo.setOwnerAllocateType(data.getOwnerAllocateType());
        layoutConfigVo.setContactRequire(data.getContactRequire());
        layoutConfigVo.setContactRecordType(data.getContactRecordType());
        layoutConfigVo.setContactDetailLayoutApiName(data.getContactDetailLayoutApiName());
        layoutConfigVo.setMapperApiName(data.getMapperApiName());
        layoutConfigVo.setMapperObjectFieldRequire(data.getAccountRequire());
        layoutConfigVo.setMapperObjectRecordType(data.getAccountRecordType());
        layoutConfigVo.setMapperObjectNameWriteRuleType(data.getAccountNameWriteRuleType());
        layoutConfigVo.setMapperObjectDetailLayoutApiName(data.getAccountDetailLayoutApiName());
        layoutConfigVo.setMapperObjectNameMatchWitch(data.getAccountNameMatchSwitch());
        return layoutConfigVo;
    }

    private LoginRegisterLayoutConfigVo toDefaultAccountLoginRegisterLayoutConfigVo(int tenantId, ERAccountActiveConfigData data, Integer owner) {
        LoginRegisterLayoutConfigVo layoutConfigVo = new LoginRegisterLayoutConfigVo();
        layoutConfigVo.setTenantId(String.valueOf(tenantId));
        layoutConfigVo.setOwners(Lists.newArrayList(owner));
        layoutConfigVo.setOwnerAllocateType(1);
        layoutConfigVo.setContactRequire(false);
        layoutConfigVo.setContactRecordType("default__c");
        layoutConfigVo.setContactDetailLayoutApiName("ContactObj_layout_generate_by_UDObjectServer__c");
        layoutConfigVo.setMapperApiName("AccountObj");
        layoutConfigVo.setMapperObjectFieldRequire(false);
        layoutConfigVo.setMapperObjectRecordType("default__c");
        layoutConfigVo.setMapperObjectNameWriteRuleType(1);
        layoutConfigVo.setMapperObjectDetailLayoutApiName("AccountObj_layout_generate_by_UDObjectServer__c");
        layoutConfigVo.setMapperObjectNameMatchWitch(false);
        return layoutConfigVo;
    }


    private LoginRegisterLayoutConfigVo toDefaultPartnerLoginRegisterLayoutConfigVo(int tenantId, ERAccountActiveConfigData data, Integer owner) {
        LoginRegisterLayoutConfigVo layoutConfigVo = new LoginRegisterLayoutConfigVo();
        layoutConfigVo.setTenantId(String.valueOf(tenantId));
        layoutConfigVo.setOwners(Lists.newArrayList(owner));
        layoutConfigVo.setOwnerAllocateType(1);
        layoutConfigVo.setContactRequire(false);
        layoutConfigVo.setContactRecordType("default__c");
        layoutConfigVo.setContactDetailLayoutApiName("ContactObj_layout_generate_by_UDObjectServer__c");
        layoutConfigVo.setMapperApiName("PartnerObj");
        layoutConfigVo.setMapperObjectFieldRequire(false);
        layoutConfigVo.setMapperObjectRecordType("default__c");
        layoutConfigVo.setMapperObjectNameWriteRuleType(1);
        layoutConfigVo.setMapperObjectDetailLayoutApiName("PartnerObj_layout_generate_by_UDObjectServer__c");
        layoutConfigVo.setMapperObjectNameMatchWitch(false);
        return layoutConfigVo;
    }

    @PostMapping(value = "/initEnterpriseLinkWxServiceMetaDataLoginRegisterConfig")
    public Result<Map<String, Object>> initEnterpriseLinkWxServiceMetaDataLoginRegisterConfig(@RequestBody BaseFlushArg arg) {
        if (!BooleanUtils.isTrue(arg.getIsInitAll()) && CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newSuccess();
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            arg.setEas(listAllUpstreamEas());
        }
        List<String> failedEas = Lists.newArrayList();
        for (String upstreamEa : arg.getEas()) {
            try {
                int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
                List<EnterpriseLinkWxServiceMetaData> enterpriseLinkWxServiceMetaDatas = enterpriseLinkWxServiceMetaDataDaoOldManager.listByUpstreamEa(tenantId, upstreamEa);
                if (CollectionUtils.isEmpty(enterpriseLinkWxServiceMetaDatas)) {
                    continue;
                }
                ListLoginRegisterConfigsArg configsArg = new ListLoginRegisterConfigsArg();
                configsArg.setLinkAppId("all");
                configsArg.setPageNumber(1);
                configsArg.setPageSize(10);
                Result<ListLoginRegisterConfigsResult> loginRegisterConfigsResult = loginRegisterConfigService.listLoginRegisterConfigs(String.valueOf(tenantId), configsArg);
                ListLoginRegisterConfigsResult configResult = loginRegisterConfigsResult.getData();
                if (!loginRegisterConfigsResult.isSuccess() || CollectionUtils.isEmpty(configResult.getDataList())) {
                    failedEas.add(upstreamEa);
                    log.warn("initEnterpriseLinkWxServiceMetaDataLoginRegisterConfig -> listLoginRegisterConfigs fail. ea={}, result={}", upstreamEa, loginRegisterConfigsResult);
                }
                if (CollectionUtils.isEmpty(configResult.getDataList())) {
                    log.warn("initEnterpriseLinkWxServiceMetaDataLoginRegisterConfig -> listLoginRegisterConfigs is empty. ea={}, result={}", upstreamEa, loginRegisterConfigsResult);
                    continue;
                }
                for (EnterpriseLinkWxServiceMetaData data : enterpriseLinkWxServiceMetaDatas) {
                    List<LoginRegisterConfigVO> configs = configResult.getDataList()
                            .stream()
                            .filter(x -> x.getMapperObjectApiName().equals(data.getMapperApiName()))
                            .filter(x -> x.getIdentityType().equals(data.getIdentityType()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(configs)) {
                        failedEas.add(upstreamEa);
                        log.warn("initEnterpriseLinkWxServiceMetaDataLoginRegisterConfig fail, LoginRegisterConfigVO is null. ea={}, result={}", upstreamEa, loginRegisterConfigsResult);
                        continue;
                    }
                    if (BooleanUtils.isTrue(arg.getExec())) {
                        LoginRegisterConfigVO configVO = configs.get(0);
                        enterpriseLinkWxServiceMetaDataDaoOldManager.updateLoginRegisterConfigId(data.get_id(), configVO.getId());
                    }
                }
            } catch (Exception e) {
                failedEas.add(upstreamEa);
                log.warn("initEnterpriseLinkWxServiceMetaDataLoginRegisterConfig error. ea={}", upstreamEa, e);
            }
        }
        Map<String, Object> resMap = Maps.newHashMap();
        resMap.put("failedEas", failedEas);
        return Result.newSuccess(resMap);
    }

    @PostMapping(value = "/flushEnterpriseIdField")
    public Result<Map<String, Object>> flushEnterpriseIdField(@RequestBody BaseFlushArg arg) {
        if (!BooleanUtils.isTrue(arg.getIsInitAll()) && CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newSuccess();
        }
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            arg.setEas(listAllUpstreamEas());
        }
        List<String> failedEas = Lists.newArrayList();
        for (String upstreamEa : arg.getEas()) {
            try {
                flushEnterpriseId(upstreamEa, arg.getExec());
            } catch (Throwable throwable) {
                log.warn("", throwable);
                failedEas.add(upstreamEa);
            }
        }
        Map<String, Object> resMap = Maps.newHashMap();
        resMap.put("failedEas", failedEas);
        return Result.newSuccess(resMap);
    }

    public Result<Void> flushEnterpriseId(String upstreamEa, boolean execute) {
        int upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
        User user = User.systemUser(String.valueOf(upstreamEi));

        EnterpriseRelationQueryArg queryArg = new EnterpriseRelationQueryArg();
        queryArg.setHasFsAccount(true);
        queryArg.setOffset(0);
        queryArg.setLimit(5000);

        QueryResult<IObjectData> queryResult = enterpriseRelationDao.getQueryResult(user, queryArg);

        if (queryResult.getTotalNumber() <= 0) {
            return Result.newResult(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getErrorMessage());
        }

        for (IObjectData data : queryResult.getData()) {
            EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(data);
            if (!enterpriseRelationObj.getHasFsAccount() && org.apache.commons.lang.StringUtils.isBlank(enterpriseRelationObj.getEnterpriseAccount())) {
                continue;
            }

            // 已补完数据直接过滤
            if (enterpriseRelationObj.getEnterpriseId() != null) {
                continue;
            }

            Integer enterpriseId = eieaConverter.enterpriseAccountToId(enterpriseRelationObj.getEnterpriseAccount());
            data.set(EnterpriseRelationObjConstant.ENTERPRISE_ID, enterpriseId);

            if (execute) {
                IObjectData updateObjectData = serviceFacade.batchUpdateByFields(user, Collections.singletonList(data), Collections.singletonList(EnterpriseRelationObjConstant.ENTERPRISE_ID)).get(0);
                log.info("flushEnterpriseId-updateObjectData:{}", updateObjectData);
            } else {
                log.info("flushEnterpriseId-ea:{}, ei:{}", enterpriseRelationObj.getEnterpriseAccount(), enterpriseRelationObj.getEnterpriseId());
            }
        }

        return Result.newSuccess();
    }


    @RequestMapping(value = "/flushErDepartmentCodeUnion")
    public void flushErDepartmentCodeUnion(@RequestBody BaseFlushArg arg) {
        if (!BooleanUtils.isTrue(arg.getIsInitAll()) && CollectionUtils.isEmpty(arg.getTenantIds())) {
            log.warn("flushErDepartmentCodeUnion ea is empty");
            return;
        }


        List<Set<String>> tenantIds = null;
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            try {
                tenantIds = flashCommonService.getAllCrmTenants("biz_er_department", "tenant_id");
            } catch (Exception ex) {
                log.warn("getAllCrmTenants error ", ex);
            }
        } else if (ObjectUtils.isNotEmpty(arg.getTenantIds())) {
            tenantIds = new ArrayList<>();
            tenantIds.add(arg.getTenantIds());
        }

        if (ObjectUtils.isEmpty(tenantIds)) {
            log.warn("flushErDepartmentCodeUnion tenantIds is empty");
            return;
        }

        for (Set<String> currentTenantIds : tenantIds) {
            for (String currentTenantId : currentTenantIds) {

                try {
                    if (tenantService.isInvalidTenant(currentTenantId)) {
                        rateLimiter.acquire(1);
                        continue;
                    }

                    String updateEmptyValueSQL = String.format(UPDATE_EMPTY_DEPARTMENT_CODE_SQL, currentTenantId, 100);
                    while (true) {
                        int effect = dataMapper.setTenantId(currentTenantId).updateBySql(updateEmptyValueSQL);
                        if (effect <= 0) {
                            break;
                        }
                        rateLimiter.acquire(effect);
                    }

                    String deleteSQL = String.format(DELETE_UNIQUE_FIELD_SQL, currentTenantId, 100);
                    while (true) {
                        int effect = objectDataServiceImpl.deleteBySql(currentTenantId, deleteSQL);
                        if (effect <= 0) {
                            break;
                        }
                        rateLimiter.acquire(effect);
                    }

                    AtomicReference<String> lastId = new AtomicReference<>();
                    do {
                        String sql = ObjectUtils.isEmpty(lastId.get()) ?
                                String.format(FIND_DEPARTMENT_CODE_SQL, currentTenantId, 100) :
                                String.format(FIND_DEPARTMENT_CODE_SQL_2, currentTenantId, lastId.get(), 100);
                        List<Map> dataList = objectDataServiceImpl.findBySql(currentTenantId, sql);
                        if (ObjectUtils.isEmpty(dataList)) {
                            break;
                        }
                        rateLimiter.acquire(dataList.size());

                        List<Unique> uniqueList = new ArrayList<>();
                        for (Map<String, Object> data : dataList) {
                            String id = MapUtils.getString(data, "id");
                            lastId.set(id);
                            String departmentCode = MapUtils.getString(data, ErDepartmentObjConstant.DEPARTMENT_CODE);

                            Unique unique = new Unique();
                            unique.setUniqueId(IdGenerator.get());
                            unique.setDataId(id);
                            unique.setFieldName(ErDepartmentObjConstant.DEPARTMENT_CODE);
                            unique.setValue(departmentCode);
                            unique.setDescribeApiName(ErDepartmentObjConstant.API_NAME);
                            unique.setTenantId(currentTenantId);
                            uniqueList.add(unique);
                        }
                        uniqueMapper.setTenantId(currentTenantId).batchInsert(uniqueList);
                        rateLimiter.acquire(dataList.size());
                    } while (ObjectUtils.isNotEmpty(lastId.get()));

                    //如果自定义过描述了，刷描述
                    Map<String, IObjectDescribe> objects = serviceFacade.findObjects(currentTenantId, Lists.newArrayList(ErDepartmentObjConstant.API_NAME));
                    IObjectDescribe objectDescribe = org.apache.commons.collections4.MapUtils.emptyIfNull(objects).get(ErDepartmentObjConstant.API_NAME);
                    if (objectDescribe != null && BooleanUtils.isTrue(objectDescribe.isUdef())) {
                        IFieldDescribe departmentCodeField = objectDescribe.getFieldDescribe(ErDepartmentObjConstant.DEPARTMENT_CODE);
                        String updateSql = String.format(UPDATE_FIELD_SQL, currentTenantId, departmentCodeField.getId());
                        dataMapper.setTenantId(currentTenantId).updateBySql(updateSql);
                        baseObjectDescribeService.purgeDescribeCache(currentTenantId, objectDescribe.getApiName());
                    }
                } catch (Exception ex) {
                    log.warn("refresh tenantId error {} ", currentTenantId, ex);
                }
            }
        }
    }


    @RequestMapping(value = "/flushStartStopTime")
    public void flushStartStopTime(@RequestBody BaseFlushArg arg) {
        if (!BooleanUtils.isTrue(arg.getIsInitAll()) && CollectionUtils.isEmpty(arg.getTenantIds())) {
            log.warn("flushErDepartmentCodeUnion ea is empty");
            return;
        }


        List<Set<String>> tenantIds = null;
        if (BooleanUtils.isTrue(arg.getIsInitAll())) {
            try {
                tenantIds = flashCommonService.getAllCrmTenants("biz_er_department", "tenant_id");
            } catch (Exception ex) {
                log.warn("getAllCrmTenants error ", ex);
            }
        } else if (ObjectUtils.isNotEmpty(arg.getTenantIds())) {
            tenantIds = new ArrayList<>();
            tenantIds.add(arg.getTenantIds());
        }

        if (ObjectUtils.isEmpty(tenantIds)) {
            log.warn("flushErDepartmentCodeUnion tenantIds is empty");
            return;
        }

        for (Set<String> currentTenantIds : tenantIds) {
            for (String currentTenantId : currentTenantIds) {

                try {
                    if (tenantService.isInvalidTenant(currentTenantId)) {
                        rateLimiter.acquire(1);
                        continue;
                    }


                    AtomicReference<String> lastId = new AtomicReference<>();
                    do {
                        String sql = ObjectUtils.isEmpty(lastId.get()) ?
                                String.format(SELECT_ENTERPRISE_RELATION_START_TIME_IS_NULL_SQL, currentTenantId, 100) :
                                String.format(SELECT_ENTERPRISE_RELATION_START_TIME_IS_NULL_SQL_2, currentTenantId, lastId.get(), 100);

                        List<Map> datas = dataMapper.setTenantId(currentTenantId).findBySql(sql);
                        if (ObjectUtils.isEmpty(datas)) {
                            break;
                        }
                        rateLimiter.acquire(datas.size());


                        List<String> updateSqlList = new ArrayList<>();
                        for (Map data : datas) {
                            String id = MapUtils.getString(data, "id");
                            lastId.set(MapUtils.getString(data, "id"));

                            EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(data);
                            Long startTime = enterpriseRelationObj.getCreateTime();
                            Long stopTime = null;
                            if (enterpriseRelationObj.getRelationType() == EnterpriseRelationType.RELIVE.getType()) {
                                stopTime = enterpriseRelationObj.getLastModifiedTime();
                            }
                            String updateSQl = String.format(UPDATE_ENTERPRISE_RELATION_START_STOP_TIME_SQL, startTime, stopTime, currentTenantId, id);
                            updateSqlList.add(updateSQl);
                        }

                        if (ObjectUtils.isNotEmpty(updateSqlList)) {
                            String fullUpdateSql = Joiner.on(";").join(updateSqlList);
                            dataMapper.setTenantId(currentTenantId).updateBySql(fullUpdateSql);
                            rateLimiter.acquire(updateSqlList.size());
                        }
                    } while (ObjectUtils.isNotEmpty(lastId.get()));


                    AtomicReference<String> employeeLastId = new AtomicReference<>();
                    do {
                        String sql = ObjectUtils.isEmpty(employeeLastId.get()) ?
                                String.format(SELECT_PUBLIC_EMPLOYEE_START_TIME_IS_NULL_SQL, currentTenantId, 100) :
                                String.format(SELECT_PUBLIC_EMPLOYEE_START_TIME_IS_NULL_SQL_2, currentTenantId, employeeLastId.get(), 100);


                        List<Map> datas = dataMapper.setTenantId(currentTenantId).findBySql(sql);
                        if (ObjectUtils.isEmpty(datas)) {
                            break;
                        }
                        rateLimiter.acquire(datas.size());


                        List<String> updateSqlList = new ArrayList<>();
                        for (Map data : datas) {
                            String id = MapUtils.getString(data, "id");
                            employeeLastId.set(MapUtils.getString(data, "id"));

                            PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(data);
                            Long startTime = publicEmployeeObj.getCreateTime();
                            Long stopTime = null;
                            if (publicEmployeeObj.getType() == PublicEmployeeTypeEnum.TYPE_NOT_PUBLIC_EMPLOYEE.getType()) {
                                stopTime = publicEmployeeObj.getLastModifiedTime();
                            }
                            String updateSQl = String.format(UPDATE_PUBLIC_EMPLOYEE_START_STOP_TIME_SQL, startTime, stopTime, currentTenantId, id);
                            updateSqlList.add(updateSQl);
                        }

                        if (ObjectUtils.isNotEmpty(updateSqlList)) {
                            String fullUpdateSql = Joiner.on(";").join(updateSqlList);
                            dataMapper.setTenantId(currentTenantId).updateBySql(fullUpdateSql);
                            rateLimiter.acquire(updateSqlList.size());
                        }
                    } while (ObjectUtils.isNotEmpty(employeeLastId.get()));
                } catch (Exception ex) {
                    log.warn("refresh tenantId error {} ", currentTenantId, ex);
                    continue;
                }
            }
        }
    }

}
