package com.facishare.rest.account.innerrest;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.cache.RedisCacheKeyBuilder;
import com.facishare.enterprise.common.constant.PublicEmployeeTypeEnum;
import com.facishare.enterprise.common.constant.*;
import com.facishare.enterprise.common.enums.BusinessErrorEnum;
import com.facishare.enterprise.common.enums.ErBindThirdAccountTypeEnum;
import com.facishare.enterprise.common.enums.ObjectLifeStatusEnum;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.model.OuterRoleVo;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.*;
import com.facishare.er.api.model.EmployeeInfo;
import com.facishare.er.api.model.EnterpriseMetaData;
import com.facishare.er.api.model.arg.MergeAppRoleAssignsArg;
import com.facishare.er.api.model.data.UserRoleData;
import com.facishare.er.api.model.enums.AccessLevelEnum;
import com.facishare.er.api.model.result.BatchGetOuterAccountResult;
import com.facishare.er.api.model.vo.*;
import com.facishare.fsc.api.model.CreateFileShareId;
import com.facishare.fsc.api.service.SharedFileService;
import com.facishare.global.rest.arg.DownstreamEaArg;
import com.facishare.global.rest.arg.DownstreamOuterTenantIdArg;
import com.facishare.global.rest.common.HeaderObj;
import com.facishare.global.rest.dao.mongo.FxiaokeEnterpriseAssociationEntityDao;
import com.facishare.global.rest.dao.mongo.WechatOpenIdPhoneMapperDao;
import com.facishare.global.rest.model.entity.WechatAppIdAndSecret;
import com.facishare.global.rest.model.entity.WechatOpenIdPhoneMapper;
import com.facishare.global.rest.service.GlobalService;
import com.facishare.linkapp.api.arg.LinkAppIdArg;
import com.facishare.linkapp.api.result.UpstreamEmployeeRolesVo;
import com.facishare.linkapp.api.service.HttpUpstreamService;
import com.facishare.linkapp.api.service.LinkAppOuterRoleService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.RequestContext.RequestContextBuilder;
import com.facishare.paas.appframework.core.model.RequestContext.RequestSource;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.timezone.client.TimezoneService;
import com.facishare.paas.timezone.client.UserInfo;
import com.facishare.rest.account.action.*;
import com.facishare.rest.account.common.DuplicateException;
import com.facishare.rest.account.dao.EnterpriseRelationDao;
import com.facishare.rest.account.dao.PublicEmployeeDao;
import com.facishare.rest.account.dao.data.EnterpriseRelationQueryArg;
import com.facishare.rest.account.dao.data.PublicEmployeeQueryArg;
import com.facishare.rest.account.manager.*;
import com.facishare.rest.account.manager.data.SendSmsEmployeeData;
import com.facishare.rest.account.remote.CrmManager;
import com.facishare.rest.account.remote.FxiaokeAccountManager;
import com.facishare.rest.account.remote.OuterRoleRemoteBaseManager;
import com.facishare.rest.account.remote.WeChatManager;
import com.facishare.rest.account.service.*;
import com.facishare.rest.account.util.ConfigUtil;
import com.facishare.rest.account.util.PasswordLevelUtil;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseRunStatusArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseRunStatusResult;
import com.facishare.uc.api.model.fscore.RunStatus;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.userlogin.api.service.RSAService;
import com.facishare.wechat.dubborestouterapi.service.union.EaBindInfoRestService;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.crmrestapi.common.contants.*;
import com.fxiaoke.crmrestapi.result.FindFunctionalCurrencyResult;
import com.fxiaoke.crmrestapi.result.MultiCurrencyStatusResult.Status;
import com.fxiaoke.crmrestapi.service.CurrencyService;
import com.fxiaoke.enterpriserelation.objrest.arg.*;
import com.fxiaoke.enterpriserelation.objrest.common.*;
import com.fxiaoke.enterpriserelation.objrest.result.*;
import com.fxiaoke.enterpriserelation.objrest.common.IdLongData;
import com.fxiaoke.enterpriserelation.objrest.common.IntResult;
import com.fxiaoke.enterpriserelation.objrest.common.ListMapResult;
import com.fxiaoke.enterpriserelation.objrest.common.LongOffsetResult;
import com.fxiaoke.enterpriserelation.objrest.common.MapOffsetResult;
import com.fxiaoke.enterpriserelation.objrest.result.*;
import com.fxiaoke.enterpriserelation.objrest.result.BatchGetOuterByRolesResult.OuterUserRoleData;
import com.fxiaoke.enterpriserelation.objrest.result.UnboundWxServiceEmpCountVo;
import com.fxiaoke.enterpriserelation.objrest.result.UnboundWxServiceEmpVo;
import com.fxiaoke.enterpriserelation.objrest.result.BatchGetOuterByRolesResult.OuterUserRoleData.SimpleOuterUserData;
import com.fxiaoke.enterpriserelation.objrest.result.BatchGetOuterByRolesResult.OuterUserRoleData.SimpleOuterUserData;
import com.fxiaoke.enterpriserelation.objrest.result.vo.EaEmpVo;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.fxiaoke.enterpriserelation.objrest.service.PublicEmployeeObjService;
import com.fxiaoke.enterpriserelation2.arg.InvalidSessionForResetPasswordArg;
import com.fxiaoke.enterpriserelation2.service.AuthService;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.enterprise.common.constant.BigDataConstant.MAX_QUERY_RECORD_LIMIT;

@Slf4j
@Service
public class PublicEmployeeObjInnerServiceImpl implements PublicEmployeeObjService {
    @Autowired
    private ActionCallManager actionCallManager;
    @Autowired
    private PublicEmployeeManager publicEmployeeManager;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private PublicEmployeeDao publicEmployeeDao;
    @Autowired
    private EnterpriseRelationDao enterpriseRelationDao;
    @Autowired
    private FxiaokeEnterpriseAssociationEntityDao fxiaokeEnterpriseAssociationEntityDaoOldManager;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private LinkAppFunctionPermissionManager linkAppFunctionPermissionManager;
    @Autowired
    private EnterpriseLinkWxServiceConfigService enterpriseLinkWxServiceConfigService;
    @Autowired
    private GlobalService globalService;
    @Autowired
    private EnterpriseMetaDataManager enterpriseMetaDataManager;
    @Autowired
    private EnterpriseRelationAddActionService enterpriseRelationAddActionService;
    @Autowired
    private PublicEmployeeAddActionService publicEmployeeAddActionService;
    @Autowired
    private FxiaokeAccountManager fxiaokeAccountManager;
    @Autowired
    private EnterpriseRelationObjService enterpriseRelationObjService;
    @Autowired
    private EnterpriseRelationManager enterpriseRelationManager;
    @Autowired
    private LinkAppOuterRoleService linkAppOuterRoleService;
    @Autowired
    private HttpUpstreamService httpUpstreamService;
    @Autowired
    private OuterRoleRemoteBaseManager outerRoleRemoteBaseManager;
    @Autowired
    private ErOuterRoleService outerRoleService;
    @Autowired
    private FxiaokeEmployeeAssociationService fxiaokeEmployeeAssociationService;
    @Autowired
    private CrmManager crmManager;
    @Autowired
    private TimezoneService timezoneService;
    @Autowired
    private CurrencyService currencyService;
    @Autowired
    private SharedFileService sharedFileService;
    @Autowired
    private RSAService rsaService;
    @Autowired
    private AuthService authService;
    @Autowired
    private WeChatManager weChatManager;
    @Autowired
    private MiniProgramAuthGrayService miniProgramAuthGrayService;
    @Autowired
    private WechatOpenIdPhoneMapperDao wechatOpenIdPhoneMapperDaoOldManager;
    @Autowired
    private EaBindInfoRestService eaBindInfoRestService;
    @Autowired
    @Qualifier("jedisSupport")
    private MergeJedisCmd mergeJedisCmd;
    @Autowired
    private GlobalMetaManager globalMetaManager;
    @Autowired
    private EnterpriseRelationAccountService enterpriseRelationAccountService;

    @Override
    public PublicEmployeeStartActionResult startAction(ServiceContext serviceContext, PublicEmployeeStartActionArg arg) {
        User user = serviceContext.getUser();
        PublicEmployeeStartPublicEmployeeAction.Result actionResult = actionCallManager.triggerPublicEmployeeStartAction(user, arg.getDownstreamOuterUid(), arg.getOuterRoleIds(), arg.isIgnoreSelfFilter());
        PublicEmployeeStartActionResult result = new PublicEmployeeStartActionResult();
        result.setObjectData(actionResult.getObjectData());
        return result;
    }

    @Override
    public EmployeeConfigResult getEmployeeConfig(ServiceContext serviceContext, GetEmployeeConfigArg arg) {
        String upstreamEi = serviceContext.getTenantId();
        String redisCacheKey = RedisCacheKeyBuilder.buildGetEmployeeConfigCacheKey(upstreamEi, arg.getOuterUid());
        String redisRes = mergeJedisCmd.get(redisCacheKey);
        if (StringUtils.isNotBlank(redisRes)) {
            return JsonUtil.fromJson(redisRes, EmployeeConfigResult.class);
        } else {
            EmployeeConfigResult res = null;
            if (!GuestorIdentityInfoConstants.OUTER_UID.equals(arg.getOuterUid())) {
                PublicEmployeeObj objectData = publicEmployeeDao.getById(User.systemUser(upstreamEi), arg.getOuterUid());
                if (objectData == null) {
                    res = new EmployeeConfigResult();
                    res.setErrorCode(ResultCode.PARAMS_ERROR.getErrorCode());
                    res.setErrorMessage("can't find data by outerUid=" + arg.getOuterUid());
                    return res;
                }
                res = convertPublicEmployeeObj2EmployeeConfigResult(objectData);
            } else {
                res = getGuestorEmployeeConfigResult(arg.getDefaultLang());
            }
            res.setErrorCode(ResultCode.SUCCESS.getErrorCode());
            mergeJedisCmd.setex(redisCacheKey, 120L, JsonUtil.toJson(res));
            return res;
        }
    }

    private EmployeeConfigResult getGuestorEmployeeConfigResult(String defaultLang) {
        EmployeeConfigResult res = new EmployeeConfigResult();
        res.setOuterTenantId(GuestorIdentityInfoConstants.OUTER_TENANTID);
        res.setOuterUid(GuestorIdentityInfoConstants.OUTER_UID);
        res.setErLanguage(defaultLang);
        res.setErTimeZone("Asia/Shanghai");
        res.setErMcCurrency("CNY");
        return res;
    }

    private EmployeeConfigResult convertPublicEmployeeObj2EmployeeConfigResult(PublicEmployeeObj objectData) {
        PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(objectData);
        EmployeeConfigResult res = new EmployeeConfigResult();
        res.setOuterTenantId(publicEmployeeObj.getOuterTenantId());
        res.setOuterUid(publicEmployeeObj.getOuterUid());
        res.setErLanguage(publicEmployeeObj.getLanguage() == null ? "zh-CN" : publicEmployeeObj.getLanguage());
        res.setErTimeZone(publicEmployeeObj.getTimeZone() == null ? "Asia/Shanghai" : publicEmployeeObj.getTimeZone());
        res.setErMcCurrency(publicEmployeeObj.getMcCurrency() == null ? "CNY" : publicEmployeeObj.getMcCurrency());
        return res;
    }

    @Override
    public PublicEmployeeStopActionResult stopAction(ServiceContext serviceContext, PublicEmployeeStopActionArg arg) {
        User user = serviceContext.getUser();
        PublicEmployeeStopPublicEmployeeAction.Result actionResult = actionCallManager.triggerPublicEmployeeStopAction(user, String.valueOf(arg.getDownstreamOuterUid()), arg.isIgnoreSelfFilter());
        PublicEmployeeStopActionResult result = new PublicEmployeeStopActionResult();
        result.setObjectData(actionResult.getObjectData());
        return result;
    }

    @Override
    public EditActionResult editAction(ServiceContext serviceContext, EditActionArg arg) {
        User user = serviceContext.getUser();
        PublicEmployeeEditAction.Result actionResult = actionCallManager.triggerPublicEmployeeEditAction(user, ObjectDataDocument.of(arg.getObjectData()), arg.getIncrementUpdate(), arg.getAttribute());
        EditActionResult result = new EditActionResult();
        result.setObjectData(actionResult.getObjectData());
        result.setIsDuplicate(actionResult.getIsDuplicate());
        return result;
    }

    @Override
    public String updateId(ServiceContext serviceContext, IdLongData idLongData) {
        return publicEmployeeManager.updateId(serviceContext.getUser().getTenantIdInt(), idLongData.getId() + "");
    }

    @Override
    public AddActionResult addAction(ServiceContext serviceContext, AddActionArg arg) {
        User user = serviceContext.getUser();
        Map<String, Object> attributes = new HashMap<>();
        if (BooleanUtils.isTrue(arg.getHasFsAccount())) {
            attributes.put(EnterpriseRelationObjConstant.HAS_FS_ACCOUNT, true);
        }
        if (BooleanUtils.isTrue(arg.getNoCheckMapperMobileOrLoginEmail())) {
            attributes.put(PublicEmployeeAddAction.NOT_CHECK_MOBILE_OR_LOGIN_EMAIL_ARG, true);
        }
        PublicEmployeeAddAction.Result actionResult = actionCallManager.triggerPublicEmployeeAddAction(user, ObjectDataDocument.of(arg.getObjectData()), attributes);
        AddActionResult result = new AddActionResult();
        result.setObjectData(actionResult.getObjectData());
        result.setIsDuplicate(actionResult.getIsDuplicate());
        return result;
    }

    @Override
    public List<String> batchGetOuterUids(ServiceContext serviceContext, Long downstreamOuterTenantId) {
        List<String> ids = Lists.newArrayList();
        List<IObjectData> iObjectDatas = publicEmployeeDao.listByOuterTenantId(serviceContext.getUser(), downstreamOuterTenantId);
        if (!CollectionUtil.isEmpty(iObjectDatas)) {
            ids = iObjectDatas.stream().map(x -> x.getId()).collect(Collectors.toList());
        }
        return ids;
    }

    @Override
    public BatchGetObjectDatas batchGetObjectDatasIncludeStoppedAndInvalid(ServiceContext serviceContext, Long downstreamOuterTenantId) {
        BatchGetObjectDatas result = new BatchGetObjectDatas();
        List<Map<String, Object>> objectDatas = Lists.newArrayList();
        List<Integer> types = Lists.newArrayList(PublicEmployeeTypeEnum.TYPE_IS_PUBLIC_EMPLOYEE.getType(), PublicEmployeeTypeEnum.TYPE_NOT_PUBLIC_EMPLOYEE.getType());
        List<IObjectData> iObjectDatas = publicEmployeeDao.listByOuterTenantId(serviceContext.getUser(), downstreamOuterTenantId, types, true);
        if (!CollectionUtil.isEmpty(iObjectDatas)) {
            objectDatas = iObjectDatas.stream().map(ObjectDataDocument::of).collect(Collectors.toList());
        }
        result.setObjectDatas(objectDatas);
        return result;
    }

    @Override
    public BatchGetObjectDatas batchGetObjectDatasIgnoreBaseVisibleRangeIncludeStoppedAndInvalid(ServiceContext serviceContext, Long downstreamOuterTenantId) {
        BatchGetObjectDatas result = new BatchGetObjectDatas();
        List<Map<String, Object>> objectDatas = Lists.newArrayList();
        List<IObjectData> iObjectDatas = publicEmployeeDao.listByOuterTenantIdIgnoreBaseVisibleRange(serviceContext.getUser(), downstreamOuterTenantId, null, true);
        if (!CollectionUtil.isEmpty(iObjectDatas)) {
            objectDatas = iObjectDatas.stream().map(ObjectDataDocument::of).collect(Collectors.toList());
        }
        result.setObjectDatas(objectDatas);
        return result;
    }


    @Override
    public ListByConditionResult listByCondition(ServiceContext serviceContext, ListPublicEmployeeObjByConditionArg arg) {
        PublicEmployeeQueryArg queryArg = com.facishare.enterprise.common.util.BeanUtil.copy(PublicEmployeeQueryArg.class, arg);
        if (arg.getTypes() == null && queryArg.getTypes() != null) {//测试代码，验证后删除
            log.error("copy error,arg={},queryArg={}", arg, queryArg);
            queryArg.setTypes(null);
        }
        User user = serviceContext.getUser();
        if (SuperUserConstants.USER_ID.equals(user.getUserIdInt()) && BooleanUtils.isTrue(queryArg.getUseDataPermission())) {
            log.error("listByCondition, userId = -10000 & useDataPermission = true");
        }
        if (!user.isOutUser() && BooleanUtils.isTrue(queryArg.getUseDataPermission())) {
            boolean isRelationAdmin = fxiaokeAccountManager.isRelationAdmin(user);
            if (isRelationAdmin) {
                queryArg.setUseDataPermission(false);
            }
        }
        QueryResult<IObjectData> query = publicEmployeeDao.getQuery(serviceContext.getUser(), queryArg);
        List<IObjectData> iObjectDatas = query.getData();
        List<Map<String, Object>> collect = iObjectDatas.stream().map(ObjectDataDocument::of).collect(Collectors.toList());
        ListByConditionResult res = new ListByConditionResult();
        res.setObjectDatas(collect);
        res.setTotal(query.getTotalNumber());
        return res;
    }

    @Override
    public ListByConditionResult listByConditionWithLimitCheck(ServiceContext serviceContext, ListPublicEmployeeObjByConditionArg arg) {
        PublicEmployeeQueryArg queryArg = com.facishare.enterprise.common.util.BeanUtil.copy(PublicEmployeeQueryArg.class, arg);
        if (arg.getTypes() == null && queryArg.getTypes() != null) {//测试代码，验证后删除
            log.error("copy error,arg={},queryArg={}", arg, queryArg);
            queryArg.setTypes(null);
        }
        User user = serviceContext.getUser();
        if (SuperUserConstants.USER_ID.equals(user.getUserIdInt()) && BooleanUtils.isTrue(queryArg.getUseDataPermission())) {
            log.error("listByCondition, userId = -10000 & useDataPermission = true");
        }
        if (BooleanUtils.isTrue(queryArg.getUseDataPermission())) {
            boolean isRelationAdmin = fxiaokeAccountManager.isRelationAdmin(user);
            if (isRelationAdmin) {
                queryArg.setUseDataPermission(false);
            }
        }
        QueryResult<IObjectData> query = publicEmployeeDao.getQueryWithLimitCheck(serviceContext.getUser(), queryArg);
        List<IObjectData> iObjectDatas = query.getData();
        List<Map<String, Object>> collect = iObjectDatas.stream().map(ObjectDataDocument::of).collect(Collectors.toList());
        ListByConditionResult res = new ListByConditionResult();
        res.setObjectDatas(collect);
        res.setTotal(query.getTotalNumber());
        return res;
    }

    @Override
    public ListMapResult listBySearchTextAndEnterpriseName(ServiceContext serviceContext, ListBySearchTextAndEnterpriseNameArg arg) {
        List<IObjectData> iObjectDatas = publicEmployeeDao.listBySearchTextAndEnterpriseName(serviceContext.getUser(), arg.getTypes(), arg.getSearchStr(), arg.getOffset(), arg.getLimit());
        List<Map<String, Object>> mapList = iObjectDatas.stream().map(ObjectDataDocument::of).collect(Collectors.toList());
        return ListMapResult.newInstance(mapList);
    }

    @Override
    public GetObjectData getObjectData(ServiceContext serviceContext, GetObjectDataArg arg) {
        PublicEmployeeObj obj = publicEmployeeDao.getByDataLifeStatus(serviceContext.getUser(), arg.getId(), arg.getIncludeInvalid(), arg.getIncludeDeleted());
        GetObjectData result = new GetObjectData();
        result.setObjectData(ObjectDataDocument.of(obj));
        return result;
    }

    @Override
    public ListMapResult batchGetObjectDatas(ServiceContext serviceContext, IdsLongArg arg) {
        List<IObjectData> iObjectDatas = publicEmployeeDao.batchGetByIds(serviceContext.getUser(), arg.getIds());
        List<Map<String, Object>> mapList = iObjectDatas.stream().map(ObjectDataDocument::of).collect(Collectors.toList());
        return ListMapResult.newInstance(mapList);
    }

    @Override
    public GetPersonalErLocaleResult getPersonalErLocale(ServiceContext serviceContext, GetPersonalErLocaleArg getArg) {
        Long downstreamOuterTenantId = getArg.getDownstreamOuterTenantId();
        Long downstreamOuterUid = getArg.getDownstreamOuterUid();
        String defaultErLanguage = "zh-CN";
        if (downstreamOuterTenantId.equals(GuestorIdentityInfoConstants.OUTER_TENANTID)) {
            GetPersonalErLocaleResult r = new GetPersonalErLocaleResult();
            r.setErLocale(defaultErLanguage);
            return r;
        }
        GetObjectDataArg getObjectDataArg = GetObjectDataArg.newIncludeDeletedArg(downstreamOuterUid);
        PublicEmployeeObj publicEmployeeObjectData = PublicEmployeeObj.newInstance(getObjectData(serviceContext, getObjectDataArg).getObjectData());
        String publicEmployeeErLanguage = publicEmployeeObjectData.getLanguage();
        if (!Strings.isNullOrEmpty(publicEmployeeErLanguage)) {
            GetPersonalErLocaleResult r = new GetPersonalErLocaleResult();
            r.setErLocale(publicEmployeeErLanguage);
            return r;
        }
        String employeeId = publicEmployeeObjectData.getEmployeeId();
        if (StringUtils.isNotEmpty(employeeId)) {
            try {
                String downstreamEa = enterpriseRelationManager.getEaByOuterTenantId(serviceContext.getUser().getTenantIdInt(), publicEmployeeObjectData.getOuterTenantId());
                GetEnterpriseRunStatusArg arg = new GetEnterpriseRunStatusArg();
                arg.setEnterpriseAccount(downstreamEa);
                GetEnterpriseRunStatusResult getEnterpriseRunStatusResult = enterpriseEditionService.getEnterpriseRunStatus(arg);
                if (!getEnterpriseRunStatusResult.getRunStatus().equals(RunStatus.RUN_STATUS_NORMAL)) {
                    GetPersonalErLocaleResult r = new GetPersonalErLocaleResult();
                    r.setErLocale(defaultErLanguage);
                    return r;
                }

                int downEi = eieaConverter.enterpriseAccountToId(downstreamEa);
                IFilter filter = new com.facishare.paas.metadata.impl.search.Filter();
                filter.setFieldName("user_id");
                filter.setFieldValues(Lists.newArrayList(employeeId));
                filter.setOperator(Operator.EQ);
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.addFilters(Lists.newArrayList(filter));
                query.setLimit(1);
                query.setOffset(0);
                List<IObjectData> personnelObjDatas = serviceFacade.findBySearchQuery(User.systemUser(downEi + ""), "PersonnelObj", query).getData();
                if (!CollectionUtil.isEmpty(personnelObjDatas)) {
                    String language = (String) personnelObjDatas.get(0).get("language");
                    if (StringUtils.isNotBlank(language)) {
                        defaultErLanguage = language;
                    }
                }
            } catch (Exception e) {
                log.warn("get personal language error employee={}", publicEmployeeObjectData, e);
            }
        }
        GetPersonalErLocaleResult r = new GetPersonalErLocaleResult();
        r.setErLocale(defaultErLanguage);
        return r;
    }

    @Override
    public ListDownstreamEmployeeSimpleInfoByOuterUidsResult listDownstreamEmployeeSimpleInfoByOuterUids(ServiceContext serviceContext, OuterUidsArg arg) {
        ListDownstreamEmployeeSimpleInfoByOuterUidsResult res = new ListDownstreamEmployeeSimpleInfoByOuterUidsResult();
        List<DownstreamEmployeeSimpleInfoData> downstreamEmployeeSimpleInfoDataList = Lists.newArrayList();
        res.setDownstreamEmployeeSimpleInfoDataList(downstreamEmployeeSimpleInfoDataList);

        Set<Long> downstreamOuterUids = arg.getDownstreamOuterUids();
        if (CollectionUtils.isEmpty(downstreamOuterUids)) {
            return res;
        }
        List<PublicEmployeeObj> obj = publicEmployeeDao.batchGetByIds(User.systemUser(serviceContext.getTenantId()), arg.getDownstreamOuterUids()).stream().map(PublicEmployeeObj::newInstance)
                .collect(Collectors.toList());
        List<String> outerTenantIds = obj.stream().map(PublicEmployeeObj::getOuterTenantId).filter(Objects::nonNull).map(String::valueOf).distinct().collect(Collectors.toList());
        Map<Long, EnterpriseRelationObj> outerTenantIdObjMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(outerTenantIds)) {
            List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds(serviceContext.getTenantId(), outerTenantIds, EnterpriseRelationObjConstant.API_NAME);
            if (objectDataByIds != null) {
                outerTenantIdObjMap = objectDataByIds.stream().map(EnterpriseRelationObj::newInstance)
                        .collect(Collectors.toMap(EnterpriseRelationObj::getOuterTenantId, Function.identity(), (o1, o2) -> o1));
            }
        }

        for (PublicEmployeeObj publicEmployeeObj : obj) {
            DownstreamEmployeeSimpleInfoData downstreamEmployeeSimpleInfoData = new DownstreamEmployeeSimpleInfoData();
            Long outerTenantId = publicEmployeeObj.getOuterTenantId();
            EnterpriseRelationObj relationObj = outerTenantIdObjMap.get(outerTenantId);

            downstreamEmployeeSimpleInfoData.setOuterUid(publicEmployeeObj.getOuterUid());
            downstreamEmployeeSimpleInfoData.setOuterTenantId(publicEmployeeObj.getOuterTenantId());
            downstreamEmployeeSimpleInfoData.setEmployeeName(publicEmployeeObj.getName());
            downstreamEmployeeSimpleInfoData.setDownstreamEnterpriseName(relationObj == null ? publicEmployeeObj.getName() : relationObj.getName());
            downstreamEmployeeSimpleInfoData.setRelationType(relationObj == null ? publicEmployeeObj.getType() : relationObj.getRelationType());
            downstreamEmployeeSimpleInfoData.setType(publicEmployeeObj.getType());

            downstreamEmployeeSimpleInfoDataList.add(downstreamEmployeeSimpleInfoData);
        }
        return res;
    }

    @Override
    public IntResult updateDataManager(ServiceContext serviceContext, UpdateDataManagerArg arg) {
        int result = publicEmployeeDao.updateDataManager(serviceContext.getUser(), arg.getDownstreamOuterUids(), arg.getIsDataManager());
        return IntResult.newInstance(result);
    }

    @Override
    public IntResult updateRelationOwner(ServiceContext serviceContext, UpdateRelationOwnerArg arg) {
        publicEmployeeManager.updateOrChangeDownstreamRelationOwner(serviceContext.getUser(), arg.getDownstreamOuterTenantId(), arg.getDownstreamOuterUid(), true);
        return IntResult.newInstance(1);
    }

    @Override
    public QueryFriendEmployeesResult queryFriendEmployees(ServiceContext serviceContext, QueryFriendEmployeesArg arg) {
        QueryFriendEmployeesResult result = new QueryFriendEmployeesResult();
        String upstreamEa = getEa(serviceContext);
        String userId = getUserId(serviceContext);

        // 互联管理员身份校验
        boolean isRelationManger = fxiaokeAccountManager.isRelationAdmin(serviceContext.getUser());
        if (!isRelationManger) {
            throw new ValidateException(ResultCode.INSUFFICIENT_RELATION_ADMIN_READ_AUTHORITY.getErrorMessage());
        }

        // 获取上游元数据
        EnterpriseMetaData upstreamMetaData = enterpriseMetaDataManager.getMetaByAccount(upstreamEa);
        if (Objects.isNull(upstreamMetaData) || upstreamMetaData.getMaxRelationNumber() <= 0) {  //没有配额不返回数据
            result.setEaEmpVos(Lists.newArrayList());
            return result;
        }

        // 获取所有伙伴/下游，企业 * 员工 <= 2w
        List<IObjectData> allEnterpriseRelationObjectDatas = Lists.newArrayList();
        List<IObjectData> allPublicEmployeeObjectDatas = Lists.newArrayList();
        if (BooleanUtils.isTrue(arg.getGetAll())) {
            OffsetUtil.offsetFunction(1000, (offset, limit) -> {
                if (allEnterpriseRelationObjectDatas.size() >= MAX_QUERY_RECORD_LIMIT) {
                    return 0;
                }

                EnterpriseRelationQueryArg enterpriseRelationQueryArg = new EnterpriseRelationQueryArg();
                enterpriseRelationQueryArg.setRelationTypes(Lists.newArrayList(EnterpriseRelationType.FRIEND.getType()));
                enterpriseRelationQueryArg.setIdentityType(IdentityType.CORP.getType());
                enterpriseRelationQueryArg.setOffset(offset);
                enterpriseRelationQueryArg.setLimit(limit);
                List<IObjectData> enterpriseRelationObjectDatas = enterpriseRelationDao.listByQuery(serviceContext.getUser(), enterpriseRelationQueryArg);
                if (CollectionUtils.isEmpty(enterpriseRelationObjectDatas)) {
                    return 0;
                }

                allEnterpriseRelationObjectDatas.addAll(enterpriseRelationObjectDatas);
                return enterpriseRelationObjectDatas.size();
            });
        } else {
            ListAllDownstreamDetailWithDefaultMaxLimitArg listAllDownstreamDetailArg = new ListAllDownstreamDetailWithDefaultMaxLimitArg();
            listAllDownstreamDetailArg.setIdentityType(IdentityType.CORP.getType());
            ListAllDownstreamDetailWithDefaultMaxLimitResult listAllDownstreamDetailResult = enterpriseRelationObjService
                    .listAllDownstreamDetailWithDefaultMaxLimit(serviceContext, listAllDownstreamDetailArg);

            List<Map<String, Object>> enterpriseRelationMaps = listAllDownstreamDetailResult.getEnterpriseRelationObjectDatas();
            List<Map<String, Object>> publicEmployeeMaps = listAllDownstreamDetailResult.getPublicEmployeeObjectDatas();
            List<ObjectData> enterpriseRelationObjectDatas = enterpriseRelationMaps.stream().map(ObjectData::new).collect(Collectors.toList());
            List<ObjectData> publicEmployeeObjectDatas = publicEmployeeMaps.stream().map(ObjectData::new).collect(Collectors.toList());

            allEnterpriseRelationObjectDatas.addAll(enterpriseRelationObjectDatas);
            allPublicEmployeeObjectDatas.addAll(publicEmployeeObjectDatas);
        }

        if (CollectionUtils.isEmpty(allEnterpriseRelationObjectDatas)) {
            result.setEaEmpVos(com.beust.jcommander.internal.Lists.newArrayList());
            return result;
        }

        // 对接的上游互联企业也
        if (BooleanUtils.isTrue(arg.getGetAll())) {
            List<IObjectData> upstreamEnterpriseRelations = listAllUpstreamEnterpriseRelations(upstreamEa);
            allEnterpriseRelationObjectDatas.addAll(upstreamEnterpriseRelations);
        }

        // 获取对方对接人
        Map<Long, List<IObjectData>> destOuterTenantIdPublicEmployeeObjectDatasMap;
        if (!arg.getGetAll()) {
            destOuterTenantIdPublicEmployeeObjectDatasMap = allPublicEmployeeObjectDatas.stream().filter(x -> !BooleanUtils.isTrue((Boolean) x.get(PublicEmployeeObjConstant.QUOTA_EXPIRED)))
                    .collect(Collectors.groupingBy(x -> Long.parseLong((String) x.get(PublicEmployeeObjConstant.OUTER_TENANT_ID)), Collectors.mapping(x -> x, Collectors.toList())));
        } else {
            destOuterTenantIdPublicEmployeeObjectDatasMap = new HashMap<>();
        }

        // 获取对方管理模式
        // 无mapping关系的直接没有账号管理方式
        List<String> eas = allEnterpriseRelationObjectDatas.stream().filter(x -> !StringUtils.isEmpty((String) x.get(EnterpriseRelationObjConstant.ENTERPRISE_ACCOUNT)))
                .map(x -> (String) x.get(EnterpriseRelationObjConstant.ENTERPRISE_ACCOUNT)).collect(Collectors.toList());
        // 构建返回数据
        List<com.fxiaoke.enterpriserelation.objrest.result.vo.EaEmpVo> eaEmpVos = buildEaEmpVo(allEnterpriseRelationObjectDatas, destOuterTenantIdPublicEmployeeObjectDatasMap);
        result.setEaEmpVos(eaEmpVos);
        return result;
    }

    private List<IObjectData> listAllUpstreamEnterpriseRelations(String downstreamEa) {
        List<IObjectData> enterpriseRelationObjectDatas = Lists.newArrayList();
        Long downstreamOuterTenantId = fxiaokeEnterpriseAssociationEntityDaoOldManager.getOuterTenantIdByEa(downstreamEa);
        DownstreamOuterTenantIdArg downstreamOuterTenantIdArg = new DownstreamOuterTenantIdArg();
        downstreamOuterTenantIdArg.setDownstreamOuterTenantId(downstreamOuterTenantId);
        List<String> allUpstreamEas = globalService.listAllUpstreamEasByDownstreamOuterTenantId(HeaderObj.newInstance(), downstreamOuterTenantIdArg).getData();
        if (CollectionUtil.isEmpty(allUpstreamEas)) {
            return enterpriseRelationObjectDatas;
        }
        for (String ea : allUpstreamEas) {
            int tenantId = eieaConverter.enterpriseAccountToId(ea);
            SimpleEnterpriseData simpleEnterpriseData = CloudContextUtil.doInCloudContextWithReturn(tenantId + "", () -> fxiaokeAccountManager.getEnterpriseInfoByEA(ea));
            if (simpleEnterpriseData == null) {
                continue;
            }
            Long outerTenantId = fxiaokeEnterpriseAssociationEntityDaoOldManager.getOuterTenantIdByEa(ea);
            ObjectData objectData = new ObjectData();
            objectData.set(EnterpriseRelationObjConstant.ID, outerTenantId + "");
            objectData.set(EnterpriseRelationObjConstant.NAME, simpleEnterpriseData.getEnterpriseName());
            objectData.set(EnterpriseRelationObjConstant.DEST_SHORT_NAME, "");
            objectData.set(EnterpriseRelationObjConstant.DEST_SHORT_NAME_SPELL, "");
            enterpriseRelationObjectDatas.add(objectData);
        }
        return enterpriseRelationObjectDatas;
    }

    private List<EaEmpVo> buildEaEmpVo(List<IObjectData> enterpirseRelationObjectDatas, Map<Long, List<IObjectData>> destOuterTenantIdPublicEmployeeObjectDatasMap) {
        List<Long> allDestOuterUids = new ArrayList<>(enterpirseRelationObjectDatas.size() * 2);
        Map<Long, String> outerTenantIdEaMap = new HashMap<>();
        for (IObjectData relation : enterpirseRelationObjectDatas) {
            Long destOuterTenantId = Long.parseLong((String) relation.get(EnterpriseRelationObjConstant.ID));
            String destEa = (String) relation.get(EnterpriseRelationObjConstant.ENTERPRISE_ACCOUNT);
            outerTenantIdEaMap.put(destOuterTenantId, destEa);
            List<IObjectData> publicEmployeeObjectDatas = destOuterTenantIdPublicEmployeeObjectDatasMap.get(destOuterTenantId);
            if (CollectionUtils.isEmpty(publicEmployeeObjectDatas)) {
                continue;
            }
            List<Long> destOuterUids = publicEmployeeObjectDatas.stream().map(x -> Long.parseLong((String) x.get(PublicEmployeeObjConstant.ID))).collect(Collectors.toList());
            allDestOuterUids.addAll(destOuterUids);
        }
        //企业卡片缓存
        return enterpirseRelationObjectDatas.stream().map(relation -> {
            EaEmpVo empVo = new EaEmpVo();
            Long destOuterTenantId = Long.parseLong((String) relation.get(EnterpriseRelationObjConstant.ID));
            empVo.setOuterTenantId(destOuterTenantId);
            String destEa = outerTenantIdEaMap.get(destOuterTenantId);
            List<IObjectData> publicEmployeeObjectDatas = destOuterTenantIdPublicEmployeeObjectDatasMap.get(destOuterTenantId);
            if (Objects.nonNull(publicEmployeeObjectDatas)) {
                for (IObjectData publicEmployeeObjectData : publicEmployeeObjectDatas) {
                    if (destEa != null) {
                        publicEmployeeObjectData.set("enterprise_account", destEa);
                    }
                }
                List<Map<String, Object>> publicEmployeeMap = publicEmployeeObjectDatas.stream().map(ObjectDataDocument::of).collect(Collectors.toList());
                empVo.setPublicEmployeeObjectDatas(publicEmployeeMap);
            } else {
                empVo.setPublicEmployeeObjectDatas(Lists.newArrayList());
            }
            empVo.setManageMode(relation.get(EnterpriseRelationObjConstant.MANAGE_MODE, Integer.class));
            empVo.setEnterpriseName((String) relation.get(EnterpriseRelationObjConstant.NAME));
            empVo.setEnterpriseShortName((String) relation.get(EnterpriseRelationObjConstant.DEST_SHORT_NAME));
            empVo.setEnterpriseShortNameSpell((String) relation.get(EnterpriseRelationObjConstant.DEST_SHORT_NAME_SPELL));
            return empVo;
        }).collect(Collectors.toList());
    }

    /**
     * 从上游的视角，通过appid过滤下游对接人
     */
    @Override
    public QueryFriendEmployeeByAppIdResult queryFriendEmployeeByAppId(ServiceContext serviceContext, QueryFriendEmployeeByAppIdArg arg) {
        String upstreamEa = getEa(serviceContext);
        String userId = getUserId(serviceContext);

        QueryFriendEmployeeByAppIdResult queryFriendEmployeeByAppIdResult = new QueryFriendEmployeeByAppIdResult();
        queryFriendEmployeeByAppIdResult.setEaEmpVos(Lists.newArrayList());

        GetAccessLevelArg accessLevelArg = new GetAccessLevelArg();
        accessLevelArg.setSceneId(arg.getSceneId());
        accessLevelArg.setSceneParam(arg.getSceneParam());
        GetAccessLevelResult accessLevelResult = enterpriseRelationObjService.getAccessLevel(serviceContext, accessLevelArg);
        RelationViewPrivilege privilege = RelationViewPrivilege.getPrivilege(RelationViewPrivilege.Privilege.INVISIBLE);
        if (AccessLevelEnum.NONE.getType() == accessLevelResult.getAccessLevel()) {
            privilege = RelationViewPrivilege.getPrivilege(RelationViewPrivilege.Privilege.INVISIBLE);
        }
        if (AccessLevelEnum.ALL.getType() == accessLevelResult.getAccessLevel()) {
            privilege = RelationViewPrivilege.getPrivilege(RelationViewPrivilege.Privilege.ALL_VISIBLE);
        }
        if (AccessLevelEnum.DEFAULT.getType() == accessLevelResult.getAccessLevel()) {
            //检测操作者权限【0：不可见，1：自身对接范围可见，2：全部伙伴企业可见】
            privilege = checkAppPrivilege(arg.getAppId(), upstreamEa, Integer.valueOf(userId));
        }
        if (privilege.getPrivilege() == RelationViewPrivilege.Privilege.INVISIBLE) {
            return queryFriendEmployeeByAppIdResult;
        }

        //查询分配给应用的角色
        List<OuterRoleVo> roleVos = linkAppOuterRoleService.listAppRoles(upstreamEa, arg.getAppId()).getData();
        if (CollectionUtil.isEmpty(roleVos)) {
            return queryFriendEmployeeByAppIdResult;
        }
        List<String> appRoleIds = roleVos.stream().map(OuterRoleVo::getRoleId).collect(Collectors.toList());

        List<Map<String, Object>> allAuthPublicEmployeeObjectDatas = Lists.newArrayList();
        final List<Map<String, Object>> allEnterpriseRelationObjectDatas = Lists.newArrayList();// 包含未授权企业
        //自身对接范围可见，需要过滤掉不是自己负责的对接企业，这类企业应该不多
        if (privilege.getPrivilege() == RelationViewPrivilege.Privilege.RELATIVE_VISIBLE) {
            ListEnterpriseRelationObjByConditionArg listEnterpriseRelationObjByConditionArg = ListEnterpriseRelationObjByConditionArg.newStartRelationInstance();
            listEnterpriseRelationObjByConditionArg.setUseDataPermission(true);
            List<Map<String, Object>> downstreamEnterpriseRelationObject = enterpriseRelationObjService.listByCondition(serviceContext, listEnterpriseRelationObjByConditionArg).getObjectDatas();
            if (CollectionUtils.isEmpty(downstreamEnterpriseRelationObject)) {
                return queryFriendEmployeeByAppIdResult;
            }
            allEnterpriseRelationObjectDatas.addAll(downstreamEnterpriseRelationObject);
            List<Long> downOuterTenantIds = downstreamEnterpriseRelationObject.stream().map(x -> Long.parseLong((String) x.get(EnterpriseRelationObjConstant.ID))).collect(Collectors.toList());
            OffsetUtil.splitFunction(downOuterTenantIds, 1000, (list) -> {
                if (allAuthPublicEmployeeObjectDatas.size() >= MAX_QUERY_RECORD_LIMIT) {
                    return Lists.newArrayList();
                }
                ListPublicEmployeesByOuterRoleIdsByPageArg publicEmployeesByOuterRoleIdsArg = new ListPublicEmployeesByOuterRoleIdsByPageArg();
                publicEmployeesByOuterRoleIdsArg.setDownstreamOuterTenantIds(list);
                publicEmployeesByOuterRoleIdsArg.setOuterRoleIds(appRoleIds);
                publicEmployeesByOuterRoleIdsArg.setOffset(0);
                publicEmployeesByOuterRoleIdsArg.setLimit(1000);
                List<Map<String, Object>> dataList = listPublicEmployeeObjectDatasByOuterRoleIdsByPage(serviceContext, publicEmployeesByOuterRoleIdsArg).getDataList();
                allAuthPublicEmployeeObjectDatas.addAll(dataList);
                return Lists.newArrayList();
            });
        } else {
            //获取本企业已授权该应用的下游企业与对接人
            OffsetUtil.offsetFunction(1000, (offset, limit) -> {
                if (allAuthPublicEmployeeObjectDatas.size() >= MAX_QUERY_RECORD_LIMIT) {
                    return 0;
                }

                ListPublicEmployeesByOuterRoleIdsByPageArg enterpriseRelationByOuterRoleIdsArg = new ListPublicEmployeesByOuterRoleIdsByPageArg();
                enterpriseRelationByOuterRoleIdsArg.setOuterRoleIds(appRoleIds);
                enterpriseRelationByOuterRoleIdsArg.setOffset(offset);
                enterpriseRelationByOuterRoleIdsArg.setLimit(limit);
                List<Map<String, Object>> authEnterpriseRelationObjectDatas = listEnterpriseRelationObjectDatasByOuterRoleIdsByPage(serviceContext, enterpriseRelationByOuterRoleIdsArg).getDataList();
                int resultSize = authEnterpriseRelationObjectDatas.size();
                if (CollectionUtil.isEmpty(authEnterpriseRelationObjectDatas)) {
                    return 0;
                }
                allEnterpriseRelationObjectDatas.addAll(authEnterpriseRelationObjectDatas);

                List<Long> authDownOuterTenantIds = authEnterpriseRelationObjectDatas.stream().map(x -> Long.parseLong((String) x.get(EnterpriseRelationObjConstant.ID))).collect(Collectors.toList());
                ListPublicEmployeesByOuterRoleIdsByPageArg publicEmployeesByOuterRoleIdsArg = new ListPublicEmployeesByOuterRoleIdsByPageArg();
                publicEmployeesByOuterRoleIdsArg.setDownstreamOuterTenantIds(authDownOuterTenantIds);
                publicEmployeesByOuterRoleIdsArg.setOuterRoleIds(appRoleIds);
                List<Map<String, Object>> publicEmployeeObjectDatas = listPublicEmployeeObjectDatasByOuterRoleIdsByPage(serviceContext, publicEmployeesByOuterRoleIdsArg).getDataList();
                if (CollectionUtil.isEmpty(publicEmployeeObjectDatas)) {
                    return resultSize;
                }

                allAuthPublicEmployeeObjectDatas.addAll(publicEmployeeObjectDatas);
                return resultSize;
            });
        }

        if (CollectionUtils.isEmpty(allAuthPublicEmployeeObjectDatas)) {
            return queryFriendEmployeeByAppIdResult;
        }
        Map<Long, Map<String, Object>> idEnterpriseRelationObjMap = allEnterpriseRelationObjectDatas.stream()
                .collect(Collectors.toMap(x -> Long.parseLong((String) x.get(EnterpriseRelationObjConstant.ID)), x -> x));
        Map<Long, List<Map<String, Object>>> outerTenantIdPublicEmployeeObjsMap = allAuthPublicEmployeeObjectDatas.stream()
                .collect(Collectors.groupingBy(x -> Long.parseLong((String) x.get(PublicEmployeeObjConstant.OUTER_TENANT_ID))));

        queryFriendEmployeeByAppIdResult = buildQueryFriendEmployeeByAppIdResult(idEnterpriseRelationObjMap, outerTenantIdPublicEmployeeObjsMap);
        return queryFriendEmployeeByAppIdResult;
    }

    private QueryFriendEmployeeByAppIdResult buildQueryFriendEmployeeByAppIdResult(Map<Long, Map<String, Object>> idEnterpriseRelationObjMap,
                                                                                   Map<Long, List<Map<String, Object>>> outerTenantIdPublicEmployeeObjsMap) {
        QueryFriendEmployeeByAppIdResult queryFriendEmployeeByAppIdResult = new QueryFriendEmployeeByAppIdResult();
        List<EaEmpVo> eaEmpVos = Lists.newArrayList();
        for (Entry<Long, List<Map<String, Object>>> entry : outerTenantIdPublicEmployeeObjsMap.entrySet()) {
            Long downstreamOuterTenantId = entry.getKey();
            EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(idEnterpriseRelationObjMap.get(downstreamOuterTenantId));
            if (Objects.isNull(enterpriseRelationObj)) {
                continue;
            }

            EaEmpVo tmpEaInfo = new EaEmpVo();
            tmpEaInfo.setEnterpriseAccount(enterpriseRelationObj.getEnterpriseAccount());
            tmpEaInfo.setOuterTenantId(downstreamOuterTenantId);
            tmpEaInfo.setEnterpriseName(enterpriseRelationObj.getName());
            tmpEaInfo.setEnterpriseShortName(enterpriseRelationObj.getDestShortName());
            tmpEaInfo.setEnterpriseShortNameSpell(enterpriseRelationObj.getDestShortNameSpell());
            tmpEaInfo.setManageMode(enterpriseRelationObj.getManageMode());

            List<Map<String, Object>> publicEmployees = Lists.newArrayList();
            for (Map<String, Object> publicEmployee : entry.getValue()) {
                long outerUid = Long.parseLong((String) publicEmployee.get(PublicEmployeeObjConstant.ID));
                List<RoleVo> roleList = Lists.newArrayList();
                publicEmployee.put("roles", roleList);
                if (enterpriseRelationObj.getEnterpriseAccount() != null) {
                    publicEmployee.put("enterprise_account", enterpriseRelationObj.getEnterpriseAccount());
                }
                publicEmployees.add(publicEmployee);
            }
            tmpEaInfo.setPublicEmployeeObjectDatas(publicEmployees);
            eaEmpVos.add(tmpEaInfo);
        }
        queryFriendEmployeeByAppIdResult.setEaEmpVos(eaEmpVos);
        return queryFriendEmployeeByAppIdResult;
    }

    //校验应用选人可见权限
    private RelationViewPrivilege checkAppPrivilege(String appId, String enterpriseAccount, Integer operatorId) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(enterpriseAccount);
        boolean isRelationManager = fxiaokeAccountManager.isRelationAdmin(operatorId, tenantId + "");
        //互联管理员可见全部下游
        if (isRelationManager) {
            return RelationViewPrivilege.getPrivilege(RelationViewPrivilege.Privilege.ALL_VISIBLE);
        }
        //判断应用管理角色
        LinkAppIdArg linkAppIdArg = new LinkAppIdArg();
        linkAppIdArg.setLinkAppId(appId);
        linkAppIdArg.setAuthContextWithEaAndFsUserId(enterpriseAccount, operatorId);
        com.facishare.enterprise.common.result.Result<UpstreamEmployeeRolesVo> userPrivilege = httpUpstreamService.getEmployeeLinkAppRoles(linkAppIdArg);
        if (!userPrivilege.isSuccess()) {  //查询失败则标记不可见
            log.warn("fail to get user privilege,arg:{},result:{}", linkAppIdArg, userPrivilege);
            return RelationViewPrivilege.getPrivilege(RelationViewPrivilege.Privilege.INVISIBLE);
        }
        /* 当前应用管理模块由自己的角色定义：
         * 1-应用管理员， 2-对接人，3-普通员工
         * 这里用户的可见范围跟用户的角色有关，但是定义不一定和应用管理模块完全保持一致。
         * 比如应用管理模块没有定义  互联管理员/系统管理员的角色。
         * 这里操作者权限的定义
         * 0-其它，1-应用管理员/互联管理员/系统管理员， 2-普通对接人，3-全局对接人
         * 应用管理员与全局对接人可见分组数据 added on 6.0.1
         * */
        if (userPrivilege.getData().getRoles().contains(1)) {
            return RelationViewPrivilege.getPrivilege(RelationViewPrivilege.Privilege.ALL_VISIBLE);
        } else if (userPrivilege.getData().getRoles().contains(2)) {
            return RelationViewPrivilege.getPrivilege(RelationViewPrivilege.Privilege.RELATIVE_VISIBLE);
        } else {
            return RelationViewPrivilege.getPrivilege(RelationViewPrivilege.Privilege.INVISIBLE);
        }
    }

    @Setter
    @Getter
    private static class RelationViewPrivilege {
        private int privilege;
        //自身可见的对接范围
        private List<String> friendAccounts;

        private RelationViewPrivilege() {
        }

        static RelationViewPrivilege getPrivilege(int privilege) {
            RelationViewPrivilege result = new RelationViewPrivilege();
            result.setPrivilege(privilege);
            result.setFriendAccounts(null);
            return result;
        }

        interface Privilege {
            //不可见
            int INVISIBLE = 0;
            //自身对接范围可见
            int RELATIVE_VISIBLE = 1;
            //全部伙伴企业可见
            int ALL_VISIBLE = 2;
        }
    }

    @Override
    public UnboundWxServiceEmpCountVo countUnboundWxServicePublicEmployees(ServiceContext serviceContext, CountUnboundWxServicePublicEmployeesArg arg) {
        String upstreamEa = arg.getUpstreamEa();
        Integer employeeId = arg.getEmployeeId();

        UnboundWxServiceEmpCountVo unboundWxServiceEmpCountVo = new UnboundWxServiceEmpCountVo();
        // 检测当前企业的微信服务号配置信息
        com.facishare.enterprise.common.result.Result<List<WXServiceInfoVO>> result = enterpriseLinkWxServiceConfigService.listBindServices(upstreamEa, employeeId);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            unboundWxServiceEmpCountVo.setUnboundNumber(0);
            unboundWxServiceEmpCountVo.setAllUnboundDownstreamEmp(new ArrayList<>());
            return unboundWxServiceEmpCountVo;
        }
        //筛选开启绑定引导的服务号ID
        List<String> srcWxAppIds = result.getData().stream().filter(x -> org.apache.commons.lang.BooleanUtils.isTrue(x.getOpenGuide())).map(WXServiceInfoVO::getWxAppId).collect(Collectors.toList());
        if (srcWxAppIds.size() == 0) {
            unboundWxServiceEmpCountVo.setUnboundNumber(0);
            unboundWxServiceEmpCountVo.setAllUnboundDownstreamEmp(new ArrayList<>());
            return unboundWxServiceEmpCountVo;
        }
        List<UnboundWxServiceEmpVo> unboundWxServiceEmpVos = new ArrayList<>();
        AtomicInteger totalCount = new AtomicInteger();
        OffsetUtil.offsetFunction(200, (offset, limit) -> {
            //获取所有下游对接企业
            List<Integer> relationTypes = EnterpriseRelationType.hasRelationsTypes().stream().map(EnterpriseRelationType::getType).collect(Collectors.toList());
            EnterpriseRelationQueryArg enterpriseRelationQueryArg = new EnterpriseRelationQueryArg();
            enterpriseRelationQueryArg.setRelationTypes(relationTypes);
            enterpriseRelationQueryArg.setOffset(offset);
            enterpriseRelationQueryArg.setLimit(limit);
            List<IObjectData> enterpriseRelationObjectDatas = enterpriseRelationDao.listByQuery(serviceContext.getUser(), enterpriseRelationQueryArg);

            if (CollectionUtils.isEmpty(enterpriseRelationObjectDatas)) {
                return 0;
            }

            List<Long> outerTenantIds = enterpriseRelationObjectDatas.stream().map(x -> Long.parseLong((String) x.get(EnterpriseRelationObjConstant.DEST_OUTER_TENANT_ID)))
                    .collect(Collectors.toList());
            Map<Long, IObjectData> idEnterpriseRelationMap = enterpriseRelationObjectDatas.stream()
                    .collect(Collectors.toMap(x -> Long.parseLong((String) x.get(EnterpriseRelationObjConstant.DEST_OUTER_TENANT_ID)), x -> x));

            //获取我方企业所有的下游对接人
            PublicEmployeeQueryArg publicEmployeeQueryArg = new PublicEmployeeQueryArg();
            publicEmployeeQueryArg.setOuterTenantIds(outerTenantIds);
            publicEmployeeQueryArg.setOrderByData(OrderByData.createTimeAsc());
            QueryResult<IObjectData> publicEmployeeDaoQuery = publicEmployeeDao.getQuery(serviceContext.getUser(), publicEmployeeQueryArg);
            List<PublicEmployeeObj> publicEmployeeObjs = publicEmployeeDaoQuery.getData().stream().map(PublicEmployeeObj::newInstance).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(publicEmployeeObjs)) {
                return enterpriseRelationObjectDatas.size();
            }

            //获取所有下游对接人已经绑定的微信服务号
            List<Long> outerUids = publicEmployeeObjs.stream().map(PublicEmployeeObj::getOuterUid).distinct().collect(Collectors.toList());
            List<ERAccountVo> erBindThirdAccounts = enterpriseRelationAccountService.getErAccountByOuterUIdsAndWxAppIds(upstreamEa, outerUids, srcWxAppIds,
                    ErBindThirdAccountTypeEnum.WX_SERVICE.getType()).getData();
            Map<Long, List<ERAccountVo>> wxServiceMap = erBindThirdAccounts.stream().collect(Collectors.groupingBy(ERAccountVo::getOuterUid, Collectors.toList()));

            //初始化方法的返回对象

            //下游对接人绑定的服务号 和 开启引导绑定的服务号进行匹配，匹配不通过则认为没有绑定服务号
            for (PublicEmployeeObj card : publicEmployeeObjs) {
                Set<String> empBindWxAppIds;
                //取得单个员工绑定的所有服务号
                if (wxServiceMap.get(card.getOuterUid()) == null) {
                    empBindWxAppIds = new HashSet<>();
                } else {
                    empBindWxAppIds = wxServiceMap.get(card.getOuterUid()).stream().map(ERAccountVo::getWxAppId).collect(Collectors.toSet());
                }

                //是否未绑定的标识，默认未绑定
                boolean isUnbound = true;
                //只要有一个 开启绑定的服务号 被 下游对接人 绑定，则认为是 已绑定
                for (String wxAppId : srcWxAppIds) {
                    if (empBindWxAppIds.contains(wxAppId)) {
                        //如果有一个没绑定，修改标识，跳出循环
                        isUnbound = false;
                        break;
                    }
                }
                //如果匹配结果为未绑定，则添加到list中
                if (isUnbound) {
                    UnboundWxServiceEmpVo unboundWxServiceEmpVo = new UnboundWxServiceEmpVo();
                    IObjectData objectData = idEnterpriseRelationMap.get(card.getOuterTenantId());
                    if (objectData != null) {
                        String enterpriseName = (String) objectData.get(EnterpriseRelationObjConstant.NAME);
                        unboundWxServiceEmpVo.setEnterpriseName(enterpriseName);
                    }
                    unboundWxServiceEmpVo.setOuterUid(card.getOuterUid());
                    unboundWxServiceEmpVo.setEmplpyeeName(card.getName());
                    unboundWxServiceEmpVo.setOuterTenantId(card.getOuterTenantId());
                    unboundWxServiceEmpVo.setMobilePhone(card.getMobile());
                    unboundWxServiceEmpVos.add(unboundWxServiceEmpVo);
                }
            }
            int nowCount = totalCount.addAndGet(publicEmployeeObjs.size());
            if (nowCount > 500) {//超过500个不统计
                return 0;
            }
            return enterpriseRelationObjectDatas.size();
        });
        unboundWxServiceEmpCountVo.setUnboundNumber(unboundWxServiceEmpVos.size());
        unboundWxServiceEmpCountVo.setAllUnboundDownstreamEmp(unboundWxServiceEmpVos);
        return unboundWxServiceEmpCountVo;
    }

    @Override
    public LongOffsetResult listOuterUidsByOuterRoleIdsByPage(ServiceContext serviceContext, ListPublicEmployeesByOuterRoleIdsByPageArg arg) {
        if (arg == null || CollectionUtils.isEmpty(arg.getOuterRoleIds())) {
            LongOffsetResult res = new LongOffsetResult();
            res.setDataList(new ArrayList<>());
            return res;
        }
        return publicEmployeeDao.listOuterUidsByOuterRoleIdsByPage(arg, serviceContext.getUser());
    }

    @Override
    public LongOffsetResult listOuterTenantIdsByOuterRoleIdsByPage(ServiceContext serviceContext, ListPublicEmployeesByOuterRoleIdsByPageArg arg) {
        return enterpriseRelationDao.listOuterTenantIdsByOuterRoleIdsByPage(arg, serviceContext.getUser());
    }

    @Override
    public LongOffsetResult listOuterTenantIdsByOuterRoleIdsByPageV2(ServiceContext serviceContext, ListPublicEmployeesByOuterRoleIdsByPageArg arg) {
        return enterpriseRelationDao.listOuterTenantIdsByOuterRoleIdsByPageV2(arg, serviceContext.getUser());
    }

    @Override
    public MapOffsetResult listPublicEmployeeObjectDatasByOuterRoleIdsByPage(ServiceContext serviceContext, ListPublicEmployeesByOuterRoleIdsByPageArg arg) {
        LongOffsetResult idRes = this.listOuterUidsByOuterRoleIdsByPage(serviceContext, arg);
        MapOffsetResult offsetResult = MapOffsetResult.newEmptyPage();
        if (CollectionUtils.isEmpty(idRes.getDataList())) {
            return offsetResult;
        }
        List<IObjectData> iObjectDatas = publicEmployeeDao.batchGetByIds(serviceContext.getUser(), idRes.getDataList());
        offsetResult.setDataList(iObjectDatas.stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
        return offsetResult;
    }

    @Override
    public MapOffsetResult listEnterpriseRelationObjectDatasByOuterRoleIdsByPage(ServiceContext serviceContext, ListPublicEmployeesByOuterRoleIdsByPageArg arg) {
        MapOffsetResult offsetResult = MapOffsetResult.newEmptyPage();
        LongOffsetResult idRes = this.listOuterTenantIdsByOuterRoleIdsByPageV2(serviceContext, arg);
        if (CollectionUtils.isEmpty(idRes.getDataList())) {
            return offsetResult;
        }
        List<IObjectData> iObjectDatas = enterpriseRelationDao.batchGetByIds(serviceContext.getUser(), idRes.getDataList());
        offsetResult.setDataList(iObjectDatas.stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
        return offsetResult;
    }

    @Override
    public Result<Void> batchUpdatePersonalEmployees(ServiceContext serviceContext, BatchUpdatePersonalEmployeesArg arg) {
        Integer tenantIdInt = serviceContext.getUser().getTenantIdInt();
        User user = serviceContext.getUser();
        String upstreamEa = eieaConverter.enterpriseIdToAccount(tenantIdInt);
        if (CollectionUtils.isEmpty(arg.getPersonalEmployees())) {
            return Result.newResult(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getErrorMessage());
        }
        if (!Strings.isNullOrEmpty(arg.getAccountId())) {
            String crmCustomerName = serviceFacade.findNameByIds(user, AccountFieldContants.API_NAME, Lists.newArrayList(arg.getAccountId())).get(arg.getAccountId());

            ObjectDataDocument updateObjectData = new ObjectDataDocument();
            updateObjectData.put(EnterpriseRelationObjConstant.ID, arg.getTargetOuterTenantId() + "");
            updateObjectData.put(EnterpriseRelationObjConstant.MAPPER_ACCOUNT_ID, arg.getAccountId());
            if (StringUtils.isNotBlank(crmCustomerName)) {
                crmCustomerName = crmCustomerName.length() > 100 ? crmCustomerName.substring(0, 100) : crmCustomerName;
                String spell = JPinYinUtil.createSpell(crmCustomerName);
                updateObjectData.put(EnterpriseRelationObjConstant.NAME, crmCustomerName);
                updateObjectData.put(EnterpriseRelationObjConstant.DEST_SHORT_NAME, crmCustomerName);
                updateObjectData.put(EnterpriseRelationObjConstant.DEST_SHORT_NAME_SPELL, spell);
                updateObjectData.put(EnterpriseRelationObjConstant.ENTERPRISE_NAME_SPELL, spell);
            }
            this.actionCallManager.triggerEnterpriseRelationEditAction(user, updateObjectData, true);
        }
        arg.getPersonalEmployees().stream().forEach(person -> {
            ObjectDataDocument updateObjectData = new ObjectDataDocument();
            updateObjectData.put(PublicEmployeeObjConstant.ID, person.getOuterUid() + "");
            updateObjectData.put(PublicEmployeeObjConstant.CONTRACT_ID, person.getContactId());
            IObjectData contractObj = getContractObj(user, person.getContactId());
            if (contractObj != null) {
                String contactName = contractObj.get(ContactFieldContants.NAME, String.class);
                String email = contractObj.get(ContactFieldContants.EMAIL, String.class);
                String mobile = contractObj.get(ContactFieldContants.MOBILE, String.class);
                if (StringUtils.isEmpty(mobile)) {
                    mobile = contractObj.get(ContactFieldContants.MOBILE1, String.class);
                }
                if (StringUtils.isNotEmpty(mobile)) {
                    updateObjectData.put(PublicEmployeeObjConstant.MOBILE, mobile);
                }
                if (StringUtils.isNotEmpty(contactName)) {
                    updateObjectData.put(PublicEmployeeObjConstant.NAME, contactName);
                    updateObjectData.put(PublicEmployeeObjConstant.NAME_SPELL, JPinYinUtil.createSpell(contactName));
                }
                if (StringUtils.isNotEmpty(email)) {
                    updateObjectData.put(PublicEmployeeObjConstant.EMAIL, email);
                }
            }
            this.actionCallManager.triggerPublicEmployeeEditAction(user, updateObjectData, true);
            publicEmployeeManager.doCreateDestPublicEmployeeUpdateAssociationInnerEvent(upstreamEa, person.getTargetOuterTenantId(), person.getOuterUid(), person.getContactId());
            MergeAppRoleAssignsArg mergeAppRoleAssignsArg = new MergeAppRoleAssignsArg();
            mergeAppRoleAssignsArg.setOuterRoleIds(person.getOuterRoleIds());
            mergeAppRoleAssignsArg.setOuterUids(com.google.common.collect.Lists.newArrayList(person.getOuterUid()));
            mergeAppRoleAssignsArg.setUpstreamEa(upstreamEa);
            linkAppFunctionPermissionManager.mergeOuterUserOuterRoles(user, person.getOuterRoleIds(), com.google.common.collect.Lists.newArrayList(person.getOuterUid()));
        });
        return Result.newResult(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getErrorMessage());
    }

    /**
     * 查询联系人信息
     */
    private IObjectData getContractObj(User user, String contactId) {
        IObjectData objectData = serviceFacade.findObjectData(user, contactId, ContactFieldContants.API_NAME);
        return objectData;
    }

    @Override
    public GetUserPositionResult getUserPosition(ServiceContext serviceContext, NoArg arg) {
        String ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(serviceContext.getTenantId()));
        boolean relationAdmin = fxiaokeAccountManager.isRelationAdmin(serviceContext.getUser());
        GetUserPositionResult userPositionResult = new GetUserPositionResult();
        if (relationAdmin) {
            DownstreamEaArg downstreamEaArg = new DownstreamEaArg();
            downstreamEaArg.setEa(ea);
            Boolean isDownstream = globalService.isDownstream(HeaderObj.newInstance(), downstreamEaArg).getData();
            userPositionResult.setIsDownEaConnAdmin(isDownstream);
            EnterpriseQuotaVo enterpriseQuota = enterpriseMetaDataManager.getEnterpriseQuota(ea);
            Boolean isUpstream = enterpriseQuota.getMaxQuota() > 0;
            userPositionResult.setIsUpEaConnAdmin(isUpstream);
        }
        return userPositionResult;
    }

    @Override
    public BatchGetOuterByRolesResult batchGetOuterByRoles(ServiceContext serviceContext, BatchGetOuterByRolesArg arg) {
        BatchGetOuterByRolesResult result = new BatchGetOuterByRolesResult();
        result.setOuterUserRoleDatas(Lists.newArrayList());

        int tenantId = getTenantIdInt(serviceContext);
        Long downstreamOuterTenantId = arg.getDownstreamOuterTenantId();

        if (StringUtils.isNotEmpty(arg.getMapperObjectId())) {
            if (arg.getMapperApiName().equals(ContactFieldContants.API_NAME)) {
                List<PublicEmployeeObj> publicEmployeeObjs = publicEmployeeDao.listByContactIds(serviceContext.getUser(), Lists.newArrayList(arg.getMapperObjectId()));
                if (!CollectionUtils.isEmpty(publicEmployeeObjs)) {
                    downstreamOuterTenantId = publicEmployeeObjs.get(0).getOuterTenantId();
                }
            } else {
                List<Integer> relationTypes = Lists
                        .newArrayList(EnterpriseRelationType.CREATING.getType(), EnterpriseRelationType.FRIEND.getType(), EnterpriseRelationType.RELIVE_BY_QUOTAEXPIRE.getType());

                EnterpriseRelationQueryArg queryArg = new EnterpriseRelationQueryArg();
                queryArg.setRelationTypes(relationTypes);
                if (AccountFieldContants.API_NAME.equals(arg.getMapperApiName())) {
                    queryArg.setMapperAccountIds(Lists.newArrayList(arg.getMapperObjectId()));
                } else {
                    queryArg.setMapperPartnerIds(Lists.newArrayList(arg.getMapperObjectId()));
                }
                List<IObjectData> objectDatas = enterpriseRelationDao.listByQuery(serviceContext.getUser(), queryArg);
                if (!CollectionUtil.isEmpty(objectDatas)) {
                    if (objectDatas.size() == 1) {
                        EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(objectDatas.get(0));
                        downstreamOuterTenantId = enterpriseRelationObj.getOuterTenantId();
                    }
                    if (objectDatas.size() > 1) {
                        for (IObjectData objectData : objectDatas) {
                            EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(objectData);
                            if (IdentityType.CORP.getType() == enterpriseRelationObj.getIdentityType()) {
                                downstreamOuterTenantId = enterpriseRelationObj.getOuterTenantId();
                                break;
                            }
                        }
                    }
                }
            }
        }

        if (downstreamOuterTenantId == null) {
            return result;
        }

        for (String roleId : arg.getRoleIds()) {
            List<Long> roleUsers = null;
            if (roleId.equals("outtenant_manager_role")) {
                List<PublicEmployeeObj> dataManagers = publicEmployeeDao.listDataManagers(serviceContext.getUser(), downstreamOuterTenantId);
                roleUsers = dataManagers.stream().map(PublicEmployeeObj::getOuterUid).collect(Collectors.toList());
            } else if (roleId.equals("outtenant_manager_leader_role")) {
                PublicEmployeeObj relationOwner = publicEmployeeDao.getRelationOwner(serviceContext.getUser(), downstreamOuterTenantId);
                roleUsers = Lists.newArrayList(relationOwner.getOuterUid());
            } else {
                List<OuterRoleVo> roleInfos = outerRoleService.batchGetOuterRoles(tenantId, Lists.newArrayList(roleId)).getData();
                if (!CollectionUtil.isEmpty(roleInfos)) {
                    List<UserRoleData> userRoleDatas = outerRoleRemoteBaseManager
                            .queryOuterUserRoleByConditions(Integer.parseInt(getUserId(serviceContext)), tenantId, null, Lists.newArrayList(roleId), Lists.newArrayList(downstreamOuterTenantId));
                    if (!CollectionUtils.isEmpty(userRoleDatas)) {
                        roleUsers = userRoleDatas.stream().map(x -> Long.parseLong(x.getUserId())).collect(Collectors.toList());
                    }
                }
            }
            if (!CollectionUtils.isEmpty(roleUsers)) {
                List<SimpleOuterUserData> simpleOuterUserDataList = getSimpleOuterUserDataList(serviceContext.getUser(), roleUsers);
                OuterUserRoleData outerUserRoleData = new OuterUserRoleData(roleId, simpleOuterUserDataList);
                result.getOuterUserRoleDatas().add(outerUserRoleData);
            }
        }
        return result;
    }

    private List<SimpleOuterUserData> getSimpleOuterUserDataList(User user, List<Long> outerUids) {
        List<IObjectData> iObjectDatas = publicEmployeeDao.batchGetByIds(user, outerUids);
        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newObjInstance(iObjectDatas);
        return publicEmployeeObjs.stream().map(this::toSimpleOuterUserData).collect(Collectors.toList());
    }

    private SimpleOuterUserData toSimpleOuterUserData(PublicEmployeeObj obj) {
        SimpleOuterUserData simpleOuterUserData = new SimpleOuterUserData();
        simpleOuterUserData.setOuterUid(obj.getOuterUid());
        simpleOuterUserData.setName(obj.getName());
        return simpleOuterUserData;
    }

    @Override
    public GetRelationOwnerResult getRelationOwner(ServiceContext serviceContext, GetRelationOwnerArg arg) {
        PublicEmployeeObj relationOwner = publicEmployeeDao.getRelationOwner(serviceContext.getUser(), arg.getDownstreamOuterUid());
        GetRelationOwnerResult result = new GetRelationOwnerResult();
        result.setRelationOwner(ObjectDataDocument.of(relationOwner));
        return result;
    }

    @Override
    public ListRelationOwnersResult listRelationOwners(ServiceContext serviceContext, ListRelationOwnersArg arg) {
        ListRelationOwnersResult result = new ListRelationOwnersResult();
        List<PublicEmployeeObj> relationOwners = publicEmployeeDao.listRelationOwners(serviceContext.getUser(), arg.getDownstreamOuterTenantIds());
        List<Map<String, Object>> objectDatas = relationOwners.stream().map(ObjectDataDocument::of).collect(Collectors.toList());
        result.setRelationOwners(objectDatas);
        return result;
    }

    @Override
    public Result<Void> batchSetActiveStateByOuterUids(ServiceContext serviceContext, BatchSetActiveStateByOuterUidsArg arg) {
        if (CollectionUtils.isEmpty(arg.getOuterUids())) {
            return Result.newResult(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getErrorMessage());
        }
        List<ObjectDataDocument> updateArgs = new ArrayList<>();
        for (Long outerUid : arg.getOuterUids()) {
            ObjectDataDocument updateObjectData = new ObjectDataDocument();
            updateObjectData.put(PublicEmployeeObjConstant.ID, outerUid + "");
            updateObjectData.put(PublicEmployeeObjConstant.ACTIVE_STATE, arg.getActiveState() + "");
            updateArgs.add(updateObjectData);
        }
        publicEmployeeDao.batchIncrementUpdate(serviceContext, PublicEmployeeObjConstant.API_NAME, updateArgs);
        // 不需要同步老库
        return Result.newResult(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getErrorMessage());
    }

    @Override
    public Result<Void> changeRelationOwner(ServiceContext serviceContext, ChangeRelationOwnerArg arg) {
        // 重置老数据
        List<String> ids = batchGetOuterUids(serviceContext, arg.getDownstreamTenantId());
        for (String id : ids) {
            Map<String, Object> objectData = new HashMap<>();
            objectData.put(PublicEmployeeObjConstant.ID, id);
            objectData.put(PublicEmployeeObjConstant.RELATION_OWNER, false);

            EditActionArg editActionArg = new EditActionArg();
            editActionArg.setObjectData(objectData);
            editActionArg.setIncrementUpdate(true);
            editAction(serviceContext, editActionArg);
        }

        // 更新新数据
        Map<String, Object> objectData = new HashMap<>();
        objectData.put(PublicEmployeeObjConstant.ID, arg.getDownstreamOuterUid() + "");
        objectData.put(PublicEmployeeObjConstant.RELATION_OWNER, true);

        EditActionArg editActionArg = new EditActionArg();
        editActionArg.setObjectData(objectData);
        editActionArg.setIncrementUpdate(true);
        editAction(serviceContext, editActionArg);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> updateDataPermission(ServiceContext serviceContext, UpdateDataPermissionArg arg) {
        if (CollectionUtils.isEmpty(arg.getDownstreamOuterUids())) {
            return Result.newSuccess();
        }

        // 重置老数据
        List<String> ids = batchGetOuterUids(serviceContext, arg.getDownstreamTenantId());
        for (String id : ids) {
            Map<String, Object> objectData = new HashMap<>();
            objectData.put(PublicEmployeeObjConstant.ID, id);
            objectData.put(PublicEmployeeObjConstant.DATA_MANAGER, false);

            EditActionArg editActionArg = new EditActionArg();
            editActionArg.setObjectData(objectData);
            editActionArg.setIncrementUpdate(true);
            editAction(serviceContext, editActionArg);
        }

        // 更新新数据
        for (Long downstreamOuterUid : arg.getDownstreamOuterUids()) {
            Map<String, Object> objectData = new HashMap<>();
            objectData.put(PublicEmployeeObjConstant.ID, downstreamOuterUid + "");
            objectData.put(PublicEmployeeObjConstant.DATA_MANAGER, BooleanUtils.toBoolean(arg.getHasPermission()));

            EditActionArg editActionArg = new EditActionArg();
            editActionArg.setObjectData(objectData);
            editActionArg.setIncrementUpdate(true);
            editAction(serviceContext, editActionArg);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<Long>> batchCreatePublicEmployees(ServiceContext serviceContext, BatchCreatePublicEmployeesArg arg) {
        Result<BatchCreatePublicEmployeesResult> batchCreatePublicEmployeesResultResult = this.batchCreatePublicEmployeesForErAccountInner(serviceContext, arg);
        if (!batchCreatePublicEmployeesResultResult.isSuccess()) {
            return Result.newResult(batchCreatePublicEmployeesResultResult.getErrCode(), batchCreatePublicEmployeesResultResult.getErrMessage());
        }
        List<PublicEmployeeObj> publicEmployeeObjs = new ArrayList<>(PublicEmployeeObj.newInstance(batchCreatePublicEmployeesResultResult.getData().getObjectDatas()));
        List<Long> outerUids = publicEmployeeObjs.stream().map(PublicEmployeeObj::getOuterUid).collect(Collectors.toList());
        return Result.newError(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getDescription(), outerUids);
    }

    @Override
    public Result<BatchCreatePublicEmployeesResult> batchCreatePublicEmployeesForErAccountInner(ServiceContext serviceContext, BatchCreatePublicEmployeesArg arg) {
        // 创建互联用户
        Map<String, Object> enterpriseRelationObjectData = enterpriseRelationObjService.getObjectData(serviceContext, arg.getDownstreamOuterTenantId()).getObjectData();
        List<Map<String, Object>> existPublicEmployeeObjectDatas = arg.isIgnoreBaseVisibleRange() ?
                batchGetObjectDatasIgnoreBaseVisibleRangeIncludeStoppedAndInvalid(serviceContext, arg.getDownstreamOuterTenantId()).getObjectDatas() :
                batchGetObjectDatasIncludeStoppedAndInvalid(serviceContext, arg.getDownstreamOuterTenantId()).getObjectDatas();
        Map<String, Map<String, Object>> existPublicEmployeeIdObjectDatasMap = Maps.newHashMap();
        if (!CollectionUtil.isEmpty(existPublicEmployeeObjectDatas)) {
            existPublicEmployeeIdObjectDatasMap = existPublicEmployeeObjectDatas.stream().collect(Collectors.toMap(x -> x.get(PublicEmployeeObjConstant.ID).toString(), x -> x));
        }

        Map<Integer, Long> fs2OuterMap = Maps.newHashMap();
        String downstreamEa = fxiaokeEnterpriseAssociationEntityDaoOldManager.getEaByOuterTenantId(arg.getDownstreamOuterTenantId());
        List<EmployeeInfo> employeeInfos = fxiaokeAccountManager.batchGetSimpleEmployees(arg.getDownstreamEmployeeIds(), downstreamEa);
        List<Integer> employeeIds = employeeInfos.stream().map(EmployeeInfo::getEmployeeId).collect(Collectors.toList());
        BatchGetOuterAccountResult batchGetOuterAccountResult = fxiaokeEmployeeAssociationService.batchGetOuterAccountByFs(downstreamEa, employeeIds).getData();
        if (batchGetOuterAccountResult != null && !MapUtils.isNullOrEmpty(batchGetOuterAccountResult.getFs2OuterMap())) {
            fs2OuterMap = batchGetOuterAccountResult.getFs2OuterMap();
        }

        String upstreamEa = getEa(serviceContext);
        List<Integer> upstreamCrmAdmins = crmManager.getCrmAdmins(upstreamEa);
        String mapperAccountId = (String) enterpriseRelationObjectData.get(EnterpriseRelationObjConstant.MAPPER_ACCOUNT_ID);
        String mapperPartnerId = (String) enterpriseRelationObjectData.get(EnterpriseRelationObjConstant.MAPPER_PARTNER_ID);
        String mapperCrmObjectId = StringUtils.isEmpty(mapperAccountId) ? mapperPartnerId : mapperAccountId;
        String mapperObjApiName = StringUtils.isEmpty(mapperAccountId) ? CrmObjectApiNameEnum.PARTNER.getName() : CrmObjectApiNameEnum.CUSTOMER.getName();

        List<Map<String, Object>> outerUsers = Lists.newArrayList();
        for (EmployeeInfo employeeInfo : employeeInfos) {
            Long downstreamOuterUid = fs2OuterMap.get(employeeInfo.getEmployeeId());
            if (downstreamOuterUid != null && !MapUtils.isNullOrEmpty(existPublicEmployeeIdObjectDatasMap) && existPublicEmployeeIdObjectDatasMap.containsKey(downstreamOuterUid + "")) {
                Map<String, Object> objectData = existPublicEmployeeIdObjectDatasMap.get(downstreamOuterUid + "");
                if (objectData.get(ObjectLifeStatusEnum.LIFE_STATUS_API_NAME).toString().equals(LifeStatusEnum.Invalid.getValue())) {
                    return Result
                            .newResult(ResultCode.PARAMS_ERROR.getErrorCode(), String.format(BusinessErrorEnum.USER_DEPRECATED.getDesc(), objectData.get(PublicEmployeeObjConstant.NAME).toString()));
                }

                List<String> updateFields = new ArrayList<>();
                objectData.put(IObjectData.TENANT_ID, serviceContext.getTenantId());

                objectData.put(PublicEmployeeObjConstant.EMPLOYEE_ID, employeeInfo.getEmployeeId() + "");
                updateFields.add(PublicEmployeeObjConstant.EMPLOYEE_ID);

                //如果已经加入组织架构树当中，附属部门添加当前传入的互联部门
                if (StringUtils.isNotBlank(arg.getErDepartmentId())) {
                    if (ObjectUtils.isNotEmpty(objectData.get(PublicEmployeeObjConstant.ORG_MANAGE_TYPE))) {
                        //如果主属部门为空，则设置主属部门为当前传入的互联部门
                        if (ObjectUtils.isEmpty(objectData.get(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT))) {
                            updateFields.add(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT);
                            objectData.put(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT, Lists.newArrayList(arg.getErDepartmentId()));
                        } else {
                            List<String> viceErDepartments = (List) objectData.get(PublicEmployeeObjConstant.VICE_ER_DEPARTMENTS);
                            if (ObjectUtils.isEmpty(viceErDepartments)) {
                                viceErDepartments = new ArrayList<>();
                                objectData.put(PublicEmployeeObjConstant.VICE_ER_DEPARTMENTS, viceErDepartments);
                            }
                            if (!viceErDepartments.contains(arg.getErDepartmentId())) {
                                viceErDepartments.add(arg.getErDepartmentId());
                                updateFields.add(PublicEmployeeObjConstant.VICE_ER_DEPARTMENTS);
                            }
                        }
                        //否则，第一次添加修改对应的主属部门
                    } else {
                        updateFields.add(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT);
                        objectData.put(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT, Lists.newArrayList(arg.getErDepartmentId()));
                    }
                }

                if (ObjectUtils.isNotEmpty(arg.getDownstreamTenantIds())) {
                    objectData.put(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PUBLIC_DATA);
                    objectData.put(IObjectData.DOWNSTREAM_TENANT_ID, arg.getDownstreamTenantIds());
                    updateFields.add(IObjectData.PUBLIC_DATA_TYPE);
                    updateFields.add(IObjectData.DOWNSTREAM_TENANT_ID);
                }
                if (ObjectUtils.isNotEmpty(arg.getOrgManageType())) {
                    objectData.put(PublicEmployeeObjConstant.ORG_MANAGE_TYPE, Lists.newArrayList(arg.getOrgManageType()));
                    updateFields.add(PublicEmployeeObjConstant.ORG_MANAGE_TYPE);
                }
                IActionContext actionContext = ActionContextExt.of(User.systemUser(serviceContext.getTenantId()), RequestContextManager.getContext()).getContext();
                if (arg.isIgnoreBaseVisibleRange()) {
                    actionContext.put(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, true);
                }
                serviceFacade.batchUpdateByFields(actionContext, Lists.newArrayList(ObjectDataExt.of(objectData).getObjectData()), updateFields);
                String type = (String) objectData.get(PublicEmployeeObjConstant.TYPE);

                // 如果互联用戶合法 无需再次启用
                if (String.valueOf(PublicEmployeeTypeEnum.TYPE_IS_PUBLIC_EMPLOYEE.getType()).equals(type)) {
                    outerUsers.add(objectData);
                } else {
                    PublicEmployeeStartActionArg startActionArg = new PublicEmployeeStartActionArg();
                    startActionArg.setDownstreamOuterUid(downstreamOuterUid);
                    startActionArg.setOuterRoleIds(arg.getOuterRoleIds());
                    startActionArg.setIgnoreSelfFilter(true);
                    try {
                        PublicEmployeeStartActionResult publicEmployeeStartActionResult = startAction(serviceContext, startActionArg);
                        outerUsers.add(publicEmployeeStartActionResult.getObjectData());
                    } catch (Exception e) {
                        log.warn("启用互联用户失败", e);
                    }
                }
            } else {
                com.fxiaoke.crmrestapi.common.data.ObjectData objectData = new com.fxiaoke.crmrestapi.common.data.ObjectData();
                objectData.put(PublicEmployeeObjConstant.NAME, employeeInfo.getEmployeeName());
                objectData.put(PublicEmployeeObjConstant.MOBILE, employeeInfo.getMobile());
                objectData.put(PublicEmployeeObjConstant.LOGIN_EMAIL, employeeInfo.getEmail());
                objectData.put(PublicEmployeeObjConstant.EMPLOYEE_ID, employeeInfo.getEmployeeId() + "");
                objectData.put(PublicEmployeeObjConstant.OUTER_TENANT_ID, arg.getDownstreamOuterTenantId() + "");
                if (StringUtils.isNotBlank(arg.getErDepartmentId())) {
                    objectData.put(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT, Lists.newArrayList(arg.getErDepartmentId()));
                }
                objectData.put(PublicEmployeeObjConstant.QUOTA_EXPIRED, enterpriseRelationObjectData.get(EnterpriseRelationObjConstant.QUOTA_EXPIRED)); // 如果互联关系上是配额到期，那么对接人上也要设置配额到期为true
                if (!CollectionUtil.isEmpty(arg.getOuterRoleIds())) {
                    objectData.put(PublicEmployeeObjConstant.OUTER_ROLE_IDS, arg.getOuterRoleIds());
                }
                if (ObjectUtils.isNotEmpty(arg.getDownstreamOuterTenantId())) {
                    objectData.put(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PUBLIC_DATA);
                    objectData.put(IObjectData.DOWNSTREAM_TENANT_ID, arg.getDownstreamTenantIds());
                }
                if (ObjectUtils.isNotEmpty(arg.getOrgManageType())) {
                    objectData.put(PublicEmployeeObjConstant.ORG_MANAGE_TYPE, Lists.newArrayList(arg.getOrgManageType()));
                }

                AddActionArg addActionArg = new AddActionArg();
                addActionArg.setObjectData(objectData);
                AddActionResult addActionResult = addAction(serviceContext, addActionArg);

                Long outerUid = Long.parseLong((String) addActionResult.getObjectData().get(PublicEmployeeObjConstant.ID));
                outerUsers.add(addActionResult.getObjectData());

                // 将该下游对接人添加到上游的CRM联系人中
                CrmCustomerContactVo crmCustomerContactVo = new CrmCustomerContactVo();
                crmCustomerContactVo.setName(employeeInfo.getEmployeeName());
                crmCustomerContactVo.setMobile(employeeInfo.getMobile());
                crmCustomerContactVo.setTel(employeeInfo.getMobile());
                crmCustomerContactVo.setEa(downstreamEa);
                crmCustomerContactVo.setCustomerName((String) enterpriseRelationObjectData.get(EnterpriseRelationObjConstant.NAME));
                if (!CollectionUtil.isEmpty(upstreamCrmAdmins)) {
                    CommonPoolUtil.execute(() -> {
                        String contractId = crmManager.createContactIfCan(upstreamEa, upstreamCrmAdmins.get(0), mapperObjApiName, mapperCrmObjectId, crmCustomerContactVo);
                        if (!Strings.isNullOrEmpty(contractId)) {
                            Map<String, Object> editObjectData = new HashMap<>();
                            editObjectData.put(PublicEmployeeObjConstant.ID, outerUid);
                            editObjectData.put(PublicEmployeeObjConstant.CONTRACT_ID, contractId);

                            EditActionArg editActionArg = new EditActionArg();
                            editActionArg.setObjectData(editObjectData);
                            editAction(serviceContext, editActionArg);
                        }
                    });
                }
            }
        }
        BatchCreatePublicEmployeesResult result = new BatchCreatePublicEmployeesResult();
        result.setObjectDatas(outerUsers);
        return Result.newError(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getDescription(), result);
    }

//    private User getUserFromPublicData(String tenantId, IObjectDescribe describe, Map<String, Object> objectData) {
//        if (!describe.isPublicObject()) {
//            return User.systemUser(tenantId);
//        }
//        //如果可见范围里面不包含更新的企业，但是互联人员已存在的情况下会无法更新
//        List<String> downstreamTenantId = MapUtils.get(objectData, IObjectData.DOWNSTREAM_TENANT_ID);
//        if (ObjectUtils.isNotEmpty(downstreamTenantId) && !downstreamTenantId.contains(tenantId)) {
//            return User.systemUser(downstreamTenantId.get(0));
//        }
//        if (ObjectUtils.isEmpty(downstreamTenantId)) {
//            return User.systemUser(describe.getUpstreamTenantId());
//        }
//        return User.systemUser(tenantId);
//    }

    @Override
    public InitTimeZoneResult initTimeZone(ServiceContext serviceContext, InitTimeZoneArg arg) {
        if (GuestorIdentityInfoConstants.OUTER_UID.equals(arg.getDownstreamOuterUid())) {
            String timeZone = timezoneService.findTenantTimeZone(String.valueOf(arg.getTenantId())).getId();
            return new InitTimeZoneResult(timeZone);
        }
        // 有纷享身份（主站或纷享app进入互联）：取纷享身份时区；纷享身份也没有时区则取上游租户默认时区；以上情况都取不到则默认：Asia/Shanghai
        String timeZone = null;
        PublicEmployeeObj downstreamObj = publicEmployeeDao.getById(serviceContext.getUser(), arg.getDownstreamOuterUid());
        if (downstreamObj != null && !Strings.isNullOrEmpty(downstreamObj.getEmployeeId())) {
            String downstreamEa = enterpriseRelationManager.getEaByOuterTenantId(serviceContext.getUser().getTenantIdInt(), downstreamObj.getOuterTenantId());
            UserInfo fsUser = UserInfo.of(String.valueOf(eieaConverter.enterpriseAccountToId(downstreamEa)), downstreamObj.getEmployeeId());
            Optional<ZoneId> optional = timezoneService.findUserTimeZone(fsUser);
            if (optional.isPresent()) {
                timeZone = optional.get().getId();
            } else {
                timeZone = timezoneService.findTenantTimeZone(String.valueOf(arg.getTenantId())).getId();
            }
        } else {// 没有纷享身份：1.互联身份没有设置时区，默认取上游企业的默认时区；2.互联身份设置了时区，取互联身份时区
            timeZone = timezoneService.findTenantTimeZone(String.valueOf(arg.getTenantId())).getId();
        }
        updateTimeZone(serviceContext, new UpdateTimeZoneArg(arg.getDownstreamOuterUid(), timeZone));
        return new InitTimeZoneResult(timeZone);
    }

    @Override
    public Result<Void> updateTimeZone(ServiceContext serviceContext, UpdateTimeZoneArg arg) {
        Map<String, Object> objectData = new HashMap<>();
        objectData.put(PublicEmployeeObjConstant.ID, arg.getDownstreamOuterUid() + "");
        objectData.put(PublicEmployeeObjConstant.ER_TIME_ZONE, arg.getTimeZone());
        return incrementEditAction(serviceContext, objectData);
    }

    @Override
    public InitCurrencyCodeResult initCurrencyCode(ServiceContext serviceContext, InitCurrencyCodeArg arg) {
        // 上游没开启币种管理，取不到本位币
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(getTenantIdInt(serviceContext), -10000);
        if (currencyService.multiCurrencyStatus(headerObj).getStatus() != Status.OPEN.ordinal()) {
            return new InitCurrencyCodeResult();
        }
        // 互联用户默认取上游币种管理中的本位币
        FindFunctionalCurrencyResult functionalCurrency = currencyService.findFunctionalCurrency(headerObj);
        if (GuestorIdentityInfoConstants.OUTER_UID.equals(arg.getDownstreamOuterUid())) {
            return new InitCurrencyCodeResult(functionalCurrency.getCurrencyCode());
        }
        updateCurrencyCode(serviceContext, new UpdateCurrencyCodeArg(arg.getDownstreamOuterUid(), functionalCurrency.getCurrencyCode()));
        return new InitCurrencyCodeResult(functionalCurrency.getCurrencyCode());
    }

    @Override
    public Result<Void> updateCurrencyCode(ServiceContext serviceContext, UpdateCurrencyCodeArg arg) {
        Map<String, Object> objectData = new HashMap<>();
        objectData.put(PublicEmployeeObjConstant.ID, arg.getDownstreamOuterUid() + "");
        objectData.put(PublicEmployeeObjConstant.ER_MC_CURRENCY, arg.getCurrencyCode());
        return incrementEditAction(serviceContext, objectData);
    }

    @Override
    public Result<Void> updateMobile(ServiceContext serviceContext, UpdateMobileArg arg) {
        Map<String, Object> objectData = new HashMap<>();
        objectData.put(PublicEmployeeObjConstant.ID, arg.getDownstreamOuterUid() + "");
        objectData.put(PublicEmployeeObjConstant.MOBILE, arg.getMobile());
        return incrementEditAction(serviceContext, objectData);
    }

    @Override
    public Result<Void> updateLoginEmail(ServiceContext serviceContext, UpdateLoginEmailArg arg) {
        Map<String, Object> objectData = new HashMap<>();
        objectData.put(PublicEmployeeObjConstant.ID, arg.getDownstreamOuterUid() + "");
        objectData.put(PublicEmployeeObjConstant.LOGIN_EMAIL, arg.getLoginEmail());
        objectData.put(PublicEmployeeObjConstant.EMAIL, arg.getLoginEmail());
        return incrementEditAction(serviceContext, objectData);
    }

    @Override
    public Result<Void> updatePersonalErLocale(ServiceContext serviceContext, UpdatePersonalErLocaleArg arg) {
        Map<String, Object> objectData = new HashMap<>();
        objectData.put(PublicEmployeeObjConstant.ID, arg.getDownstreamOuterUid() + "");
        objectData.put(PublicEmployeeObjConstant.ER_LANGUAGE, arg.getErLocale());
        return incrementEditAction(serviceContext, objectData);
    }

    private Result<Void> incrementEditAction(ServiceContext serviceContext, Map<String, Object> objectData) {
        EditActionArg editActionArg = new EditActionArg();
        editActionArg.setObjectData(objectData);
        editActionArg.setIncrementUpdate(true);
        editAction(serviceContext, editActionArg);
        return Result.newSuccess();
    }

    @Override
    public ListOuterUserResult listOuterUser(ServiceContext serviceContext, ListOuterUserArg arg) {
        ListOuterUserResult result = new ListOuterUserResult();
        result.setOuterUserDatas(Lists.newArrayList());

        ListPublicEmployeeObjByConditionArg conditionArg = ListPublicEmployeeObjByConditionArg.newStartType();
        conditionArg.setOuterTenantIds(Lists.newArrayList(arg.getDownstreamOuterTenantId()));
        conditionArg.setOffset(arg.getOffset());
        conditionArg.setLimit(arg.getLimit());
        List<Map<String, Object>> objectDatas = listByCondition(serviceContext, conditionArg).getObjectDatas();
        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newInstance(objectDatas);

        if (CollectionUtil.isEmpty(publicEmployeeObjs)) {
            return result;
        }

        //获取所有下游对接人已经绑定的微信服务号
        List<ListOuterUserResult.OuterUserData> outerUserResultList = new ArrayList<>();
        List<Long> outerUids = publicEmployeeObjs.stream().map(PublicEmployeeObj::getOuterUid).collect(Collectors.toList());
        List<ERAccountVo> erBindThirdAccounts = enterpriseRelationAccountService.getErAccountByOuterUIdsAndWxAppIds(serviceContext.getEa(), outerUids, Lists.newArrayList(arg.getWxAppId()),
                ErBindThirdAccountTypeEnum.WX_SERVICE.getType()).getData();
        Map<Long, ERAccountVo> outerUidToWxMap = erBindThirdAccounts.stream().collect(Collectors.toMap(ERAccountVo::getOuterUid, Function.identity()));
        Map<Long, String> outerUidToOpenIdMap = erBindThirdAccounts.stream().filter(x -> StringUtils.isNotEmpty(x.getWxOpenId()))
                .collect(Collectors.toMap(ERAccountVo::getOuterUid, ERAccountVo::getWxOpenId));
        List<String> openIds = new ArrayList<>(outerUidToOpenIdMap.values());
        for (PublicEmployeeObj publicEmployeeObj : publicEmployeeObjs) {
            if (publicEmployeeObj.getOuterUid().equals(arg.getDownstreamOuterUid())) {
                continue;
            }
            ERAccountVo erAccountVo = outerUidToWxMap.get(publicEmployeeObj.getOuterUid());
            ListOuterUserResult.OuterUserData outerUserData = new ListOuterUserResult.OuterUserData();
            outerUserData.setOuterTenantId(publicEmployeeObj.getOuterTenantId());
            outerUserData.setOuterUid(publicEmployeeObj.getOuterUid());
            outerUserData.setName(publicEmployeeObj.getName());
            outerUserData.setPhone(publicEmployeeObj.getMobile());
            if (erAccountVo != null) {
                outerUserData.setIsBindWeChat(true);
            }
            outerUserResultList.add(outerUserData);
        }

        String upstreamEa = getEa(serviceContext);
        List<IObjectData> wechatFanDataList = getWechatFanByPhone(serviceContext.getUser(), arg.getWxAppId(), openIds);
        Map<String, IObjectData> openIdToWxUserMap = wechatFanDataList.stream().collect(Collectors.toMap(x -> (String) x.get(WechatFanFieldContants.WX_OPEN_ID), Function.identity()));
        for (ListOuterUserResult.OuterUserData outerUserData : outerUserResultList) {
            if (!outerUserData.getIsBindWeChat()) {
                continue;
            }
            String openId = outerUidToOpenIdMap.get(outerUserData.getOuterUid());
            if (StringUtils.isEmpty(openId)) {
                continue;
            }
            IObjectData wechatFanData = openIdToWxUserMap.get(openId);
            if (Objects.isNull(wechatFanData)) {
                continue;
            }
            List<Map<String, String>> headImageList = (ArrayList) wechatFanData.get("wx_head_image");
            if (CollectionUtils.isEmpty(headImageList)) {
                continue;
            }
            Map<String, String> headImage = headImageList.get(0);
            String nPath = headImage.get("path") + "." + headImage.get("ext");
            String aPath = createFileShareId(upstreamEa, "1000", nPath);
            if (StringUtils.isNotEmpty(aPath)) {
                outerUserData.setHeadImageFileId(aPath);
            }
            outerUserData.setWechatUserName(wechatFanData.getName());
        }
        result.setOuterUserDatas(outerUserResultList);
        return result;
    }

    private String createFileShareId(String ea, String fsUserId, String path) {
        CreateFileShareId.Arg fileArg = new CreateFileShareId.Arg();
        try {
            fileArg.ea = ea;
            if (StringUtils.isBlank(fsUserId)) {
                fsUserId = "1000";
            }
            fileArg.employeeId = Integer.parseInt(fsUserId);
            if ("-10000".equals(fsUserId)) {
                fileArg.employeeId = 1000;
            }
            fileArg.path = path;
            //CreateFileShareId.Result result = sharedFileService.createFileShareId(fileArg);
            CreateFileShareId.Result result = CloudContextUtil.doInCloudContextWithReturn("" + eieaConverter.enterpriseAccountToId(ea), () -> sharedFileService.createFileShareId(fileArg));
            return result.fileId;
        } catch (Exception e) {
            log.warn("transfer product picture path to fileId failed, ea={},fsUserId={},path={}", fileArg.ea, fsUserId, fileArg.path, e);
        }
        return null;
    }

    private List<IObjectData> getWechatFanByPhone(User user, String wxAppId, List<String> wxOpenIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.getFilters().add(buildFilter(WechatFanFieldContants.WX_APP_ID, Lists.newArrayList(wxAppId), Operator.EQ));
        query.getFilters().add(buildFilter(WechatFanFieldContants.WX_OPEN_ID, Lists.newArrayList(wxOpenIds), Operator.IN));
        query.setOffset(0);
        query.setLimit(wxOpenIds.size());
        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(user, WechatFanFieldContants.API_NAME, query);
        if (CollectionUtils.isEmpty(result.getData())) {
            return Lists.newArrayList();
        }
        return result.getData();
    }

    @Override
    public Result<Void> createOuterUser(ServiceContext serviceContext, CreateOuterUserArg arg) {
        Map<String, Object> objectData = new HashMap<>();
        objectData.put(PublicEmployeeObjConstant.NAME, arg.getName());
        objectData.put(PublicEmployeeObjConstant.MOBILE, arg.getPhone());
        objectData.put(PublicEmployeeObjConstant.OUTER_TENANT_ID, arg.getDownstreamOuterTenantId() + "");
        try {
            CrmCustomerContactVo crmCustomerContactVo = new CrmCustomerContactVo();
            crmCustomerContactVo.setName(arg.getName());
            crmCustomerContactVo.setMobile(arg.getPhone());
            crmCustomerContactVo.setTel(arg.getPhone());
            String contractId = crmManager.createContactIfCan(getEa(serviceContext), -10000, arg.getObjectApiName(), arg.getCrmObjectId(), crmCustomerContactVo);
            objectData.put(PublicEmployeeObjConstant.CONTRACT_ID, contractId);
        } catch (Exception e) {
            log.warn("", e);
        }
        List<String> outerRoleIds = new ArrayList<>();
        //默认第一个角色为主角色
        if (arg.getToAddMainAppRoleDatas() != null) {
            for (AppRoleData toAddMainAppRoleData : arg.getToAddMainAppRoleDatas()) {
                outerRoleIds.add(toAddMainAppRoleData.getOuterRoleId());
            }
        }
        if (arg.getToAddAppRoleDatas() != null) {
            for (AppRoleData toAddAppRoleData : arg.getToAddAppRoleDatas()) {
                outerRoleIds.add(toAddAppRoleData.getOuterRoleId());
            }
        }
        if (!outerRoleIds.isEmpty()) {
            objectData.put(PublicEmployeeObjConstant.OUTER_ROLE_IDS, outerRoleIds);
        }
        AddActionArg addActionArg = new AddActionArg();
        addActionArg.setObjectData(objectData);
        addAction(serviceContext, addActionArg);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> updateLoginPassword(ServiceContext serviceContext, UpdateLoginPasswordArg arg) {
        int upstreamEi = getTenantIdInt(serviceContext);
        try {
            String passwordRaw = RSAUtil.decrypt(ConfigUtil.erPublicEmployeePasswordPrivateKey, arg.getPassword());

            if (StringUtils.isNotBlank(passwordRaw)) {
                if (PasswordLevelUtil.checkPasswordWeak(passwordRaw)) {
                    return Result.newError(BusinessErrorEnum.PASSWORD_TOO_WEAK.getErrorCode(), BusinessErrorEnum.PASSWORD_TOO_WEAK.getDesc());
                }
            }

            // 更新密码
            publicEmployeeDao.updatePublicEmployeeLoginPassword(serviceContext.getUser(), arg.getDownstreamOuterUid(), passwordRaw);
            // 清除session
            com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj
                    .newInstance(upstreamEi, arg.getDownstreamTenantId(), arg.getDownstreamOuterUid());
            InvalidSessionForResetPasswordArg invalidSessionArg = new InvalidSessionForResetPasswordArg();
            invalidSessionArg.setUpstreamEi(upstreamEi);
            invalidSessionArg.setDownstreamOuterUid(arg.getDownstreamOuterUid());
            authService.invalidSessionForResetPassword(headerObj, invalidSessionArg);
        } catch (Exception e) {
            log.warn("updateLoginPassword error.", e);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), ResultCode.SYSTEM_ERROR.getErrorMessage());
        }
        return Result.newResult(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getErrorMessage());
    }

    @Override
    public Result<Void> sendBindInviteNotify(ServiceContext serviceContext, SendBindInviteNotifyArg arg) {
        Integer tenantId = serviceContext.getUser().getTenantIdInt();
        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        List<IObjectData> publicEmployeeObjs = publicEmployeeDao.batchGetByIds(User.systemUser(tenantId + ""), arg.getSendOuterUids());
        List<SendSmsEmployeeData> employees = new ArrayList<>();
        for (IObjectData objectData : publicEmployeeObjs) {
            PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(objectData);
            SendSmsEmployeeData sendSmsEmployeeData = new SendSmsEmployeeData();
            sendSmsEmployeeData.setOuterUid(publicEmployeeObj.getOuterUid());
            sendSmsEmployeeData.setMobile(publicEmployeeObj.getMobile());
            sendSmsEmployeeData.setLoginEmail(publicEmployeeObj.getLoginEmail());
            sendSmsEmployeeData.setPasswordRaw(null);
            employees.add(sendSmsEmployeeData);
        }
        publicEmployeeManager.sendBindInviteNotify(ea, employees);
        return Result.newSuccess();
    }

    /**
     * 对外函数接口
     */
    @Override
    public Result getPublicEmployeesBindInfo(ServiceContext serviceContext, GetPublicEmployeesBindInfoArg arg) {
        if (arg.getDownstreamOuterUid() == null || arg.getDownstreamOuterTenantId() == null) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.PARAMS_ERROR.getErrorMessage());
        }
        GetPublicEmployeesBindInfoResult result = new GetPublicEmployeesBindInfoResult();
        List<GetPublicEmployeesBindInfoResult.BindWxServiceData> bindWxServiceDatas = Lists.newArrayList();
        List<GetPublicEmployeesBindInfoResult.UsedWxProgramData> usedWxProgramDatas = Lists.newArrayList();
        result.setBindWxServiceDatas(bindWxServiceDatas);
        result.setUsedWxProgramDatas(usedWxProgramDatas);

        String upstreamEa = getEa(serviceContext);

        // 微信公众号
        List<ERAccountVo> erBindThirdAccounts = enterpriseRelationAccountService.getFsAccountVoByOuterUserIds(upstreamEa, Lists.newArrayList(arg.getDownstreamOuterUid()),
                ErBindThirdAccountTypeEnum.WX_SERVICE.getType()).getData();
        List<IObjectData> objectDatas = CommonPoolUtil.execute(erBindThirdAccounts, 500, x -> {
            try {
                SearchTemplateQuery query = this.buildSearchWechatFanArg(x);
                QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(serviceContext.getUser(), WechatFanFieldContants.API_NAME, query);
                return queryResult.getData();
            } catch (Exception e) {
                log.warn("", e);
                return Lists.newArrayList();
            }
        });
        Map<String, String> wxServiceAppIdToNameMap = weChatManager.getWxAppIdNameByEa(upstreamEa);
        Map<String, IObjectData> wxAppIdAndOpenIdToDataMap = objectDatas.stream()
                .collect(Collectors.toMap(objectData -> objectData.get(WechatFanFieldContants.WX_APP_ID) + "-" + objectData.get(WechatFanFieldContants.WX_OPEN_ID), Function.identity(), (v1, v2) -> v1));
        for (ERAccountVo erAccountVo : erBindThirdAccounts) {
            String wxServiceAppId = erAccountVo.getWxAppId();
            if (!wxServiceAppIdToNameMap.containsKey(wxServiceAppId)) {
                continue;
            }
            String wxServiceName = wxServiceAppIdToNameMap.get(wxServiceAppId);

            GetPublicEmployeesBindInfoResult.BindWxServiceData bindWxServiceData = new GetPublicEmployeesBindInfoResult.BindWxServiceData();
            bindWxServiceData.setWxServiceId(erAccountVo.getWxAppId());
            bindWxServiceData.setWxServiceName(wxServiceName);

            String appIdAndOpenIdKey = wxServiceAppId + "-" + erAccountVo.getWxOpenId();
            IObjectData objectData = wxAppIdAndOpenIdToDataMap.get(appIdAndOpenIdKey);
            if (objectData != null && "attention".equals(objectData.get("attention_status"))) {
                bindWxServiceData.setIsFollow(true);
                bindWxServiceData.setFollowTime((Long) objectData.get("attention_time"));
            } else if (objectData != null && !"attention".equals(objectData.get("attention_status"))) {
                bindWxServiceData.setIsFollow(false);
                bindWxServiceData.setFollowTime(0L);
            } else {
                bindWxServiceData.setIsFollow(null);
                bindWxServiceData.setFollowTime(null);
            }
            if (erAccountVo.getBindTime() != null) {
                bindWxServiceData.setBindTime(erAccountVo.getBindTime().getTime());
            }
            bindWxServiceDatas.add(bindWxServiceData);
        }

        // 微信小程序
        com.facishare.enterprise.common.result.Result<List<WechatAppIdAndSecret>> miniProgramResult = miniProgramAuthGrayService.listByEaWithAll(upstreamEa);
        if (!miniProgramResult.isSuccess()) {
            return Result.newError(miniProgramResult.getErrCode(), miniProgramResult.getErrMessage());
        }

        if (CollectionUtils.isEmpty(miniProgramResult.getData())) {
            return Result.newError(Result.ResultCode.SUCCESS.getErrorCode(), Result.ResultCode.SUCCESS.getErrorMessage(), result);
        }
        Map<String, WechatAppIdAndSecret> wxAppIdMiniProgramDataMap = miniProgramResult.getData().stream().collect(Collectors.toMap(WechatAppIdAndSecret::getAppId, Function.identity()));

        List<ERAccountVo> erBindThirdWxAppletsAccounts = enterpriseRelationAccountService.getErAccountByOuterUIdsAndWxAppIds(upstreamEa, Lists.newArrayList(arg.getDownstreamOuterUid()),
                wxAppIdMiniProgramDataMap.keySet(), ErBindThirdAccountTypeEnum.WX_MINI_PROGRAM.getType()).getData();
        for (ERAccountVo erAccountVo : erBindThirdWxAppletsAccounts) {
            String wxAppId = erAccountVo.getWxAppId();

            GetPublicEmployeesBindInfoResult.UsedWxProgramData usedWxProgramData = new GetPublicEmployeesBindInfoResult.UsedWxProgramData();
            usedWxProgramData.setWxProgramId(wxAppId);
            usedWxProgramData.setWxProgramName(wxAppIdMiniProgramDataMap.get(wxAppId).getName());
            usedWxProgramData.setFirstUseTime(erAccountVo.getBindTime().getTime());
            usedWxProgramDatas.add(usedWxProgramData);
        }
        return Result.newError(Result.ResultCode.SUCCESS.getErrorCode(), Result.ResultCode.SUCCESS.getErrorMessage(), result);
    }

    /**
     * 对外函数接口
     */
    @Override
    public Result listWxOpenIdsByPhone(ServiceContext serviceContext, ListWxOpenIdsByPhoneArg arg) {
        if (StringUtils.isEmpty(arg.getPhone()) || StringUtils.isEmpty(arg.getWxAppId()) || !checkUpstreamEaAndWxAppIdBindRelation(getEa(serviceContext), arg.getWxAppId(), arg.getType())) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.PARAMS_ERROR.getErrorMessage());
        }
        List<ListWxOpenIdsByPhoneResult> results = Lists.newArrayList();
        List<WechatOpenIdPhoneMapper> mappers = wechatOpenIdPhoneMapperDaoOldManager.listByAppIdsAndPhone(arg.getPhone(), Lists.newArrayList(arg.getWxAppId()));
        for (WechatOpenIdPhoneMapper mapper : mappers) {
            ListWxOpenIdsByPhoneResult result = new ListWxOpenIdsByPhoneResult();
            result.setWxOpenId(mapper.getOpenId());
            result.setCreateTime(mapper.get_id().getDate());
            results.add(result);
        }
        results.sort(Comparator.comparing(ListWxOpenIdsByPhoneResult::getCreateTime));
        return Result.newError(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getErrorMessage(), results);
    }

    @Override
    public ValidPublicEmployeeLicenseQuoteResult validPublicEmployeeLicenseQuote(ServiceContext serviceContext, ValidPublicEmployeeLicenseQuoteArg arg) {
        ValidPublicEmployeeLicenseQuoteResult res = new ValidPublicEmployeeLicenseQuoteResult();
        Integer publicEmployeeIdentityType = arg.getPublicEmployeeIdentityType();
        if (publicEmployeeIdentityType == null || IdentityType.getByTypeOrNull(arg.getPublicEmployeeIdentityType()) == null) {
            res.setValid(false);
            res.setErrorCode(ResultCode.LOGIN_IDENTITY_ERROR.getErrorCode());
            res.setErrorMessage(ResultCode.LOGIN_EMAIL_REPEAT.getErrorMessage());
            return res;
        }
        Integer tenantId = serviceContext.getUser().getTenantIdInt();

        try {
            if (IdentityType.CORP.getType() == publicEmployeeIdentityType && !publicEmployeeManager.checkHasRemainPartnerUserLimitQuota(tenantId, 1)) {
                res.setValid(false);
                res.setErrorCode(BusinessErrorEnum.PARTNER_USER_FIRM_LIMIT_OUT.getErrorCode());
                res.setErrorMessage(BusinessErrorEnum.PARTNER_USER_FIRM_LIMIT_OUT.getDesc());
                return res;
            }
            // 判断终端用户数配额(个人类型互联用户)
            if (IdentityType.PERSONAL.getType() == publicEmployeeIdentityType && !publicEmployeeManager.checkHasRemainTerminalUserLimitQuota(tenantId, 1)) {
                res.setValid(false);
                res.setErrorCode(BusinessErrorEnum.TERMINAL_USER_LIMIT_OUT.getErrorCode());
                res.setErrorMessage(BusinessErrorEnum.TERMINAL_USER_LIMIT_OUT.getDesc());
                return res;
            }
        } catch (Exception e) {
            log.warn("", e);
        }

        try {
            // 校验互联企业创建参数
            Long outerTenantId = checkEnterpriseRelationAddArgAndGetOuterTenantId(serviceContext, arg);
            if (outerTenantId != null && StringUtils.isNotBlank(arg.getPublicEmployeeObjName())) {
                // 校验互联用户创建参数 此时需要校验互联用户手机号参数
                RequestContext requestContext = RequestContext.builder().requestSource(RequestSource.INNER).tenantId(tenantId + "").user(serviceContext.getUser()).build();
                ActionContext actionContext = new ActionContext(requestContext, null, null);
                ObjectDataDocument objectDataDocument = new ObjectDataDocument();
                objectDataDocument.put(PublicEmployeeObjConstant.NAME, arg.getPublicEmployeeObjName());
                if (arg.getPublicEmployeeObjMobile() != null) {
                    objectDataDocument.put(PublicEmployeeObjConstant.MOBILE, arg.getPublicEmployeeObjMobile());
                }
                if (arg.getPublicEmployeeObjEmail() != null) {
                    objectDataDocument.put(PublicEmployeeObjConstant.EMAIL, arg.getPublicEmployeeObjEmail());
                }
                objectDataDocument.put(PublicEmployeeObjConstant.OUTER_TENANT_ID, outerTenantId + "");
                objectDataDocument.put(EnterpriseRelationObjConstant.RECORD_TYPE, arg.getPublicEmployeeObjRecordType());
                publicEmployeeAddActionService.before(actionContext, objectDataDocument);
            }
        } catch (ValidateException e) {
            res.setValid(false);
            res.setErrorCode(e.getErrorCode());
            res.setErrorMessage(e.getMessage());
            return res;
        } catch (DuplicateException e) {
            // ignore
        }
        res.setValid(true);
        return res;
    }

    @Override
    public Result fixPublicEmployeeObjEmployeeIdData(ServiceContext serviceContext, FixPublicEmployeeObjEmployeeIdDataArg arg) {
        if (CollectionUtils.isEmpty(arg.getObjectDatas())) {
            return Result.newResult(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getErrorMessage());
        }
        boolean error = false;
        for (PublicEmployeeObj publicEmployeeObj : PublicEmployeeObj.newInstance(arg.getObjectDatas())) {
            try {
                if (StringUtils.isNotBlank(publicEmployeeObj.getEmployeeId())) {
                    continue;
                }
                publicEmployeeManager.fixPublicEmployeeObjEmployeeIdData(serviceContext.getUser(), publicEmployeeObj);
            } catch (Exception e) {
                error = true;
                log.warn("fixPublicEmployeeObjEmployeeIdData ex ei={}", serviceContext.getUser().getTenantIdInt(), e);
            }
        }
        if (error) {
            return Result.newResult(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.PARAMS_ERROR.getErrorMessage());
        }
        return Result.newResult(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getErrorMessage());
    }

    private Long checkEnterpriseRelationAddArgAndGetOuterTenantId(ServiceContext serviceContext, ValidPublicEmployeeLicenseQuoteArg arg) throws ValidateException, DuplicateException {
        Integer tenantId = serviceContext.getUser().getTenantIdInt();
        RequestContextBuilder reqBuilder = RequestContext.builder().requestSource(RequestSource.INNER).tenantId(tenantId + "").user(serviceContext.getUser());
        RequestContext requestContext = reqBuilder.build();
        ActionContext actionContext = new ActionContext(requestContext, null, null);
        actionContext.setAttribute(EnterpriseRelationAddAction.NOT_CHECK_MAPPER_OBJECT_ID_ARG, true);
        if (StringUtils.isNotBlank(arg.getMapperObjectId())) {
            IObjectData relationObj = enterpriseRelationManager.getByMapperId(tenantId, arg.getMapperApiName(), arg.getMapperObjectId());
            if (relationObj != null) {
                // 关联id有对应互联用户 则需要再校验name等参数
                return EnterpriseRelationObj.newInstance(relationObj).getOuterTenantId();
            }
        } else {
            // 关联id没有对应互联企业  验证name参数
            if (StringUtils.isNotBlank(arg.getEnterpriseRelationObjName())) {
                ObjectDataDocument objectDataDocument = new ObjectDataDocument();
                objectDataDocument.put(EnterpriseRelationObjConstant.NAME, arg.getEnterpriseRelationObjName());
                objectDataDocument.put(EnterpriseRelationObjConstant.RECORD_TYPE, arg.getEnterpriseRelationObjRecordType());
                enterpriseRelationAddActionService.before(objectDataDocument, actionContext);
            }
        }
        return null;
    }

    @Override
    public IdLongData getAndIncCurrentOuterUIdNumber(ServiceContext serviceContext, NoArg arg) {
        long id = globalMetaManager.getAndIncCurrentOuterUIdNumber();
        IdLongData idLongData = new IdLongData();
        idLongData.setId(id);
        return idLongData;
    }

    @Override
    public Result<Void> changeMobile(ServiceContext serviceContext, ChangeMobileArg arg) {
        // 更新互联用户手机 -> 触发删改同步 -> 修改联系人手机
        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put(PublicEmployeeObjConstant.ID, arg.getDownstreamOuterUid() + "");
        objectData.put(PublicEmployeeObjConstant.MOBILE, arg.getMobile());
        EditActionArg editActionArg = new EditActionArg();
        editActionArg.setObjectData(objectData);
        editAction(serviceContext, editActionArg);
        // 退出登录
        com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj
                .newInstance(serviceContext.getUser().getTenantIdInt(), arg.getDownstreamOuterTenantId(), arg.getDownstreamOuterUid());
        InvalidSessionForResetPasswordArg invalidSessionArg = new InvalidSessionForResetPasswordArg();
        invalidSessionArg.setUpstreamEi(serviceContext.getUser().getTenantIdInt());
        invalidSessionArg.setDownstreamOuterUid(arg.getDownstreamOuterUid());
        authService.invalidSessionForResetPassword(headerObj, invalidSessionArg);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> invalidAction(ServiceContext serviceContext, InvalidActionArg invalidActionArg) {
        User user = serviceContext.getUser();
        actionCallManager.triggerPublicEmployeeInvalidAction(user, invalidActionArg.getId());
        return Result.newSuccess();
    }

    @Override
    public FindPublicEmployeeByNameResult findPublicEmployeeByName(ServiceContext serviceContext, FindPublicEmployeeByNameArg arg) {
        FindPublicEmployeeByNameResult result = new FindPublicEmployeeByNameResult();
        result.setPublicEmployeeInfos(ImmutableList.of());
        if (ObjectUtils.isEmpty(arg.getParams())) {
            return result;
        }


        Set<String> employeeNames = new HashSet<>();
        Set<Long> outerTenantIds = new HashSet<>();
        for (FindPublicEmployeeByNameArg.Param param : arg.getParams()) {
            if (param.getOuterTenantId() == null || StringUtils.isEmpty(param.getEmployeeName())) {
                continue;
            }
            employeeNames.add(param.getEmployeeName());
            outerTenantIds.add(param.getOuterTenantId());
        }

        if (ObjectUtils.isEmpty(employeeNames)) {
            return result;
        }

        PublicEmployeeQueryArg queryArg = new PublicEmployeeQueryArg();
        if (arg.getType() == null) {
            queryArg.setTypes(null);
        } else {
            queryArg.setTypes(Lists.newArrayList(arg.getType()));
        }
        queryArg.setEmployeeNames(Lists.newArrayList(employeeNames));
        queryArg.setOuterTenantIds(Lists.newArrayList(outerTenantIds));
        queryArg.setSelectFields(Lists.newArrayList(PublicEmployeeObjConstant.TYPE, PublicEmployeeObjConstant.OUTER_UID, PublicEmployeeObjConstant.OUTER_TENANT_ID, PublicEmployeeObjConstant.NAME));
        QueryResult<IObjectData> query = publicEmployeeDao.getQuery(serviceContext.getUser(), queryArg);
        if (ObjectUtils.isEmpty(query)) {
            return result;
        }

        Map<Long, Set<String>> outerTenantId2EmployeeNames = arg.getParams().stream()
                .filter(param -> param.getOuterTenantId() != null && ObjectUtils.isNotEmpty(param.getEmployeeName()))
                .collect(Collectors.groupingBy(FindPublicEmployeeByNameArg.Param::getOuterTenantId,
                        Collectors.mapping(FindPublicEmployeeByNameArg.Param::getEmployeeName, Collectors.toSet())));


        List<FindPublicEmployeeByNameResult.PublicEmployeeInfo> publicEmployeeInfos = Lists.newArrayList();
        for (IObjectData objectData : query.getData()) {
            PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(objectData);
            Set<String> nameSet = outerTenantId2EmployeeNames.getOrDefault(publicEmployeeObj.getOuterTenantId(), ImmutableSet.of());
            if (!nameSet.contains(publicEmployeeObj.getName())) {
                continue;
            }
            FindPublicEmployeeByNameResult.PublicEmployeeInfo publicEmployeeInfo = new FindPublicEmployeeByNameResult.PublicEmployeeInfo();
            publicEmployeeInfo.setType(publicEmployeeObj.getType());
            publicEmployeeInfo.setOuterUserId(publicEmployeeObj.getOuterUserId());
            publicEmployeeInfo.setOuterTenantId(publicEmployeeObj.getOuterTenantId());
            publicEmployeeInfo.setOuterEmployeeName(publicEmployeeObj.getName());
            publicEmployeeInfos.add(publicEmployeeInfo);
        }
        result.setPublicEmployeeInfos(publicEmployeeInfos);

        return result;
    }

    private boolean checkUpstreamEaAndWxAppIdBindRelation(String upstreamEa, String wxAppId, Integer type) {
        // 校验公众号/小程序是否属于该上游企业
        if (UnionAuthType.SMALL_PROGRAM.getType() == type) {
            com.facishare.enterprise.common.result.Result<String> belongEaResult = miniProgramAuthGrayService.getAppBelongEa(wxAppId);
            if (belongEaResult.isSuccess() && "all".equalsIgnoreCase(belongEaResult.getData()) || upstreamEa.equals(belongEaResult.getData())) {
                return true;
            }
        }
        if (UnionAuthType.WE_CHAT_SERVICE_PROGRAM.getType() == type) {
            com.facishare.wechat.union.common.result.Result<String> eaBindInfoResult = eaBindInfoRestService.getEaByWxAppId(wxAppId);
            if (eaBindInfoResult.isSuccess() && upstreamEa.equals(eaBindInfoResult.getData())) {
                return true;
            }
        }
        return false;
    }

    private SearchTemplateQuery buildSearchWechatFanArg(List<ERAccountVo> entityList) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(entityList.size());
        searchQuery.setOffset(0);
        List<Wheres> wheresList = new ArrayList<>();
        Map<String, List<String>> wxAppIdAndOpenIdsMap = new HashMap<>();
        for (ERAccountVo entity : entityList) {
            if (org.apache.commons.lang.StringUtils.isEmpty(entity.getWxAppId())) {
                continue;
            }
            List<String> wxOpenIds = wxAppIdAndOpenIdsMap.get(entity.getWxAppId());
            if (wxOpenIds == null) {
                wxOpenIds = new ArrayList<>();
                wxAppIdAndOpenIdsMap.put(entity.getWxAppId(), wxOpenIds);
            }
            wxOpenIds.add(entity.getWxOpenId());
        }
        for (Entry<String, List<String>> entry : wxAppIdAndOpenIdsMap.entrySet()) {
            IFilter wxAppIdFilter = buildFilter(WechatFanFieldContants.WX_APP_ID, Lists.newArrayList(entry.getKey()), Operator.EQ);
            IFilter wxOpenIdFilter = buildFilter(WechatFanFieldContants.WX_OPEN_ID, entry.getValue(), Operator.IN);
            wheresList.add(buildWheresByFilter(Lists.newArrayList(wxAppIdFilter, wxOpenIdFilter)));
        }
        searchQuery.setWheres(wheresList);
        return searchQuery;
    }

    private Wheres buildWheresByFilter(List<IFilter> filters) {
        Wheres wheres = new Wheres();
        wheres.setFilters(filters);
        return wheres;
    }

    private IFilter buildFilter(String fieldName, List<String> fieldValues, Operator operator) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setFieldValues(fieldValues);
        filter.setOperator(operator);
        return filter;
    }

    private String getEa(ServiceContext serviceContext) {
        String tenantId = serviceContext.getUser().getTenantId();
        if (org.apache.commons.lang.StringUtils.isEmpty(tenantId)) {
            return null;
        }
        return eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
    }

    private String getTenantId(ServiceContext serviceContext) {
        return serviceContext.getUser().getTenantId();
    }

    private int getTenantIdInt(ServiceContext serviceContext) {
        return serviceContext.getUser().getTenantIdInt();
    }

    private String getUserId(ServiceContext serviceContext) {
        return serviceContext.getUser().getUserId();
    }
}
