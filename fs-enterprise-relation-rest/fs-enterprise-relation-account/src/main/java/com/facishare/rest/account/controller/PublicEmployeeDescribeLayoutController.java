package com.facishare.rest.account.controller;

import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.AccountManageMode;
import com.facishare.enterprise.common.constant.IdentityType;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.model.OuterRoleVo;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.enterprise.common.util.GrayUtils;
import com.facishare.enterprise.common.util.I18nUtil;
import com.facishare.er.api.model.vo.EnterpriseMetaVo;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.BooleanFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.enterprise.common.util.I18nUtil;
import com.facishare.rest.account.dao.EnterpriseRelationDao;
import com.facishare.rest.account.remote.CrmManager;
import com.facishare.rest.account.remote.FxiaokeAccountManager;
import com.facishare.rest.account.service.EnterpriseMetaDataService;
import com.facishare.rest.account.service.ErOuterRoleService;
import com.facishare.rest.account.service.InnerErDepartmentService;
import com.facishare.rest.account.util.EnterpriseRelationLayoutUtil;
import com.facishare.rest.account.util.PaasControllerUtil;
import com.facishare.rest.account.util.PublicEmployeeControllerUtil;
import com.facishare.rest.account.util.fieldbuilder.SelectOptionBuilder;
import com.facishare.rest.account.util.fieldbuilder.TextFieldDescribeBuilder;
import com.fxiaoke.crmrestapi.common.contants.ContactFieldContants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.FindCurrencyList;
import com.fxiaoke.crmrestapi.result.FindCurrencyList.Currency;
import com.fxiaoke.crmrestapi.result.MultiCurrencyStatusResult.Status;
import com.fxiaoke.crmrestapi.service.CurrencyService;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Language;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: jiangxch
 * @date: 2021/10/18 11:39
 */
@Slf4j
public class PublicEmployeeDescribeLayoutController extends StandardDescribeLayoutController {
    public final static String BASE_FIELD_SECTION = "base_field_section__c";
    public final static String DEFAULT_ER_LANGUAGE = "zh-CN";
    public final static String DEFAULT_ER_TIMEZONE = "Asia/Shanghai";
    public final static String DEFAULT_ER_CURRENCY = "CNY";
    private EnterpriseRelationDao enterpriseRelationDao = SpringUtil.getContext().getBean(EnterpriseRelationDao.class);
    private EnterpriseMetaDataService enterpriseMetaDataService = SpringUtil.getContext().getBean(EnterpriseMetaDataService.class);
    private ErOuterRoleService outerRoleService = SpringUtil.getContext().getBean(ErOuterRoleService.class);
    private CrmManager crmManager = SpringUtil.getContext().getBean(CrmManager.class);
    private InnerErDepartmentService erDepartmentService = SpringUtil.getContext().getBean(InnerErDepartmentService.class);
    private CurrencyService currencyService = SpringUtil.getContext().getBean(CurrencyService.class);
    private FxiaokeAccountManager fxiaokeAccountManager = SpringUtil.getContext().getBean(FxiaokeAccountManager.class);
    private EIEAConverter eieaConverter = SpringUtil.getContext().getBean(EIEAConverter.class);

    protected void doFunPrivilegeCheck() {
        User user = controllerContext.getUser();
        boolean isRelationAdmin = fxiaokeAccountManager.isRelationAdmin(user);
        if (isRelationAdmin) {
            return;
        }
        super.doFunPrivilegeCheck();
    }

    @SneakyThrows
    protected Result after(Arg arg, Result result) {
        User user = this.controllerContext.getUser();
        result = super.after(arg, result);
        if (!arg.isIncludeLayout()) {
            return result;
        }
        FormComponent formComponent = PaasControllerUtil.getFormComponent(result);
        if (UdobjConstants.LAYOUT_TYPE_ADD.equals(arg.getLayout_type())) {
            Set<String> readOnlyFieldSet = new HashSet<>();
            Set<String> removeFieldSet = new HashSet<>();
            //手机字段处理：只读
            readOnlyFieldSet.add(PublicEmployeeObjConstant.MOBILE);
            //外部id处理:不显示
            removeFieldSet.add(PublicEmployeeObjConstant.OUTER_UID);
            removeFieldSet.add(PublicEmployeeObjConstant.EMPLOYEE_ID);
            removeFieldSet.add(PublicEmployeeObjConstant.START_TIME);
            removeFieldSet.add(PublicEmployeeObjConstant.STOP_TIME);
            //图片不允许编辑，因为图片存储的是头像，而渠道门户的编辑页，存储的是 头像文件，后台管理保存的图片是企业文件
            //两者不能通用，兼容起来十分困难，需要采用 图片进行存储，所以不支持下发编辑
            removeFieldSet.add(PublicEmployeeObjConstant.PROFILE_IMAGE);
            //是否主负责人
            addRelationOwner(user, formComponent);
            //外部角色处理：新增外部角色
            addOuterRoleField(formComponent, controllerContext.getRequestContext());// 默认企业用户
            //下游语言
            addLanguageField(user, formComponent, DEFAULT_ER_LANGUAGE);
            //下游时区
            addTimeZoneField(formComponent, DEFAULT_ER_TIMEZONE);
            //下游币种
            if (multiCurrencyIsOpen(user.getTenantIdInt())) {
                addMCCurrencyField(formComponent, DEFAULT_ER_CURRENCY);
            }
            if (GrayUtils.hasInLoginEmailFeatureGray(controllerContext.getEa())) {
                readOnlyFieldSet.add(PublicEmployeeObjConstant.LOGIN_EMAIL);
                addPasswordField(formComponent);
            }
            // 如果是互联企业主从新建  设置主属部门和附属部门只读
            if (PublicEmployeeObjConstant.OUTER_TENANT_ID.equals(arg.getRelationFieldApiName())) {
                if (erDepartmentService.isErDepartmentSwitchOpened(user.getTenantIdInt())) {
                    removeFieldSet.add(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT);
                    removeFieldSet.add(PublicEmployeeObjConstant.VICE_ER_DEPARTMENTS);
                }
            }

            // 下游 新建互联用户联系人支持非必填
            if (user.isOutUser()) {
                for (IFieldSection iFieldSection : formComponent.getFieldSections()) {
                    for (IFormField formField : iFieldSection.getFields()) {
                        if ("contract_id".equals(formField.getFieldName()) && formField.isRequired()) {
                            formField.setRequired(false);
                        }
                    }
                }
            }
            //是否发送开通短信
            String ea = eieaConverter.enterpriseIdToAccount(user.getTenantIdInt());
            com.facishare.enterprise.common.result.Result<EnterpriseMetaVo> enterpriseMetaData = enterpriseMetaDataService.getEnterpriseMetaData(ea);
            addIsSendInviteNotifyField(formComponent, BooleanUtils.isTrue(enterpriseMetaData.getData().getAutoSendSMSWhenAccountCreate()));
            PaasControllerUtil.fillSelectOptionForBooleanField(user.getTenantIdInt(), result.getObjectDescribe(), PublicEmployeeObjConstant.DATA_MANAGER, false);
            PaasControllerUtil.setReadOnlyFormFields(formComponent, BASE_FIELD_SECTION, readOnlyFieldSet);
            PaasControllerUtil.removeFormFields(formComponent, BASE_FIELD_SECTION, removeFieldSet);
        }
        if (UdobjConstants.LAYOUT_TYPE_EDIT.equals(arg.getLayout_type())) {
            Set<String> readOnlyFieldSet = new HashSet<>();
            Set<String> removeFieldSet = new HashSet<>();
            //用户名称处理：企业代管、个人类型可编辑，其他不可编辑
            PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(this.objectData);
            EnterpriseRelationObj enterpriseRelationObjEntity = enterpriseRelationDao.getById(controllerContext.getUser(), publicEmployeeObj.getOuterTenantId());
            Integer accountManageMode = AccountManageMode.ASSIST_MANAGE.getType();
            if (enterpriseRelationObjEntity != null && enterpriseRelationObjEntity.getManageMode() != null) {
                accountManageMode = enterpriseRelationObjEntity.getManageMode();
            }
            //用户名称处理：企业代管 可编辑；个人类型 可编辑；默认不可编辑
            if (!(publicEmployeeObj.getIdentityType() == IdentityType.PERSONAL.getType() || AccountManageMode.ASSIST_MANAGE.getType() == accountManageMode)) {
                readOnlyFieldSet.add(PublicEmployeeObjConstant.NAME);
            }
            //手机号码处理：个人类型 不可编辑
            if (publicEmployeeObj.getIdentityType() == IdentityType.PERSONAL.getType()) {
                readOnlyFieldSet.add(PublicEmployeeObjConstant.MOBILE);
                if (GrayUtils.hasInLoginEmailFeatureGray(controllerContext.getEa())) {
                    readOnlyFieldSet.add(PublicEmployeeObjConstant.LOGIN_EMAIL);
                }
            }
            //关联联系人处理：企业类型下联系人有效不可以编辑；其他可以编辑
            if (publicEmployeeObj.getIdentityType() == IdentityType.CORP.getType()) {
                boolean isContractValid = crmManager.isDataValid(user, ContactFieldContants.API_NAME, publicEmployeeObj.getContractId());
                if (isContractValid && publicEmployeeObj.getContractId() != null) {
                    readOnlyFieldSet.add(PublicEmployeeObjConstant.CONTRACT_ID);
                }
            }
            //互联企业:个人类型 不显示;企业类型 不可以编辑
            if (publicEmployeeObj.getIdentityType() == IdentityType.CORP.getType()) {
                readOnlyFieldSet.add(PublicEmployeeObjConstant.OUTER_TENANT_ID);
            } else {
                removeFieldSet.add(PublicEmployeeObjConstant.OUTER_TENANT_ID);
            }
            removeFieldSet.add(PublicEmployeeObjConstant.STOP_TIME);
            removeFieldSet.add(PublicEmployeeObjConstant.START_TIME);
            //外部账号和主负责人不显示
            removeFieldSet.add(PublicEmployeeObjConstant.OUTER_UID);
            removeFieldSet.add(PublicEmployeeObjConstant.EMPLOYEE_ID);
            removeFieldSet.add(PublicEmployeeObjConstant.DATA_MANAGER);
            removeFieldSet.add(PublicEmployeeObjConstant.PROFILE_IMAGE);
            //是否主负责人
            addRelationOwner(user, formComponent);
            //外部角色处理：新增外部角色
            addOuterRoleField(formComponent, controllerContext.getRequestContext());
            if (GrayUtils.hasInLoginEmailFeatureGray(controllerContext.getEa())) {
                addPasswordField(formComponent);
            }
            PaasControllerUtil.setReadOnlyFormFields(formComponent, BASE_FIELD_SECTION, readOnlyFieldSet);
            PaasControllerUtil.removeFormFields(formComponent, BASE_FIELD_SECTION, removeFieldSet);
            //下游语言
            addLanguageField(user, formComponent, publicEmployeeObj.getLanguage());
            //下游时区
            addTimeZoneField(formComponent, publicEmployeeObj.getTimeZone());
            //下游币种
            if (multiCurrencyIsOpen(user.getTenantIdInt())) {
                addMCCurrencyField(formComponent, publicEmployeeObj.getMcCurrency());
            }
            PaasControllerUtil.fillSelectOptionForBooleanField(user.getTenantIdInt(), result.getObjectDescribe(), PublicEmployeeObjConstant.DATA_MANAGER, false);
        }
        if (UdobjConstants.LAYOUT_TYPE_DETAIL.equals(arg.getLayout_type())) {
            addOuterRoleField(formComponent, controllerContext.getRequestContext());
        }
        if (UdobjConstants.LAYOUT_TYPE_DETAIL.equals(arg.getLayout_type())) {
            //外部角色处理：新增外部角色
            addOuterRoleField(formComponent, controllerContext.getRequestContext());
        }
        return result;
    }

    private void addRelationOwner(User user, FormComponent formComponent) {
        PaasControllerUtil.fillSelectOptionForBooleanField(user.getTenantIdInt(), result.getObjectDescribe(), PublicEmployeeObjConstant.RELATION_OWNER, false);

        List<IFormField> addFields = new ArrayList<>();
        addFields.add(PaasControllerUtil.newFormField(PublicEmployeeObjConstant.RELATION_OWNER, IFieldType.TRUE_OR_FALSE, true, false));
        PaasControllerUtil.addFormFields(formComponent, BASE_FIELD_SECTION, addFields);
    }


    private void addOuterRoleField(FormComponent formComponent, RequestContext requestContext) {
        com.facishare.enterprise.common.result.Result<List<OuterRoleVo>> outerRoles = outerRoleService.batchGetUserRoles(controllerContext.getUser());
        PublicEmployeeControllerUtil.addOuterRolesDescribe(outerRoles.getData(), requestContext, result.getObjectDescribe());

        List<IFormField> addFields = new ArrayList<>();
        addFields.add(PaasControllerUtil.newFormField(PublicEmployeeObjConstant.OUTER_ROLE_IDS, IFieldType.SELECT_MANY, true, false));
        PaasControllerUtil.addFormFields(formComponent, BASE_FIELD_SECTION, addFields);
    }

    private void addPasswordField(FormComponent formComponent) {
        String loginPasswordLabel = I18nUtil.get(GetI18nKeyUtil.getFieldLabelKey(PublicEmployeeObjConstant.API_NAME, PublicEmployeeObjConstant.LOGIN_PASSWORD),"登录密码");//ignoreI18n
        String loginPasswordHelpText = I18nUtil.get(GetI18nKeyUtil.getFieldHelpTextKey(PublicEmployeeObjConstant.API_NAME, "text", PublicEmployeeObjConstant.LOGIN_PASSWORD),
                I18nUtil.get(I18nKeyEnum.EPI_CONTROLLER_PUBLICEMPLOYEEDESCRIBELAYOUTCONTROLLER_236));
        TextFieldDescribe passwordFieldDescribe = TextFieldDescribeBuilder.builder().apiName(PublicEmployeeObjConstant.LOGIN_PASSWORD).label(loginPasswordLabel).helpText(loginPasswordHelpText)
                .maxLength(256).build();
        PublicEmployeeControllerUtil.addFieldDescribe(result.getObjectDescribe(), passwordFieldDescribe);

        List<IFormField> addFields = new ArrayList<>();
        addFields.add(PaasControllerUtil.newFormField(PublicEmployeeObjConstant.LOGIN_PASSWORD, IFieldType.TEXT, false, false));
        PaasControllerUtil.addFormFields(formComponent, BASE_FIELD_SECTION, addFields);
    }

    /**
     * 下游时区选择组件
     */
    private void addTimeZoneField(FormComponent formComponent, String defaultValue) {
        List<IFormField> addFields = new ArrayList<>();
        addFields.add(PaasControllerUtil.newFormField(PublicEmployeeObjConstant.ER_TIME_ZONE, IFieldType.SELECT_ONE, true, false));
        PaasControllerUtil.addFormFields(formComponent, BASE_FIELD_SECTION, addFields);
        ObjectDescribeDocument objectDescribe = result.getObjectDescribe();
        Map<String, IFieldDescribe> fields = objectDescribe.toObjectDescribe().getFieldDescribeMap();
        if (fields.containsKey(PublicEmployeeObjConstant.ER_TIME_ZONE)) {
            IFieldDescribe iFieldDescribe = fields.get(PublicEmployeeObjConstant.ER_TIME_ZONE);
            iFieldDescribe.setDefaultValue(defaultValue);
        }
        if (result.getObjectData() != null) {
            IObjectData iObjectData = result.getObjectData().toObjectData();
            iObjectData.set(PublicEmployeeObjConstant.ER_TIME_ZONE, defaultValue);
        }
    }

    /**
     * 下游币种选择列表组件
     */
    private void addMCCurrencyField(FormComponent formComponent, String defaultValue) {
        List<IFormField> addFields = new ArrayList<>();
        addFields.add(PaasControllerUtil.newFormField(PublicEmployeeObjConstant.ER_MC_CURRENCY, IFieldType.SELECT_ONE, false, false));
        PaasControllerUtil.addFormFields(formComponent, BASE_FIELD_SECTION, addFields);
        IObjectDescribe objectDescribe = result.getObjectDescribe().toObjectDescribe();
        Map<String, IFieldDescribe> fields = objectDescribe.getFieldDescribeMap();
        if (fields.containsKey(PublicEmployeeObjConstant.ER_MC_CURRENCY)) {
            SelectOneFieldDescribe objectFieldDescribeDocument = (SelectOneFieldDescribe) fields.get(PublicEmployeeObjConstant.ER_MC_CURRENCY);
            objectFieldDescribeDocument.setDefaultValue(defaultValue);
            // 填充多币种下拉选项值
            List<Currency> currencyList = getCurrencyList(Integer.valueOf(controllerContext.getTenantId()));
            if (!CollectionUtil.isEmpty(currencyList)) {
                List<ISelectOption> iSelectOptions = PublicEmployeeControllerUtil.buildMutCurrencyOptions(currencyList);
                objectFieldDescribeDocument.setSelectOptions(iSelectOptions);
            }
        }
        if (result.getObjectData() != null) {
            IObjectData iObjectData = result.getObjectData().toObjectData();
            iObjectData.set(PublicEmployeeObjConstant.ER_MC_CURRENCY, defaultValue);
        }
    }

    /**
     * 下游语言选择组件
     */
    private void addLanguageField(User user, FormComponent formComponent, String defaultValue) {
        List<IFormField> addFields = new ArrayList<>();
        addFields.add(PaasControllerUtil.newFormField(PublicEmployeeObjConstant.ER_LANGUAGE, IFieldType.SELECT_ONE, true, false));
        PaasControllerUtil.addFormFields(formComponent, BASE_FIELD_SECTION, addFields);
        IObjectDescribe objectDescribe = result.getObjectDescribe().toObjectDescribe();
        Map<String, IFieldDescribe> fields = objectDescribe.getFieldDescribeMap();
        if (fields.containsKey(PublicEmployeeObjConstant.ER_LANGUAGE)) {
            SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) fields.get(PublicEmployeeObjConstant.ER_LANGUAGE);
            selectOneFieldDescribe.setDefaultValue(defaultValue);
            // 填充多语言下拉选项值
            List<Language> languages = I18nClient.getInstance().getLanguage(user.getTenantIdInt());
            if (!CollectionUtil.isEmpty(languages)) {
                List<ISelectOption> iSelectOptions = PublicEmployeeControllerUtil.buildErLanguageOptions(languages);
                selectOneFieldDescribe.setSelectOptions(iSelectOptions);
            }
        }

        if (result.getObjectDescribeExt() != null) {
            IObjectDescribe objectDescribeExt = result.getObjectDescribeExt().toObjectDescribe();
            Map<String, IFieldDescribe> fieldsExt = objectDescribeExt.getFieldDescribeMap();
            if (fieldsExt.containsKey(PublicEmployeeObjConstant.ER_LANGUAGE)) {
                SelectOneFieldDescribe selectOneFieldDescribeExt = (SelectOneFieldDescribe) fieldsExt.get(PublicEmployeeObjConstant.ER_LANGUAGE);
                // 填充多语言下拉选项值
                List<Language> languages = I18nClient.getInstance().getLanguage(user.getTenantIdInt());
                if (!CollectionUtil.isEmpty(languages)) {
                    List<ISelectOption> iSelectOptions = PublicEmployeeControllerUtil.buildErLanguageOptions(languages);
                    if (selectOneFieldDescribeExt != null) {
                        selectOneFieldDescribeExt.setSelectOptions(iSelectOptions);
                        selectOneFieldDescribeExt.setDefaultValue(defaultValue);
                    } else {// 互联企业中的互联用户布局没此字段
                        String erLanguage = I18nUtil.get(GetI18nKeyUtil.getFieldLabelKey(PublicEmployeeObjConstant.API_NAME, PublicEmployeeObjConstant.ER_LANGUAGE),"语言");//ignoreI18n
                        SelectOneFieldDescribe fieldDescribe = new SelectOneFieldDescribe();
                        fieldDescribe.setApiName(PublicEmployeeObjConstant.ER_LANGUAGE);
                        fieldDescribe.setDescribeApiName(PublicEmployeeObjConstant.API_NAME);
                        fieldDescribe.setActive(true);
                        fieldDescribe.setStatus("released");
                        fieldDescribe.setDefineType("package");
                        fieldDescribe.setLabel(erLanguage);
                        fieldDescribe.setDescription(erLanguage);
                        fieldDescribe.setDefaultValue(defaultValue);
                        fieldDescribe.setSelectOptions(iSelectOptions);
                        objectDescribeExt.addFieldDescribe(fieldDescribe);
                    }
                }
            }
        }
        if (result.getObjectData() != null) {
            IObjectData iObjectData = result.getObjectData().toObjectData();
            iObjectData.set(PublicEmployeeObjConstant.ER_LANGUAGE, defaultValue);
        }
    }

    private void addIsSendInviteNotifyField(FormComponent formComponent, boolean defaultValue) {
        String invitationNotice = I18nUtil.get(GetI18nKeyUtil.getFieldLabelKey(PublicEmployeeObjConstant.API_NAME, PublicEmployeeObjConstant.HAS_SEND_PUBLIC_EMPLOYEE_INVITE_NOTIFY),"");
        BooleanFieldDescribe fieldDescribe = new BooleanFieldDescribe();
        fieldDescribe.setApiName(PublicEmployeeObjConstant.HAS_SEND_PUBLIC_EMPLOYEE_INVITE_NOTIFY);
        fieldDescribe.setDescribeApiName(PublicEmployeeObjConstant.API_NAME);
        fieldDescribe.setActive(true);
        fieldDescribe.setStatus("released");
        fieldDescribe.setDefineType("package");
        fieldDescribe.setLabel(invitationNotice);
        fieldDescribe.setDescription(invitationNotice);
        fieldDescribe.setDefaultValue(defaultValue);
        PaasControllerUtil.fillSelectOptionForBooleanField(controllerContext.getUser().getTenantIdInt(), fieldDescribe, false);
        PublicEmployeeControllerUtil.addFieldDescribe(result.getObjectDescribe(), fieldDescribe);

        List<IFormField> addFields = new ArrayList<>();
        addFields.add(PaasControllerUtil.newFormField(PublicEmployeeObjConstant.HAS_SEND_PUBLIC_EMPLOYEE_INVITE_NOTIFY, IFieldType.TRUE_OR_FALSE, true, false));
        PaasControllerUtil.addFormFields(formComponent, BASE_FIELD_SECTION, addFields);
        IObjectDescribe objectDescribe = result.getObjectDescribe().toObjectDescribe();
        Map<String, IFieldDescribe> fields = objectDescribe.getFieldDescribeMap();
        if (fields.containsKey(PublicEmployeeObjConstant.HAS_SEND_PUBLIC_EMPLOYEE_INVITE_NOTIFY)) {
            IFieldDescribe fieldsMap = fields.get(PublicEmployeeObjConstant.HAS_SEND_PUBLIC_EMPLOYEE_INVITE_NOTIFY);
            fieldsMap.setDefaultValue(defaultValue);
        }
        if (result.getObjectData() != null) {
            IObjectData iObjectData = result.getObjectData().toObjectData();
            iObjectData.set(PublicEmployeeObjConstant.HAS_SEND_PUBLIC_EMPLOYEE_INVITE_NOTIFY, defaultValue);
        }
    }

    private List<ISelectOption> builderBooleanSelectOptions(BooleanFieldDescribe fieldDescribe) {
        List<ISelectOption> selectOptions = new LinkedList<>();
        selectOptions.add(
                SelectOptionBuilder.builder().label(I18nUtil.get(GetI18nKeyUtil.getOptionNameKey(fieldDescribe.getDescribeApiName(), fieldDescribe.getApiName(), "true"),"是")).value("true").build());//ignoreI18n
        selectOptions.add(
                SelectOptionBuilder.builder().label(I18nUtil.get(GetI18nKeyUtil.getOptionNameKey(fieldDescribe.getDescribeApiName(), fieldDescribe.getApiName(), "false"),"否")).value("false").build());//ignoreI18n
        return selectOptions;
    }

    /**
     * 检测上游企业是否开通多币种
     */
    private Boolean multiCurrencyIsOpen(Integer upstreamTenantId) {
        // 上游未开启币种管理，不可切换，不展示货币列表
        HeaderObj headerObj = new HeaderObj(upstreamTenantId, -10000);
        if (currencyService.multiCurrencyStatus(headerObj).getStatus() == Status.OPEN.ordinal()) {
            return true;
        }
        return false;
    }

    /**
     * 获取上游企业已开通的币种
     */
    private List<FindCurrencyList.Currency> getCurrencyList(Integer upstreamTenantId) {
        List<FindCurrencyList.Currency> currencyResultList = Lists.newArrayList();
        HeaderObj headerObj = new HeaderObj(upstreamTenantId, -10000);
        FindCurrencyList findCurrencyList = currencyService.findCurrencyList(headerObj);
        for (Currency currency : findCurrencyList.getCurrencyList()) {
            if (currency.getStatus() == Currency.Status.ENABLED.ordinal()) {
                currencyResultList.add(currency);
            }
        }
        return currencyResultList;
    }
}