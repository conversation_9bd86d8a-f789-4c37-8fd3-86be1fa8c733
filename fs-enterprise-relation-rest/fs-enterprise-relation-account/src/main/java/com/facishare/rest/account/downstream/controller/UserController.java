package com.facishare.rest.account.downstream.controller;

import com.facishare.enterprise.common.annotation.ControllerInfo;
import com.facishare.enterprise.common.annotation.OldController;
import com.facishare.enterprise.common.constant.AccountManageMode;
import com.facishare.enterprise.common.constant.GuestorIdentityInfoConstants;
import com.facishare.enterprise.common.constant.IdentityType;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.facishare.enterprise.common.enums.QXMessageEnums;
import com.facishare.enterprise.common.model.*;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.*;
import com.facishare.er.api.model.arg.CreateOuterUserArg;
import com.facishare.er.api.model.arg.ListOuterUserArg;
import com.facishare.er.api.model.arg.*;
import com.facishare.er.api.model.data.AppRoleData;
import com.facishare.er.api.model.data.OuterUserAppRoleData;
import com.facishare.er.api.model.result.EmployeeCardResult;
import com.facishare.er.api.model.result.OuterAdministratorResult;
import com.facishare.er.api.model.result.OuterUserResult;
import com.facishare.er.api.model.result.WeChatAppResult;
import com.facishare.linkapp.api.result.LinkAppData;
import com.facishare.linkapp.api.service.DownstreamEmployeeLinkAppRoleService;
import com.facishare.linkapp.api.service.HttpDownstreamService;
import com.facishare.linkapp.api.service.HttpLinkAppService;
import com.facishare.linkapp.api.service.LinkAppOuterRoleService;
import com.facishare.linkapp.api.vo.UpstreamLinkAppDownEmployeeOuterRoleAssignVo;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ImageExt;
import com.facishare.paas.appframework.metadata.util.FileAttachmentUtils;
import com.facishare.paas.timezone.config.TimeZoneConfig;
import com.facishare.rest.account.EM6BaseController;
import com.facishare.rest.account.action.PublicEmployeeEditAction;
import com.facishare.rest.account.downstream.arg.ChangeMobileArg;
import com.facishare.rest.account.downstream.arg.SendVerifyCodeSnsMessageArg;
import com.facishare.rest.account.downstream.arg.UpdateLoginPasswordArg;
import com.facishare.rest.account.downstream.arg.UpdateTimeZoneArg;
import com.facishare.rest.account.downstream.arg.*;
import com.facishare.rest.account.downstream.data.AccountTypeEnum;
import com.facishare.rest.account.downstream.result.*;
import com.facishare.rest.account.manager.EnterpriseRelationManager;
import com.facishare.rest.account.manager.ErObjPaasApiBusServiceManager;
import com.facishare.rest.account.remote.FxiaokeAccountManager;
import com.facishare.rest.account.remote.QixinNotificationManager;
import com.facishare.rest.account.remote.RemoteManager;
import com.facishare.rest.account.service.AuthService;
import com.facishare.rest.account.service.OuterUserAdminService;
import com.facishare.rest.account.util.CDNImagePathUtil;
import com.facishare.rest.account.util.ConfigUtil;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.crmrestapi.common.contants.AccountFieldContants;
import com.fxiaoke.crmrestapi.common.contants.PartnerFieldContants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.FindCurrencyList;
import com.fxiaoke.crmrestapi.result.FindCurrencyList.Currency;
import com.fxiaoke.crmrestapi.result.MultiCurrencyStatusResult.Status;
import com.fxiaoke.crmrestapi.service.CurrencyService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.enterpriserelation.objrest.arg.*;
import com.fxiaoke.enterpriserelation.objrest.result.InitCurrencyCodeResult;
import com.fxiaoke.enterpriserelation.objrest.result.InitTimeZoneResult;
import com.fxiaoke.enterpriserelation.objrest.result.LanguageData;
import com.fxiaoke.enterpriserelation.objrest.result.ListOuterUserResult;
import com.fxiaoke.enterpriserelation.objrest.result.ListOuterUserResult.OuterUserData;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Language;
import com.fxiaoke.model.InternationalItem;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@RestController("downstreamUserController")
@RequestMapping("/downstream/user")
@Api(tags = {"互联用户个人中心", "7.8.0"})
public class UserController extends EM6BaseController {
    private static final String PHONE_VERIFY_CODE_PREFIX = "verifyCode:";
    private static final String UPDATE_LOGIN_PASSWORD_TOKEN_KEY = "update:login:password:token:%s:%s:%s";
    private static final String CANCEL_ACCOUNT_TOKEN_KEY = "cancel:account:token:%s:%s:%s";
    @Autowired
    @Qualifier("jedisSupport")
    private MergeJedisCmd mergeJedisCmd;
    @Autowired
    private AuthService authService;
    @Autowired
    private CurrencyService currencyService;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    private EnterpriseRelationObjService enterpriseRelationObjService = ErObjPaasApiBusServiceManager.getEnterpriseRelationObjService();
    @Autowired
    private OuterUserAdminService outerUserAdminService;
    @Autowired
    private RemoteManager remoteManager;
    @Autowired
    private HttpDownstreamService httpDownstreamService;
    @Autowired
    private HttpLinkAppService httpLinkAppService;
    @Autowired
    private LinkAppOuterRoleService linkAppOuterRoleService;
    @Autowired
    private DownstreamEmployeeLinkAppRoleService downstreamEmployeeLinkAppRoleService;
    @Autowired
    private QixinNotificationManager qixinNotificationManager;
    @Autowired
    private FxiaokeAccountManager fxiaokeAccountManager;
    @Autowired
    private EnterpriseRelationManager enterpriseRelationManager;
    @Autowired
    private CloudUtil cloudUtil;

    @ApiOperation(value = "获取个人登录信息", tags = {"互联用户个人中心", "7.8.0"})
    @RequestMapping(value = "/getUserLoginInfo", method = RequestMethod.POST)
    public Result<LoginUserDataResult> getUserLoginInfo(@RequestBody GetUserLoginInfoArg arg) {
        if (Objects.isNull(getOuterUserCepData())) {
            return Result.newSuccess();
        }
        ServiceContext serviceContext = createServiceContext();

        LoginUserDataResult data = null;
        if (GuestorIdentityInfoConstants.OUTER_TENANTID.equals(getOuterUserCepData().getOuterTenantId())) {
            data = new LoginUserDataResult();
            data.setEmployeeName(GuestorIdentityInfoConstants.ROLE_NAME);
            data.setOuterUid(getOuterUserCepData().getOuterUid());
            data.setOuterTenantId(getOuterUserCepData().getOuterTenantId());
            data.setEnterpriseName(GuestorIdentityInfoConstants.ROLE_NAME);
        } else {
            GetObjectDataArg getObjectDataArg = GetObjectDataArg.newIncludeDeletedArg(getOuterUserCepData().getOuterUid());
            Map<String, Object> publicEmployeeObjectData = publicEmployeeObjService.getObjectData(serviceContext, getObjectDataArg).getObjectData();
            PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(publicEmployeeObjectData);
            data = toLoginUserDataResult(publicEmployeeObj);
            data.setProfileImage(CDNImagePathUtil.toCDNPath(data.getProfileImage(), getEa()));

            Map<String, Object> enterpriseRelationObjectData = enterpriseRelationObjService.getObjectData(serviceContext, getOuterUserCepData().getOuterTenantId()).getObjectData();
            EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(enterpriseRelationObjectData);
            data.setEnterpriseName(enterpriseRelationObj.getName());
            data.setUpstreamEnterpriseName(enterpriseRelationObj.getUpstreamShortName());
            data.setMapperAccountId(enterpriseRelationObj.getMapperAccountId());
            data.setMapper_account_id(enterpriseRelationObj.getMapperAccountId());
            data.setMapperPartnerId(enterpriseRelationObj.getMapperPartnerId());
            data.setMapper_partner_id(enterpriseRelationObj.getMapperPartnerId());
        }

        GetPersonalErLocaleArg getPersonalErLocaleArg = new GetPersonalErLocaleArg();
        getPersonalErLocaleArg.setDownstreamOuterTenantId(getOuterUserCepData().getOuterTenantId());
        getPersonalErLocaleArg.setDownstreamOuterUid(getOuterUserCepData().getOuterUid());
        String personalErLocale = "zh-CN";
        try {
            GetPersonalErLocaleResult personalErLocaleRes = publicEmployeeObjService.getPersonalErLocale(serviceContext, getPersonalErLocaleArg);
            personalErLocale = personalErLocaleRes.getErLocale();
        } catch (Exception e) {
            //ignore
        }
        data.setErLocale(personalErLocale);
        if (StringUtils.isEmpty(data.getTimeZone())) {
            InitTimeZoneArg initTimeZoneArg = new InitTimeZoneArg();
            initTimeZoneArg.setTenantId(getTenantId());
            initTimeZoneArg.setDownstreamOuterUid(getOuterUserCepData().getOuterUid());
            InitTimeZoneResult initTimeZoneResult = publicEmployeeObjService.initTimeZone(serviceContext, initTimeZoneArg);
            if (!StringUtils.isEmpty(initTimeZoneResult.getTimeZone())) {
                data.setTimeZone(initTimeZoneResult.getTimeZone());
            }
        }
        if (StringUtils.isEmpty(data.getCurrencyCode())) {
            InitCurrencyCodeArg initCurrencyCodeArg = new InitCurrencyCodeArg();
            initCurrencyCodeArg.setDownstreamTenantId(getTenantId());
            initCurrencyCodeArg.setDownstreamOuterUid(getOuterUserCepData().getOuterUid());
            InitCurrencyCodeResult initCurrencyCodeResult = publicEmployeeObjService.initCurrencyCode(serviceContext, initCurrencyCodeArg);
            if (!StringUtils.isEmpty(initCurrencyCodeResult.getCurrencyCode())) {
                data.setCurrencyCode(initCurrencyCodeResult.getCurrencyCode());
            }
        }

        data.setUpstreamEa(getOuterUserCepData().getUpstreamEa());
        data.setUpstreamOwnerId(getOuterUserCepData().getUpstreamOwnerId());
        data.setThirdType(getOuterUserCepData().getThirdType());
        data.setThirdAppId(getOuterUserCepData().getThirdAppId());
        data.setThirdUserId(getOuterUserCepData().getThirdUserId());
        data.setIdentityType(getOuterUserCepData().getIdentityType());
        data.setThird_user_id(getOuterUserCepData().getThirdUserId());
        return Result.newSuccess(data);
    }

    private LoginUserDataResult toLoginUserDataResult(PublicEmployeeObj publicEmployeeObj) {
        LoginUserDataResult result = new LoginUserDataResult();
        result.setEmployeeName(publicEmployeeObj.getName());
        result.setOuterUid(publicEmployeeObj.getOuterUid());
        result.setOuterTenantId(publicEmployeeObj.getOuterTenantId());
        result.setEmployeeId(publicEmployeeObj.getEmployeeId());
        result.setGender(publicEmployeeObj.getGender());
        result.setMobile(publicEmployeeObj.getMobile());
        result.setLoginEmail(publicEmployeeObj.getLoginEmail());
        result.setLogin_email(publicEmployeeObj.getLoginEmail());
        result.setPost(publicEmployeeObj.getJobTitle());
        result.setDepartment(publicEmployeeObj.getDepartment());
        result.setProfileImage(publicEmployeeObj.getProfileImage());
        result.setProfileImageToken(publicEmployeeObj.getProfileImageToken());
        result.setTimeZone(publicEmployeeObj.getTimeZone());
        result.setCurrencyCode(publicEmployeeObj.getMcCurrency());
        result.setErLocale(publicEmployeeObj.getLanguage());
        return result;
    }

    @ApiOperation(value = "获取时区列表", tags = {"互联用户个人中心", "7.8.0"})
    @RequestMapping(value = "/listTimeZone", method = RequestMethod.POST)
    public Result<List<TimeZoneResult>> listTimeZone() {
        // 灰度企业才有time_zone字段
        if (getOuterUserCepData() == null || !TimeZoneConfig.INSTANCE.isGray(String.valueOf(getTenantId()))) {
            return Result.newSuccess();
        }
        HeaderObj headerObj = new HeaderObj(getTenantId(), -10000);
        List<TimeZoneResult> timeZoneDataList = Lists.newArrayList();
        List<Map<String, Object>> options = objectDescribeService.getDescribe(headerObj, "PersonnelObj").getData().getDescribe().getFields().get("time_zone").getOptions();
        for (Map<String, Object> option : options) {
            TimeZoneResult data = new TimeZoneResult();
            data.setLabel((String) option.get("label"));
            data.setValue((String) option.get("value"));
            timeZoneDataList.add(data);
        }
        return Result.newSuccess(timeZoneDataList);
    }

    @ApiOperation(value = "切换下游外部用户当前时区", tags = {"互联用户个人中心", "7.8.0"})
    @RequestMapping(value = "/updateTimeZone", method = RequestMethod.POST)
    public Result<Void> updateTimeZone(@RequestBody UpdateTimeZoneArg arg) {
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }
        com.fxiaoke.enterpriserelation.objrest.arg.UpdateTimeZoneArg updateTimeZoneArg = new com.fxiaoke.enterpriserelation.objrest.arg.UpdateTimeZoneArg(getOuterUserCepData().getOuterUid(),
                arg.getTimeZone());
        com.fxiaoke.enterpriserelation.objrest.result.Result<Void> result = publicEmployeeObjService.updateTimeZone(createServiceContext(), updateTimeZoneArg);
        return Result.newResult(result.getErrCode(), result.getErrMessage());
    }

    @ApiOperation(value = "获取货币列表", tags = {"互联用户个人中心", "7.8.0"})
    @RequestMapping(value = "/listCurrency", method = RequestMethod.POST)
    public Result<List<CurrencyResult>> listCurrency() {
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }
        // 上游未开启币种管理，不可切换，不展示货币列表
        HeaderObj headerObj = new HeaderObj(getTenantId(), -10000);
        if (currencyService.multiCurrencyStatus(headerObj).getStatus() != Status.OPEN.ordinal()) {
            return Result.newError(ResultCode.UPSTREAM_MULTICURRENCY_NOT_OPEN);
        }
        // 可切换的币种信息来源于租户的币种管理，不在币种管理中或禁用的币种不会显示。
        List<CurrencyResult> currencyResultList = Lists.newArrayList();
        FindCurrencyList findCurrencyList = currencyService.findCurrencyList(headerObj);
        for (Currency currency : findCurrencyList.getCurrencyList()) {
            if (currency.getStatus() == Currency.Status.ENABLED.ordinal()) {
                currencyResultList.add(BeanUtil.copy(CurrencyResult.class, currency));
            }
        }
        return Result.newSuccess(currencyResultList);
    }

    @ApiOperation(value = "切换下游外部用户当前货币", tags = {"互联用户个人中心", "7.8.0"})
    @RequestMapping(value = "/updateCurrency", method = RequestMethod.POST)
    public Result<Void> updateCurrency(@RequestBody UpdateCurrencyArg arg) {
        if (getOuterUserCepData() == null) {
            return Result.newError(ResultCode.INVALID_LOGIN_STATE.getErrorCode(), ResultCode.INVALID_LOGIN_STATE.getDescription());
        }
        UpdateCurrencyCodeArg updateCurrencyCodeArg = new UpdateCurrencyCodeArg(getOuterUserCepData().getOuterUid(), arg.getCurrencyCode());
        com.fxiaoke.enterpriserelation.objrest.result.Result<Void> result = publicEmployeeObjService.updateCurrencyCode(createServiceContext(), updateCurrencyCodeArg);
        return Result.newResult(result.getErrCode(), result.getErrMessage());
    }

    @ApiOperation(value = "（可联调）无租户下游企业用户列表", tags = {"无租户下游用户管理", "7.9.0"}, notes = "即微信页面上[用户管理]")
    @RequestMapping(value = "listOuterUser", method = RequestMethod.POST)
    @OldController
    @ControllerInfo(test = true)
    public Result<List<OuterUserResult>> listOuterUser(@RequestBody ListOuterUserArg arg) {
        if (Objects.isNull(getOuterUserCepData())) {
            return Result.newSuccess();
        }

        Result checkResult = checkBeforeOuterUserActions(true);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        String upstreamEa = getOuterUserCepData().getUpstreamEa();
        com.fxiaoke.enterpriserelation.objrest.arg.ListOuterUserArg listOuterUserArg = new com.fxiaoke.enterpriserelation.objrest.arg.ListOuterUserArg();
        listOuterUserArg.setDownstreamOuterTenantId(getOuterUserCepData().getOuterTenantId());
        listOuterUserArg.setDownstreamOuterUid(getOuterUserCepData().getOuterUid());
        listOuterUserArg.setWxAppId(getOuterUserCepData().getThirdAppId());
        listOuterUserArg.setLimit(arg.getPageSize());
        listOuterUserArg.setOffset((arg.getPageNumber() - 1) * arg.getPageSize());
        List<ListOuterUserResult.OuterUserData> outerUserDatas = publicEmployeeObjService.listOuterUser(createServiceContext(), listOuterUserArg).getOuterUserDatas();

        if (CollectionUtils.isEmpty(outerUserDatas)) {
            return Result.newSuccess();
        }

        List<Long> downstreamOuterUids = outerUserDatas.stream().map(OuterUserData::getOuterUid).collect(Collectors.toList());
        List<OuterUserAppRoleData> listOuterUserAppRolesDatas = listOuterUserAppRoles(upstreamEa, getOuterUserCepData().getOuterTenantId(), downstreamOuterUids);
        Map<Long, List<OuterUserAppRoleData>> outerUserIdAndAppRolesMap = listOuterUserAppRolesDatas.stream()
                .collect(Collectors.groupingBy(OuterUserAppRoleData::getDownstreamOuterUid, Collectors.mapping(val -> val, Collectors.toList())));
        List<OuterUserResult> results = outerUserDatas.stream().map(val -> {
            OuterUserResult outerUserResult = new OuterUserResult();
            outerUserResult.setBindWeChat(val.getIsBindWeChat());
            outerUserResult.setHeadImageFileId(val.getHeadImageFileId());
            outerUserResult.setName(val.getName());
            outerUserResult.setOuterTenantId(val.getOuterTenantId());
            outerUserResult.setOuterUid(val.getOuterUid());
            outerUserResult.setPhone(val.getPhone());
            outerUserResult.setWechatUserName(val.getWechatUserName());
            outerUserResult.setOuterUserAppRoleDataList(outerUserIdAndAppRolesMap.get(outerUserResult.getOuterUid()));
            return outerUserResult;
        }).collect(Collectors.toList());
        return Result.newSuccess(results);
    }

    private List<OuterUserAppRoleData> listOuterUserAppRoles(String upstreamEa, Long downstreamOuterTenantId, List<Long> downstreamOuterUids) {
        List<UpstreamLinkAppDownEmployeeOuterRoleAssignVo> upstreamLinkAppDownEmployeeOuterRoleAssignVos = httpDownstreamService
                .listUpstreamLinkAppDownEmployeeOuterRoleAssignVo(upstreamEa, downstreamOuterTenantId, downstreamOuterUids).getData();
        if (upstreamLinkAppDownEmployeeOuterRoleAssignVos == null) {
            return Lists.newArrayList();
        }
        for (UpstreamLinkAppDownEmployeeOuterRoleAssignVo upstreamLinkAppDownEmployeeOuterRoleAssignVo : upstreamLinkAppDownEmployeeOuterRoleAssignVos) {
            String roleId = upstreamLinkAppDownEmployeeOuterRoleAssignVo.getRoleId();
            Result<List<String>> linkAppIdsRes = linkAppOuterRoleService.getLinkAppIdsByRoleId(upstreamEa, roleId);
            if (linkAppIdsRes.isSuccess() && CollectionUtils.isNotEmpty(linkAppIdsRes.getData())) {
                String linkAppId = linkAppIdsRes.getData().get(0);
                upstreamLinkAppDownEmployeeOuterRoleAssignVo.setLinkAppId(linkAppId);
            }
        }
        Map<String, List<String>> linAppIdAndRoleIdMap = upstreamLinkAppDownEmployeeOuterRoleAssignVos.stream().collect(
                Collectors.groupingBy(UpstreamLinkAppDownEmployeeOuterRoleAssignVo::getLinkAppId, Collectors.mapping(UpstreamLinkAppDownEmployeeOuterRoleAssignVo::getRoleId, Collectors.toList())));
        List<LinkAppData> linkAppDatas = httpLinkAppService.listLinkApp(Lists.newArrayList(linAppIdAndRoleIdMap.keySet())).getData();
        Map<String, LinkAppData> appIdAndLinkAppDataMap = linkAppDatas.stream().collect(Collectors.toMap(LinkAppData::getId, val -> val));
        Map<String, Map<String, AppRoleData>> appIdAndRoleIdAndAppRoleDataMap = Maps.newHashMap();
        linAppIdAndRoleIdMap.forEach((linAppId, roleIds) -> {
            List<OuterRoleVo> paasRoleList = remoteManager.listOuterRoles(upstreamEa, roleIds);
            if (CollectionUtils.isNotEmpty(paasRoleList)) {
                Map<String, AppRoleData> roleIdAndAppRoleDataMap = paasRoleList.stream().collect(Collectors.toMap(OuterRoleVo::getRoleId, val -> {
                    LinkAppData linkAppData = appIdAndLinkAppDataMap.get(linAppId);
                    AppRoleData appRoleData = new AppRoleData();
                    appRoleData.setLinkAppId(linAppId);
                    appRoleData.setLinkAppName(linkAppData == null ? "crm" : linkAppData.getName());
                    appRoleData.setOuterRoleId(val.getRoleId());
                    appRoleData.setRoleName(val.getRoleName());
                    appRoleData.setType(val.getRoleType());
                    return appRoleData;
                }));
                appIdAndRoleIdAndAppRoleDataMap.put(linAppId, roleIdAndAppRoleDataMap);
            }
        });
        //获取下游企业管理模式，代管模式不能变更分配
        int upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        List<Long> downstreamOuterTenantIds = upstreamLinkAppDownEmployeeOuterRoleAssignVos.stream().map(UpstreamLinkAppDownEmployeeOuterRoleAssignVo::getDownstreamOuterTenantId)
                .collect(Collectors.toList());
        Map<Long, Integer> downstreamOuterTenantIdManageModeMap = enterpriseRelationManager.batchGetAccountManageModes(User.systemUser(upstreamTenantId + ""), downstreamOuterTenantIds);
        List<OuterUserAppRoleData> dataList = upstreamLinkAppDownEmployeeOuterRoleAssignVos.stream().map(val -> {
            OuterUserAppRoleData outerUserAppRoleData = new OuterUserAppRoleData();
            outerUserAppRoleData.setAppRoleData(appIdAndRoleIdAndAppRoleDataMap.get(val.getLinkAppId()).get(val.getRoleId()));
            outerUserAppRoleData.setDownstreamOuterUid(val.getOuterUId());
            outerUserAppRoleData.setIsCurrentMain(val.getIsMain());
            Integer manageMode = downstreamOuterTenantIdManageModeMap.get(val.getDownstreamOuterTenantId());
            outerUserAppRoleData.setIsModifiable(AccountManageMode.SELF_MANAGE.getType() == manageMode);
            return outerUserAppRoleData;
        }).collect(Collectors.toList());
        return Lists.newArrayList(dataList);
    }

    @ApiOperation(value = "（可联调）获取无租户下游企业管理员信息", tags = {"无租户下游用户管理", "7.9.0"})
    @RequestMapping(value = "getOuterAdministrator", method = RequestMethod.POST)
    @OldController
    @ControllerInfo(test = true)
    public Result<OuterAdministratorResult> getOuterAdministrator() {
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }
        Result checkResult = checkBeforeOuterUserActions(false);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        ServiceContext serviceContext = createServiceContext();
        ListPublicEmployeeObjByConditionArg publicEmployeeObjByConditionArg = new ListPublicEmployeeObjByConditionArg();
        publicEmployeeObjByConditionArg.setOuterUids(Lists.newArrayList(getOuterUserCepData().getOuterUid()));
        List<Map<String, Object>> publicEmployeeObjectDatas = publicEmployeeObjService.listByCondition(serviceContext, publicEmployeeObjByConditionArg).getObjectDatas();
        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newInstance(publicEmployeeObjectDatas);
        if (CollectionUtils.isEmpty(publicEmployeeObjs)) {
            return Result.newSuccess();
        }

        PublicEmployeeObj publicEmployeeObj = publicEmployeeObjs.get(0);
        ListEnterpriseRelationObjByConditionArg relationObjByConditionArg = new ListEnterpriseRelationObjByConditionArg();
        relationObjByConditionArg.setDestOuterTenantIds(Lists.newArrayList(publicEmployeeObj.getOuterTenantId() + ""));
        List<Map<String, Object>> relationObjectDatas = enterpriseRelationObjService.listByCondition(serviceContext, relationObjByConditionArg).getObjectDatas();
        List<EnterpriseRelationObj> enterpriseRelationObjs = EnterpriseRelationObj.newInstance(relationObjectDatas);
        if (CollectionUtils.isEmpty(enterpriseRelationObjs)) {
            return Result.newSuccess();
        }

        EnterpriseRelationObj enterpriseRelationObj = enterpriseRelationObjs.get(0);
        OuterAdministratorResult result = new OuterAdministratorResult();
        result.setOuterTenantId(publicEmployeeObj.getOuterTenantId());
        result.setOuterUid(publicEmployeeObj.getOuterUid());
        result.setName(publicEmployeeObj.getName());
        result.setPhone(publicEmployeeObj.getMobile());
        result.setEnterpriseName(enterpriseRelationObj.getName());
        return Result.newSuccess(result);
    }

    @ApiOperation(value = "（可联调）获取微信服务号相关信息", tags = {"无租户下游用户管理", "7.9.0"})
    @RequestMapping(value = "getWeChatAppByWxAppId", method = RequestMethod.POST)
    @OldController
    @ControllerInfo(test = true)
    public Result<WeChatAppResult> getWeChatAppByWxAppId(@RequestBody GetWeChatAppByWxAppIdArg arg) {
        if (StringUtils.isEmpty(arg.getWxAppId())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }
        Result checkResult = checkBeforeOuterUserActions(false);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        return outerUserAdminService.getWeChatAppByWxAppId(getOuterUserCepData().getUpstreamEa(), arg.getWxAppId());
    }

    @ApiOperation(value = "用户管理角色授权获取应用角色列表(只展示上游对下游当前用户开放的应用)", tags = {"无租户下游用户管理", "7.9.0"}, notes = "用户管理角色授权获取应用列表(只展示上游对下游当前用户开放的应用)")
    @RequestMapping(value = "listOuterUserAppRoles", method = RequestMethod.POST)
    @OldController
    @ControllerInfo(test = true)
    public Result<List<OuterUserAppRoleData>> listOuterUserAppRoles(@RequestBody ListOuterUserAppRolesArg arg) {
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }

        Result checkResult = checkBeforeOuterUserActions(true);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        String upstreamEa = getOuterUserCepData().getUpstreamEa();
        Long downstreamOuterUid = getOuterUserCepData().getOuterUid();
        Long downstreamOuterTenantId = getOuterUserCepData().getOuterTenantId();
        List<OuterUserAppRoleData> listOuterUserAppRolesData = listOuterUserAppRoles(upstreamEa, downstreamOuterTenantId, Lists.newArrayList(downstreamOuterUid));
        if (CollectionUtils.isEmpty(listOuterUserAppRolesData)) {
            return Result.newSuccess();
        }
        return Result.newSuccess(listOuterUserAppRolesData);
    }

    @ApiOperation(value = "（可联调）无租户下游企业添加用户（对接人）", tags = {"无租户下游用户管理", "7.9.0"}, notes = "即微信页面上[用户管理-添加用户]")
    @RequestMapping(value = "createOuterUser", method = RequestMethod.POST)
    public Result<Void> createOuterUser(@RequestBody CreateOuterUserArg arg) {
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }

        Result checkResult = checkBeforeOuterUserActions(true);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        Long downstreamOuterTenantId = getOuterUserCepData().getOuterTenantId();
        ServiceContext serviceContext = createServiceContext();

        ListEnterpriseRelationObjByConditionArg relationObjByConditionArg = new ListEnterpriseRelationObjByConditionArg();
        relationObjByConditionArg.setDestOuterTenantIds(Lists.newArrayList("" + downstreamOuterTenantId));
        List<Map<String, Object>> relationObjectDatas = enterpriseRelationObjService.listByCondition(serviceContext, relationObjByConditionArg).getObjectDatas();
        List<EnterpriseRelationObj> enterpriseRelationObjs = EnterpriseRelationObj.newInstance(relationObjectDatas);

        if (CollectionUtils.isEmpty(enterpriseRelationObjs)) {
            return Result.newSuccess();
        }

        com.fxiaoke.enterpriserelation.objrest.arg.CreateOuterUserArg createOuterUserArg = BeanUtil.copy(com.fxiaoke.enterpriserelation.objrest.arg.CreateOuterUserArg.class, arg);
        createOuterUserArg.setDownstreamOuterTenantId(downstreamOuterTenantId);

        EnterpriseRelationObj enterpriseRelationObj = enterpriseRelationObjs.get(0);
        String mapperPartnerId = enterpriseRelationObj.getMapperPartnerId();
        if (StringUtils.isNotEmpty(mapperPartnerId)) {
            createOuterUserArg.setObjectApiName(PartnerFieldContants.API_NAME);
            createOuterUserArg.setCrmObjectId(mapperPartnerId);
            com.fxiaoke.enterpriserelation.objrest.result.Result<Void> result = publicEmployeeObjService.createOuterUser(serviceContext, createOuterUserArg);
            return Result.newResult(result.getErrCode(), result.getErrMessage());
        }

        createOuterUserArg.setObjectApiName(AccountFieldContants.API_NAME);
        createOuterUserArg.setCrmObjectId(enterpriseRelationObj.getMapperAccountId());
        com.fxiaoke.enterpriserelation.objrest.result.Result<Void> result = publicEmployeeObjService.createOuterUser(serviceContext, createOuterUserArg);
        return Result.newResult(result.getErrCode(), result.getErrMessage());
    }

    @ApiOperation(value = "（可联调）修改手机号码时，校验新手机号是否和已有对接人重复", tags = {"微信个人中心", "7.9.0"})
    @RequestMapping(value = "checkNewMobilePhone", method = RequestMethod.POST)
    @OldController
    public Result<Void> checkNewMobilePhone(@RequestBody MobilePhoneArg arg) {
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }
        if (StringUtils.isEmpty(arg.getMobile())) {
            return Result.newError(ResultCode.PARAMS_MOBILE_NULL);
        }
        ListPublicEmployeeObjByConditionArg conditionArg = ListPublicEmployeeObjByConditionArg.newStartType();
        conditionArg.setOuterTenantIds(Lists.newArrayList(getOuterUserCepData().getOuterTenantId()));
        conditionArg.setMobile(arg.getMobile());
        List<Map<String, Object>> objectDatas = publicEmployeeObjService.listByCondition(createServiceContext(), conditionArg).getObjectDatas();
        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newInstance(objectDatas);
        if (CollectionUtils.isNotEmpty(publicEmployeeObjs)) {
            return Result.newError(ResultCode.EMPLOYEE_MOBILE_EXISTED);
        }
        return Result.newSuccess();
    }

    /**
     * 校验新绑定的邮箱账号是否已经存在
     *
     * @param arg 邮箱地址
     * @return 校验结果
     */
    @RequestMapping(value = "checkNewEmail", method = RequestMethod.POST)
    public Result<Void> checkNewEmail(@RequestBody LoginEmailArg arg) {
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }
        if (StringUtils.isEmpty(arg.getEmail())) {
            return Result.newError(ResultCode.PARAMS_EMAIL_NULL);
        }
        ListPublicEmployeeObjByConditionArg conditionArg = ListPublicEmployeeObjByConditionArg.newStartType();
        conditionArg.setOuterTenantIds(Lists.newArrayList(getOuterUserCepData().getOuterTenantId()));
        conditionArg.setLoginEmails(Lists.newArrayList(arg.getEmail()));
        List<Map<String, Object>> objectDatas = publicEmployeeObjService.listByCondition(createServiceContext(), conditionArg).getObjectDatas();
        if (CollectionUtils.isNotEmpty(objectDatas)) {
            return Result.newError(ResultCode.EMPLOYEE_LOGIN_EMAIL_EXISTED);
        }

        return Result.newSuccess();
    }

    @ApiOperation(value = "（可联调）修改手机号", tags = {"微信个人中心", "7.9.0"})
    @RequestMapping(value = "updateMobileByVerificationCode", method = RequestMethod.POST)
    public Result<EmployeeCardResult> updateMobileByVerificationCode(@RequestBody MobileAndVerificationCodeArg arg) {
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }
        String code = mergeJedisCmd.get(PHONE_VERIFY_CODE_PREFIX + arg.getMobile());
        if (code == null) {
            return Result.newError(ResultCode.VERIFY_CODE_EXPIRED);
        }
        if (!code.equals(arg.getVerificationCode().toString())) {
            return Result.newError(ResultCode.VERIFY_CODE_ERROR);
        }
        UpdateMobileArg updateMobileArg = new UpdateMobileArg();
        updateMobileArg.setDownstreamOuterUid(getOuterUserCepData().getOuterUid());
        updateMobileArg.setMobile(arg.getMobile());
        com.fxiaoke.enterpriserelation.objrest.result.Result<Void> result = publicEmployeeObjService.updateMobile(createServiceContext(), updateMobileArg);
        return Result.newResult(result.getErrCode(), result.getErrMessage());
    }

    /**
     * 修改登录邮箱
     **/
    @RequestMapping(value = "updateLoginEmailByVerificationCode", method = RequestMethod.POST)
    public Result<EmployeeCardResult> updateLoginEmailByVerificationCode(@RequestBody EmailAndVerificationCodeArg arg) {
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }
        String code = mergeJedisCmd.get(PHONE_VERIFY_CODE_PREFIX + arg.getEmail());
        if (code == null) {
            return Result.newError(ResultCode.VERIFY_CODE_EXPIRED);
        }
        if (!code.equals(arg.getVerificationCode().toString())) {
            return Result.newError(ResultCode.VERIFY_CODE_ERROR);
        }
        UpdateLoginEmailArg updateLoginEmailArg = new UpdateLoginEmailArg();
        updateLoginEmailArg.setDownstreamOuterUid(getOuterUserCepData().getOuterUid());
        updateLoginEmailArg.setLoginEmail(arg.getEmail());
        com.fxiaoke.enterpriserelation.objrest.result.Result<Void> result = publicEmployeeObjService.updateLoginEmail(createServiceContext(), updateLoginEmailArg);
        return Result.newResult(result.getErrCode(), result.getErrMessage());
    }

    @ApiOperation(value = "设置个人中心的语言环境", tags = {"微信个人中心", "7.9.0"})
    @RequestMapping(value = "setPersonalLocale", method = RequestMethod.POST)
    public Result<Void> setPersonalLocale(@RequestBody SetPersonalLocaleArg arg) {
        if (getOuterUserCepData() == null || GuestorIdentityInfoConstants.OUTER_UID.equals(getOuterUserCepData().getOuterUid())) {
            Result.newError(ResultCode.NOT_SUPPORT_CONFIG_LANGUAGE);
        }
        UpdatePersonalErLocaleArg personalErLocaleArg = new UpdatePersonalErLocaleArg();
        personalErLocaleArg.setDownstreamOuterUid(getOuterUserCepData().getOuterUid());
        personalErLocaleArg.setErLocale(arg.getErLocale());
        com.fxiaoke.enterpriserelation.objrest.result.Result<Void> result = publicEmployeeObjService.updatePersonalErLocale(createServiceContext(), personalErLocaleArg);
        return Result.newResult(result.getErrCode(), result.getErrMessage());
    }

    @ApiOperation(value = "（可联调）移除无租户下游企业用户 [B]移除无租户下游企业用户deleteOuterUser", tags = {"无租户下游用户管理", "7.9.0"}, notes = "即微信页面上[用户管理-移除用户]")
    @RequestMapping(value = "deleteOuterUser", method = RequestMethod.POST)
    public Result<Void> deleteOuterUser(@RequestBody DeleteOuterUserArg arg) {
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }
        Result checkResult = checkBeforeOuterUserActions(true);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        PublicEmployeeStopActionArg stopArg = new PublicEmployeeStopActionArg();
        stopArg.setDownstreamOuterUid(arg.getOuterUid());
        publicEmployeeObjService.stopAction(createServiceContext(), stopArg);
        return Result.newSuccess();
    }

    @ApiOperation(value = "新增/移除用户的角色", tags = {"无租户下游用户管理", "7.9.0"}, notes = "新增/移除用户的角色")
    @RequestMapping(value = "batchMergeAppRoleAssigns", method = RequestMethod.POST)
    public Result<?> batchMergeAppRoleAssigns(@RequestBody BatchMergeAppRoleAssignsArg arg) {
        boolean toAddAppRoleIdsIsEmpty = CollectionUtils.isEmpty(arg.getToAddAppRoleIds());
        boolean toDeleteAppRoleIdsIsEmpty = CollectionUtils.isEmpty(arg.getToDeleteAppRoleIds());
        if (arg.getOuterUid() == null || (toAddAppRoleIdsIsEmpty && toDeleteAppRoleIdsIsEmpty && Strings.isNullOrEmpty(arg.getMainOuterRoleId()))) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (getOuterUserCepData() == null) {
            return Result.newSuccess();
        }

        Result checkResult = checkBeforeOuterUserActions(true);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        //过滤有效对接人
        ListPublicEmployeeObjByConditionArg publicEmployeeObjByConditionArg = ListPublicEmployeeObjByConditionArg.newStartType();
        publicEmployeeObjByConditionArg.setOuterUids(Lists.newArrayList(arg.getOuterUid()));
        List<Map<String, Object>> objectDatas = publicEmployeeObjService.listByCondition(createServiceContext(), publicEmployeeObjByConditionArg).getObjectDatas();
        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newInstance(objectDatas);
        if (CollectionUtils.isEmpty(publicEmployeeObjs)) {
            return Result.newError(ResultCode.SETTING_ROLE_WITH_INCORRECT_USER_INFO);
        }

        //保存分配关系
        String upstreamEa = getOuterUserCepData().getUpstreamEa();
        OuterAccountVo outerAccounts = new OuterAccountVo(publicEmployeeObjs.get(0).getOuterTenantId(), publicEmployeeObjs.get(0).getOuterUid());
        if (!toAddAppRoleIdsIsEmpty) {
            for (String appRoleId : arg.getToAddAppRoleIds()) {
                linkAppOuterRoleService.batchAddAppRoleAssigns(upstreamEa, appRoleId, Lists.newArrayList(outerAccounts));
            }
        }
        if (arg.getMainOuterRoleId() != null) {
            linkAppOuterRoleService.batchUpdateAppRoleMainAssigns(upstreamEa, arg.getMainOuterRoleId(), Lists.newArrayList(arg.getOuterUid()));
        }
        if (!toDeleteAppRoleIdsIsEmpty) {
            for (String appRoleId : arg.getToDeleteAppRoleIds()) {
                linkAppOuterRoleService.batchDeleteAppRoleAssigns(upstreamEa, appRoleId, Lists.newArrayList(outerAccounts));
            }
        }
        return Result.newSuccess();
    }

    @ApiOperation(value = "查询上游企业授权的所有应用", tags = {"下游对接人", "8.1.0"})
    @RequestMapping(value = "/listUpstreamAssignAllLinkApp", method = RequestMethod.POST)
    @OldController
    @ControllerInfo(test = true)
    public Result<List<LinkAppData>> listUpstreamAssignAllLinkApp() {
        if (Objects.isNull(getOuterUserCepData())) {
            return Result.newSuccess();
        }
        String upstreamEa = getOuterUserCepData().getUpstreamEa();
        Long outerUid = getOuterUserCepData().getOuterUid();
        if (upstreamEa != null && outerUid > 0) {
            return downstreamEmployeeLinkAppRoleService.listUpstreamAssignAllLinkAppByOuterUserId(upstreamEa, outerUid);
        }
        return Result.newSuccess(Lists.newArrayList());
    }

    private Result checkBeforeOuterUserActions(boolean checkIdentityType) {
        Long downstreamOuterUid = getOuterUserCepData().getOuterUid();
        Long downstreamOuterTenantId = getOuterUserCepData().getOuterTenantId();
        ServiceContext serviceContext = createServiceContext();

        ListPublicEmployeeObjByConditionArg conditionArg = ListPublicEmployeeObjByConditionArg.newStartType();
        conditionArg.setOuterUids(Lists.newArrayList(downstreamOuterUid));
        List<Map<String, Object>> publicEmployeeObjectDatas = publicEmployeeObjService.listByCondition(serviceContext, conditionArg).getObjectDatas();
        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newInstance(publicEmployeeObjectDatas);
        if (CollectionUtils.isEmpty(publicEmployeeObjs)) {
            return Result.newError(ResultCode.NOT_NO_TENANT_RELATION_OWNER);
        }
        PublicEmployeeObj publicEmployeeObj = publicEmployeeObjs.get(0);
        if (checkIdentityType) {
            Integer identityType = publicEmployeeObj.getIdentityType();
            if (identityType != null && identityType.equals(IdentityType.PERSONAL.getType())) {
                return Result.newError(ResultCode.PERSONAL_IDENTITY_NOT_ALLOW_ACCESS);
            }
        }
        if (BooleanUtils.isFalse(publicEmployeeObj.getRelationOwner())) {
            return Result.newError(ResultCode.NOT_NO_TENANT_RELATION_OWNER);
        }

        ListEnterpriseRelationObjByConditionArg relationObjByConditionArg = new ListEnterpriseRelationObjByConditionArg();
        relationObjByConditionArg.setDestOuterTenantIds(Lists.newArrayList(downstreamOuterTenantId + ""));
        List<Map<String, Object>> relationObjectDatas = enterpriseRelationObjService.listByCondition(serviceContext, relationObjByConditionArg).getObjectDatas();
        List<EnterpriseRelationObj> enterpriseRelationObjs = EnterpriseRelationObj.newInstance(relationObjectDatas);
        if (CollectionUtils.isEmpty(enterpriseRelationObjs) || BooleanUtils.isTrue(enterpriseRelationObjs.get(0).getHasFsAccount())) {
            return Result.newError(ResultCode.NOT_NO_TENANT_RELATION_OWNER);
        }
        return Result.newSuccess();
    }

    /**
     * 发送手机号验证码
     */
    @RequestMapping(value = "/sendVerifyCodeSnsMessageNoCheckMobileOwner", method = RequestMethod.POST)
    public Result<Void> sendVerifyCodeSnsMessageNoCheckMobileOwner(@RequestBody SendVerifyCodeSnsMessageArg arg, HttpServletRequest request) {
        return sendVerifyCodeSnsMessage(arg, false, request);
    }

    /**
     * 校验手机号验证码
     */
    @RequestMapping(value = "/validatePhoneAndVerifyCodeNoCheckMobileOwner", method = RequestMethod.POST)
    public Result<ValidatePhoneAndVerifyCodeResult> validatePhoneAndVerifyCodeNoCheckMobileOwner(@RequestBody ValidatePhoneAndVerifyCodeArg arg) {
        return validatePhoneAndVerifyCode(arg, false);
    }

    /**
     * 发送手机号验证码
     * 使用场景：修改密码
     */
    @RequestMapping(value = "/sendVerifyCodeSnsMessage", method = RequestMethod.POST)
    public Result<Void> sendVerifyCodeSnsMessage(@RequestBody SendVerifyCodeSnsMessageArg arg, HttpServletRequest request) {
        return sendVerifyCodeSnsMessage(arg, true, request);
    }

    private Result<ValidatePhoneAndVerifyCodeResult> validatePhoneAndVerifyCode(ValidatePhoneAndVerifyCodeArg arg, boolean isCheckMobileOwner) {
        if (Strings.isNullOrEmpty(arg.getMobile())) {
            return Result.newError(ResultCode.PARAMS_MOBILE_NULL);
        }
        if (Strings.isNullOrEmpty(arg.getVerifyCode())) {
            return Result.newError(ResultCode.PARAMS_VERIFYCODE_NULL);
        }
        // 校验手机号是否属于当前用户
        PublicEmployeeObj publicEmployeeObj = getPublicEmployeeObj();
        if (publicEmployeeObj == null) {
            return Result.newError(ResultCode.USER_NOT_EXIST);
        }
        if (isCheckMobileOwner && !arg.getMobile().equals(publicEmployeeObj.getMobile())) {
            return Result.newError(ResultCode.INCORRECT_MOBILE);
        }
        Result<Boolean> verifyCodeResult = authService.validatePhoneAndVerifyCode(arg.getMobile(), arg.getVerifyCode());
        if (!verifyCodeResult.isSuccess() || Boolean.FALSE.equals(verifyCodeResult.getData())) {
            return Result.newError(verifyCodeResult.getErrCode(), verifyCodeResult.getErrMessage());
        }
        ValidatePhoneAndVerifyCodeResult result = new ValidatePhoneAndVerifyCodeResult();
        result.setUpdatePasswordToken(createValidateVerifyCodeSuccessToken(UPDATE_LOGIN_PASSWORD_TOKEN_KEY));
        return Result.newSuccess(result);
    }

    private Result<Void> sendVerifyCodeSnsMessage(SendVerifyCodeSnsMessageArg arg, boolean isCheckMobileOwner, HttpServletRequest request) {
        if (Objects.isNull(getOuterUserCepData())) {
            return Result.newSuccess();
        }
        if (Strings.isNullOrEmpty(arg.getMobile())) {
            return Result.newError(ResultCode.PARAMS_MOBILE_NULL);
        }
        // 校验手机号是否属于当前用户
        PublicEmployeeObj publicEmployeeObj = getPublicEmployeeObj();
        if (publicEmployeeObj == null) {
            return Result.newError(ResultCode.USER_NOT_EXIST);
        }
        if (isCheckMobileOwner && !arg.getMobile().equals(publicEmployeeObj.getMobile())) {
            return Result.newError(ResultCode.INCORRECT_MOBILE);
        }
        return authService.sendVerifyCodeSnsMessage(arg.getMobile(), getOuterUserCepData().getUpstreamEa(), ServletUtil.getFirstAcceptLanguage(request));
    }

    /**
     * 校验手机号验证码
     * 使用场景：修改密码
     */
    @RequestMapping(value = "/validatePhoneAndVerifyCode", method = RequestMethod.POST)
    public Result<ValidatePhoneAndVerifyCodeResult> validatePhoneAndVerifyCode(@RequestBody ValidatePhoneAndVerifyCodeArg arg) {
        return validatePhoneAndVerifyCode(arg, true);
    }

    /**
     * 发送验证码email
     * 使用场景：绑定邮箱、修改邮箱
     */
    @RequestMapping(value = "/sendVerifyCodeEmailNoCheckEmailOwner", method = RequestMethod.POST)
    public Result<Void> sendVerifyCodeEmailNoCheckEmailOwner(@RequestBody SendVerifyCodeEmailArg arg, HttpServletRequest request) {
        return sendVerifyCodeToEmail(arg, false, request);
    }

    /**
     * 发送验证码email
     * 使用场景：修改密码
     */
    @RequestMapping(value = "/sendVerifyCodeEmail", method = RequestMethod.POST)
    public Result<Void> sendVerifyCodeEmail(@RequestBody SendVerifyCodeEmailArg arg, HttpServletRequest request) {
        return sendVerifyCodeToEmail(arg, true, request);
    }

    private Result<Void> sendVerifyCodeToEmail(SendVerifyCodeEmailArg arg, Boolean isCheckEmailOwner, HttpServletRequest request) {
        if (Objects.isNull(getOuterUserCepData())) {
            return Result.newSuccess();
        }
        if (Strings.isNullOrEmpty(arg.getEmail())) {
            return Result.newError(ResultCode.PARAMS_EMAIL_NULL);
        }
        if (isCheckEmailOwner) {
            // 校验loginEmail是否属于当前用户
            PublicEmployeeObj publicEmployeeObj = getPublicEmployeeObj();
            if (publicEmployeeObj == null) {
                return Result.newError(ResultCode.USER_NOT_EXIST);
            }
            if (!arg.getEmail().equals(publicEmployeeObj.getLoginEmail())) {
                return Result.newError(ResultCode.INCORRECT_EMAIL_ADDRESS);
            }
        }
        Result<Void> result = authService.sendVerifyCodeEmail(arg.getEmail(), getOuterUserCepData().getUpstreamEa(), ServletUtil.getFirstAcceptLanguage(request));
        return result;
    }

    /**
     * 校验email验证码
     */
    @RequestMapping(value = "/validateEmailAndVerifyCode", method = RequestMethod.POST)
    public Result<ValidatePhoneAndVerifyCodeResult> validateEmailAndVerifyCode(@RequestBody ValidateEmailAndVerifyCodeArg arg) {
        if (Strings.isNullOrEmpty(arg.getEmail())) {
            return Result.newError(ResultCode.PARAMS_EMAIL_NULL);
        }
        if (Strings.isNullOrEmpty(arg.getVerifyCode())) {
            return Result.newError(ResultCode.PARAMS_VERIFYCODE_NULL);
        }
        // 校验loginEmail是否属于当前用户
        PublicEmployeeObj publicEmployeeObj = getPublicEmployeeObj();
        if (publicEmployeeObj == null) {
            return Result.newError(ResultCode.USER_NOT_EXIST);
        }
        if (!arg.getEmail().equals(publicEmployeeObj.getLoginEmail())) {
            return Result.newError(ResultCode.INCORRECT_EMAIL_ADDRESS);
        }
        Result<Boolean> verifyCodeResult = authService.validateEmailAndVerifyCode(arg.getEmail(), arg.getVerifyCode());
        if (!verifyCodeResult.isSuccess() || Boolean.FALSE.equals(verifyCodeResult.getData())) {
            return Result.newError(verifyCodeResult.getErrCode(), verifyCodeResult.getErrMessage());
        }
        ValidatePhoneAndVerifyCodeResult result = new ValidatePhoneAndVerifyCodeResult();
        result.setUpdatePasswordToken(createValidateVerifyCodeSuccessToken(UPDATE_LOGIN_PASSWORD_TOKEN_KEY));
        return Result.newSuccess(result);
    }

    /**
     * 修改登录密码
     */
    @RequestMapping(value = "/updateLoginPassword", method = RequestMethod.POST)
    public Result<Void> updateLoginPassword(@RequestBody UpdateLoginPasswordArg arg) {
        if (Objects.isNull(getOuterUserCepData())) {
            return Result.newSuccess();
        }
        // 校验token
        String cacheKey = String.format(UPDATE_LOGIN_PASSWORD_TOKEN_KEY, getOuterUserCepData().getUpstreamEa(), getOuterUserCepData().getOuterTenantId(), getOuterUserCepData().getOuterUid());
        String updateLoginPasswordToken = mergeJedisCmd.get(cacheKey);
        if (StringUtils.isEmpty(updateLoginPasswordToken) || !updateLoginPasswordToken.equals(arg.getUpdatePasswordToken())) {
            return Result.newError(ResultCode.UPDATE_LOGIN_PASSWORD_TOKEN_INVALID);
        }
        // 更新密码
        com.fxiaoke.enterpriserelation.objrest.arg.UpdateLoginPasswordArg updateLoginPasswordArg = new com.fxiaoke.enterpriserelation.objrest.arg.UpdateLoginPasswordArg();
        updateLoginPasswordArg.setPassword(arg.getPassword());
        updateLoginPasswordArg.setDownstreamOuterUid(getOuterUserCepData().getOuterUid());
        updateLoginPasswordArg.setDownstreamTenantId(getOuterUserCepData().getOuterTenantId());
        com.fxiaoke.enterpriserelation.objrest.result.Result<Void> updateLoginPasswordResult = publicEmployeeObjService.updateLoginPassword(createServiceContext(), updateLoginPasswordArg);
        if (updateLoginPasswordResult.isSuccess()) {
            mergeJedisCmd.del(cacheKey);
        }
        ;
        return Result.newResult(updateLoginPasswordResult.getErrCode(), updateLoginPasswordResult.getErrMessage());
    }

    @RequestMapping(value = "/updateProfileImage", method = RequestMethod.POST)
    public Result<Void> updateProfileImage(@RequestBody UpdateProfileImageArg arg) {
        if (Objects.isNull(getOuterUserCepData())) {
            return Result.newSuccess();
        }
        if (StringUtils.isEmpty(arg.getProfileImage())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        EditActionArg editActionArg = new EditActionArg();
        Map<String, Object> objectData = new HashMap<>();
        objectData.put(PublicEmployeeObjConstant.ID, getOuterUserCepData().getOuterUid() + "");
        Map<String, String> profileImageMap = new HashMap<>();
        profileImageMap.put("path", arg.getProfileImage());
        MapUtil.putIfNotNll(objectData, PublicEmployeeObjConstant.PROFILE_IMAGE, Lists.newArrayList(profileImageMap));
        editActionArg.setIncrementUpdate(true);
        editActionArg.setObjectData(objectData);
        publicEmployeeObjService.editAction(createServiceContext(), editActionArg);
        return Result.newSuccess();
    }

    /**
     * 发送注销账号手机验证码
     */
    @RequestMapping(value = "/sendCancelAccountVerifyCode", method = RequestMethod.POST)
    public Result<Void> sendCancelAccountVerifyCode(@RequestBody SendCancelAccountVerifyCodeArg arg, HttpServletRequest request) {
        if (Objects.isNull(getOuterUserCepData())) {
            return Result.newSuccess();
        }
        PublicEmployeeObj publicEmployeeObj = getPublicEmployeeObj();
        if (publicEmployeeObj == null) {
            return Result.newError(ResultCode.USER_NOT_EXIST);
        }
        if (AccountTypeEnum.PHONE.name().toLowerCase().equals(arg.getAccountType())) {
            if (!StringUtils.isEmpty(publicEmployeeObj.getMobile())) {
                authService.sendVerifyCodeSnsMessage(publicEmployeeObj.getMobile(), getOuterUserCepData().getUpstreamEa(), ServletUtil.getFirstAcceptLanguage(request));
                return Result.newSuccess();
            }
            return Result.newError(ResultCode.USER_DOES_NOT_HAVE_A_PHONE_NUMBER);
        }
        if (AccountTypeEnum.EMAIL.name().toLowerCase().equals(arg.getAccountType())) {
            if (!StringUtils.isEmpty(publicEmployeeObj.getLoginEmail())) {
                authService.sendVerifyCodeEmail(publicEmployeeObj.getLoginEmail(), getOuterUserCepData().getUpstreamEa(), ServletUtil.getFirstAcceptLanguage(request));
                return Result.newSuccess();
            }
            return Result.newError(ResultCode.USER_DOES_NOT_HAVE_AN_EMAIL_ADDRESS);
        }
        return Result.newError(ResultCode.SEND_VERIFY_CODE_ACCOUNT_TYPE_ERROR);
    }

    /**
     * 校验注销账号验证码
     */
    @RequestMapping(value = "/validateCancelAccountVerifyCode", method = RequestMethod.POST)
    public Result<ValidateCancelAccountVerifyCodeResult> validateCancelAccountVerifyCode(@RequestBody ValidateCancelAccountVerifyCodeArg arg) {
        if (Strings.isNullOrEmpty(arg.getVerifyCode())) {
            return Result.newError(ResultCode.PARAMS_VERIFYCODE_NULL);
        }
        PublicEmployeeObj publicEmployeeObj = getPublicEmployeeObj();
        if (publicEmployeeObj == null) {
            return Result.newError(ResultCode.USER_NOT_EXIST);
        }
        Result<Boolean> verifyCodeResult = null;
        if (AccountTypeEnum.PHONE.name().toLowerCase().equals(arg.getAccountType())) {
            verifyCodeResult = authService.validatePhoneAndVerifyCode(publicEmployeeObj.getMobile(), arg.getVerifyCode());
        }
        if (AccountTypeEnum.EMAIL.name().toLowerCase().equals(arg.getAccountType())) {
            verifyCodeResult = authService.validateEmailAndVerifyCode(publicEmployeeObj.getLoginEmail(), arg.getVerifyCode());
        }
        if (verifyCodeResult == null) {
            return Result.newError(ResultCode.SEND_VERIFY_CODE_ACCOUNT_TYPE_ERROR);
        }
        if (!verifyCodeResult.isSuccess() || Boolean.FALSE.equals(verifyCodeResult.getData())) {
            return Result.newError(verifyCodeResult.getErrCode(), verifyCodeResult.getErrMessage());
        }
        ValidateCancelAccountVerifyCodeResult result = new ValidateCancelAccountVerifyCodeResult();
        result.setCancelAccountToken(createValidateVerifyCodeSuccessToken(CANCEL_ACCOUNT_TOKEN_KEY));
        return Result.newSuccess(result);
    }

    @RequestMapping(value = "/cancelAccount", method = RequestMethod.POST)
    public Result<Void> cancelAccount(@RequestBody CancelAccountArg arg) {
        if (Objects.isNull(getOuterUserCepData())) {
            return Result.newSuccess();
        }
        // 校验token
        String cacheKey = String.format(CANCEL_ACCOUNT_TOKEN_KEY, getOuterUserCepData().getUpstreamEa(), getOuterUserCepData().getOuterTenantId(), getOuterUserCepData().getOuterUid());
        String cancelAccountToken = mergeJedisCmd.get(cacheKey);
        if (StringUtils.isEmpty(cancelAccountToken) || !cancelAccountToken.equals(arg.getCancelAccountToken())) {
            return Result.newError(ResultCode.CANCEL_ACCOUNT_TOKEN_INVALID);
        }
        mergeJedisCmd.del(cacheKey);
        PublicEmployeeObj publicEmployeeObj = getPublicEmployeeObj(true);
        // 停用账号
        PublicEmployeeStopActionArg stopActionArg = new PublicEmployeeStopActionArg();
        stopActionArg.setDownstreamOuterUid(getOuterUserCepData().getOuterUid());
        publicEmployeeObjService.stopAction(createServiceContext(), stopActionArg);
        String upstreamEa = getOuterUserCepData().getUpstreamEa();
        // 发送企信
        CommonPoolUtil.execute(() -> {
            Result<CloudConfigVo> cloudConfig = cloudUtil.getCloudConfig(upstreamEa);
            String cancelAccountNotifyTemplate = I18nUtil.get("cancel.account.notify.template", "互联用户【${enterpriseName}-${publicEmployeeName}】申请注销自己的互联账号，当前系统已经自动停用其互联账号，您可以对其互联账号进行作废、回收站删除操作。前往处理：" + ConfigUtil.noCooperateUserUrl);//ignoreI18n
            String cancelAccountNotifyContent = TemplateUtil
                    .format(cancelAccountNotifyTemplate, publicEmployeeObj.getEnterpriseRelationObjName(), publicEmployeeObj.getName(), cloudConfig.getData().getCloudDomain());
            List<Integer> relationAdmins = fxiaokeAccountManager.listRelationAdmins(upstreamEa);

            InternationalItem internationalItem = new InternationalItem(QXMessageEnums.CANCEL_ACCOUNT.getKey(), publicEmployeeObj.getEnterpriseRelationObjName(), publicEmployeeObj.getName());
            qixinNotificationManager.sendCrossHelpTextMessage(upstreamEa, cancelAccountNotifyContent, internationalItem, relationAdmins);
        });
        return Result.newSuccess();
    }

    private String createValidateVerifyCodeSuccessToken(String key) {
        String cacheKey = String.format(key, getOuterUserCepData().getUpstreamEa(), getOuterUserCepData().getOuterTenantId(), getOuterUserCepData().getOuterUid());
        String token = IdUtil.generateId();
        mergeJedisCmd.setex(cacheKey, 900L, token);
        return token;
    }

    @RequestMapping(value = "/updateUserInfo", method = RequestMethod.POST)
    public Result<Void> updateUserInfo(@RequestBody UpdateUserInfoArg arg) {
        if (Objects.isNull(getOuterUserCepData())) {
            return Result.newSuccess();
        }
        if (StringUtils.isEmpty(arg.getName()) && StringUtils.isEmpty(arg.getProfileImage())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Map<String, Object> objectData = new HashMap<>();
        objectData.put(PublicEmployeeObjConstant.ID, getOuterUserCepData().getOuterUid() + "");
        if (!StringUtils.isEmpty(arg.getProfileImage())) {
            Map<String, String> profileImageMap = new HashMap<>();
            profileImageMap.put(ImageExt.PATH, arg.getProfileImage());
            MapUtil.putIfNotNll(objectData, PublicEmployeeObjConstant.PROFILE_IMAGE, Lists.newArrayList(profileImageMap));
        }
        if (!StringUtils.isEmpty(arg.getName())) {
            objectData.put(PublicEmployeeObjConstant.NAME, arg.getName());
        }
        Map<String, Object> attribute = new HashMap<>();
        attribute.put(PublicEmployeeEditAction.ALLOW_CHANGE_FIELD, Lists.newArrayList(PublicEmployeeObjConstant.PROFILE_IMAGE));

        EditActionArg editActionArg = new EditActionArg();
        editActionArg.setIncrementUpdate(true);
        editActionArg.setObjectData(objectData);
        editActionArg.setAttribute(attribute);

        publicEmployeeObjService.editAction(createServiceContext(), editActionArg);
        return Result.newSuccess();
    }

    @RequestMapping(value = "/listErLanguages", method = RequestMethod.POST)
    public Result<List<LanguageData>> listErLanguages() {
        List<LanguageData> languageDatas = Lists.newArrayList();
        OuterUserCepData outerUserCepData = getOuterUserCepData();
        if (Objects.isNull(outerUserCepData)) {
            return Result.newSuccess(Lists.newArrayList(LanguageData.newDefaultInstance(I18nUtil.defalutLanguage, I18nUtil.get(I18nKeyEnum.EPI_RESULT_LANGUAGEDATA_13))));
        }
        int upstreamEi = eieaConverter.enterpriseAccountToId(outerUserCepData.getUpstreamEa());
        List<Language> languages = I18nClient.getInstance().getLanguage(upstreamEi);
        if (CollectionUtils.isEmpty(languages)) {
            return Result.newSuccess(Lists.newArrayList(LanguageData.newDefaultInstance(I18nUtil.defalutLanguage, I18nUtil.get(I18nKeyEnum.EPI_RESULT_LANGUAGEDATA_13))));
        }
        for (Language language : languages) {
            LanguageData data = new LanguageData();
            data.setValue(language.getCode());
            data.setLabel(language.getDisplayName());
            languageDatas.add(data);
        }
        return Result.newSuccess(languageDatas);
    }

    @ApiOperation(value = "下游更新自己互联账号的手机号", tags = {"下游对接人", "8.9.0"})
    @RequestMapping(value = "/changeMobile", method = RequestMethod.POST)
    public Result<Void> changeMobile(@RequestBody ChangeMobileArg arg) {
        if (StringUtils.isEmpty(arg.getMobile())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        PublicEmployeeObj publicEmployeeObj = getPublicEmployeeObj();
        if (publicEmployeeObj == null) {
            return Result.newError(ResultCode.USER_NOT_EXIST);
        }
        if (arg.getMobile().equals(publicEmployeeObj.getMobile())) {
            return Result.newError(ResultCode.ENTER_NEW_MOBILE_PLEASE);
        }
        com.fxiaoke.enterpriserelation.objrest.arg.ChangeMobileArg changeMobileArg = new com.fxiaoke.enterpriserelation.objrest.arg.ChangeMobileArg();
        changeMobileArg.setDownstreamOuterUid(publicEmployeeObj.getOuterUid());
        changeMobileArg.setDownstreamOuterTenantId(publicEmployeeObj.getOuterTenantId());
        changeMobileArg.setMobile(arg.getMobile());
        com.fxiaoke.enterpriserelation.objrest.result.Result<Void> result = publicEmployeeObjService.changeMobile(createServiceContext(), changeMobileArg);
        if (!result.isSuccess()) {
            return Result.newError(result.getErrCode(), result.getErrMessage());
        }
        return Result.newSuccess();
    }
}