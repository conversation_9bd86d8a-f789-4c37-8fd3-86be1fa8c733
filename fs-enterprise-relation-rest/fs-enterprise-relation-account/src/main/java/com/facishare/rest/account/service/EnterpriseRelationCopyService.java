package com.facishare.rest.account.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.EnterpriseRelationObjConstant;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.constant.PublicEmployeeObjTypeEnum;
import com.facishare.enterprise.common.enums.EnterpriseRelationObjRelationTypeOption;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.util.OffsetUtil;
import com.facishare.er.api.util.BeanUtil;
import com.facishare.global.rest.dao.bizpg.LoginRegisterConfigDao;
import com.facishare.global.rest.dao.mongo.EnterpriseMetaDataEntityDao;
import com.facishare.global.rest.dao.pg.EnterpriseCardDao;
import com.facishare.global.rest.dao.pg.EnterpriseCardEntityDao;
import com.facishare.global.rest.model.entity.EnterpriseCardEntity;
import com.facishare.global.rest.model.entity.EnterpriseMetaDataEntity;
import com.facishare.global.rest.model.entity.LoginRegisterConfigEntity;
import com.facishare.linkapp.api.arg.BaseArg;
import com.facishare.linkapp.api.result.*;
import com.facishare.linkapp.api.service.HttpUpstreamService;
import com.facishare.linkapp.api.service.LinkAppAssociationObjectService;
import com.facishare.linkapp.api.service.LinkAppOuterRoleService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.rest.account.dao.data.ERAccountActiveConfigData;
import com.facishare.rest.account.dao.mongo.ERAccountActiveConfigDao;
import com.facishare.rest.account.dao.mongo.entity.ERAccountActiveConfig;
import com.facishare.rest.account.remote.PaasLicenseManager;
import com.facishare.rest.account.service.changeset.data.LoginRegisterConfigData;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 互联配置/业务数据复制服务（全量沙盒）
 *
 * <AUTHOR>
 * @since 2023/01/03 19:30
 */
@Slf4j
@Service
public class EnterpriseRelationCopyService {
    @Autowired
    private HttpUpstreamService upstreamService;
    @Autowired
    private LinkAppAssociationObjectService linkAppAssociationObjectService;
    @Autowired
    private LinkAppOuterRoleService linkAppOuterRoleService;
    @Autowired
    private PaasLicenseManager paasLicenseManager;
    @Autowired
    private EnterpriseMetaDataEntityDao enterpriseMetaDataEntityDaoOldManager;
    @Autowired
    private EnterpriseCardEntityDao enterpriseCardEntityDao;
    @Autowired
    private EnterpriseCardDao enterpriseCardDao;
    @Autowired
    private ERAccountActiveConfigDao erAccountActiveConfigDaoOldManager;

    @Autowired
    private LoginRegisterConfigService loginRegisterConfigService;

    @Autowired
    private LoginRegisterConfigDao loginRegisterConfigDao;

    @Autowired
    private SpecialTableMapper specialTableMapper;
    @Autowired
    private EIEAConverter eieaConverter;

    private int UPDATE_BATCH_SIZE = 100;

    private static final String UPDATE_PUBLIC_EMPLOYEE_TYPE_SQL = "UPDATE biz_public_employee set type = '0' WHERE (tenant_id,id) IN (SELECT tenant_id,id from biz_public_employee WHERE tenant_id = '%s' AND is_deleted IN ('0','1') AND type = '1' LIMIT %s )";
    //relation_type 停用 2 正常 1
    private static final String UPDATE_ENTERPRISE_RELATION_SQL = "UPDATE biz_enterprise_relation set relation_type = '2' WHERE (tenant_id,id) IN (SELECT tenant_id,id FROM biz_enterprise_relation WHERE tenant_id = '%s' AND is_deleted IN ('0','1') AND relation_type = '1' LIMIT %s ) ";

    /**
     * 全量复制业务数据
     *
     * @param fromEa 数据源企业账号
     * @param toEa   数据复制到企业账号
     * @return 复制结果
     */
    public boolean copyEnterpriseData(String fromEa, String toEa) {
        // 复制互联企业对象数据
        Set<Long> downstreamOuterTenantIds = copyEnterpriseRelationObjData(fromEa, toEa);
        if (downstreamOuterTenantIds != null && downstreamOuterTenantIds.size() > 0) {
            // 复制互联用户对象数据
            copyPublicEmployeeObjData(fromEa, toEa, downstreamOuterTenantIds);
        }
        return true;
    }

    /**
     * 全量复制配置数据
     *
     * @param fromEa 数据源企业账号
     * @param toEa   数据复制到企业账号
     * @return 复制结果
     */
    public boolean copyEnterpriseConfigData(String fromEa, String toEa) {
        // 复制“互联角色”相关数据
        copyEnterpriseOuterRoleData(fromEa, toEa);
        // 复制“互联应用”相关数据
        copyEnterpriseLinkAppData(fromEa, toEa);
        // 复制“互联应用”配置数据
        copyEnterpriseLinkAppConfigData(fromEa, toEa);
        // 复制“互联设置”配置数据
        copyEnterpriseRelationConfigData(fromEa, toEa);
        // 复制“互联账号自注册”配置数据（新配置）
        copyLoginRegisterConfigData(fromEa, toEa);
        // 复制“互联账号自注册”配置数据（旧配置，已废除）
        copyEnterpriseRelationAccountActiveConfigData(fromEa, toEa);
        return true;
    }

    /**
     * 复制“互联企业”对象数据
     *
     * @param fromEa 数据源企业账号
     * @param toEa   数据复制到企业账号
     * @return 复制结果
     */
    public Set<Long> copyEnterpriseRelationObjData(String fromEa, String toEa) {
        Set<Long> downstreamOuterTenantIds = Sets.newHashSet();
        try {
            Integer fromEaId = eieaConverter.enterpriseAccountToId(fromEa);
            Integer toEaId = eieaConverter.enterpriseAccountToId(toEa);
            String tableName = "biz_enterprise_relation";
            Integer maxCopyRecordRows = 10000;
            AtomicInteger insertCount = new AtomicInteger();
            OffsetUtil.offsetFunction(1000, (offset, limit) -> {
                if (insertCount.get() >= maxCopyRecordRows) {
                    return 0;
                }
                String querySql = String.format("select * from %s where tenant_id = '%d' limit %d offset %d", tableName, fromEaId, limit, offset);
                log.info("querySql->{}", querySql);
                List<Map> enterpriseRelationList = specialTableMapper.setTenantId(fromEaId + "").findBySql(querySql);
                if (CollectionUtils.isEmpty(enterpriseRelationList)) {
                    return 0;
                }
                Integer insertNum = saveEnterpriseRelationObjCopyDataToDB(downstreamOuterTenantIds, toEaId, tableName, enterpriseRelationList);
                insertCount.addAndGet(insertNum);
                return enterpriseRelationList.size();
            });
            // 返回被复制的互联企业外部ID
            return downstreamOuterTenantIds;
        } catch (Exception e) {
            log.warn("copyEnterpriseRelationObjData-error:{}", e);
        }
        return downstreamOuterTenantIds;
    }

    private Integer saveEnterpriseRelationObjCopyDataToDB(Set<Long> downstreamOuterTenantIds, Integer toEaId, String tableName, List<Map> enterpriseRelationList) {
        AtomicInteger insertCount = new AtomicInteger();
        if (toEaId == null || StringUtils.isEmpty(tableName) || CollectionUtils.isEmpty(enterpriseRelationList)) {
            return 0;
        }
        enterpriseRelationList.forEach(obj -> {
            try {
                obj.put(EnterpriseRelationObjConstant.TENANT_ID, toEaId);
                obj.put(EnterpriseRelationObjConstant.RELATION_TYPE, EnterpriseRelationObjRelationTypeOption.STOPPING.getType());
                String insertSql = getEnterpriseRelationInsertData(tableName, obj);
                if (StringUtils.isNotEmpty(insertSql)) {
                    int result = specialTableMapper.setTenantId(toEaId + "").insertBySql(insertSql);
                    log.info("biz_enterprise_relation insert-result:{}", result);
                    insertCount.addAndGet(1);
                }
                EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(obj);
                downstreamOuterTenantIds.add(enterpriseRelationObj.getOuterTenantId());
            } catch (Exception e) {
                log.warn("biz_enterprise_relation--copy fail:{}", e);
            }
        });
        return insertCount.get();
    }

    private String getEnterpriseRelationInsertData(String tableName, Map<String, Object> dataRow) {
        if (tableName == null || dataRow == null) {
            return null;
        }
        String insertSql = insertFromMap(tableName, dataRow, "id,tenant_id");
        log.info("enterpriseRelationObj-insert:{}", insertSql);
        return insertSql;
    }

    /**
     * 复制“互联用户” 对象数据
     *
     * @param fromEa                   数据源企业账号
     * @param toEa                     数据复制到企业账号
     * @param downstreamOuterTenantIds 被复制的互联企业
     * @return 复制结果
     */
    public boolean copyPublicEmployeeObjData(String fromEa, String toEa, Set<Long> downstreamOuterTenantIds) {
        try {
            Integer fromEaId = eieaConverter.enterpriseAccountToId(fromEa);
            Integer toEaId = eieaConverter.enterpriseAccountToId(toEa);
            String tableName = "biz_public_employee";
            downstreamOuterTenantIds.forEach(outerTenantId -> {
                try {
                    OffsetUtil.offsetFunction(1000, (offset, limit) -> {
                        String querySql = String.format("SELECT * FROM %s WHERE tenant_id = '%d' and outer_tenant_id = '%d' limit %d offset %d", tableName, fromEaId, outerTenantId, limit, offset);
                        log.info("querySql->{}", querySql);
                        List<Map> publicEmployeeList = specialTableMapper.setTenantId(fromEaId + "").findBySql(querySql);
                        if (CollectionUtils.isEmpty(publicEmployeeList)) {
                            return 0;
                        }
                        savePublicEmployeeCopyDataToDB(toEaId, tableName, publicEmployeeList);
                        return publicEmployeeList.size();
                    });
                } catch (Exception ex) {
                    log.warn("copyPublicEmployeeObjData--fail2:{}", ex);
                }
            });
            return true;
        } catch (Exception e) {
            log.warn("copyPublicEmployeeObjData-error:{}", e);
            return false;
        }
    }

    private void savePublicEmployeeCopyDataToDB(Integer toEaId, String tableName, List<Map> publicEmployeeList) {
        if (CollectionUtils.isEmpty(publicEmployeeList) || StringUtils.isEmpty(tableName) || toEaId == null) {
            return;
        }
        publicEmployeeList.forEach(obj -> {
            try {
                obj.put(EnterpriseRelationObjConstant.TENANT_ID, toEaId);
                obj.put(PublicEmployeeObjConstant.TYPE, PublicEmployeeObjTypeEnum.ABOLISH.getType());
                // 唐人神沙盒复制出错，特殊处理
                if (obj.containsKey("value1")) {
                    obj.remove("value1");
                }
                String insertSql = getPublicEmployeeInsertData(tableName, obj);
                log.info("biz_public_employee insert:{}", insertSql);
                if (StringUtils.isNotEmpty(insertSql)) {
                    int result = specialTableMapper.setTenantId(toEaId + "").insertBySql(insertSql);
                    log.info("biz_public_employee insert result:{}", result);
                }
            } catch (Exception e) {
                log.warn("biz_public_employee--copy fail:{}", e);
            }
        });
    }

    private String getPublicEmployeeInsertData(String tableName, Map<String, Object> dataRow) {
        if (tableName == null || dataRow == null) {
            return null;
        }
        String insertSql = insertFromMap(tableName, dataRow, "id,tenant_id");
        log.info("publicEmployObj-insert:{}", insertSql);
        return insertSql;
    }

    /**
     * 插入数据 传进来需要插入数据库的Map类型和数据库表名
     *
     * @param tableName 数据表名称
     * @param map       数据集
     */
    public String insertFromMap(String tableName, Map<String, Object> map, String indexCondition) {
        //存入key的字符串数组
        ArrayList<Object> arrKey = new ArrayList<>();
        //存入value的字符串数组
        ArrayList<Object> arrValue = new ArrayList<>();
        //拼接sql
        for (String key : map.keySet()) {
            arrKey.add(key);
        }
        for (String keys : map.keySet()) {
            arrValue.add(map.get(keys));
        }

        StringBuffer strKey = new StringBuffer();
        StringBuffer strVal = new StringBuffer();
        //遍历存的key字符串数组拼接sql
        for (int j = 0; j < arrKey.size(); j++) {
            strKey.append(arrKey.get(j));
            if (j != arrKey.size() - 1) {//拼上","最后一个不拼
                strKey.append(",");
            }
        }
        //遍历存的value字符串数组拼接sql
        for (int j = 0; j < arrValue.size(); j++) {
            if (null != arrValue.get(j)) {
                strVal.append(SqlEscaper.pg_quote(arrValue.get(j).toString()));
            } else {
                strVal.append("" + null + "");
            }
            if (j != arrValue.size() - 1) {//拼上","最后一个不拼
                strVal.append(",");
            }
        }
        String stringEntryKey = strKey.toString();
        String stringEntryVal = strVal.toString();
        //插入sql语句
        String sqlEntry = "INSERT INTO " + tableName + " (" + stringEntryKey + ") VALUES (" + stringEntryVal + ") ";
        if (indexCondition != null && indexCondition != "") {
            sqlEntry += String.format(" ON CONFLICT (%s) DO NOTHING", indexCondition);
        }
        return sqlEntry;
    }

    /**
     * 复制“互联角色”数据
     *
     * @param fromEa 数据源企业账号
     * @param toEa   数据复制到企业账号
     * @return 复制结果
     */
    public boolean copyEnterpriseOuterRoleData(String fromEa, String toEa) {
        // 暂时不支持，Paas侧会完成数据复制
        return true;
    }

    /**
     * 复制“互联应用”数据
     *
     * @param fromEa 数据源企业账号
     * @param toEa   数据复制到企业账号
     * @return 复制结果
     */
    public boolean copyEnterpriseLinkAppData(String fromEa, String toEa) {
        // 复制互联应用相关数据
        BaseArg baseArg = new BaseArg();
        baseArg.setAuthContext(new BaseArg.AuthContext(fromEa, 1000));
        List<LinkAppData> fromEnterpriseLinkApps = upstreamService.listEnterpriseLinkApps(baseArg).getData();
        if (CollectionUtils.isEmpty(fromEnterpriseLinkApps)) {
            return true;
        }
        List<String> fromLinkAppIds = fromEnterpriseLinkApps.stream().map(LinkAppData::getId).collect(Collectors.toList());
        // 查询互联应用过期时间
        Map<String, Integer> linkAppIdAvailableTimeMap = paasLicenseManager.batchGetLinkAppAvailableTime(fromEa, fromLinkAppIds);
        fromLinkAppIds.removeIf(x -> {
            // 移除过期应用
            Integer availableTimeDay = linkAppIdAvailableTimeMap.get(x);
            if (availableTimeDay != null && availableTimeDay <= 0) {
                return true;
            } else {
                return false;
            }
        });

        if (CollectionUtils.isEmpty(fromLinkAppIds)) {
            return true;
        }
        // 1、复制企业-互联应用关联数据
        copyUpstreamEnterpriseLinkAppRelationData(fromEa, toEa, fromLinkAppIds);
        // 2、复制互联应用-对象关联数据
        copyUpstreamEnterpriseLinkAppObjectAssociationData(fromEa, toEa, fromLinkAppIds);
        // 3、复制互联应用-外部角色关联数据
        copyUpstreamLinkAppOuterRoleAssociationData(fromEa, toEa, fromLinkAppIds);
        return true;
    }

    private void copyUpstreamLinkAppOuterRoleAssociationData(String fromEa, String toEa, List<String> fromLinkAppIds) {
        try {
            // 复制互联应用-外部角色关联数据
            List<UpstreamLinkAppOuterRoleAssociationData> fromOuterRoleDatas = linkAppOuterRoleService.listByUpstreamEaAndLinkAppIds(fromEa, fromLinkAppIds).getData();
            List<UpstreamLinkAppOuterRoleAssociationData> toEaData = linkAppOuterRoleService.listByUpstreamEaAndLinkAppIds(toEa, fromLinkAppIds).getData();
            if (!CollectionUtils.isEmpty(toEaData)) {
                List<String> hasExistsRoleIds = toEaData.stream().map(x -> x.getLinkAppId() + "_" + x.getRoleId()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(hasExistsRoleIds)) {
                    // 移除已经存在的角色ID，防止重复添加
                    fromOuterRoleDatas.removeIf(x -> hasExistsRoleIds.contains(x.getLinkAppId() + "_" + x.getRoleId()));
                }
            }
            fromOuterRoleDatas.forEach(x -> {
                x.setId(null);
                x.setUpstreamEa(toEa);
            });
            if (CollectionUtils.isEmpty(fromOuterRoleDatas)) {
                return;
            }
            linkAppOuterRoleService.batchAddUpstreamLinkAppOuterRoleAssociationData(fromOuterRoleDatas);
        } catch (Exception e) {
            log.warn("copyUpstreamLinkAppOuterRoleAssociationData-error:{}", e);
        }
    }

    private void copyUpstreamEnterpriseLinkAppObjectAssociationData(String fromEa, String toEa, List<String> fromLinkAppIds) {
        try {
            // 复制互联应用-对象关联数据
            List<UpstreamLinkAppObjectAssociationData> fromObjectDatas = linkAppAssociationObjectService.listByUpstreamEaAndLinkAppIds(fromEa, fromLinkAppIds).getData();
            List<UpstreamLinkAppObjectAssociationData> toEaData = linkAppAssociationObjectService.listByUpstreamEaAndLinkAppIds(toEa, fromLinkAppIds).getData();
            if (!CollectionUtils.isEmpty(toEaData)) {
                List<String> hasExistsObjectApiNames = toEaData.stream().map(x -> x.getObjectApiName() + "_" + x.getLinkAppId()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(hasExistsObjectApiNames)) {
                    // 移除已经存在的对象,防止重复
                    fromObjectDatas.removeIf(x -> hasExistsObjectApiNames.contains(x.getObjectApiName() + "_" + x.getLinkAppId()));
                }
            }
            fromObjectDatas.forEach(x -> {
                x.setId(null);
                x.setUpstreamEa(toEa);
            });
            if (CollectionUtils.isEmpty(fromObjectDatas)) {
                return;
            }
            linkAppAssociationObjectService.batchAddUpstreamLinkAppObjectAssociationData(fromObjectDatas);
        } catch (Exception e) {
            log.warn("copyUpstreamEnterpriseLinkAppObjectAssociationData-error:{}", e);
        }
    }

    private void copyUpstreamEnterpriseLinkAppRelationData(String fromEa, String toEa, List<String> fromLinkAppIds) {
        try {
            //  复制企业-互联应用关联数据
            List<UpstreamEnterpriseLinkAppRelationData> fromRelationDatas = upstreamService.listByUpstreamEaAndLinkAppIds(fromEa, fromLinkAppIds).getData();
            List<UpstreamEnterpriseLinkAppRelationData> toEaData = upstreamService.listByUpstreamEaAndLinkAppIds(toEa, fromLinkAppIds).getData();
            if (!CollectionUtils.isEmpty(toEaData)) {
                List<String> hasExistsAppIds = toEaData.stream().map(x -> x.getLinkAppId()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(hasExistsAppIds)) {
                    // 移除已经存在的互联应用,防止重复
                    fromRelationDatas.removeIf(x -> hasExistsAppIds.contains(x.getLinkAppId()));
                }
            }
            fromRelationDatas.forEach(x -> {
                x.setId(null);
                x.setUpstreamEa(toEa);
            });
            if (CollectionUtils.isEmpty(fromRelationDatas)) {
                return;
            }
            upstreamService.batchAddUpstreamEnterpriseLinkAppRelationData(fromRelationDatas);
        } catch (Exception e) {
            log.warn("copyUpstreamEnterpriseLinkAppRelationData-error:{}", e);
        }
    }

    /**
     * 复制“互联应用配置”数据
     *
     * @param fromEa 数据源企业账号
     * @param toEa   数据复制到企业账号
     * @return 复制结果
     */
    public boolean copyEnterpriseLinkAppConfigData(String fromEa, String toEa) {
        try {
            // 1、业务对象下业务类型的分配复制（Paas侧复制）
            // 2、业务对象下分配布局的复制（Paas侧复制）
            // 3、场景管理的复制（Paas侧复制）
            // 4、互联应用“基础数据权限”数据复制
            List<UpstreamLinkAppDataAuthPolicyData> toEaData = upstreamService.listUpstreamLinkAppDataAuthPolicyByUpstreamEa(toEa).getData();
            if (!CollectionUtils.isEmpty(toEaData)) {
                // 已经存在数据共享策略则不重复复制
                return true;
            }
            List<UpstreamLinkAppDataAuthPolicyData> upstreamLinkAppDataAuthPolicies = upstreamService.listUpstreamLinkAppDataAuthPolicyByUpstreamEa(fromEa).getData();
            if (CollectionUtils.isEmpty(upstreamLinkAppDataAuthPolicies)) {
                return true;
            }
            upstreamLinkAppDataAuthPolicies.forEach(authPolicy -> {
                String policyId = authPolicy.getPolicyId();
                String newPolicyID = UUID.randomUUID().toString();
                copyUpstreamPolicyAuthDownstreamEaData(policyId, newPolicyID);
                copyUpstreamPolicyDataAuthRangeData(policyId, newPolicyID);
                copyUpstreamLinkAppDataAuthPolicyData(toEa, authPolicy, newPolicyID);
            });
            // 5、web端应用首页配置（Paas侧复制）
            // 6、app端应用首页配置（Paas侧复制）
            return true;
        } catch (Exception e) {
            log.warn("copyEnterpriseLinkAppConfigData-error:{}", e);
            return false;
        }
    }

    /**
     * 复制上游企业互联应用数据共享策略数据
     *
     * @param toEa        目标企业账号
     * @param authPolicy  来源企业数据共享策略
     * @param newPolicyID 新共享策略ID
     */
    private void copyUpstreamLinkAppDataAuthPolicyData(String toEa, UpstreamLinkAppDataAuthPolicyData authPolicy, String newPolicyID) {
        try {
            authPolicy.setId(null);
            authPolicy.setPolicyId(newPolicyID);
            authPolicy.setUpstreamEa(toEa);
            Result<Integer> result = upstreamService.insertUpstreamLinkAppDataAuthPolicy(authPolicy);
            log.info("copyUpstreamLinkAppDataAuthPolicyData--result:{}", result);
        } catch (Exception e) {
            log.warn("copyUpstreamLinkAppDataAuthPolicyData--fail:{}", e);
        }
    }

    /**
     * 复制上游企业互联应用数据共享策略的业务对象数据可见范围
     *
     * @param policyId    旧数据共享策略
     * @param newPolicyID 新数据共享策略
     */
    private void copyUpstreamPolicyDataAuthRangeData(String policyId, String newPolicyID) {
        try {
            List<UpstreamPolicyDataAuthRangeData> upstreamPolicyDataAuthRanges = upstreamService.listUpstreamPolicyDataAuthRangeByPolicyIds(Lists.newArrayList(policyId)).getData();
            if (!CollectionUtils.isEmpty(upstreamPolicyDataAuthRanges)) {
                List<CrmObjectAuthRangeVo> objectAuthRanges = Lists.newArrayList();
                upstreamPolicyDataAuthRanges.forEach(authRange -> {
                    CrmObjectAuthRangeVo authRangeVo = new CrmObjectAuthRangeVo();
                    authRangeVo.setDownstreamAuthRange(authRange.getDownstreamAuthRange());
                    authRangeVo.setObjectApiName(authRange.getObjectApiName());
                    objectAuthRanges.add(authRangeVo);
                });
                Result<Boolean> result = upstreamService.batchAddUpstreamPolicyDataAuthRange(newPolicyID, objectAuthRanges);
                log.info("copyUpstreamPolicyDataAuthRangeData--result:{}", result);
            }
        } catch (Exception e) {
            log.warn("copyUpstreamPolicyDataAuthRangeData--fail:{}", e);
        }
    }

    /**
     * 复制上游企业互联应用数据共享给下游企业规则
     *
     * @param policyId    旧数据共享策略ID
     * @param newPolicyID 新数据共享策略ID
     */
    private void copyUpstreamPolicyAuthDownstreamEaData(String policyId, String newPolicyID) {
        try {
            List<UpstreamPolicyAuthDownstreamData> upstreamPolicyAuthDownstreams = upstreamService.listUpstreamPolicyAuthDownstreamByPolicyIds(Lists.newArrayList(policyId)).getData();
            if (CollectionUtils.isEmpty(upstreamPolicyAuthDownstreams)) {
                return;
            }
            if (!CollectionUtils.isEmpty(upstreamPolicyAuthDownstreams)) {
                List<Long> downstreamOuterTenantIds = upstreamPolicyAuthDownstreams.stream().map(i -> i.getDownstreamOuterTenantId()).collect(Collectors.toList());
                Result<Boolean> result = upstreamService.batchAddUpstreamPolicyAuthDownstream(newPolicyID, downstreamOuterTenantIds);
                log.info("copyUpstreamPolicyAuthDownstreamEaData--result:{}", result);
            }
        } catch (Exception ex) {
            log.warn("copyUpstreamPolicyAuthDownstreamEaData--fail:{}", ex);
        }
    }

    /**
     * 复制新版企业信息
     * 910版本把企业信息字段从mongo移到了企业卡片表
     *
     * @param fromEa
     * @param toEa
     * @return
     */
    public boolean copyEnterpriseInfoData(String fromEa, String toEa) {
        try {
            Long fromOuterTenantId = enterpriseCardEntityDao.getOuterTenantIdByEa(fromEa);
            if (fromOuterTenantId == null) {
                return false;
            }

            EnterpriseCardEntity fromEnterpriseCardEntity = enterpriseCardEntityDao.findOrCreateToSet(fromOuterTenantId);
            if (fromEnterpriseCardEntity == null) {
                return false;
            }

            Long toOuterTenantId = enterpriseCardEntityDao.getOuterTenantIdByEa(toEa);
            if (toOuterTenantId == null) {
                return false;
            }

            EnterpriseCardEntity toEnterpriseCardEntity = enterpriseCardEntityDao.findOrCreateToSet(toOuterTenantId);
            if (toEnterpriseCardEntity == null) {
                return false;
            }
            // 不复制企业名称及其多语项
            toEnterpriseCardEntity.setLogoPath(fromEnterpriseCardEntity.getLogoPath());
            toEnterpriseCardEntity.setIsShowLogo(fromEnterpriseCardEntity.getIsShowLogo());
            toEnterpriseCardEntity.setEnterpriseNameDisplayMode(fromEnterpriseCardEntity.getEnterpriseNameDisplayMode());

            enterpriseCardDao.upsertById(toEnterpriseCardEntity);
            return true;
        } catch (Exception e) {
            log.warn("copyEnterpriseInfoData-error:", e);
            return false;
        }
    }

    /**
     * 复制“互联设置”数据
     *
     * @param fromEa 数据源企业账号
     * @param toEa   数据复制到企业账号
     * @return 复制结果
     */
    public boolean copyEnterpriseRelationConfigData(String fromEa, String toEa) {
        try {
            EnterpriseMetaDataEntity toEaDataEntity = enterpriseMetaDataEntityDaoOldManager.find(toEa);
            // 查询目标企业互联配置数据
            EnterpriseMetaDataEntity enterpriseMetaDataEntity = enterpriseMetaDataEntityDaoOldManager.find(fromEa);
            if (enterpriseMetaDataEntity != null) {
                enterpriseMetaDataEntity.setEnterpriseAccount(toEa);
                boolean result;
                if (toEaDataEntity != null) {
                    result = enterpriseMetaDataEntityDaoOldManager.findAndUpdate(enterpriseMetaDataEntity);
                } else {
                    result = enterpriseMetaDataEntityDaoOldManager.save(enterpriseMetaDataEntity);
                }
                log.info("copyEnterpriseRelationConfigData--result:{}", result);
            }

            // 复制【互联配置-企业信息】数据
            return copyEnterpriseInfoData(fromEa, toEa);
        } catch (Exception e) {
            log.warn("copyEnterpriseRelationConfigData-error:", e);
            return false;
        }
    }

    /**
     * 复制互联账号自注册配置数据
     *
     * @param fromEa 数据源企业账号
     * @param toEa   数据复制到企业账号
     */
    public boolean copyLoginRegisterConfigData(String fromEa, String toEa) {
        try {
            // TODO 两个ea不在同云如何处理？
            int fromEi = eieaConverter.enterpriseAccountToId(fromEa);
            int toEi = eieaConverter.enterpriseAccountToId(toEa);

            List<LoginRegisterConfigEntity> loginRegisterConfigList = loginRegisterConfigDao.selectByTenantId(fromEi + "");
            if (CollectionUtils.isEmpty(loginRegisterConfigList)) {
                return true;
            }

            for (LoginRegisterConfigEntity entity : loginRegisterConfigList) {
                LoginRegisterConfigData loginRegisterConfigData = loginRegisterConfigService.getLoginRegisterConfigDataById(fromEi + "", entity.getId());
                loginRegisterConfigService.upsertLoginRegisterConfigData(toEi + "", loginRegisterConfigData);
            }
            return true;
        } catch (Throwable throwable) {
            log.warn("copyLoginRegisterConfigData-error", throwable);
            return false;
        }
    }

    /**
     * 复制互联账号自注册配置数据
     *
     * @param fromEa 数据源企业账号
     * @param toEa   数据复制到企业账号
     */
    public boolean copyEnterpriseRelationAccountActiveConfigData(String fromEa, String toEa) {
        try {
            // TODO 两个ea不在同云如何处理？
            int fromEi = eieaConverter.enterpriseAccountToId(fromEa);
            List<ERAccountActiveConfig> fromEaConfigDataList = erAccountActiveConfigDaoOldManager.getByUpstreamEa(fromEi, fromEa);
            if (CollectionUtils.isEmpty(fromEaConfigDataList)) {
                return true;
            }

            fromEaConfigDataList.forEach(config -> {
                int toEi = eieaConverter.enterpriseAccountToId(toEa);
                ObjectId id = new ObjectId();
                config.set_id(id);
                config.setUpstreamEa(toEa);
                ERAccountActiveConfigData newConfig = BeanUtil.deepCopy(config, ERAccountActiveConfigData.class);
                int updateResult = erAccountActiveConfigDaoOldManager.updateByUpstreamEa(toEi, toEa, newConfig);
                log.info("ea:{},linkType:{},identityType:{},mapperApiName:{},result:{}", toEa, config.getLinkType(), config.getIdentityType(), config.getMapperApiName(), updateResult);
            });
            return true;
        } catch (Exception e) {
            log.warn("copyEnterpriseRelationAccountActiveConfigData-error:", e);
            return false;
        }
    }


    /**
     * 互联企业，互联用户 复制出来的数据要被设置为停用
     *
     * @param toTenantId
     */
    public boolean updateEnterpriseAndEmployeeStatus(String toTenantId) {
        try {
            String updatePublicEmployeeSQL = String.format(UPDATE_PUBLIC_EMPLOYEE_TYPE_SQL, toTenantId, UPDATE_BATCH_SIZE);
            while (true) {
                int effect = specialTableMapper.setTenantId(toTenantId).batchUpdateBySql(updatePublicEmployeeSQL);
                if (effect <= 0) {
                    break;
                }
            }

            String updateEnterpriseRelationSQL = String.format(UPDATE_ENTERPRISE_RELATION_SQL, toTenantId, UPDATE_BATCH_SIZE);
            while (true) {
                int effect = specialTableMapper.setTenantId(toTenantId).batchUpdateBySql(updateEnterpriseRelationSQL);
                if (effect <= 0) {
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("", e);
        }

        return false;
    }
}
