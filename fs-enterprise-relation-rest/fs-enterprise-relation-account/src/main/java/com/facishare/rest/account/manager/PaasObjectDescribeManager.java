package com.facishare.rest.account.manager;

import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.EnterpriseRelationObjConstant;
import com.facishare.enterprise.common.constant.FunctionPrivilegeConstant;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.exception.RelationException;
import com.facishare.enterprise.common.model.paas.AddButtonData;
import com.facishare.enterprise.common.model.paas.AddDescribeFieldData;
import com.facishare.enterprise.common.model.paas.AddLayoutData;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.retry.RetryUtil;
import com.facishare.enterprise.common.util.*;
import com.facishare.er.api.model.enums.SalesmanOpenPublicEmployeeAccountSwitchStatus;
import com.facishare.global.rest.dao.mongo.EnterpriseMetaDataEntityDao;
import com.facishare.global.rest.model.entity.EnterpriseMetaDataEntity;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutController;
import com.facishare.paas.appframework.core.predef.service.ObjectDesignerService;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.appframework.metadata.layout.LayoutStructure;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.CommonComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.privilege.api.FunctionPrivilegeRestService;
import com.facishare.privilege.api.module.PrivilegeContext;
import com.facishare.privilege.api.module.function.IncrementUpdateFuncAccessByRoleCodeVo.Argument;
import com.facishare.rest.account.remote.RemoteManager;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.crmrestapi.arg.*;
import com.fxiaoke.crmrestapi.common.contants.AccountFieldContants;
import com.fxiaoke.crmrestapi.common.contants.ContactFieldContants;
import com.fxiaoke.crmrestapi.common.contants.PartnerFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.common.data.TypeHashMap;
import com.fxiaoke.crmrestapi.result.BatchSaveRolePermissionsResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.CreateLayoutRuleResult;
import com.fxiaoke.crmrestapi.result.FindRolePermission;
import com.fxiaoke.crmrestapi.result.FindRolesPermissionListResult;
import com.fxiaoke.crmrestapi.result.FindRuleRelatedInfo;
import com.fxiaoke.crmrestapi.result.Permission;
import com.fxiaoke.crmrestapi.result.SaveRolePermission;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.crmrestapi.service.ObjectLayoutRuleService;
import com.fxiaoke.enterpriserelation.objrest.arg.FlushPaasDescribeArg;
import com.fxiaoke.paasauthrestapi.arg.CreateButtonArg;
import com.fxiaoke.paasauthrestapi.arg.FindButtonListArg;
import com.fxiaoke.paasauthrestapi.common.result.FunctionPrivilegeResult;
import com.fxiaoke.paasauthrestapi.result.FindButtonListResult;
import com.fxiaoke.paasauthrestapi.service.FunctionPrivilegeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.reflect.TypeToken;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.io.Resources;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;

import static com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance;

/**
 * @author: jiangxch
 * @date: 2022/1/19 14:23
 */
@Component
@Slf4j
public class PaasObjectDescribeManager {
    @Autowired
    private RemoteManager remoteManager;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private ObjectDesignerService objectDesignerService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private EnterpriseMetaDataEntityDao enterpriseMetaDataEntityDaoOldManager;
    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private FunctionPrivilegeRestService functionPrivilegeRestService;
    @Autowired
    private ObjectLayoutRuleService objectLayoutRuleService;
    //[对象apiName,[字段apiName,字段json body]]
    public static Map<String, List<AddDescribeFieldData>> businessOperatorObjectAddFieldsMap = new HashMap<>();
    public static Map<String, List<AddButtonData>> businessOperatorObjectAddButtonsMap = new HashMap<>();
    public static Map<String, List<AddLayoutData>> businessOperatorObjectAddLayoutMap = new HashMap<>();
    public static Map<String, List<SaveRelevantTeamPermissionArg>> saveRelevantTeamPermissionMap = new HashMap<>();
    public static final String initEr = "initEr";
    public static final String initBusiness = "initBusiness";
    public static final String initErDepartmentSwitch = "initErDepartmentSwitch";
    public static final String INIT_ENTERPRISE_IT_CONTROL = "init_enterprise_it_control";
    public static final String ACCOUNT_OBJ_DEFAULT_DETAIL_LAYOUT_API_NAME = "AccountObj_layout_generate_by_UDObjectServer__c";
    public static final String PARTNER_OBJ_DEFAULT_DETAIL_LAYOUT_API_NAME = "PartnerObj_layout_generate_by_UDObjectServer__c";
    public static final String PARTNER_OBJ_ENTERPRISE_IT_CONTROL_LAYOUT_API_NAME = "PartnerObj_enterprise_it_control_layout__c";
    public static final String PARTNER_OBJ_ENTERPRISE_IT_CONTROL_RECORD_TYPE_API_NAME = "record_enterprise_it_control_layout__c";
    public static final Map<String, Map<String, Map<String, Object>>> apiNameFieldDataCache = new HashMap<>();
    public static final Map<String, String> localFilePathFileContentLocalCache = new ConcurrentHashMap<>();
    public static String erSystemLayoutRuleContent;

    static {
        try {
            businessOperatorObjectAddFieldsMap = JsonUtil.fromJson(StreamUtils.copyToString(Resources.getResourceAsStream("object/PaasObjectDecribe.json"), Charset.defaultCharset()),
                    new com.google.gson.reflect.TypeToken<Map<String, List<AddDescribeFieldData>>>() {
                    }.getType());
            businessOperatorObjectAddButtonsMap = JsonUtil.fromJson(StreamUtils.copyToString(Resources.getResourceAsStream("object/PaasObjectButton.json"), Charset.defaultCharset()),
                    new com.google.gson.reflect.TypeToken<Map<String, List<AddButtonData>>>() {
                    }.getType());
            businessOperatorObjectAddLayoutMap = JsonUtil.fromJson(StreamUtils.copyToString(Resources.getResourceAsStream("object/PaasObjectLayout.json"), Charset.defaultCharset()),
                    new com.google.gson.reflect.TypeToken<Map<String, List<AddLayoutData>>>() {
                    }.getType());
            erSystemLayoutRuleContent = StreamUtils.copyToString(Resources.getResourceAsStream("object/EnterpriseRelationObj/layout/rule/EnterpriseRelationObj.Edit.Rule.json"),
                    Charset.defaultCharset());
            saveRelevantTeamPermissionMap = JsonUtil.fromJson(StreamUtils.copyToString(Resources.getResourceAsStream("object/PaasRelevantTeamPermission.json"), Charset.defaultCharset()),
                    new com.google.gson.reflect.TypeToken<Map<String, List<SaveRelevantTeamPermissionArg>>>() {
                    }.getType());
        } catch (Exception e) {
            log.error("", e);
        }
        log.info("===============================>");
        log.info("businessOperatorObjectAddFieldsMap={}", businessOperatorObjectAddFieldsMap);
        log.info("businessOperatorObjectAddButtonsMap={}", businessOperatorObjectAddButtonsMap);
        log.info("businessOperatorObjectAddLayoutMap={}", businessOperatorObjectAddLayoutMap);
    }

    public Result<Void> initErObjectDescribe(String upstreamEa, Boolean needRetry) {
        try {
            // 初始化互联对象布局规则
            this.initErLayoutRule(upstreamEa);
            // 初始化互联企业关联合作伙伴对象字段描述
            this.initEnterpriseRelationObjMapperPartnerField(upstreamEa, needRetry);
            // 开启系统库的互联企业和对接人对象 http://wiki.firstshare.cn/pages/viewpage.action?pageId=158602399
            if (GrayUtils.hasInBusinessFeatureGray(upstreamEa)) {
                this.initErBusiness(upstreamEa);
            }
            initRelevantTeamPermission(upstreamEa);
        } catch (Exception e) {
            log.warn("", e);
            return Result.newError(ResultCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }

    /**
     * 如果用户已经设置其他的相关团队数据权限，就不再设置，如果没设置过，进行初始化
     *
     * @param upstreamEa
     */
    public void initRelevantTeamPermission(String upstreamEa) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(upstreamEa);
            List<SaveRelevantTeamPermissionArg> initPermission = saveRelevantTeamPermissionMap.get("initPermission");
            Map<String, Map<String, Set<String>>> existsRulePermission = findExistsRulePermission(ei, initPermission);
            //1 如果没被初始化过直接写入
            HeaderObj headerObj = newInstance(ei, SuperUserConstants.USER_ID);
            if (ObjectUtils.isEmpty(existsRulePermission)) {
                for (SaveRelevantTeamPermissionArg arg : initPermission) {
                    objectDescribeCrmService.batchSaveRelevantTeamPermission(headerObj, arg);
                }
                return;
            }

            //2 第二次开启/重试，挨个检查是否设置过相关团队数据权限,如果没设置过再写入，防止重复写入
            List<SaveRelevantTeamPermissionArg> argList = new ArrayList<>(initPermission.size());
            BeanUtil.copyList(SaveRelevantTeamPermissionArg.class, argList, initPermission);
            Iterator<SaveRelevantTeamPermissionArg> saveRelevantTeamPermissionArgIterator = argList.iterator();
            while (saveRelevantTeamPermissionArgIterator.hasNext()) {
                SaveRelevantTeamPermissionArg saveRelevantTeamPermissionArg = saveRelevantTeamPermissionArgIterator.next();
                Iterator<RelevantTeamPermissionArg> permissionArgIterator = saveRelevantTeamPermissionArg.getPermission_list().iterator();
                while (permissionArgIterator.hasNext()) {
                    RelevantTeamPermissionArg relevantTeamPermissionArg = permissionArgIterator.next();
                    // 一个对象的一批字段是同时设置的，如果任意一个字段被修改过，就认为发生过变化，删除参数不更新
                    Set<String> existsFieldApiName = (Set<String>) existsRulePermission
                            .getOrDefault(saveRelevantTeamPermissionArg.getDescribe_api_name(), Collections.EMPTY_MAP)
                            .getOrDefault(relevantTeamPermissionArg.getRole_type(), Collections.EMPTY_SET);
                    boolean exists = relevantTeamPermissionArg.getRole_permission_list().stream().anyMatch(it -> existsFieldApiName.contains(it.getField_api_name()));
                    if (exists) {
                        permissionArgIterator.remove();
                    }
                }

                if (ObjectUtils.isEmpty(saveRelevantTeamPermissionArg.getPermission_list())) {
                    saveRelevantTeamPermissionArgIterator.remove();
                }
            }

            if (ObjectUtils.isEmpty(argList)) {
                log.info("duplicate initRelevantTeamPermission skip");
                return;
            }

            for (SaveRelevantTeamPermissionArg arg : initPermission) {
                objectDescribeCrmService.batchSaveRelevantTeamPermission(headerObj, arg);
            }
        } catch (Throwable throwable) {
            log.warn("initRelevantTeamPermission error", throwable);
        }
    }

    private Map<String, Map<String, Set<String>>> findExistsRulePermission(int ei, List<SaveRelevantTeamPermissionArg> initPermission) {
        Map<String, Map<String, Set<String>>> result = new HashMap<>();
        HeaderObj headerObj = new HeaderObj(ei, -10000);

        try {
            for (SaveRelevantTeamPermissionArg saveRelevantTeamPermissionArg : initPermission) {
                for (RelevantTeamPermissionArg relevantTeamPermissionArg : saveRelevantTeamPermissionArg.getPermission_list()) {
                    FindRolesPermissionListArg findRolesPermissionListArg = new FindRolesPermissionListArg();
                    findRolesPermissionListArg.setDescribe_api_name(saveRelevantTeamPermissionArg.getDescribe_api_name());
                    findRolesPermissionListArg.setRole_type(String.valueOf(relevantTeamPermissionArg.getRole_type()));
                    findRolesPermissionListArg.setInclude_roles(false);
                    findRolesPermissionListArg.setOut_permission(false);
                    com.fxiaoke.crmrestapi.common.result.Result<FindRolesPermissionListResult> rolesPermissionList = objectDescribeCrmService.findRolesPermissionList(headerObj, findRolesPermissionListArg);
                    if (!rolesPermissionList.isSuccess() || rolesPermissionList.getData() == null) {
                        log.warn("find rule permission error {} {}", findRolesPermissionListArg, result);
                        continue;
                    }

                    List<FindRolePermission> rolePermissionList = rolesPermissionList.getData().getRole_permission_list();
                    if (ObjectUtils.isEmpty(rolePermissionList)) {
                        continue;
                    }

                    for (FindRolePermission findRolePermission : rolePermissionList) {
                        if (!Objects.equals("disable", findRolePermission.getPermission_type())) {
                            result.computeIfAbsent(saveRelevantTeamPermissionArg.getDescribe_api_name(), it -> new HashMap<>())
                                    .computeIfAbsent(findRolesPermissionListArg.getRole_type(), it -> new HashSet<>())
                                    .add(findRolePermission.getField_api_name());
                        }
                    }
                }
            }

            return result;
        } catch (Exception e) {
            log.error("findExistsRulePermission error {}", initPermission, e);
        }

        return MapUtils.EMPTY_SORTED_MAP;
    }

    public Result<Void> addObjectFieldWithKey(Integer ei, String addObjectFieldKey) {
        try {
            List<AddDescribeFieldData> addDescribeFieldDatas = businessOperatorObjectAddFieldsMap.get(addObjectFieldKey);
            if (addDescribeFieldDatas == null) {
                return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), "key=addObjectFieldKey " + ResultCode.NO_SET_FIELD.getDescription());
            }
            // 初始化字段
            for (AddDescribeFieldData addDescribeFieldData : addDescribeFieldDatas) {
                addDescribeFieldForCrmObject(ei, addDescribeFieldData.getDescribeApiName(), addDescribeFieldData, true);
            }
        } catch (Exception e) {
            log.warn("", e);
            return Result.newError(ResultCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }

    public Result<Void> initErBusiness(String upstreamEa) {
        try {
            this.initObjectDescribe(upstreamEa, AccountFieldContants.API_NAME);
            this.initObjectDescribe(upstreamEa, ContactFieldContants.API_NAME);
            initPartnerObjectDescribe(upstreamEa);
        } catch (Exception e) {
            log.warn("", e);
            return Result.newError(ResultCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }

    private void initPartnerObjectDescribe(String upstreamEa) {
        Boolean openPartner = remoteManager.partnerStatusIsOpen(upstreamEa);
        if (Boolean.TRUE.equals(openPartner)) {
            this.initObjectDescribe(upstreamEa, PartnerFieldContants.API_NAME);
        }
    }

    private Result<Void> initObjectDescribe(String upstreamEa, String apiName) {
        try {
            // 初始化字段
            for (AddDescribeFieldData addDescribeFieldData : businessOperatorObjectAddFieldsMap.get(initBusiness)) {
                if (apiName.equals(addDescribeFieldData.getDescribeApiName())) {
                    addDescribeFieldForCrmObject(upstreamEa, addDescribeFieldData.getDescribeApiName(), addDescribeFieldData, true); // 支持幂等
                }
            }
//                // 初始化按钮
//                for (AddButtonData addButtonData : businessOperatorObjectAddButtonsMap.get(initBusiness)) {
//                    if (apiName.equals(addButtonData.getDescribeApiName())) {
//                        addButtonForCrmObject(upstreamEa, addButtonData.getDescribeApiName(), addButtonData, true);
//                    }
//                }
        } catch (Exception e) {
            log.warn("", e);
            return Result.newError(ResultCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }

    public Result<Void> initEnterpriseRelationObjMapperPartnerField(String upstreamEa, boolean needRetry) {
        boolean success = RetryUtil.addRetryTask(() -> {
            boolean res = this.doInitEnterpriseRelationObjMapperPartnerField(upstreamEa); // 支持幂等
            if (!needRetry) {
                return true;
            }
            return res;
        }, Lists.newArrayList(5, 20, 40, 80, 300, 600), TimeUnit.MINUTES);
        if (!success) {
            return Result.newError(ResultCode.TIME_TASK_RETRY);
        }
        return Result.newSuccess();
    }

    public boolean doInitEnterpriseRelationObjMapperPartnerField(String upstreamEa) {
        try {
            Boolean openPartner = remoteManager.hasPartnerObj(upstreamEa);
            if (Boolean.TRUE.equals(openPartner)) {
                // 初始化字段
                for (AddDescribeFieldData addDescribeFieldData : businessOperatorObjectAddFieldsMap.get(initEr)) {
                    addDescribeFieldForCrmObject(upstreamEa, addDescribeFieldData.getDescribeApiName(), addDescribeFieldData, true); // 支持幂等
                }
                // 业务员开通互联账号按钮开启时未开通合作伙伴，后续再开通时需要单独初始化
                EnterpriseMetaDataEntity enterpriseMetaDataEntity = enterpriseMetaDataEntityDaoOldManager.find(upstreamEa);
                Integer salesmanOpenStatus = enterpriseMetaDataEntity.getSalesmanOpenPublicEmployeeAccountSwitchStatus();
                if (salesmanOpenStatus != null && salesmanOpenStatus == SalesmanOpenPublicEmployeeAccountSwitchStatus.OPENED.getStatus()) {
                    // 初始化按钮
                    for (AddButtonData addButtonData : businessOperatorObjectAddButtonsMap.get(initBusiness)) {
                        if (PartnerFieldContants.API_NAME.equals(addButtonData.getButton().getDescribeApiName())) {
                            addButtonForCrmObject(upstreamEa, addButtonData.getButton().getDescribeApiName(), addButtonData, true); // 支持幂等
                        }
                    }
                    // 初始化布局
                    List<AddLayoutData> components = businessOperatorObjectAddLayoutMap.get(PaasObjectDescribeManager.initBusiness);
                    addLayoutComponents(upstreamEa, "-10000", PartnerFieldContants.API_NAME, PARTNER_OBJ_DEFAULT_DETAIL_LAYOUT_API_NAME, components, true); // 支持幂等
                    // 渠道经理角色初始化
                    addFuncAccessByRole(upstreamEa, "-10000", FunctionPrivilegeConstant.CHANNEL_MANAGER_ROLE_CODE, FunctionPrivilegeConstant.ER_BUSINESS_FUNC_CODES, true);// 支持幂等
                }
                this.initObjectDescribe(upstreamEa, PartnerFieldContants.API_NAME); //支持幂等
                return true;
            }
        } catch (Exception e) {
            log.warn("", e);
        }
        return false;
    }

    public boolean initErLayoutRule(String upstreamEa) {
        return RetryUtil.addRetryTask(() -> {
            try {
                int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
                com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(tenantId, -10000);

                TypeHashMap<String, Object> erSystemLayoutRuleMap = JsonUtil.fromJson(erSystemLayoutRuleContent, new com.google.gson.reflect.TypeToken<TypeHashMap<String, Object>>() {
                }.getType());

                FindRuleRelatedInfoArg findRuleRelatedInfoArg = new FindRuleRelatedInfoArg();
                findRuleRelatedInfoArg.setDescribeApiName(EnterpriseRelationObjConstant.API_NAME);
                findRuleRelatedInfoArg.setIncludeDescribe(false);
                com.fxiaoke.crmrestapi.common.result.Result<FindRuleRelatedInfo> ruleRelatedInfoResult = objectLayoutRuleService.findRuleRelatedInfo(headerObj, findRuleRelatedInfoArg);
                if (!ruleRelatedInfoResult.isSuccess()) {
                    return false;
                }
                FindRuleRelatedInfo ruleData = ruleRelatedInfoResult.getData();
                List<TypeHashMap<String, Object>> layoutRuleList = ruleData.getLayoutRuleList();
                if (!CollectionUtils.isEmpty(layoutRuleList)) {
                    for (TypeHashMap<String, Object> layoutRule : layoutRuleList) {
                        String apiName = layoutRule.getString("api_name");
                        String systemApiName = erSystemLayoutRuleMap.getString("api_name");
                        if (systemApiName.equals(apiName)) {
                            return true;
                        }
                    }
                }

                CreateLayoutRuleArg createLayoutRuleArg = new CreateLayoutRuleArg();
                createLayoutRuleArg.setJson_data(erSystemLayoutRuleMap);
                com.fxiaoke.crmrestapi.common.result.Result<CreateLayoutRuleResult> layoutRuleResult = objectLayoutRuleService.createLayoutRule(headerObj, createLayoutRuleArg);
                if (!layoutRuleResult.isSuccess()) {
                    return false;
                }
            } catch (Exception e) {
                log.warn("initErLayoutRule error. upstreamEa={}", upstreamEa, e);
            }
            return true;
        });
    }

    private boolean addFuncAccessByRole(String upstreamEa, String operatorId, String roleCode, List<String> funcCodes, boolean hasRetry) {
        return RetryUtil.addRetryTask(() -> {
            boolean success = false;
            try {
                int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
                PrivilegeContext context = new PrivilegeContext();
                context.setTenantId(tenantId);
                context.setOperatorId(Integer.parseInt(operatorId));
                Argument updateFuncArg = new Argument();
                updateFuncArg.setRoleCode(roleCode);
                updateFuncArg.setAddFuncCodes(funcCodes);
                functionPrivilegeRestService.incrementUpdateFuncAccessByRoleCode(context, updateFuncArg);
                success = true;
            } catch (Exception e) {
                log.warn("doAddFuncAccessByRole error.", e);
            }
            if (!hasRetry) {
                return true;
            }
            return success;
        });
    }

    /**
     * 给客户、合作伙伴、联系人对象预设【开通互联企业】和 【停用互联企业】按钮
     */
    public Result<Void> initErBusinessButton(String upstreamEa) {
        try {
            List<AddButtonData> addButtonDatas = businessOperatorObjectAddButtonsMap.get(PaasObjectDescribeManager.initBusiness);
            Boolean isOpenPartner = remoteManager.partnerStatusIsOpen(upstreamEa);
            for (AddButtonData addButtonData : addButtonDatas) {
                if (AccountFieldContants.API_NAME.equals(addButtonData.getButton().getDescribeApiName()) || ContactFieldContants.API_NAME.equals(addButtonData.getButton().getDescribeApiName())
                        || PartnerFieldContants.API_NAME.equals(addButtonData.getButton().getDescribeApiName()) && isOpenPartner) {
                    addButtonForCrmObject(upstreamEa, addButtonData.getButton().getDescribeApiName(), addButtonData, true);
                }
            }
        } catch (Exception e) {
            log.warn("initErBusinessButton error.", e);
            return Result.newError(ResultCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }

    public Result<Void> initErBusinessLayout(String upstreamEa, String operatorId) {
        try {
            List<AddLayoutData> components = businessOperatorObjectAddLayoutMap.get(PaasObjectDescribeManager.initBusiness);
            addLayoutComponents(upstreamEa, operatorId, AccountFieldContants.API_NAME, ACCOUNT_OBJ_DEFAULT_DETAIL_LAYOUT_API_NAME, components, true);

            Boolean isOpenPartner = remoteManager.partnerStatusIsOpen(upstreamEa);
            if (isOpenPartner) {
                addLayoutComponents(upstreamEa, operatorId, PartnerFieldContants.API_NAME, PARTNER_OBJ_DEFAULT_DETAIL_LAYOUT_API_NAME, components, true);
            }
        } catch (Exception e) {
            log.warn("initErBusinessLayout error.", e);
            return Result.newError(ResultCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }

    private boolean addButtonForCrmObject(String upstreamEa, String describeApiName, AddButtonData addButtonData, boolean hasRetry) {
        return RetryUtil.addRetryTask(() -> {
            boolean success = doAddButtonForCrmObject(upstreamEa, describeApiName, addButtonData);
            if (!hasRetry) {
                return true;
            }
            return success;
        });
    }

    public boolean doAddButtonForCrmObject(String upstreamEa, String describeApiName, AddButtonData addButtonData) {
        int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        com.fxiaoke.paasauthrestapi.common.data.HeaderObj headerObj = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(tenantId, SuperUserConstants.USER_ID);
        try {
            Set<String> buttonApiNameSet = Sets.newHashSet();
            headerObj.put("x-fs-userInfo", 1000);
            FindButtonListArg findButtonListArg = new FindButtonListArg();
            findButtonListArg.setDescribeApiName(describeApiName);
            FindButtonListResult findButtonListResult = functionPrivilegeService.findButtonList(headerObj, findButtonListArg).getResult();
            findButtonListResult.getButtonList().forEach(x -> {
                buttonApiNameSet.add(x.getApi_name());
            });
            if (buttonApiNameSet.contains(addButtonData.getButton().getButtonApiName())) {
                return true;
            }
            List<String> roles = addButtonData.getRoles();
            if (CollectionUtil.isEmpty(roles)) {
                roles = Lists.newArrayList();
            }
            if (!roles.contains(FunctionPrivilegeConstant.CRM_ADMIN_ROLE_CODE)) {
                roles.add(FunctionPrivilegeConstant.CRM_ADMIN_ROLE_CODE);// CRM管理员
            }
            CreateButtonArg createButtonArg = new CreateButtonArg();
            createButtonArg.setRoles(roles);
            CreateButtonArg.Button button = new CreateButtonArg.Button();
            CreateButtonArg.PostAction postAction = new CreateButtonArg.PostAction();
            createButtonArg.setPost_actions(Lists.newArrayList(postAction));
            button.setButton_type("common");
            button.setDefine_type("custom");
            button.setDescribe_api_name(describeApiName);
            button.setApi_name(addButtonData.getButton().getButtonApiName());
            button.setDescription(addButtonData.getButton().getDescribe());
            button.setIs_active(true);
            button.setIs_deleted(false);
            button.setJump_url("");
            button.setLabel(addButtonData.getButton().getLabel());
            button.setParam_form(Lists.newArrayList());
            button.setWheres(JsonUtil.fromJson(addButtonData.getButton().getWheres(), new TypeToken<List<Map>>() {
            }.getType()));
            button.setUse_pages(addButtonData.getButton().getUsePages());
            createButtonArg.setButton(JSON.toJSONString(button));
            FunctionPrivilegeResult<Void> res = functionPrivilegeService.createButton(headerObj, createButtonArg);
            if (res != null && res.getErrCode() == 0) {
                return true;
            }
        } catch (Exception e) {
            log.warn("create sync button error detail={}", e);
            return false;
        }
        return false;
    }

    public boolean updateLayoutByLocalFile(int tenantId, String localLayoutFilePath) {
        String content = localFilePathFileContentLocalCache.get(localLayoutFilePath);
        if (content == null) {
            try {
                content = StreamUtils.copyToString(Resources.getResourceAsStream(localLayoutFilePath), Charset.defaultCharset());
                localFilePathFileContentLocalCache.put(localLayoutFilePath, content);
            } catch (IOException e) {
                log.warn("", e);
                return false;
            }
        }
        try {
            List<ILayout> layoutList = new ArrayList<>();
            ILayout layout = new Layout(Document.parse(content));
            layoutList.add(layout);
            layoutLogicService.createOrUpdateLayoutForDevTools(tenantId + "", EnterpriseRelationObjConstant.API_NAME, layoutList);
        } catch (Exception e) {
            log.warn("", e);
            return false;
        }
        return true;
    }

    public boolean addDescribeFieldForCrmObject(String upstreamEa, String describeApiName, AddDescribeFieldData addDescribeFieldData, boolean hasRetry) {
        int ei = eieaConverter.enterpriseAccountToId(upstreamEa);
        return addDescribeFieldForCrmObject(ei, describeApiName, addDescribeFieldData, hasRetry);
    }

    public boolean addErDepartmentFieldForErObj(Integer ei) {
        try {
            // 初始化字段
            for (AddDescribeFieldData addDescribeFieldData : businessOperatorObjectAddFieldsMap.get(initErDepartmentSwitch)) {
                boolean success = addObjectField(ei, addDescribeFieldData.getDescribeApiName(), addDescribeFieldData);
                if (!success) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.warn("", e);
        }
        return false;
    }

    public boolean addOutOwnOrOrgErDepartmentField(Integer ei, String apiName, ObjectDescribe describe, boolean isDep) {
        String fieldApiName = isDep ? "out_data_own_department" : "out_data_own_organization";
        boolean existsField = MapUtils.emptyIfNull(describe.getFields()).containsKey(fieldApiName);
        if (existsField) {
            return true;
        }

        boolean publicObject = IObjectDescribe.PUBLIC_OBJECT_VISIBLE_SCOPE.contains(describe.getVisibleScope()) && ObjectUtils.isNotEmpty(describe.getUpstreamTenantId());
        ei = publicObject ? Integer.valueOf(describe.getUpstreamTenantId()) : ei;
        String fieldDescribeJson = isDep ? getOutDataOwnDepartmentFieldJson(publicObject, ei) : getOutDataOwnOrganizationFieldJson(publicObject, ei);
        String fieldsLayoutJson = "[]";
        String groupFieldsJson = "[]";

        boolean success = true;
        AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
        arg.setDescribeAPIName(apiName);
        arg.setFieldDescribe(fieldDescribeJson);
        arg.setGroupFields(groupFieldsJson);
        arg.setLayoutList(fieldsLayoutJson);
        HeaderObj headerObj = new HeaderObj(ei, -10000);
        try {
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> controllerGetDescribeResultResult = objectDescribeCrmService.addDescribeCustomField(headerObj, arg);
            if (controllerGetDescribeResultResult == null || !controllerGetDescribeResultResult.isSuccess()) {
                log.warn("objectDescribeCrmService.addDescribeCustomField ex, upstreamTenantId={}", ei, controllerGetDescribeResultResult);
                success = false;
            }
        } catch (Exception e) {
            log.warn("objectDescribeCrmService.addDescribeCustomField ex, upstreamTenantId={}", ei, e);
            success = false;
        }
        return success;
    }

    private String getOutDataOwnOrganizationFieldJson(boolean publicObject, Integer upstreamTenantId) {
        if (!publicObject) {
            return "{\"type\":\"out_department\",\"define_type\":\"package\",\"api_name\":\"out_data_own_organization\",\"label\":\"" + I18nUtil.get(I18nKeyEnum.EPI_MANAGER_PAASOBJECTDESCRIBEMANAGER_505) + "\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"is_single\":true,\"status\":\"new\"}";

        }
        return "{\"type\":\"out_department\",\"define_type\":\"package\",\"api_name\":\"out_data_own_organization\",\"label\":\"" + I18nUtil.get(upstreamTenantId, I18nKeyEnum.EPI_MANAGER_PAASOBJECTDESCRIBEMANAGER_505) + "\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"is_single\":true,\"status\":\"new\",\"belong_tenant_id\":\"-99999\"}";
    }

    private String getOutDataOwnDepartmentFieldJson(boolean publicObject, Integer upstreamTenantId) {
        if (!publicObject) {
            return "{\"type\":\"out_department\",\"define_type\":\"package\",\"api_name\":\"out_data_own_department\",\"label\":\"" + I18nUtil.get(I18nKeyEnum.EPI_MANAGER_PAASOBJECTDESCRIBEMANAGER_503) + "\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"is_single\":true,\"status\":\"new\"}";
        }
        return "{\"type\":\"out_department\",\"define_type\":\"package\",\"api_name\":\"out_data_own_department\",\"label\":\"" + I18nUtil.get(upstreamTenantId, I18nKeyEnum.EPI_MANAGER_PAASOBJECTDESCRIBEMANAGER_503) + "\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"is_single\":true,\"status\":\"new\",\"belong_tenant_id\":\"-99999\"}";
    }

    public boolean addDescribeFieldForCrmObject(Integer upstreamTenantId, String describeApiName, AddDescribeFieldData addDescribeFieldData, boolean hasRetry) {
        return RetryUtil.addRetryTask(() -> {
            Boolean success = addObjectField(upstreamTenantId, describeApiName, addDescribeFieldData);
            if (success == null) {
                return true;
            }
            if (!hasRetry) {
                return true;
            }
            return success;
        });
    }

    public boolean addObjectField(Integer upstreamTenantId, String describeApiName, AddDescribeFieldData addDescribeFieldData) {
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> controllerGetDescribeResultResult = null;
        boolean success = true;
        try {
            if (!Boolean.TRUE.equals(addDescribeFieldData.getHasUpdateField()) && hasExitsFieldForCrmObject(upstreamTenantId, describeApiName, addDescribeFieldData.getFieldApiName())) {
                return true;
            }
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(describeApiName);
            arg.setFieldDescribe(addDescribeFieldData.getFieldDescribeJson());
            arg.setGroupFields(addDescribeFieldData.getGroupFieldsJson());
            arg.setLayoutList(addDescribeFieldData.getFieldsLayoutJson());
            HeaderObj headerObj = new HeaderObj(upstreamTenantId, -10000);
            if (Boolean.TRUE.equals(addDescribeFieldData.getHasUpdateField())) {
                controllerGetDescribeResultResult = objectDescribeCrmService.updateCustomFieldDescribe(headerObj, arg);
            } else {
                controllerGetDescribeResultResult = objectDescribeCrmService.addDescribeCustomField(headerObj, arg);
            }
        } catch (Exception e) {
            log.warn("objectDescribeCrmService.addDescribeCustomField ex, upstreamTenantId={}, controllerGetDescribeResultResult={}", upstreamTenantId, controllerGetDescribeResultResult, e);
            success = false;
        }
        if (controllerGetDescribeResultResult == null || !controllerGetDescribeResultResult.isSuccess()) {
            success = false;
        }
        return success;
    }

    public boolean hasExitsFieldForCrmObject(Integer tenantId, String describeApiName, String fieldApiName) {
        ObjectDescribe describe = getObjectDescribe(tenantId, describeApiName);
        HashMap<String, FieldDescribe> fields = describe.getFields();
        if (fields != null && fields.containsKey(fieldApiName)) {
            return true;
        }
        return false;
    }

    public ObjectDescribe getObjectDescribe(String ea, String objectApiName) {
        int tenantId = eieaConverter.enterpriseAccountToId(ea);
        return getObjectDescribe(tenantId, objectApiName);
    }

    public ObjectDescribe getObjectDescribe(Integer tenantId, String objectApiName) {
        HeaderObj headerObj = new HeaderObj(tenantId, -10000);
        return objectDescribeService.getDescribe(headerObj, objectApiName).getData().getDescribe();
    }

    public Boolean addLayoutComponents(String upstreamEa, String operatorId, String objectApiName, String layoutApiName, List<AddLayoutData> addLayoutDatas, Boolean hasRetry) {
        return RetryUtil.addRetryTask(() -> {
            boolean success = doAddLayoutComponents(upstreamEa, operatorId, objectApiName, layoutApiName, addLayoutDatas);
            if (!hasRetry) {
                return true;
            }
            return success;
        });
    }

    public ILayout getLayoutByApiName(User user, String layoutApiName, String objectApiName) {
        return serviceFacade.findLayoutByApiName(user, layoutApiName, objectApiName);
    }

    /**
     * 查不到则创建布局
     *
     * @return 是否创建
     */
    private boolean createLayout(User user, String layoutApiName, String objectApiName, ILayout layout) {
        if (getLayoutByApiName(user, layoutApiName, objectApiName) != null) {
            log.warn("布局已经存在，无法新建, tenant:{}, objectApiName:{}, layoutApiName:{}", user.getTenantId(), objectApiName, layout.getName());
            return false;
        } else {
            serviceFacade.createLayout(user, layout);
            return true;
        }
    }

    /**
     * 创建集团管理员合作伙伴布局
     */
    public Boolean createPartnerObjRecordTypeAndLayoutForEnterpriseItControl(String upstreamEa) {
//        try {
//            int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
//            User user = User.systemUser(tenantId + "");
//            // 查合作伙伴默认布局
//            ILayout partnerObjDefaultLayout = serviceFacade.findLayoutByApiName(user, PARTNER_OBJ_DEFAULT_DETAIL_LAYOUT_API_NAME, PartnerFieldContants.API_NAME);
//            // 合作伙伴布局的name的显示名称改为 集团分子公司名称
//            if (partnerObjDefaultLayout == null) {
//                log.warn("createPartnerObjRecordTypeAndLayoutForEnterpriseItControl partnerObjDefaultLayout == null,ea={}", upstreamEa);
//                return false;
//            }
//            LayoutExt partnerObjDefaultLayoutExt = LayoutExt.of(partnerObjDefaultLayout);
//            Optional<FormComponentExt> formComponentOptional = partnerObjDefaultLayoutExt.getFormComponent();
//            if (!formComponentOptional.isPresent()) {
//                log.warn("createPartnerObjRecordTypeAndLayoutForEnterpriseItControl !formComponentOptional.isPresent()={},ea={}",!formComponentOptional.isPresent(), upstreamEa);
//                return false;
//            }
//            IFieldSection fieldSection = formComponentOptional.get().getBaseFieldSection().get();
//
//
//            ServiceContext serviceContext = createServiceContext(tenantId + "", operatorId);
//            StandardDesignerLayoutController.Arg arg = new StandardDesignerLayoutController.Arg();
//            arg.setIncludeLayout(true);
//            arg.setIncludeDescribe(false);
//            arg.setDescribeApiName(PartnerFieldContants.API_NAME);
//            arg.setLayoutApiName(PARTNER_OBJ_ENTERPRISE_IT_CONTROL_LAYOUT_API_NAME);
//            ILayout layout = objectDesignerService.findDescribeAndV3Layout(arg, serviceContext).getLayout().toLayout();// 使用该接口以兼容老布局，否则部分对象不返回LayoutStructure
//
//            WebDetailLayout webDetailLayout = WebDetailLayout.of(layout);
//            List<IComponent> oldComponents = LayoutExt.of(layout).getComponentsSilently();
//            Set<String> oldComponentsApiNameSet = oldComponents.stream().map(IComponent::getName).collect(Collectors.toSet());
//            List<IComponent> componentsAdded = Lists.newArrayList();
//            for (AddLayoutData addLayoutData : addLayoutDatas) {
//                List<IComponent> componentsToAdd = addLayoutData.getComponents().stream().map(CommonComponent::new).collect(Collectors.toList());
//                componentsToAdd = componentsToAdd.stream().filter(x -> !oldComponentsApiNameSet.contains(x.getName())).collect(Collectors.toList());
//                if (CollectionUtils.isEmpty(componentsToAdd)) {
//                    continue;
//                }
//                Map<String, Object> layoutStructure = layout.getLayoutStructure();
//                if (layoutStructure == null) {
//                    log.warn("addLayoutComponents fail, LayoutStructure is null. upstreamEa={}, objectApiName={}, layoutApiName={}, components={}", upstreamEa, objectApiName, layoutApiName,
//                        addLayoutData);
//                    return false;
//                }
//                List<Map> rows = (List<Map>) layoutStructure.get(LayoutStructure.LAYOUT);
//                List<String> firstColumn = ((List<List<String>>) rows.get(rows.size() - 1).get(LayoutStructure.COMPONENTS)).get(0);
//                int beforeIndex = firstColumn.indexOf(addLayoutData.getBeforeApiName());
//                if (beforeIndex > -1) {
//                    // 添加到指定组件后
//                    webDetailLayout.addComponentsToLeftLayout(componentsToAdd, beforeIndex + 1);
//                } else {
//                    webDetailLayout.addComponentsToLeftLayout(componentsToAdd, firstColumn.size());
//                }
//                componentsAdded.addAll(componentsToAdd);
//            }
//            if (!CollectionUtil.isEmpty(componentsAdded)) {
//                serviceFacade.updateLayout(serviceContext.getUser(), layout);
//            }
//        } catch (Exception e) {
//            log.warn("doAddLayoutComponents error.", e);
//            return false;
//        }
        return true;
    }

    public Boolean doAddLayoutComponents(String upstreamEa, String operatorId, String objectApiName, String layoutApiName, List<AddLayoutData> addLayoutDatas) {
        try {
            int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
            ServiceContext serviceContext = createServiceContext(tenantId + "", operatorId);
            StandardDesignerLayoutController.Arg arg = new StandardDesignerLayoutController.Arg();
            arg.setIncludeLayout(true);
            arg.setIncludeDescribe(false);
            arg.setDescribeApiName(objectApiName);
            arg.setLayoutApiName(layoutApiName);

            ILayout layout = null;
            try {
                RequestContextManager.setContext(serviceContext.getRequestContext());
                layout = objectDesignerService.findDescribeAndV3Layout(arg, serviceContext).getLayout().toLayout();// 使用该接口以兼容老布局，否则部分对象不返回LayoutStructure
            } finally {
                RequestContextManager.removeContext();
            }

            WebDetailLayout webDetailLayout = WebDetailLayout.of(layout);
            List<IComponent> oldComponents = LayoutExt.of(layout).getComponentsSilently();
            Set<String> oldComponentsApiNameSet = oldComponents.stream().map(IComponent::getName).collect(Collectors.toSet());
            List<IComponent> componentsAdded = Lists.newArrayList();
            for (AddLayoutData addLayoutData : addLayoutDatas) {
                List<IComponent> componentsToAdd = addLayoutData.getComponents().stream().map(CommonComponent::new).collect(Collectors.toList());
                componentsToAdd = componentsToAdd.stream().filter(x -> !oldComponentsApiNameSet.contains(x.getName())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(componentsToAdd)) {
                    continue;
                }
                Map<String, Object> layoutStructure = layout.getLayoutStructure();
                if (layoutStructure == null) {
                    log.warn("addLayoutComponents fail, LayoutStructure is null. upstreamEa={}, objectApiName={}, layoutApiName={}, components={}", upstreamEa, objectApiName, layoutApiName,
                            addLayoutData);
                    return false;
                }
                List<Map> rows = (List<Map>) layoutStructure.get(LayoutStructure.LAYOUT);
                List<String> firstColumn = ((List<List<String>>) rows.get(rows.size() - 1).get(LayoutStructure.COMPONENTS)).get(0);
                int beforeIndex = firstColumn.indexOf(addLayoutData.getBeforeApiName());
                if (beforeIndex > -1) {
                    // 添加到指定组件后
                    webDetailLayout.addComponentsToLeftLayout(componentsToAdd, beforeIndex + 1);
                } else {
                    webDetailLayout.addComponentsToLeftLayout(componentsToAdd, firstColumn.size());
                }
                componentsAdded.addAll(componentsToAdd);
            }
            if (!CollectionUtil.isEmpty(componentsAdded)) {
                serviceFacade.updateLayout(serviceContext.getUser(), layout);
            }
        } catch (Exception e) {
            log.warn("doAddLayoutComponents error.", e);
            return false;
        }
        return true;
    }

    private ServiceContext createServiceContext(String tenantId, String userId) {
        User user = User.builder().tenantId(tenantId).userId(userId).build();
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(user).build();
        return new ServiceContext(requestContext, "", "");
    }

    public boolean initEnterpriseRelationObjRelatedTeamPermission(Integer tenantId, boolean retry) {
        AtomicBoolean firstRes = new AtomicBoolean(false);
        try {
            RetryUtil.addRetryTask(() -> {
                boolean success = doInitEnterpriseRelationObjRelatedTeamPermission(tenantId);
                firstRes.set(success);
                if (!retry) {
                    return true;
                }
                return success;
            });
        } catch (Exception e) {
            log.warn("ex, ", e);
        }
        return firstRes.get();
    }

    public boolean doInitEnterpriseRelationObjRelatedTeamPermission(Integer tenantId) {
        if (!hasOpenEnterpriseRelationObj(tenantId)) {
            log.warn("hasOpenEnterpriseRelationObj=false, tenantId=" + tenantId);
            return false;
        }
        // 是否已经设置互联用户对象相关团队外部负责人权限
        boolean hasOwnerSetPublicEmployeePermission = getHasSetPublicEmployeeObjPermissionByRoleType(tenantId, 1);
        // 是否已经设置互联用户对象相关团队普通成员权限
        boolean hasNormalMemberSetPublicEmployeePermission = getHasSetPublicEmployeeObjPermissionByRoleType(tenantId, 4);
        return batchSaveRolePermissions(tenantId, hasOwnerSetPublicEmployeePermission, hasNormalMemberSetPublicEmployeePermission);
    }

    /**
     * 判断是否已经设置了互联用户相关团队权限
     *
     * @param roleType 角色类型，普通成员"4"  相关团队负责人 1
     * @return true->设置了， false -> 未设置
     */
    private boolean getHasSetPublicEmployeeObjPermissionByRoleType(Integer tenantId, Integer roleType) {
        HeaderObj headerObj = new HeaderObj(tenantId, -10000);
        FindRolesPermissionListArg findRolesPermissionListArg = new FindRolesPermissionListArg();
        findRolesPermissionListArg.setDescribe_api_name(EnterpriseRelationObjConstant.API_NAME);
        findRolesPermissionListArg.setRole_type(roleType + "");
        findRolesPermissionListArg.setInclude_roles(false);
        findRolesPermissionListArg.setOut_permission(false);
        com.fxiaoke.crmrestapi.common.result.Result<FindRolesPermissionListResult> rolesPermissionList = objectDescribeCrmService.findRolesPermissionList(headerObj, findRolesPermissionListArg);
        FindRolesPermissionListResult data = rolesPermissionList.getData();
        List<FindRolePermission> rolePermissionList = data.getRole_permission_list();
        for (FindRolePermission rolePermission : rolePermissionList) {
            String objectApiName = rolePermission.getObject_api_name();
            if (PublicEmployeeObjConstant.API_NAME.equals(objectApiName)) {
                if ("write".equals(rolePermission.getPermission_type())) { // 不是写权限，则需要设置
                    return true;
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    private boolean batchSaveRolePermissions(Integer tenantId, boolean hasOwnerPermission, boolean hasNormalMemberPermission) {
        HeaderObj headerObj = new HeaderObj(tenantId, -10000);
        if (hasOwnerPermission && hasNormalMemberPermission) {
            return true;
        }
        List<SaveRolePermission> rolePermissions = Lists.newArrayList();

        SaveRolePermission rolePermission = new SaveRolePermission();
        rolePermission.setObject_api_name(PublicEmployeeObjConstant.API_NAME);
        rolePermission.setField_api_name(PublicEmployeeObjConstant.OUTER_TENANT_ID);
        rolePermission.setType("write");

        rolePermissions.add(rolePermission);

        List<Permission> permissionList = Lists.newArrayList();

        if (!hasOwnerPermission) {
            Permission permission = new Permission();
            permission.setRole_type("1");
            permission.setRole_permission_list(rolePermissions);
            permissionList.add(permission);
        }

        if (!hasNormalMemberPermission) {
            Permission permission = new Permission();
            permission.setRole_type("4");
            permission.setRole_permission_list(rolePermissions);
            permissionList.add(permission);
        }

        BatchSaveRolePermissionsArg batchSaveRolePermissionsArg = new BatchSaveRolePermissionsArg();
        batchSaveRolePermissionsArg.setOut_permission(false);
        batchSaveRolePermissionsArg.setDescribe_api_name(EnterpriseRelationObjConstant.API_NAME);
        batchSaveRolePermissionsArg.setPermission_list(permissionList);

        com.fxiaoke.crmrestapi.common.result.Result<BatchSaveRolePermissionsResult> batchSaveRolePermissionsResultResult = objectDescribeCrmService.batchSaveRolePermissions(headerObj, batchSaveRolePermissionsArg);
        return batchSaveRolePermissionsResultResult.getData().isSuccess();
    }

    public boolean hasOpenEnterpriseRelationObj(Integer tenantId) {
        DescribeResult describeAndLayout = serviceFacade.findDescribeAndLayout(User.systemUser(tenantId + ""), EnterpriseRelationObjConstant.API_NAME, false, null);
        return describeAndLayout != null && describeAndLayout.getObjectDescribe() != null;
    }

    public Result<Void> doFlushPaasObjectDescribe(Integer ei, String describeApiName, Map<String, List<String>> updateFieldApiNameFieldDataKeys, Boolean forceFlush) {
        if (!EnterpriseRelationObjConstant.API_NAME.equals(describeApiName) && !PublicEmployeeObjConstant.API_NAME.equals(describeApiName)) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.MUST_BE_RELATION_ENTERPRISE_OR_USER.getDescription());
        }
        if (CollectionUtil.isEmpty(updateFieldApiNameFieldDataKeys)) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.MUST_PASS_NEW_FIELD.getDescription());
        }

        // 获取旧描述
        ObjectDescribe describe = getObjectDescribe(ei, describeApiName);
        HashMap<String, FieldDescribe> fields = describe.getFields();
        Map<String, Map<String, Object>> oldFields = new HashMap<>(fields);

        // 获取新描述
        Map<String, Map<String, Object>> newFields = getFieldDescribeFromLocalFile(describeApiName);

        // 对比字段
        for (Entry<String, List<String>> updateOrAddFieldApiNameFieldDataKeysEntry : updateFieldApiNameFieldDataKeys.entrySet()) {
            String fieldApiName = updateOrAddFieldApiNameFieldDataKeysEntry.getKey();
            List<String> fieldDataKeys = updateOrAddFieldApiNameFieldDataKeysEntry.getValue();

            Map<String, Object> newFieldData = newFields.get(fieldApiName);
            if (newFieldData == null) {
                return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(),
                        ResultCode.NEW_DESCRIPTION_NO_THIS_FIELD.getDescription() + ",fieldApiName=" + fieldApiName + ",apiName=" + describeApiName);
            }
            if (CollectionUtil.isEmpty(fieldDataKeys)) {
                return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.MUST_PASS_NEW_ARG.getDescription() + ":fieldDataKey");
            }
            Map<String, Object> oldFieldData = oldFields.get(fieldApiName);
            if (oldFieldData == null) {
                return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(),
                        ResultCode.OLD_DESCRIPTION_NO_THIS_FIELD.getDescription() + ",fieldApiName=" + fieldApiName + ",apiName=" + describeApiName);
            }
            StringBuilder diffLog = new StringBuilder();
            boolean hasUpdateField = true;
            // 对比要更新的字段key对应值是否均不一致 以此判断是否需要更新描述
            for (String fieldDataKey : fieldDataKeys) {// 字段对应的属性
                Object newFieldDataValue = newFieldData.get(fieldDataKey);
                Object oldFieldDataValue = oldFieldData.get(fieldDataKey);
                diffLog.append("{key=").append(fieldDataKey).append(",old=").append(oldFieldDataValue).append(",new=").append(newFieldDataValue).append("}").append(",");
                // 只能添加新属性，不能去掉老属性
                if ((newFieldDataValue == null && oldFieldDataValue == null) || (newFieldDataValue == null && oldFieldDataValue != null)) {
                    return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.NEW_AND_OLD_FIELD_BE_NULL.getDescription() + ",fieldApiName=" + fieldApiName + ",apiName=" + describeApiName);
                }
                // oldFieldDataValue 还是使用gson做序列化，所以在判断equals时候需要特殊处理一下
                if ((oldFieldDataValue == null || StringUtils.isBlank(oldFieldDataValue.toString())) && newFieldDataValue != null) {
                    // 加薪属性
                    break;
                } else if ((newFieldDataValue instanceof Integer) && (oldFieldDataValue instanceof Number)) {
                    if (newFieldDataValue.equals(((Number) oldFieldDataValue).intValue())) {// 当前只支持基本数据类型
                        hasUpdateField = false;// 只要一个属性相同 就不更新该字段
                        break;
                    }
                } else if ((newFieldDataValue instanceof Long) && (oldFieldDataValue instanceof Number)) {
                    if (newFieldDataValue.equals(((Number) oldFieldDataValue).longValue())) {// 当前只支持基本数据类型
                        hasUpdateField = false;// 只要一个属性相同 就不更新该字段
                        break;
                    }
                } else {
                    if (newFieldDataValue.equals(oldFieldDataValue)) {// 当前只支持基本数据类型
                        hasUpdateField = false;// 只要一个属性相同 就不更新该字段
                        break;
                    }
                }
            }
            if (hasUpdateField) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(describeApiName);
                arg.setFieldDescribe(JsonUtil.toJson(newFieldData));
                arg.setGroupFields("[]");
                arg.setLayoutList("[]");
                HeaderObj headerObj = new HeaderObj(ei, -10000);
                log.info("doFlushPaasObjectDescribe update field,ei={},diffLog={}, arg={}", ei, diffLog.toString(), JsonUtil.toJson(arg));
                if (BooleanUtils.isTrue(forceFlush)) {
                    com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> res = objectDescribeCrmService.updateCustomFieldDescribe(headerObj, arg);
                }
            }
        }
        return Result.newSuccess();
    }

    private static Map<String, Map<String, Object>> getFieldDescribeFromLocalFile(String describeApiName) {
        Map<String, Map<String, Object>> fieldData = apiNameFieldDataCache.get(describeApiName);
        if (fieldData != null) {
            return fieldData;
        }
        String fieldName = "object/" + describeApiName + "/describe/" + describeApiName + ".json";
        try {
            Map<String, Object> objectDescribe = JsonUtil
                    .fromJsonByJackson(StreamUtils.copyToString(Resources.getResourceAsStream(fieldName), Charset.defaultCharset()), new TypeReference<Map<String, Object>>() {
                    });
            fieldData = (Map<String, Map<String, Object>>) objectDescribe.get("fields");
            apiNameFieldDataCache.put(describeApiName, fieldData);
        } catch (Exception e) {
            log.warn("", e);
            throw new RelationException(ResultCode.PARAMS_ERROR.getErrorCode(), "read file error,fieldName=" + fieldName);
        }
        return fieldData;
    }

    public Result<Void> flushPaasObjectDescribe(FlushPaasDescribeArg arg) {
        for (Entry<String, Map<String, List<String>>> entry : arg.getFlushDescribeApiNameFieldApiNamesMap().entrySet()) {
            Result<Void> res = doFlushPaasObjectDescribe(arg.getEi(), entry.getKey(), entry.getValue(), arg.getForceFlush());
            if (!res.isSuccess()) {
                return res;
            }
        }
        return Result.newSuccess();
    }

    public void initCrmObjectDefineForEnterpriseItControl(String ea) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        this.createPartnerObjRecordTypeAndLayoutForEnterpriseItControl(ea);
        // 互联部门新增字段 互联用户新增字段
        this.addObjectFieldWithKey(ei, PaasObjectDescribeManager.INIT_ENTERPRISE_IT_CONTROL);
    }
}