package com.facishare.rest.account.service.flush.licenseupgrade;

import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.BaseRowModel;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.EnterpriseRelationObjConstant;
import com.facishare.enterprise.common.constant.EnterpriseRelationType;
import com.facishare.enterprise.common.constant.OrderTpyeEnum;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.util.*;
import com.facishare.enterprise.common.util.I18nUtil;
import com.facishare.er.api.model.enums.UpstreamOpenedEnterpriseStatusConstant;
import com.facishare.global.rest.arg.PageListArg;
import com.facishare.global.rest.dao.mongo.FxiaokeEnterpriseAssociationEntityDao;
import com.facishare.global.rest.service.GlobalService;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.Result.ParaInfoResult;
import com.facishare.paas.license.arg.BatchQueryModuleParaArg;
import com.facishare.paas.license.arg.CreateIndustryArg;
import com.facishare.paas.license.arg.ProductLicenseArg;
import com.facishare.paas.license.arg.QueryExpireTimeArg;
import com.facishare.paas.license.arg.QueryModuleParaArg;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.constant.LicenseConstant.LicenseType;
import com.facishare.paas.license.constant.LicenseConstant.ProductType;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.IndustryPojo;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.license.pojo.ModuleParaPojo;
import com.facishare.paas.license.pojo.ProductInfoPojo;
import com.facishare.paas.license.pojo.ProductLicensePojo;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.facishare.paas.license.pojo.QueryExpiredTimePojo;
import com.facishare.rest.account.dao.mongo.entity.UpstreamOpenedEnterpriseRecordEntity;
import com.facishare.rest.account.dao.mongo.UpstreamOpenedEnterpriseRecordEntityDao;
import com.facishare.rest.account.remote.PaasLicenseManager;
import com.facishare.rest.account.remote.data.DownstreamOpenLicenseData;
import com.facishare.rest.account.service.EnterpriseLicenseService;
import com.facishare.rest.account.service.flush.licenseupgrade.data.EnterpriseRunEnvData;
import com.facishare.rest.account.service.flush.licenseupgrade.data.HistorySalesOrderData;
import com.facishare.rest.account.service.flush.licenseupgrade.data.LicenseQuotaCompareResult;
import com.facishare.rest.account.service.flush.licenseupgrade.data.ProductObj;
import com.facishare.rest.account.service.flush.licenseupgrade.data.ProductObjConstant;
import com.facishare.rest.account.service.flush.licenseupgrade.data.SalesOrderData;
import com.facishare.rest.account.service.flush.licenseupgrade.data.SalesOrderObj;
import com.facishare.rest.account.service.flush.licenseupgrade.data.SalesOrderObjConstant;
import com.facishare.rest.account.service.flush.licenseupgrade.data.SalesOrderProductObj;
import com.facishare.rest.account.service.flush.licenseupgrade.data.SalesOrderProductObjConstant;
import com.facishare.rest.account.util.ExcelUtil;
import com.facishare.rest.account.webrest.data.EnterpriseLicenseCodeEnum;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseRunStatusArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseRunStatusResult;
import com.facishare.uc.api.model.fscore.EnterpriseEnvironment;
import com.facishare.uc.api.model.fscore.RunStatus;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateV3Result;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.reflect.TypeToken;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

/**
 * 企业“互联产品包”license数据升级服务
 *
 * <AUTHOR>
 * @since 2022/11/15
 */
@Slf4j
@Service
public class EnterpriseLicenseUpgradeService {
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private GlobalService globalService;
    @Autowired
    private UpstreamOpenedEnterpriseRecordEntityDao openedEnterpriseRecordEntityDao;
    @Autowired
    private PaasLicenseManager paasLicenseManager;
    @Autowired
    private EnterpriseLicenseService enterpriseLicenseService;
    @Autowired
    private FxiaokeEnterpriseAssociationEntityDao fxiaokeEnterpriseAssociationEntityDao;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private LicenseClient licenseClient;
    @Autowired
    private EIEAConverter eieaConverter;
    private static final Integer FS_EI = 1; // 测试环境：76157，生产环境：1
    public static final String ORDER_OWNER = I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_128);
    public static final String ORDER_STATUS = I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_129);
    public static final String ORDER_RECORD_TYPE = I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_130);
    // 上游企业开通的“1+N互联企业数”
    public static final String ONE_OR_N_RELATION_EA = "one_or_n_relation_ea";
    // 上游企业开通的“1&N互联企业数”
    public static final String ONE_AND_N_RELATION_EA = "one_and_n_relation_ea";
    // 白名单企业
    private static List<String> oldWhiteListEnterpriseAccounts = Lists.newArrayList();
    // 上游企业使用的配额数
    private static Map<Integer, Map<String, Integer>> usedQuotaMap = Maps.newHashMap();
    // 企业状态：ea-->false:未停用，true:已停用
    private static Map<String, Boolean> enterpriseStateMap = Maps.newHashMap();

    static {
        // 白名单1+N上游企业
        ConfigFactory.getInstance().getConfig("fs-er-key-gray", config -> {
            oldWhiteListEnterpriseAccounts = Splitter.on(",").splitToList(config.get("oldRelationEnterpriseForOnePlusN"));
        });
    }

    /**
     * 通过旧的销售订单数据生成新产品包销售订单数据（旧的产品包转换成新的产品包） 1、“互联应用基础包”产品包 2、“下游企业端口数[100个]”产品包 3、“代理通下游配额包[10个]”产品包 4、“微信用户数【50000】”产品包
     */
    public void upgradeOldSaleOrderData(String historySalesOrderFileName, List<String> upstreamEas) {
        // 1、从文件中读取历史订单数据
        Map<Integer, List<HistorySalesOrderData>> oldLicenseOrderData = readSalesOrderFromFile(historySalesOrderFileName, upstreamEas);
        if (oldLicenseOrderData == null || oldLicenseOrderData.isEmpty()) {
            log.warn("从文件:{},读取订单数据失败！", historySalesOrderFileName);
            return;
        }
        List<SalesOrderData> newSalesOrderData = Lists.newArrayList();
        // 2、解析与转换为新的产品订单数据
        oldLicenseOrderData.forEach((tenantId, oldSalesOrderDataList) -> {
            if (CollectionUtils.isEmpty(oldSalesOrderDataList)) {
                return;
            }

            try {
                // 是否开通"微信用户数[50000个]" 产品包
                ModuleParaPojo servicePlusLimit = queryLicenseModuleParaQuota(tenantId, EnterpriseLicenseCodeEnum.SERVICE_PLUS_LIMIT.getMainCode(),
                    EnterpriseLicenseCodeEnum.SERVICE_PLUS_LIMIT.getCode());
                Boolean hasServicePlusLimit = servicePlusLimit == null ? false : true;
                // 查询已开通新产品包配额数量
                HashSet<String> newParaKeys = Sets.newHashSet("partner_firm_limit", "partner_user_firm_limit", "terminal_user_firm_limit");
                Map<String, ModuleParaPojo> newProductQuota = queryEnterpriseLicenseModulePara(tenantId, "fconnect", "interconnect_app_basic_app", newParaKeys);
                // 循环处理企业历史订单数据
                oldSalesOrderDataList.forEach(oldSalesOrderData -> {
                    try {
                        List<SalesOrderData> salesOrderData = null;
                        // 处理“下游企业端口数[100个]”订单数据
                        if (oldSalesOrderData.getProductCode().equals(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getCode())) {
                            salesOrderData = processDownstreamFirmLimitLicenseData(oldSalesOrderData, newProductQuota);
                        } else if (oldSalesOrderData.getProductCode().equals(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_10_LIMIT.getCode())) {
                            // 处理“代理通下游配额包[10个]”订单数据
                            salesOrderData = processDownstreamFirm10LimitLicenseData(oldSalesOrderData, newProductQuota);
                        } else if (oldSalesOrderData.getProductCode().equals(EnterpriseLicenseCodeEnum.SERVICE_PLUS_LIMIT.getCode())) {
                            // 处理“微信用户数【50000】”订单数据
                            salesOrderData = processServicePlusLimitLicenseData(oldSalesOrderData, newProductQuota);
                        } else if (oldSalesOrderData.getProductCode().equals(EnterpriseLicenseCodeEnum.INTERCONNECT_APP_BASIC_APP.getCode())) {
                            // 处理“互联应用基础包”订单数据
                            salesOrderData = processInterConnectAppBasicAppData(oldSalesOrderData, hasServicePlusLimit, newProductQuota);
                        }
                        if (salesOrderData != null && salesOrderData.size() > 0) {
                            newSalesOrderData.addAll(salesOrderData);
                        }
                    } catch (Exception e) {
                        log.warn("upgradeOldSaleOrderData-fail:tenantId:{},tenantAccount:{},error:{}", oldSalesOrderData.getEnterpriseId(), oldSalesOrderData.getEnterpriseAccount(), e.getMessage());
                    }
                });
                Thread.sleep(300L);
            } catch (InterruptedException e) {
                log.warn("upgradeOldSaleOrderData-error:{}", e);
            }
        });
        // 3、生成补单数据导入文件
        if (CollectionUtils.isNotEmpty(newSalesOrderData)) {
            String fileName = String.format("import_sales_order_%s.xlsx", DateUtil.dateFormat(new Date(), "yyyyMMdd_ss"));
            writeDataToExcelFile(fileName, newSalesOrderData, SalesOrderData.class);
        }
    }

    /**
     * 为所无销售订单的企业升级新互联产品包订单数据
     *
     * @param upstreamEas 企业Ea列表
     */
    public void upgradeEnterpriseLicenseOrderForNotHasOldOrderEa(List<String> upstreamEas) {
        if (CollectionUtils.isEmpty(upstreamEas)) {
            log.warn("The upstreamEas is empty!");
            return;
        }
        List<SalesOrderData> newSalesOrderData = Lists.newArrayList();
        // 2、循环比对每个企业开通的产品包配额数
        upstreamEas.forEach(enterpriseAccount -> {
            try {
                if (checkEnterpriseIsStop(enterpriseAccount)) {
                    return;
                }
                Integer tenantId = eieaConverter.enterpriseAccountToId(enterpriseAccount);
                Integer usedEnterpriseRelationQuota = enterpriseLicenseService.countUpstreamRelationEnterpriseHasFsAccountUsers(tenantId);
                Integer usedChannelUserQuota = enterpriseLicenseService.countUpstreamChannelUsers(tenantId);
                Integer usedTerminalUserQuota = enterpriseLicenseService.countUpstreamTerminalUsers(tenantId);
                ProductVersionPojo crmVersionData = getCrmVersionData(tenantId);
                if (crmVersionData == null) {
                    return;
                }
                // 2.1、查询企业开通的所有老产品包配额数
                HashSet<String> oldParaKeys = Sets.newHashSet("downstream_firm_limit", "downstream_firm_10_limit", "service_plus_limit");
                Map<String, ModuleParaPojo> oldProductQuota = queryEnterpriseLicenseModulePara(tenantId, "fconnect", "interconnect_app_basic_app", oldParaKeys);
                // 2.2、查询企业开通的所有新产品包配额数
                HashSet<String> newParaKeys = Sets.newHashSet("partner_firm_limit", "partner_user_firm_limit", "terminal_user_firm_limit");
                Map<String, ModuleParaPojo> newProductQuota = queryEnterpriseLicenseModulePara(tenantId, "fconnect", "interconnect_app_basic_app", newParaKeys);
                // 2.3、检测新旧产品包配额是否一致
                // 1+N连接企业数
                SalesOrderData parentFirmLimitOrder = createSalesOrderData(tenantId, enterpriseAccount, oldProductQuota, newProductQuota, usedEnterpriseRelationQuota, "partner_firm_limit",
                    crmVersionData);
                // 渠道用户数
                SalesOrderData partnerUserFirmLimitOrder = createSalesOrderData(tenantId, enterpriseAccount, oldProductQuota, newProductQuota, usedChannelUserQuota, "partner_user_firm_limit",
                    crmVersionData);
                // 终端用户数
                SalesOrderData terminalUserFirmLimitOrder = createSalesOrderData(tenantId, enterpriseAccount, oldProductQuota, newProductQuota, usedTerminalUserQuota, "terminal_user_firm_limit",
                    crmVersionData);
                if (parentFirmLimitOrder != null) {
                    newSalesOrderData.add(parentFirmLimitOrder);
                }
                if (partnerUserFirmLimitOrder != null) {
                    newSalesOrderData.add(partnerUserFirmLimitOrder);
                }
                if (terminalUserFirmLimitOrder != null) {
                    newSalesOrderData.add(terminalUserFirmLimitOrder);
                }
            } catch (Exception e) {
                log.warn("upgradeEnterpriseLicenseOrderForNotHasOldOrder-error:ea-->{}:{}", enterpriseAccount, e);
            }
        });
        // 3、生成补单数据导入文件
        if (CollectionUtils.isNotEmpty(newSalesOrderData)) {
            String fileName = String.format("import_sales_order_%s.xlsx", DateUtil.dateFormat(new Date(), "yyyyMMdd_ss"));
            writeDataToExcelFile(fileName, newSalesOrderData, SalesOrderData.class);
        }
    }

    /**
     * 根据旧产品包配额生成新产品包配额订单
     *
     * @param tenantId 企业ID
     * @param enterpriseAccount 企业账号
     * @param oldProductQuota 旧产品包配额
     * @param newProductQuota 新产品包配额
     * @param usedQuota 已使用产品包配额数
     * @param produceCode 新产品包名称
     * @param crmVersion crm版本
     * @return 新产品包补单订单
     */
    private SalesOrderData createSalesOrderData(Integer tenantId, String enterpriseAccount, Map<String, ModuleParaPojo> oldProductQuota, Map<String, ModuleParaPojo> newProductQuota, Integer usedQuota,
        String produceCode, ProductVersionPojo crmVersion) {
        String productName = "";
        Double quantity = 0.0;
        Double resourceNum = 0.0;

        // “下游企业端口数[100个]” 产品配额（老）
        ModuleParaPojo downstreamFirmLimit = oldProductQuota.get(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getCode());
        // "代理通下游配额包[10个]" 产品配额（老）
        ModuleParaPojo downstreamFirm10Limit = oldProductQuota.get(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_10_LIMIT.getCode());
        // "微信用户数[50000个]" 产品配额（老）
        ModuleParaPojo servicePlugLimit = oldProductQuota.get(EnterpriseLicenseCodeEnum.SERVICE_PLUS_LIMIT.getCode());

        switch (produceCode) {
            case "partner_firm_limit": // 1+N连接企业数
                productName = EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT.getName();
                // “1+N连接企业数” 产品配额（新）
                ModuleParaPojo partnerFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT.getCode());
                Boolean hasPartnerFirmLimit = partnerFirmLimit != null && Integer.parseInt(partnerFirmLimit.getParaValue()) > 0;
                if (!hasPartnerFirmLimit && usedQuota > 0 && (downstreamFirmLimit != null && Integer.parseInt(downstreamFirmLimit.getParaValue()) > 0)) {
                    resourceNum += Double.parseDouble(downstreamFirmLimit.getParaValue());
                }
                if (!hasPartnerFirmLimit && usedQuota > 0 && (downstreamFirm10Limit != null && Integer.parseInt(downstreamFirm10Limit.getParaValue()) > 0)) {
                    resourceNum += Double.parseDouble(downstreamFirm10Limit.getParaValue());
                }
                quantity = resourceNum / EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT.getNum();
                break;
            case "partner_user_firm_limit": // 渠道用户数
                productName = EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT.getName();
                // "渠道用户" 产品配额（新）
                ModuleParaPojo partnerUserFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT.getCode());
                Boolean hasPartnerUserFirmLimit = partnerUserFirmLimit != null && Integer.parseInt(partnerUserFirmLimit.getParaValue()) > 0;
                if (!hasPartnerUserFirmLimit && usedQuota > 0 && (downstreamFirmLimit != null && Integer.parseInt(downstreamFirmLimit.getParaValue()) > 0)) {
                    resourceNum += Double.parseDouble(downstreamFirmLimit.getParaValue()) * 5;
                }
                if (!hasPartnerUserFirmLimit && usedQuota > 0 && (downstreamFirm10Limit != null && Integer.parseInt(downstreamFirm10Limit.getParaValue()) > 0)) {
                    resourceNum += Double.parseDouble(downstreamFirm10Limit.getParaValue()) * 5;
                }
                quantity = resourceNum / EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT.getNum();
                break;
            case "terminal_user_firm_limit": // 终端用户数
                productName = EnterpriseLicenseCodeEnum.TERMINAL_USER_FIRM_LIMIT.getName();
                // “终端用户[10000个]” 产品配额（新）
                ModuleParaPojo terminalUserFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.TERMINAL_USER_FIRM_LIMIT.getCode());
                Boolean hasTerminalUserFirmLimit = terminalUserFirmLimit != null && Integer.parseInt(terminalUserFirmLimit.getParaValue()) > 0;
                if (!hasTerminalUserFirmLimit && usedQuota > 0 && (servicePlugLimit != null && Integer.parseInt(servicePlugLimit.getParaValue()) > 0)) {
                    resourceNum = Double.parseDouble(servicePlugLimit.getParaValue());
                    quantity = resourceNum / EnterpriseLicenseCodeEnum.TERMINAL_USER_FIRM_LIMIT.getNum();
                }
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(productName) && resourceNum > 0) {
            SalesOrderData orderData = new SalesOrderData();
            orderData.setEnterpriseId(tenantId);
            orderData.setEnterpriseAccount(enterpriseAccount);
            orderData.setProductId(productName);
            orderData.setQuantity(quantity);
            orderData.setResourceNum(resourceNum);
            orderData.setBeginDate(new Date(crmVersion.getStartTime()));
            orderData.setEndDate(new Date(crmVersion.getExpiredTime()));
            orderData.setOwner(ORDER_OWNER);
            orderData.setRecordType(ORDER_RECORD_TYPE);
            orderData.setStatus(ORDER_STATUS);
            return orderData;
        }
        return null;
    }

    /**
     * 核对所有上游企业开通的互联产品包License配额数据
     */
    public void checkUpstreamEnterpriseOpenedLicenseQuotaData(List<String> upstreamEas) {
        // 1、读取所有上游企业
        List<String> allUpstreamEa = upstreamEas;
        if (CollectionUtils.isEmpty(allUpstreamEa)) {
            allUpstreamEa = getAllUpstreamEaList();
        }
        // 2、循环比对每个企业开通的产品包配额数
        if (CollectionUtils.isNotEmpty(allUpstreamEa)) {
            List<LicenseQuotaCompareResult> compareResult = Lists.newArrayList();
            allUpstreamEa.forEach(enterpriseAccount -> {
                try {
                    if (checkEnterpriseIsStop(enterpriseAccount)) {
                        return;
                    }
                    Integer tenantId = eieaConverter.enterpriseAccountToId(enterpriseAccount);
                    Integer usedEnterpriseRelationQuota = enterpriseLicenseService.countUpstreamRelationEnterpriseHasFsAccountUsers(tenantId);
                    Integer usedChannelUserQuota = enterpriseLicenseService.countUpstreamChannelUsers(tenantId);
                    Integer usedTerminalUserQuota = enterpriseLicenseService.countUpstreamTerminalUsers(tenantId);
                    // 2.1、查询企业开通的所有老产品包配额数
                    HashSet<String> oldParaKeys = Sets.newHashSet("downstream_firm_limit", "downstream_firm_10_limit", "service_plus_limit");
                    Map<String, ModuleParaPojo> oldProductQuota = queryEnterpriseLicenseModulePara(tenantId, "fconnect", "interconnect_app_basic_app", oldParaKeys);
                    // 2.2、查询企业开通的所有新产品包配额数
                    HashSet<String> newParaKeys = Sets.newHashSet("partner_firm_limit", "partner_user_firm_limit", "terminal_user_firm_limit");
                    Map<String, ModuleParaPojo> newProductQuota = queryEnterpriseLicenseModulePara(tenantId, "fconnect", "interconnect_app_basic_app", newParaKeys);
                    // 2.3、比对各个产品包配额数据（互联企业数、渠道用户数、终端用户数）
                    LicenseQuotaCompareResult partnerFirmLimitQuotaCompareResult = comparePartnerFirmLimitQuota(tenantId, enterpriseAccount, oldProductQuota, newProductQuota,
                        usedEnterpriseRelationQuota);
                    LicenseQuotaCompareResult partnerUserFirmLimitQuotaCompareResult = comparePartnerUserFirmLimitQuota(tenantId, enterpriseAccount, oldProductQuota, newProductQuota,
                        usedChannelUserQuota);
                    LicenseQuotaCompareResult terminalUserFirmLimitQuotaCompareResult = compareTerminalUserFirmLimitQuota(tenantId, enterpriseAccount, oldProductQuota, newProductQuota,
                        usedTerminalUserQuota);
                    compareResult.add(partnerFirmLimitQuotaCompareResult);
                    compareResult.add(partnerUserFirmLimitQuotaCompareResult);
                    compareResult.add(terminalUserFirmLimitQuotaCompareResult);
                } catch (Exception e) {
                    log.warn("checkUpstreamEnterpriseOpenedLicenseQuotaData-error:{}", e);
                }
            });
            // 3、记录核对结果
            if (CollectionUtils.isNotEmpty(compareResult)) {
                String fileName = String.format("license_compare_result_%s.xlsx", DateUtil.formatYYYYMMDD(new Date()));
                writeDataToExcelFile(fileName, compareResult, LicenseQuotaCompareResult.class);
            }
        }
    }

    /**
     * “终端用户数”产品包配额核对
     *
     * @param tenantId 企业ID
     * @param enterpriseAccount 企业账号
     * @param oldProductQuota 老产品配额
     * @param newProductQuota 新产品配额
     * @return 比对结果
     */
    private LicenseQuotaCompareResult compareTerminalUserFirmLimitQuota(Integer tenantId, String enterpriseAccount, Map<String, ModuleParaPojo> oldProductQuota,
        Map<String, ModuleParaPojo> newProductQuota, Integer usedQuota) {
        // "微信用户数[50000个]" 产品配额（老）
        ModuleParaPojo servicePlugLimit = oldProductQuota.get(EnterpriseLicenseCodeEnum.SERVICE_PLUS_LIMIT.getCode());
        // “终端用户[10000个]” 产品配额（新）
        ModuleParaPojo terminalUserFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.TERMINAL_USER_FIRM_LIMIT.getCode());

        Integer oldProductQuotaNum = servicePlugLimit == null ? 0 : Integer.parseInt(servicePlugLimit.getParaValue());
        Integer newProductQuotaNum = terminalUserFirmLimit == null ? 0 : Integer.parseInt(terminalUserFirmLimit.getParaValue());
        LicenseQuotaCompareResult licenseQuotaCompareResult = new LicenseQuotaCompareResult();
        licenseQuotaCompareResult.setEnterpriseId(tenantId);
        licenseQuotaCompareResult.setEnterpriseAccount(enterpriseAccount);
        licenseQuotaCompareResult.setOldProductCode(EnterpriseLicenseCodeEnum.SERVICE_PLUS_LIMIT.getCode());
        licenseQuotaCompareResult.setOldProductName(EnterpriseLicenseCodeEnum.SERVICE_PLUS_LIMIT.getName());
        licenseQuotaCompareResult.setOldMaxQuota(oldProductQuotaNum);
        licenseQuotaCompareResult.setRate(1);
        licenseQuotaCompareResult.setNewProductCode(EnterpriseLicenseCodeEnum.TERMINAL_USER_FIRM_LIMIT.getCode());
        licenseQuotaCompareResult.setNewProductName(EnterpriseLicenseCodeEnum.TERMINAL_USER_FIRM_LIMIT.getName());
        licenseQuotaCompareResult.setNewMaxQuota(newProductQuotaNum);
        licenseQuotaCompareResult.setDiffQuotaNum(oldProductQuotaNum - newProductQuotaNum);
        licenseQuotaCompareResult.setUsedQuotaNum(usedQuota);
        return licenseQuotaCompareResult;
    }

    /**
     * “渠道用户数”产品包配额核对
     *
     * @param tenantId 企业ID
     * @param enterpriseAccount 企业账号
     * @param oldProductQuota 老产品配额
     * @param newProductQuota 新产品配额
     * @return 配额比对结果
     */
    private LicenseQuotaCompareResult comparePartnerUserFirmLimitQuota(Integer tenantId, String enterpriseAccount, Map<String, ModuleParaPojo> oldProductQuota,
        Map<String, ModuleParaPojo> newProductQuota, Integer usedQuota) {
        // “下游企业端口数[100个]” 产品配额（老）
        ModuleParaPojo downstreamFirmLimit = oldProductQuota.get(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getCode());
        // "代理通下游配额包[10个]" 产品配额（老）
        ModuleParaPojo downstreamFirm10Limit = oldProductQuota.get(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_10_LIMIT.getCode());
        // "渠道用户" 产品配额（新）
        ModuleParaPojo partnerUserFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT.getCode());

        // 老配额 = “下游企业端口数[100个]” 产品配额*5 + "代理通下游配额包[10个]" 产品配额*5
        Integer oldProductQuotaNum = 0;
        Integer newProductQuotaNum = 0;
        StringBuilder oldProductName = new StringBuilder();
        StringBuilder oldProductCode = new StringBuilder();
        StringBuilder newProductName = new StringBuilder();
        if (downstreamFirmLimit != null) {
            oldProductQuotaNum += Integer.parseInt(downstreamFirmLimit.getParaValue());
            oldProductName.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getName() + ",");
            oldProductCode.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getCode() + ",");
        }
        if (downstreamFirm10Limit != null) {
            oldProductQuotaNum += Integer.parseInt(downstreamFirm10Limit.getParaValue());
            oldProductName.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_10_LIMIT.getName());
            oldProductCode.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_10_LIMIT.getCode());
        }
        if (oldProductName.length() == 0) {
            oldProductName.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getName() + ",");
            oldProductCode.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getCode() + ",");
        }
        if (partnerUserFirmLimit != null) {
            newProductQuotaNum += Integer.parseInt(partnerUserFirmLimit.getParaValue());
        }
        newProductName.append(EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT.getName());

        LicenseQuotaCompareResult licenseQuotaCompareResult = new LicenseQuotaCompareResult();
        licenseQuotaCompareResult.setEnterpriseId(tenantId);
        licenseQuotaCompareResult.setEnterpriseAccount(enterpriseAccount);
        licenseQuotaCompareResult.setOldProductCode(oldProductCode.toString());
        licenseQuotaCompareResult.setOldProductName(oldProductName.toString());
        licenseQuotaCompareResult.setOldMaxQuota(oldProductQuotaNum);
        licenseQuotaCompareResult.setRate(5);
        licenseQuotaCompareResult.setNewProductCode(EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT.getCode());
        licenseQuotaCompareResult.setNewProductName(newProductName.toString());
        licenseQuotaCompareResult.setNewMaxQuota(newProductQuotaNum);
        licenseQuotaCompareResult.setDiffQuotaNum(oldProductQuotaNum - newProductQuotaNum);
        licenseQuotaCompareResult.setUsedQuotaNum(usedQuota);
        return licenseQuotaCompareResult;
    }

    /**
     * “互联企业数”产品包配额核对
     *
     * @param tenantId 企业ID
     * @param enterpriseAccount 企业账号
     * @param oldProductQuota 老产品配额
     * @param newProductQuota 新产品配额
     * @return 配额比对结果
     */
    private LicenseQuotaCompareResult comparePartnerFirmLimitQuota(Integer tenantId, String enterpriseAccount, Map<String, ModuleParaPojo> oldProductQuota, Map<String, ModuleParaPojo> newProductQuota,
        Integer usedQuota) {
        // “下游企业端口数[100个]” 产品配额（老）
        ModuleParaPojo downstreamFirmLimit = oldProductQuota.get(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getCode());
        // "代理通下游配额包[10个]" 产品配额（老）
        ModuleParaPojo downstreamFirm10Limit = oldProductQuota.get(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_10_LIMIT.getCode());
        // “1+N连接企业数” 产品配额（新）
        ModuleParaPojo partnerFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT.getCode());

        // 老配额 = “下游企业端口数[100个]” 产品配额 + "代理通下游配额包[10个]" 产品配额
        Integer oldProductQuotaNum = 0;
        Integer newProductQuotaNum = 0;
        StringBuilder oldProductName = new StringBuilder();
        StringBuilder oldProductCode = new StringBuilder();
        StringBuilder newProductName = new StringBuilder();
        if (downstreamFirmLimit != null) {
            oldProductQuotaNum += Integer.parseInt(downstreamFirmLimit.getParaValue());
            oldProductName.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getName() + ",");
            oldProductCode.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getCode() + ",");
        }
        if (downstreamFirm10Limit != null) {
            oldProductQuotaNum += Integer.parseInt(downstreamFirm10Limit.getParaValue());
            oldProductName.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_10_LIMIT.getName());
            oldProductCode.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_10_LIMIT.getCode());
        }
        if (oldProductName.length() == 0) {
            oldProductName.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getName() + ",");
            oldProductCode.append(EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getCode() + ",");
        }
        if (partnerFirmLimit != null) {
            newProductQuotaNum += Integer.parseInt(partnerFirmLimit.getParaValue());
        }
        newProductName.append(EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT.getName());
        LicenseQuotaCompareResult licenseQuotaCompareResult = new LicenseQuotaCompareResult();
        licenseQuotaCompareResult.setEnterpriseId(tenantId);
        licenseQuotaCompareResult.setEnterpriseAccount(enterpriseAccount);
        licenseQuotaCompareResult.setOldProductCode(oldProductCode.toString());
        licenseQuotaCompareResult.setOldProductName(oldProductName.toString());
        licenseQuotaCompareResult.setOldMaxQuota(oldProductQuotaNum);
        licenseQuotaCompareResult.setRate(1);
        licenseQuotaCompareResult.setNewProductCode(EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT.getCode());
        licenseQuotaCompareResult.setNewProductName(newProductName.toString());
        licenseQuotaCompareResult.setNewMaxQuota(newProductQuotaNum);
        licenseQuotaCompareResult.setDiffQuotaNum(oldProductQuotaNum - newProductQuotaNum);
        licenseQuotaCompareResult.setUsedQuotaNum(usedQuota);
        return licenseQuotaCompareResult;
    }

    /**
     * 查询企业老产品包license包配额
     *
     * @param tenantId 企业ID
     */
    public Map<String, ModuleParaPojo> queryEnterpriseLicenseModulePara(Integer tenantId, String appId, String moduleCode, Set<String> paraKeys) {
        BatchQueryModuleParaArg queryArg = new BatchQueryModuleParaArg();
        LicenseContext context = new LicenseContext();
        context.setTenantId(tenantId + "");
        context.setUserId(CrmConstants.SYSTEM_USER + "");
        context.setAppId(appId);
        queryArg.setContext(context);
        Map<String, Set<String>> moduleCodeParaKeyMap = Maps.newHashMap();
        moduleCodeParaKeyMap.put(moduleCode, paraKeys);
        queryArg.setModuleCodeParaKeyMap(moduleCodeParaKeyMap);
        com.facishare.paas.license.common.Result result = licenseClient.batchQueryModulePara(queryArg);
        Map<String, ModuleParaPojo> data = Maps.newHashMap();
        if (result.getErrCode() == 0 && result.getResult() != null) {
            Map<String, List<ModuleParaPojo>> licenseModuleMap = (Map<String, List<ModuleParaPojo>>) result.getResult();
            log.info("batchQueryModulePara:{}", licenseModuleMap);
            data = licenseModuleMap.get(moduleCode).stream().collect(Collectors.toMap(ModuleParaPojo::getParaKey, i -> i, (p1, p2) -> p2));
            return data;
        }
        return data;
    }

    /**
     * 查询开通的产品配额
     *
     * @param tenantId 企业ID
     * @param moduleCode 模块code
     * @param paraKey 产品code
     * @return com.facishare.paas.license.pojo.ModuleParaPojo
     * @since 22:09 2022/11/19
     **/
    public ModuleParaPojo queryLicenseModuleParaQuota(Integer tenantId, String moduleCode, String paraKey) {
        LicenseContext context = new LicenseContext();
        context.setTenantId(tenantId + "");
        context.setUserId(CrmConstants.SYSTEM_USER + "");
        context.setAppId("CRM");
        QueryModuleParaArg moduleParaArg = new QueryModuleParaArg();
        moduleParaArg.setContext(context);
        moduleParaArg.setModuleCode(moduleCode);
        moduleParaArg.setParaKeys(Sets.newHashSet(paraKey));
        ParaInfoResult result = licenseClient.queryModulePara(moduleParaArg);
        if (result != null && result.getResult() != null && !result.getResult().isEmpty()) {
            List<ModuleParaPojo> moduleParaPojoList = result.getResult();
            for (ModuleParaPojo module : moduleParaPojoList) {
                if (module.getParaKey().equals(paraKey)) {
                    return module;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有上游企业
     *
     * @return java.util.List<java.lang.Integer>
     * @since 12:09 2022/11/21
     **/
    public List<String> getAllUpstreamEaList() {
        List<String> dataList = Lists.newArrayList();
        OffsetUtil.offsetFunction(1000, (offset, limit) -> {
            PageListArg arg = new PageListArg();
            arg.setLimit(limit);
            arg.setOffset(offset);
            com.facishare.global.rest.common.HeaderObj headerObj = com.facishare.global.rest.common.HeaderObj.newInstance();
            com.facishare.global.rest.result.Result<List<String>> listResult = globalService.listAllUpstreamEas(headerObj, arg);
            if (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                dataList.addAll(listResult.getData());
                return listResult.getData().size();
            }
            return 0;
        });

        return dataList;
    }

    /**
     * 从文件中读取客户历史互联产品包订单数据
     */
    public Map<Integer, List<HistorySalesOrderData>> readSalesOrderFromFile(String fileName, List<String> upstreamEas) {
        try {
            InputStream inputStream = new FileInputStream(fileName);
            Map<Integer, List<HistorySalesOrderData>> excelData = Maps.newHashMap();
            ExcelUtil.readModel(inputStream, HistorySalesOrderData.class, new AnalysisEventListener() {
                Map<Integer, HistorySalesOrderData> interconnectAppBasicApp = Maps.newHashMap();
                Date currentDate = DateUtil.now();

                @Override
                public void invoke(Object object, AnalysisContext context) {
                    try {
                        HistorySalesOrderData rowData = (HistorySalesOrderData) object;
                        // 过滤无效订单数据
                        if ("--".equals(rowData.getEndDate()) || "--".equals(rowData.getEnterpriseAccount()) || "--".equals(rowData.getEndDate())) {
                            return;
                        }
                        // 企业是否在指定名单中
                        if (CollectionUtils.isNotEmpty(upstreamEas) && !upstreamEas.contains(rowData.getEnterpriseAccount())) {
                            return;
                        }
                        // 过滤已停用的租户
                        if (checkEnterpriseIsStop(rowData.getEnterpriseAccount())) {
                            return;
                        }
                        Integer tenantId = rowData.getEnterpriseId();
                        if (tenantId == null || tenantId == 0) {
                            try {
                                tenantId = eieaConverter.enterpriseAccountToId(rowData.getEnterpriseAccount());
                            } catch (Exception e) {
                                log.warn("ea:{},error:{}", rowData.getEnterpriseAccount(), e);
                                return;
                            }
                        }
                        rowData.setEnterpriseId(tenantId);
                        if (!excelData.containsKey(tenantId)) {
                            excelData.put(tenantId, Lists.newArrayList());
                        }
                        Date orderEndDate = rowData.getEndDate();
                        // 过滤已过期的订单数据
                        if (orderEndDate != null && orderEndDate.after(currentDate)) {
                            String productCode = rowData.getProductCode();
                            // 过滤重复的“互联应用基础包”订单数据，保留最近的那笔订单
                            if ("interconnect_app_basic_app".equals(productCode)) {
                                if (!interconnectAppBasicApp.containsKey(tenantId) || orderEndDate.after(interconnectAppBasicApp.get(tenantId).getEndDate())) {
                                    interconnectAppBasicApp.put(tenantId, rowData);
                                }
                            } else if (rowData.getResourceTotal() > 0) {
                                excelData.get(tenantId).add(rowData);
                            }
                        }
                    } catch (Exception e) {
                        log.warn("process excel row data error:{}", e);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    if (!interconnectAppBasicApp.isEmpty()) {
                        interconnectAppBasicApp.forEach((tenantId, salesOrderData) -> {
                            if (excelData.containsKey(tenantId)) {
                                excelData.get(tenantId).add(salesOrderData);
                            } else {
                                excelData.put(tenantId, Lists.newArrayList(salesOrderData));
                            }
                        });
                    }
                }
            });
            inputStream.close();
            return excelData;
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 比对互联配额有异常的企业
     *
     * @param fileName 历史产品销售订单
     * @param filterEaList 配额有异常的企业名单
     * @param resultSaveFileDir 比对结果
     */
    public void compareHistoryOrderDataFromExcelFile(String fileName, List<String> filterEaList, String resultSaveFileDir) {
        try {
            Map<String, String> result = Maps.newHashMap();
            InputStream inputStream = new FileInputStream(fileName);
            List<HistorySalesOrderData> diffHistoryOrderData = Lists.newArrayList();
            ExcelUtil.readModel(inputStream, HistorySalesOrderData.class, new AnalysisEventListener() {
                Date currentDate = DateUtil.now();

                @Override
                public void invoke(Object object, AnalysisContext context) {
                    HistorySalesOrderData rowData = (HistorySalesOrderData) object;
                    // 过滤无效订单数据
                    if ("--".equals(rowData.getEndDate()) || "--".equals(rowData.getEnterpriseAccount()) || "--".equals(rowData.getEndDate())) {
                        return;
                    }
                    Date orderEndDate = rowData.getEndDate();
                    String orderStatus = I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_732);
                    if (orderEndDate != null) {
                        if (orderEndDate.after(currentDate)) {
                            orderStatus = I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_735);
                        } else {
                            orderStatus = I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_737);
                        }
                    }
                    String ea = rowData.getEnterpriseAccount();
                    if (filterEaList.contains(ea)) {
                        result.put(ea, I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_742) + orderStatus);
                        if (orderEndDate != null && orderEndDate.after(currentDate)) {
                            diffHistoryOrderData.add(rowData);
                        }
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {

                }
            });
            inputStream.close();
            if (CollectionUtils.isNotEmpty(diffHistoryOrderData)) {
                String fixOrderFileName = resultSaveFileDir + "/" + String.format("his_order_%s.xlsx", DateUtil.dateFormat(new Date(), "yyyy-MM-dd_ss"));
                log.info("diffHistoryOrderData:{}", fixOrderFileName);
                writeDataToExcelFile(fixOrderFileName, diffHistoryOrderData, HistorySalesOrderData.class);
            }
            if (CollectionUtils.isNotEmpty(filterEaList) && !result.isEmpty()) {
                Set<String> hasOrderEas = result.keySet();
                List<String> notHasOrderEas = filterEaList.stream().filter(ea -> !hasOrderEas.contains(ea)).collect(Collectors.toList());
                notHasOrderEas.forEach(ea -> {
                    log.warn("{}:无历史订单", ea);
                });
            }
            if (!result.isEmpty()) {
                result.forEach((key, val) -> {
                    log.warn("{}:{}", key, val);
                });
            }
        } catch (Exception e) {
            log.error("{}", e);
        }
    }

    /**
     * 检测某个企业CRM账号是否停用
     *
     * @param ea 企业账号
     * @return true:停用，false:正常
     */
    public Boolean checkEnterpriseIsStop(String ea) {
        try {
            if (enterpriseStateMap.containsKey(ea)) {
                return enterpriseStateMap.get(ea);
            } else {
                GetEnterpriseRunStatusArg statusArg = new GetEnterpriseRunStatusArg();
                statusArg.setEnterpriseAccount(ea);
                GetEnterpriseRunStatusResult enterpriseRunStatus = enterpriseEditionService.getEnterpriseRunStatus(statusArg);
                boolean isStop = enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_NORMAL ? false : true;
                enterpriseStateMap.put(ea, isStop);
                log.info("checkEnterpriseIsStop-->ea:{},isStop:{}", ea, isStop);
                return isStop;
            }
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 把新的订单数据写入xlx文件
     *
     * @param fileName 文件名称
     * @param salesOrderDataList 销售订单数据
     */
    public void writeDataToExcelFile(String fileName, List<? extends BaseRowModel> salesOrderDataList, Class<? extends BaseRowModel> classZ) {
        if (CollectionUtils.isNotEmpty(salesOrderDataList)) {
            byte[] excelData = ExcelUtil.createOneSheetExcelByModel(salesOrderDataList, classZ);
            try {
                String dirPath = ResourceUtils.getURL("classpath:").getPath();
                String fileFullPath = dirPath + fileName;
                log.info("targetFilePath:{}", fileFullPath);
                OutputStream fop = new FileOutputStream(fileFullPath);
                fop.write(excelData);
                fop.flush();
                fop.close();
            } catch (FileNotFoundException e) {
                log.warn("writeSalesOrderDataToFile:{}", e);
                throw new RuntimeException(e);
            } catch (IOException e) {
                log.warn("writeSalesOrderDataToFile:{}", e);
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 处理历史“互联应用基础包”数据 1. 1套“互联应用基础包”产品包拆分为100套“1+N连接企业”产品包 2. 1套“互联应用基础包”产品包拆分为500套“渠道用户”产品包 3. 1套“互联应用基础包”产品包拆分为1套“终端用户[10000个]”产品包
     *
     * @param oldSalesOrderData 订单数据
     * @return 新的产品包补单数据
     */
    public List<SalesOrderData> processInterConnectAppBasicAppData(HistorySalesOrderData oldSalesOrderData, Boolean hasServicePlusLimit, Map<String, ModuleParaPojo> newProductQuota) {
        Integer enterpriseId = oldSalesOrderData.getEnterpriseId();
        if (!usedQuotaMap.containsKey(enterpriseId)) {
            countEnterpriseUsedQuotaNum(enterpriseId);
        }
        List<SalesOrderData> newOrderList = Lists.newArrayList();
        Map<String, Integer> usedErQuotaMap = usedQuotaMap.get(enterpriseId);
        // “1+N连接企业数” 产品配额（新）
        ModuleParaPojo partnerFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT.getCode());
        Integer hasPartnerQuotaNum = partnerFirmLimit == null ? 0 : Integer.parseInt(partnerFirmLimit.getParaValue());
        // "渠道用户数" 产品配额（新）
        ModuleParaPojo partnerUserFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT.getCode());
        Integer hasPartnerUserQuotaNum = partnerUserFirmLimit == null ? 0 : Integer.parseInt(partnerUserFirmLimit.getParaValue());
        // “终端用户数[10000个]” 产品配额（新）
        ModuleParaPojo terminalUserFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.TERMINAL_USER_FIRM_LIMIT.getCode());
        Integer hasTerminalUserQuotaNum = terminalUserFirmLimit == null ? 0 : Integer.parseInt(terminalUserFirmLimit.getParaValue());
        // 有开通过有租户的下游企业则生成“1+N连接企业”产品包补单数据
        if (usedErQuotaMap.get(ONE_OR_N_RELATION_EA) > 0) {
            convertSalesOrderData(oldSalesOrderData, newOrderList, EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT, 100.0, hasPartnerQuotaNum);
        }
        // 有开通过无租户的下游企业则生成“1&N连接企业”产品包补单数据
        if (usedErQuotaMap.get(ONE_AND_N_RELATION_EA) > 0) {
            convertSalesOrderData(oldSalesOrderData, newOrderList, EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT, 500.0, hasPartnerUserQuotaNum);
        }
        // 开通"微信用户数[50000个]" 产品包,则生成5个“终端用户（10000）”产品包补单数据
        if (hasServicePlusLimit && (terminalUserFirmLimit == null || Integer.parseInt(terminalUserFirmLimit.getParaValue()) == 0)) {
            convertSalesOrderData(oldSalesOrderData, newOrderList, EnterpriseLicenseCodeEnum.TERMINAL_USER_FIRM_LIMIT, 10000.0, hasTerminalUserQuotaNum);
        }
        return newOrderList;
    }

    /**
     * 处理历史“下游企业端口数[100个]”订单数据
     *
     * @param oldSalesOrderData 订单数据
     * @return 新的产品包补单数据
     */
    public List<SalesOrderData> processDownstreamFirmLimitLicenseData(HistorySalesOrderData oldSalesOrderData, Map<String, ModuleParaPojo> newProductQuota) {
        Integer enterpriseId = oldSalesOrderData.getEnterpriseId();
        if (!usedQuotaMap.containsKey(enterpriseId)) {
            countEnterpriseUsedQuotaNum(enterpriseId);
        }
        List<SalesOrderData> newOrderList = Lists.newArrayList();
        Map<String, Integer> usedErQuotaMap = usedQuotaMap.get(enterpriseId);
        // “1+N连接企业数” 产品配额（新）
        ModuleParaPojo partnerFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT.getCode());
        Integer hasPartnerQuotaNum = partnerFirmLimit == null ? 0 : Integer.parseInt(partnerFirmLimit.getParaValue());
        // "渠道用户数" 产品配额（新）
        ModuleParaPojo partnerUserFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT.getCode());
        Integer hasPartnerUserQuotaNum = partnerUserFirmLimit == null ? 0 : Integer.parseInt(partnerUserFirmLimit.getParaValue());
        // 有开通过有租户的下游企业则生成“1+N连接企业”产品包补单数据
        if (usedErQuotaMap.get(ONE_OR_N_RELATION_EA) > 0) {
            convertSalesOrderData(oldSalesOrderData, newOrderList, EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT, oldSalesOrderData.getResourceTotal(), hasPartnerQuotaNum);
        }
        // 有开通过无租户的下游企业则生成“1&N连接企业”产品包补单数据
        if (usedErQuotaMap.get(ONE_AND_N_RELATION_EA) > 0) {
            convertSalesOrderData(oldSalesOrderData, newOrderList, EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT, oldSalesOrderData.getResourceTotal() * 5, hasPartnerUserQuotaNum);
        }
        return newOrderList;
    }

    /**
     * 处理历史“代理通下游配额包[10个]”订单数据 1. 1套“代理通下游配额包[10个]”产品包拆分为10套“1+N连接企业”产品包 2. 1套“代理通下游配额包[10个]”产品包拆分为50套“渠道用户”产品包
     *
     * @param oldSalesOrderData 订单数据
     * @return 新的产品包补单数据
     */
    public List<SalesOrderData> processDownstreamFirm10LimitLicenseData(HistorySalesOrderData oldSalesOrderData, Map<String, ModuleParaPojo> newProductQuota) {
        Integer enterpriseId = oldSalesOrderData.getEnterpriseId();
        if (!usedQuotaMap.containsKey(enterpriseId)) {
            countEnterpriseUsedQuotaNum(enterpriseId);
        }
        List<SalesOrderData> newOrderList = Lists.newArrayList();
        Map<String, Integer> usedErQuotaMap = usedQuotaMap.get(enterpriseId);
        // “1+N连接企业数” 产品配额（新）
        ModuleParaPojo partnerFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT.getCode());
        Integer hasPartnerQuotaNum = partnerFirmLimit == null ? 0 : Integer.parseInt(partnerFirmLimit.getParaValue());
        // "渠道用户数" 产品配额（新）
        ModuleParaPojo partnerUserFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT.getCode());
        Integer hasPartnerUserQuotaNum = partnerUserFirmLimit == null ? 0 : Integer.parseInt(partnerUserFirmLimit.getParaValue());
        // 有开通过有租户的下游企业则生成“1+N连接企业”产品包补单数据
        if (usedErQuotaMap.get(ONE_OR_N_RELATION_EA) > 0) {
            convertSalesOrderData(oldSalesOrderData, newOrderList, EnterpriseLicenseCodeEnum.PARTNER_FIRM_LIMIT, oldSalesOrderData.getResourceTotal(), hasPartnerQuotaNum);
        }
        // 有开通过无租户的下游企业则生成“1&N连接企业”产品包补单数据
        if (usedErQuotaMap.get(ONE_AND_N_RELATION_EA) > 0) {
            convertSalesOrderData(oldSalesOrderData, newOrderList, EnterpriseLicenseCodeEnum.PARTNER_USER_FIRM_LIMIT, oldSalesOrderData.getResourceTotal() * 5, hasPartnerUserQuotaNum);
        }
        return newOrderList;
    }

    /**
     * 处理“微信用户数[50000个]”产品包历史订单数据
     *
     * @param oldSalesOrderData 旧订单数据
     * @return 新产品包订单数据
     */
    public List<SalesOrderData> processServicePlusLimitLicenseData(HistorySalesOrderData oldSalesOrderData, Map<String, ModuleParaPojo> newProductQuota) {
        Integer enterpriseId = oldSalesOrderData.getEnterpriseId();
        if (!usedQuotaMap.containsKey(enterpriseId)) {
            countEnterpriseUsedQuotaNum(enterpriseId);
        }
        List<SalesOrderData> newOrderList = Lists.newArrayList();
        // “终端用户数[10000个]” 产品配额（新）
        ModuleParaPojo terminalUserFirmLimit = newProductQuota.get(EnterpriseLicenseCodeEnum.TERMINAL_USER_FIRM_LIMIT.getCode());
        Integer hasTerminalUserQuotaNum = terminalUserFirmLimit == null ? 0 : Integer.parseInt(terminalUserFirmLimit.getParaValue());
        // 生成5个“终端用户（10000）”产品包补单数据
        convertSalesOrderData(oldSalesOrderData, newOrderList, EnterpriseLicenseCodeEnum.TERMINAL_USER_FIRM_LIMIT, oldSalesOrderData.getResourceTotal(), hasTerminalUserQuotaNum);
        return newOrderList;
    }

    /**
     * 把旧的产品包订单转换成新的产品包订单数据
     *
     * @param oldSalesOrderData 旧订单数据
     * @param newOrderList 新订单数据
     * @param productObj 新产品包数据
     * @param resourceTotal 产品资源总量
     */
    private void convertSalesOrderData(HistorySalesOrderData oldSalesOrderData, List<SalesOrderData> newOrderList, EnterpriseLicenseCodeEnum productObj, Double resourceTotal, Integer hasQuota) {
        SalesOrderData salesOrderData = new SalesOrderData();
        salesOrderData.setOwner(ORDER_OWNER);
        if (resourceTotal > 0) {
            salesOrderData.setResourceNum(resourceTotal);
            salesOrderData.setQuantity(resourceTotal / productObj.getNum());
        } else {
            salesOrderData.setResourceNum(oldSalesOrderData.getResourceTotal());
            salesOrderData.setQuantity(oldSalesOrderData.getResourceTotal() / productObj.getNum());
        }
        salesOrderData.setEnterpriseId(oldSalesOrderData.getEnterpriseId());
        salesOrderData.setEnterpriseAccount(oldSalesOrderData.getEnterpriseAccount());
        salesOrderData.setProductId(productObj.getName());
        salesOrderData.setBeginDate(oldSalesOrderData.getBeginDate());
        salesOrderData.setEndDate(oldSalesOrderData.getEndDate());
        salesOrderData.setRecordType(ORDER_RECORD_TYPE);
        salesOrderData.setOwner(ORDER_OWNER);
        salesOrderData.setStatus(ORDER_STATUS);
        salesOrderData.setHasQuotaNum(hasQuota);
        newOrderList.add(salesOrderData);
    }

    /**
     * 统计上游企业开通的“1+N互联企业数”和“1&N互联企业数”
     *
     * @param enterpriseId 上游企业ID
     */
    public void countEnterpriseUsedQuotaNum(Integer enterpriseId) {
        if (!usedQuotaMap.containsKey(enterpriseId)) {
            //"1+N 互联企业数量"
            Integer oneOrNEnterpriseNum = countUpstreamRelationEnterpriseHasFsAccountUsers(enterpriseId);
            //"1&N 互联企业数量"
            Integer oneAndNEnterpriseNum = countUpstreamRelationEnterpriseHasNotFsAccountUsers(enterpriseId);
            Map<String, Integer> eaUsedQuotaMap = Maps.newHashMap();
            eaUsedQuotaMap.put(ONE_OR_N_RELATION_EA, oneOrNEnterpriseNum);
            eaUsedQuotaMap.put(ONE_AND_N_RELATION_EA, oneAndNEnterpriseNum);
            usedQuotaMap.put(enterpriseId, eaUsedQuotaMap);
        }
    }

    /**
     * 统计1&N连接企业数
     *
     * @param upstreamEi 上游企业ID
     * @return 1&N连接企业数
     */
    public Integer countUpstreamRelationEnterpriseHasFsAccountUsers(Integer upstreamEi) {
        try {
            SearchQuery query = new SearchQuery();
            query.addFilter(EnterpriseRelationObjConstant.HAS_FS_ACCOUNT, Lists.newArrayList("true"), FilterOperatorEnum.EQ);
            query.addFilter(EnterpriseRelationObjConstant.RELATION_TYPE, Lists.newArrayList(EnterpriseRelationType.FRIEND.getType() + ""), FilterOperatorEnum.EQ.getValue());
            query.addFilter(EnterpriseRelationObjConstant.RECORD_TYPE, Lists.newArrayList(EnterpriseRelationObjConstant.RECORD_TYPE_IDENTITY_TYPE_CORP), FilterOperatorEnum.EQ);
            String searchJson = JsonUtil.toJson(query);
            //Integer recordNum = enterpriseLicenseService.getObjectDataCountWithCrossCloud(upstreamEi, EnterpriseRelationObjConstant.API_NAME, searchJson);
            Integer recordNum = enterpriseLicenseService.getObjectDataCount(upstreamEi, EnterpriseRelationObjConstant.API_NAME, searchJson);
            log.info("countUpstreamRelationEnterpriseHasFsAccountUsers--upstreamEi:{},recordNum:{}", upstreamEi, recordNum);
            return recordNum;
        } catch (Exception e) {
            log.warn("countUpstreamRelationEnterpriseHasFsAccountUsers--error:{}", e);
            return 0;
        }
    }

    /**
     * 统计1+N连接企业数
     *
     * @param upstreamEi 上游企业ID
     * @return 1+N连接企业数
     */
    public Integer countUpstreamRelationEnterpriseHasNotFsAccountUsers(Integer upstreamEi) {
        try {
            SearchQuery query = new SearchQuery();
            query.addFilter(EnterpriseRelationObjConstant.HAS_FS_ACCOUNT, Lists.newArrayList("false"), FilterOperatorEnum.EQ);
            query.addFilter(EnterpriseRelationObjConstant.RELATION_TYPE, Lists.newArrayList(EnterpriseRelationType.FRIEND.getType() + ""), FilterOperatorEnum.EQ.getValue());
            query.addFilter(EnterpriseRelationObjConstant.RECORD_TYPE, Lists.newArrayList(EnterpriseRelationObjConstant.RECORD_TYPE_IDENTITY_TYPE_CORP), FilterOperatorEnum.EQ);
            String searchJson = JsonUtil.toJson(query);
            //Integer recordNum = enterpriseLicenseService.getObjectDataCountWithCrossCloud(upstreamEi, EnterpriseRelationObjConstant.API_NAME, searchJson);
            Integer recordNum = enterpriseLicenseService.getObjectDataCount(upstreamEi, EnterpriseRelationObjConstant.API_NAME, searchJson);
            log.info("countUpstreamRelationEnterpriseHasNotFsAccountUsers--upstreamEi:{},recordNum:{}", upstreamEi, recordNum);
            return recordNum;
        } catch (Exception e) {
            log.warn("countUpstreamRelationEnterpriseHasNotFsAccountUsers--error:{}", e);
            return 0;
        }
    }

    /**
     * 生成历史白名单企业下游配额“下游精简版配额包【5人】”产品包销售订单数据 产品包名：下游精简版配额包【5人】 产品编码：office_edition_substitute_5_limit 产品主编码：office_edition_substitute 产品有限期：上游企业的CRM有效期 产品数量：取客户的老的“对接企业数”配额
     */
    public void createOldWhiteListEnterpriseSalesOrderData() {
        List<SalesOrderData> orderDataList = Lists.newArrayList();
        // 1、查询"下游精简版配额包【5人】"产品包数据
        ProductObj productObjData = getProductObjData(FS_EI, EnterpriseLicenseCodeEnum.OFFICE_EDITION_SUBSTITUTE_5_LIMIT.getCode());
        if (productObjData == null) {
            return;
        }
        // 2、处理所有白名单企业数据
        if (CollectionUtils.isNotEmpty(oldWhiteListEnterpriseAccounts)) {
            oldWhiteListEnterpriseAccounts.forEach(tenantAccount -> {
                try {
                    if (checkEnterpriseIsStop(tenantAccount)) {
                        return;
                    }
                    Integer tenantId = eieaConverter.enterpriseAccountToId(tenantAccount);
                    // 2.1、检测是否已经开通过下游配额包
                    Map<String, DownstreamOpenLicenseData> openedLicenseDataMap = paasLicenseManager.listDownstreamOpenLicenseDatas(tenantAccount);
                    if (openedLicenseDataMap != null && openedLicenseDataMap.size() > 0) {
                        return;
                    }
                    ProductVersionPojo crmVersionData = getCrmVersionData(tenantId);
                    if (crmVersionData == null) {
                        return;
                    }
                    // 2.2、查询企业开通的“下游企业端口数[100个]”配额数
                    Integer enterpriseRelationQuota = enterpriseLicenseService
                        .getEnterpriseLicenseQuotaByProductCode(tenantId, EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getMainCode(), EnterpriseLicenseCodeEnum.DOWNSTREAM_FIRM_LIMIT.getCode());
                    Double quantity = enterpriseRelationQuota == 0 ? 1 : Double.valueOf(enterpriseRelationQuota);
                    // 2.3、生产新的产品包补单数据
                    SalesOrderData data = new SalesOrderData();
                    data.setEnterpriseId(tenantId);
                    data.setEnterpriseAccount(tenantAccount);
                    data.setProductId(productObjData.getName());
                    data.setRecordType(ORDER_RECORD_TYPE);
                    data.setResourceNum(quantity);
                    data.setQuantity(quantity);
                    data.setBeginDate(new Date(crmVersionData.getStartTime()));
                    data.setEndDate(new Date(crmVersionData.getExpiredTime()));
                    data.setOwner(ORDER_OWNER);
                    data.setStatus(ORDER_STATUS);
                    orderDataList.add(data);
                    // 休息1秒，防止数据库压力过大
                    Thread.sleep(300L);
                } catch (Exception e) {
                    log.warn("createOldWhiteListEnterpriseSalesOrderData-error:tenantAccount:{},error:{}", tenantAccount, e);
                }
            });
        }
        // 3、生成补单数据导入文件
        if (CollectionUtils.isNotEmpty(orderDataList)) {
            String fileName = String.format("import_whiteListEa_office_edition_substitute_order_%s.xlsx", DateUtil.dateFormat(new Date(), "yyyyMMdd_ss"));
            writeDataToExcelFile(fileName, orderDataList, SalesOrderData.class);
        }
    }

    /**
     * 生成白名单企业开通下游经销商版记录
     */
    public void createOldWhiteListUpstreamOpenedDownstreamCrmRecord() {
        if (CollectionUtils.isNotEmpty(oldWhiteListEnterpriseAccounts)) {
            // 2、处理所有白名单企业开通的所有限期为9999的有租户下游企业记录
            oldWhiteListEnterpriseAccounts.forEach(upstreamEa -> {
                try {
                    if (checkEnterpriseIsStop(upstreamEa)) {
                        return;
                    }
                    Integer upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
                    ProductVersionPojo upstreamCrmVersion = getCrmVersionData(upstreamEi);
                    if (upstreamCrmVersion == null) {
                        return;
                    }
                    // 2.1、查询指定上游企业开通的所有"有租户"的下游企业
                    List<String> outerTenantIds = listAllHasFsAccountDownstreamEa(upstreamEi);
                    if (CollectionUtils.isEmpty(outerTenantIds)) {
                        return;
                    }
                    // 2.2、获取所有下游企业的tenantId
                    List<String> downstreamEaList = fxiaokeEnterpriseAssociationEntityDao.batchGetEaByOuterTenantIdList(outerTenantIds.stream().map(i -> Long.valueOf(i)).collect(Collectors.toSet()));
                    if (CollectionUtils.isEmpty(downstreamEaList)) {
                        return;
                    }
                    // 2.3、检查所有下游企业开通的版本及过期时间是否9999
                    downstreamEaList.forEach(downstreamEa -> {
                        try {
                            if (checkEnterpriseIsStop(downstreamEa)) {
                                return;
                            }
                            Integer downstreamEi = eieaConverter.enterpriseAccountToId(downstreamEa);
                            ProductVersionPojo downstreamCrmVersion = getCrmVersionData(downstreamEi);
                            // 检查下游企业开通的是否"协同账号 1年"并且过期时间为9999年
                            if (downstreamCrmVersion != null && DateUtil.dateFormat(downstreamCrmVersion.getExpiredTime(), "yyyy").equals("9999")) {
                                List<UpstreamOpenedEnterpriseRecordEntity> historyOpenedRecord = openedEnterpriseRecordEntityDao
                                    .listByDownstreamEaAndModuleCode(upstreamEi, downstreamEa, downstreamCrmVersion.getCurrentVersion());
                                if (CollectionUtils.isEmpty(historyOpenedRecord)) {
                                    Integer crmNumber = downstreamCrmVersion.getMaxHeadCount();
                                    Long downstreamOuterTenantId = fxiaokeEnterpriseAssociationEntityDao.getOuterTenantIdByEa(downstreamEa);
                                    // 2.4、写入UpstreamOpenedEnterpriseRecord表
                                    UpstreamOpenedEnterpriseRecordEntity upstreamOpenedEnterpriseRecordEntity = new UpstreamOpenedEnterpriseRecordEntity();
                                    upstreamOpenedEnterpriseRecordEntity.setUpstreamEa(upstreamEa);
                                    upstreamOpenedEnterpriseRecordEntity.setDownstreamEa(downstreamEa);
                                    upstreamOpenedEnterpriseRecordEntity.setDownstreamOuterTenantId(downstreamOuterTenantId);
                                    upstreamOpenedEnterpriseRecordEntity.setBeginTime(new Date(upstreamCrmVersion.getStartTime()));
                                    upstreamOpenedEnterpriseRecordEntity.setEndTime(new Date(upstreamCrmVersion.getExpiredTime()));
                                    upstreamOpenedEnterpriseRecordEntity.setCrmCount(crmNumber);
                                    upstreamOpenedEnterpriseRecordEntity.setOrderType(OrderTpyeEnum.RELATION_DWONSTREAM.getType());
                                    upstreamOpenedEnterpriseRecordEntity.setUpstreamModuleCode(upstreamCrmVersion.getCurrentVersion());
                                    upstreamOpenedEnterpriseRecordEntity.setDownstreamModuleCode(downstreamCrmVersion.getCurrentVersion());
                                    upstreamOpenedEnterpriseRecordEntity.setStatus(UpstreamOpenedEnterpriseStatusConstant.ASSOCIATE);
                                    openedEnterpriseRecordEntityDao.create(upstreamEi, upstreamOpenedEnterpriseRecordEntity);
                                }
                            }
                        } catch (Exception e) {
                            log.warn("createOpenedHasFsAccountDownstreamRecord:{}", e);
                        }
                    });

                    // 休息1秒，防止数据库压力过大
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    log.warn("createOpenedHasFsAccountDownstreamRecord:{}", e);
                }
            });
        }
    }

    /**
     * 生成所有白名单企业开通的有租户下游企业购买“协同账号1年”产品包销售订单数据 产品包名：“协同账号1年” 产品编码：office_edition 产品数量：5个 有效期限：上游企业的CRM有效期
     */
    public void createOldWhiteListUpstreamOpenedDownstreamCrmOrderData() {
        if (CollectionUtils.isNotEmpty(oldWhiteListEnterpriseAccounts)) {
            List<SalesOrderData> orderDataList = Lists.newArrayList();
            // 1、处理所有白名单企业开通的所有限期为9999的有租户下游企业记录
            oldWhiteListEnterpriseAccounts.forEach(tenantAccount -> {
                try {
                    if (checkEnterpriseIsStop(tenantAccount)) {
                        return; //已停用企业不处理
                    }
                    Integer upstreamEi = eieaConverter.enterpriseAccountToId(tenantAccount);
                    ProductVersionPojo upstreamCrmVersion = getCrmVersionData(upstreamEi);
                    if (upstreamCrmVersion == null) {
                        return;
                    }
                    // 1.1、查询指定上游企业开通的所有"有租户"的下游企业
                    List<EnterpriseRelationObj> hasFsAccountEnterpriseList = listAllHasFsAccountEnterpriseRelationObj(upstreamEi);
                    if (CollectionUtils.isEmpty(hasFsAccountEnterpriseList)) {
                        return;
                    }
                    Map<String, String> downstreamEaToNameMaps = hasFsAccountEnterpriseList.stream().filter(i -> StringUtils.isNotEmpty(i.getEnterpriseAccount()))
                        .collect(Collectors.toMap(EnterpriseRelationObj::getEnterpriseAccount, EnterpriseRelationObj::getName, (k1, k2) -> k1));
                    Collection<Long> outerTenantIds = hasFsAccountEnterpriseList.stream().map(EnterpriseRelationObj::getOuterTenantId).collect(Collectors.toSet());
                    // 1.2、获取所有下游企业的tenantId
                    List<String> downstreamEaList = fxiaokeEnterpriseAssociationEntityDao.batchGetEaByOuterTenantIdList(outerTenantIds);
                    if (CollectionUtils.isEmpty(downstreamEaList)) {
                        return;
                    }
                    // 1.3、检查所有下游企业开通的版本及过期时间是否9999
                    downstreamEaList.forEach(downstreamEa -> {
                        try {
                            if (checkEnterpriseIsStop(downstreamEa)) {
                                return; //已停用企业不处理
                            }
                            Integer downstreamEi = eieaConverter.enterpriseAccountToId(downstreamEa);
                            ProductVersionPojo downstreamCrmVersion = getCrmVersionData(downstreamEi);
                            // 检查下游企业开通的版本有效期过期时间为9999年
                            if (downstreamCrmVersion != null && DateUtil.dateFormat(downstreamCrmVersion.getExpiredTime(), "yyyy").equals("9999")) {
                                String enterpriseName = downstreamEaToNameMaps.containsKey(downstreamEa) ? downstreamEaToNameMaps.get(downstreamEa) : "";
                                // 1.4、生成订单数据
                                SalesOrderData data = new SalesOrderData();
                                data.setEnterpriseId(downstreamEi);
                                data.setEnterpriseAccount(downstreamEa);
                                data.setEnterpriseName(enterpriseName);
                                data.setProductId(downstreamCrmVersion.getCurrentVersion());
                                data.setRecordType(ORDER_RECORD_TYPE);
                                data.setResourceNum(1.0);
                                data.setQuantity(1.0);
                                data.setBeginDate(new Date(upstreamCrmVersion.getStartTime()));
                                data.setEndDate(new Date(upstreamCrmVersion.getExpiredTime()));
                                data.setOwner(ORDER_OWNER);
                                data.setStatus(ORDER_STATUS);
                                orderDataList.add(data);
                            }
                        } catch (Exception e) {
                            log.warn("createOldWhiteListEnterpriseOpenCrmSalesOrderData-fail:{}", e);
                        }
                    });
                    Thread.sleep(300L);
                } catch (InterruptedException e) {
                    log.warn("createOldWhiteListEnterpriseOpenCrmSalesOrderData:{}", e);
                }
            });
            // 2、生成订单数据导入文件
            if (CollectionUtils.isNotEmpty(orderDataList)) {
                String fileName = String.format("import_office_edition_downstream_order_%s.xlsx", DateUtil.dateFormat(new Date(), "yyyyMMdd_ss"));
                writeDataToExcelFile(fileName, orderDataList, SalesOrderData.class);
            }
        }
    }

    /**
     * 查询企业所有开通纷享账号的下游企业
     *
     * @param upstreamEi 上游企业ID
     * @return 下游企业外部ID列表
     */
    public List<String> listAllHasFsAccountDownstreamEa(Integer upstreamEi) {
        List<String> outerTenantIds = Lists.newArrayList();
        try {
            OffsetUtil.offsetFunction(1000, (offset, limit) -> {
                List<ObjectData> objectDataList = listAllHasFsAccountDownstreamEnterprise(upstreamEi, offset, limit);
                if (CollectionUtils.isNotEmpty(objectDataList)) {
                    List<String> outerTenantIdList = objectDataList.stream().map(i -> i.getId()).collect(Collectors.toList());
                    outerTenantIds.addAll(outerTenantIdList);
                    return outerTenantIdList.size();
                }
                return 0;
            });
        } catch (Exception e) {
            log.warn("listAllHasFsAccountDownstreamEa-error:{}", e);
        }

        return outerTenantIds;
    }

    /**
     * 查询所有有租户下游互联企业
     *
     * @param upstreamEi 上游企业ID
     * @return 有租户下游企业列表
     */
    public List<EnterpriseRelationObj> listAllHasFsAccountEnterpriseRelationObj(Integer upstreamEi) {
        List<EnterpriseRelationObj> hasFsAccountEaList = Lists.newArrayList();
        try {
            OffsetUtil.offsetFunction(1000, (offset, limit) -> {
                List<ObjectData> objectDataList = listAllHasFsAccountDownstreamEnterprise(upstreamEi, offset, limit);
                if (CollectionUtils.isNotEmpty(objectDataList)) {
                    List<EnterpriseRelationObj> enterpriseRelationObjList = objectDataList.stream().map(i -> EnterpriseRelationObj.newInstance(i)).collect(Collectors.toList());
                    hasFsAccountEaList.addAll(enterpriseRelationObjList);
                    return enterpriseRelationObjList.size();
                }
                return 0;
            });
        } catch (Exception e) {
            log.warn("listAllHasFsAccountDownstreamEa-error:{}", e);
        }
        return hasFsAccountEaList;
    }

    private List<ObjectData> listAllHasFsAccountDownstreamEnterprise(Integer upstreamEi, Integer offset, Integer limit) {
        SearchQuery query = new SearchQuery();
        query.setLimit(limit);
        query.setOffset(offset);
        query.addFilter(EnterpriseRelationObjConstant.HAS_FS_ACCOUNT, Lists.newArrayList("true"), FilterOperatorEnum.EQ);
        query.addFilter(EnterpriseRelationObjConstant.RELATION_TYPE, Lists.newArrayList(EnterpriseRelationType.FRIEND.getType() + ""), FilterOperatorEnum.EQ.getValue());
        query.addFilter(EnterpriseRelationObjConstant.RECORD_TYPE, Lists.newArrayList(EnterpriseRelationObjConstant.RECORD_TYPE_IDENTITY_TYPE_CORP), FilterOperatorEnum.EQ);
        String searchJson = JsonUtil.toJson(query);
        List<ObjectData> objectDataList = enterpriseLicenseService
            .getObjectDataList(upstreamEi, EnterpriseRelationObjConstant.API_NAME, searchJson, Lists.newArrayList("_id", "name", "enterprise_account", "dest_outer_tenant_id"));
        return objectDataList;
    }

    /**
     * 查询企业开通的CRM版本信息
     *
     * @param tenantId 企业ID
     * @return crm版本信息
     */
    public ProductVersionPojo getCrmVersionData(Integer tenantId) {
        List<ProductVersionPojo> productVersionList = getProductVersion(tenantId);
        if (CollectionUtils.isNotEmpty(productVersionList)) {
            for (ProductVersionPojo productLicense : productVersionList) {
                // CRM主版本
                if ("0".equals(productLicense.getProductType()) && "CRM".equals(productLicense.getProductName())) {
                    return productLicense;
                }
            }
        }
        return null;
    }

    /**
     * 查询企业开通的License版本
     *
     * @param tenantId 企业ID
     */
    public List<ProductVersionPojo> getProductVersion(Integer tenantId) {
        QueryProductArg queryArg = new QueryProductArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(tenantId + "");
        licenseContext.setUserId(CrmConstants.SYSTEM_USER + "");
        queryArg.setLicenseContext(licenseContext);
        LicenseVersionResult result = licenseClient.queryProductVersion(queryArg);
        if (result.getResult() != null) {
            List<ProductVersionPojo> productVersionPojoList = result.getResult();
            log.info("productVersionPojoList:{}", productVersionPojoList);
            return productVersionPojoList;
        }
        return null;
    }

    /**
     * 查询企业开通的产品License信息
     *
     * @param tenantId 企业ID
     * @param productCode 产品编码
     */
    public QueryExpiredTimePojo getCrmLicenseExpiredData(Integer tenantId, String productCode) {
        QueryExpireTimeArg queryArg = new QueryExpireTimeArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(tenantId + "");
        licenseContext.setUserId(CrmConstants.SYSTEM_USER + "");
        queryArg.setContext(licenseContext);
        queryArg.setProductCodes(Lists.newArrayList(productCode));
        com.facishare.paas.license.common.Result<List<QueryExpiredTimePojo>> result = licenseClient.queryLicenseExpired(queryArg);
        if (result.getErrCode() == 0 && result.getResult() != null && result.getResult().size() > 0) {
            List<QueryExpiredTimePojo> data = result.getResult();
            log.info("queryLicenseExpired:{}", data.get(0));
            QueryExpiredTimePojo queryExpiredTimePojo = BeanUtil.deepFastJsonCopy(data.get(0), new TypeToken<QueryExpiredTimePojo>() {
            }.getType());
            return queryExpiredTimePojo;
        }
        return null;
    }

    /**
     * 查询指定上游企业开通的所有渠道版本Crm企业
     *
     * @param upstreamEa 上游企业ID
     * @return 上游企业开通的渠道版企业列表
     */
    public List<UpstreamOpenedEnterpriseRecordEntity> getUpstreamOpenOfficeEditionDownstream(String upstreamEa) {
        int upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
        List<UpstreamOpenedEnterpriseRecordEntity> upstreamOpenedEnterpriseRecords = openedEnterpriseRecordEntityDao.listByUpstreamEa(upstreamEi, upstreamEa);
        if (CollectionUtils.isNotEmpty(upstreamOpenedEnterpriseRecords)) {
            List<UpstreamOpenedEnterpriseRecordEntity> officeEditionDownstream = upstreamOpenedEnterpriseRecords.stream().filter(item -> {
                return item.getUpstreamModuleCode().equals(EnterpriseLicenseCodeEnum.OFFICE_EDITION_SUBSTITUTE_5_LIMIT.getMainCode()) && item.getDownstreamModuleCode()
                    .equals(EnterpriseLicenseCodeEnum.OFFICE_EDITION.getCode()) && item.getOrderIds().isEmpty();
            }).collect(Collectors.toList());
            return officeEditionDownstream;
        }
        return Lists.newArrayList();
    }

    /**
     * 根据产品编码查询产品信息
     *
     * @param productCodeList 互联产品编码
     * @return 产品数据
     */
    public List<ProductObj> listProductObjData(List<String> productCodeList) {
        List<ProductObj> dataList = Lists.newArrayList();
        SearchQuery query = new SearchQuery();
        query.addFilter(ProductObjConstant.PRODUCT_CODE, productCodeList, FilterOperatorEnum.IN.getValue());
        query.addFilter(ProductObjConstant.IS_DELETED, Lists.newArrayList("0"), FilterOperatorEnum.EQ.getValue());
        SearchTemplateQuery templateQuery = new SearchTemplateQuery();
        templateQuery.setDataRolePermission(false);
        templateQuery.setOffset(0);
        templateQuery.setLimit(1000);
        templateQuery.setDataRolePermission(false);
        templateQuery.setFilters(query.getFilters());
        String searchJson = JsonUtil.toJson(templateQuery);
        List<ObjectData> productObjList = getObjectDataList(FS_EI, ProductObjConstant.API_NAME, searchJson);
        if (CollectionUtils.isNotEmpty(productObjList)) {
            productObjList.forEach(objectData -> {
                dataList.add(ProductObj.newInstance(objectData));
            });
        }
        return dataList;
    }

    /**
     * 获取产品对象信息
     */
    public ProductObj getProductObjData(Integer upstreamEi, String produceCode) {
        HeaderObj headerObj = HeaderObj.newInstance(upstreamEi, -10000);
        SearchQuery query = new SearchQuery();
        query.addFilter(ProductObjConstant.PRODUCT_CODE, Lists.newArrayList(produceCode), FilterOperatorEnum.EQ.getValue());
        query.addFilter(ProductObjConstant.IS_DELETED, Lists.newArrayList("0"), FilterOperatorEnum.EQ.getValue());
        SearchTemplateQuery templateQuery = new SearchTemplateQuery();
        templateQuery.setDataRolePermission(false);
        templateQuery.setOffset(0);
        templateQuery.setLimit(1);
        templateQuery.setNeedReturnCountNum(false);
        templateQuery.setDataRolePermission(false);
        templateQuery.setFilters(query.getFilters());
        String searchJson = JsonUtil.toJson(templateQuery);
        FindByQueryV3Arg queryArg = new FindByQueryV3Arg();
        queryArg.setDescribeApiName(ProductObjConstant.API_NAME);
        queryArg.setNeedCount(false);
        queryArg.setSearchQueryInfoJson(searchJson);
        Result<QueryBySearchTemplateV3Result> result = objectDataServiceV3.findByQuery(headerObj, queryArg);
        if (result.isSuccess() && result.getData() != null && result.getData().getQueryResult() != null && result.getData().getQueryResult().getDataList().size() > 0) {
            ObjectData objectData = result.getData().getQueryResult().getDataList().get(0);
            return ProductObj.newInstance(objectData);
        }
        return null;
    }

    /**
     * 查询指定产品包关联的销售订单产品数据
     *
     * @param productIds 产品包ID
     * @return 订单产品列表
     */
    public List<SalesOrderProductObj> listSalesOrderProductObjData(List<String> productIds) {
        List<SalesOrderProductObj> dataList = Lists.newArrayList();
        SearchQuery query = new SearchQuery();
        query.addFilter(SalesOrderProductObjConstant.PRODUCT_ID, productIds, FilterOperatorEnum.IN.getValue());
        query.addFilter(SalesOrderProductObjConstant.IS_DELETED, Lists.newArrayList("0"), FilterOperatorEnum.EQ.getValue());
        SearchTemplateQuery templateQuery = new SearchTemplateQuery();
        templateQuery.setDataRolePermission(false);
        templateQuery.setOffset(0);
        templateQuery.setLimit(1000);
        templateQuery.setDataRolePermission(false);
        templateQuery.setFilters(query.getFilters());
        String searchJson = JsonUtil.toJson(templateQuery);
        List<ObjectData> salesOrderProductObjList = getObjectDataList(FS_EI, SalesOrderProductObjConstant.API_NAME, searchJson);
        if (CollectionUtils.isNotEmpty(dataList)) {
            salesOrderProductObjList.forEach(objectData -> {
                dataList.add(SalesOrderProductObj.newInstance(objectData));
            });
        }
        return dataList;
    }

    /**
     * 查询指定销售订单数据
     *
     * @param orderIds 销售订单ID
     * @return 销售订单数据
     */
    public List<SalesOrderObj> listSalesOrderObjData(List<String> orderIds, Integer limit, Integer offset) {
        SearchQuery query = new SearchQuery();
        List<SalesOrderObj> dataList = Lists.newArrayList();
        query.addFilter(SalesOrderObjConstant.ID, orderIds, FilterOperatorEnum.EQ.getValue());
        query.addFilter(SalesOrderObjConstant.IS_DELETED, Lists.newArrayList("0"), FilterOperatorEnum.EQ.getValue());
        SearchTemplateQuery templateQuery = new SearchTemplateQuery();
        templateQuery.setDataRolePermission(false);
        templateQuery.setOffset(offset);
        templateQuery.setLimit(limit);
        templateQuery.setDataRolePermission(false);
        templateQuery.setFilters(query.getFilters());
        String searchJson = JsonUtil.toJson(query);
        List<ObjectData> salesOrderObjList = getObjectDataList(FS_EI, SalesOrderObjConstant.API_NAME, searchJson);
        if (CollectionUtils.isNotEmpty(salesOrderObjList)) {
            salesOrderObjList.forEach(objectData -> {
                dataList.add(SalesOrderObj.newInstance(objectData));
            });
        }
        return dataList;
    }

    /**
     * 查询对象化数据
     *
     * @param upstreamEi 上游企业ID
     * @param apiName 对象名称
     * @param searchJson 查询条件
     * @return 对象数据列表
     */
    private List<ObjectData> getObjectDataList(Integer upstreamEi, String apiName, String searchJson) {
        HeaderObj headerObj = HeaderObj.newInstance(upstreamEi, -10000);
        FindByQueryV3Arg queryArg = new FindByQueryV3Arg();
        queryArg.setDescribeApiName(apiName);
        queryArg.setNeedCount(false);
        queryArg.setSearchQueryInfoJson(searchJson);
        Result<QueryBySearchTemplateV3Result> result = objectDataServiceV3.findByQuery(headerObj, queryArg);
        if (result.isSuccess() && result.getData() != null) {
            return result.getData().getQueryResult().getDataList();
        }
        return Lists.newArrayList();
    }

    /**
     * 开通指定产品包license
     *
     * @param productCode 产品编码
     * @param upstreamEaList 要开通的企业列表
     * @param resourceNum 产品包资源数量
     * @throws InterruptedException
     */
    public void flushErLicenseQuotaData(String productCode, List<String> upstreamEaList, Integer resourceNum) throws InterruptedException {
        List<String> allUpstreamEaList = upstreamEaList;
        if (CollectionUtils.isEmpty(allUpstreamEaList)) {
            //allUpstreamEaList = getAllUpstreamEaList();
            log.info("allUpstreamEaList is empty:{}", allUpstreamEaList.size());
            return;
        }
        log.info("allUpstreamEaList:{}", allUpstreamEaList.size());
        if (StringUtils.isEmpty(productCode)) {
            log.info("The productCode is empty");
            return;
        }
        // 互联应用基础包
        if ("interconnect_app_basic_app".equals(productCode)) {
            openErAppLicenseQuota(productCode, upstreamEaList, resourceNum);
            return;
        }
        for (String ea : allUpstreamEaList) {
            if (checkEnterpriseIsStop(ea)) {
                continue;
            }
            Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
            ProductLicenseArg arg = new ProductLicenseArg();
            LicenseContext context = new LicenseContext();
            context.setAppId("CRM");
            context.setTenantId(tenantId + "");
            context.setUserId(CrmConstants.SYSTEM_USER + "");
            arg.setContext(context);
            ProductLicensePojo licensePojo = new ProductLicensePojo();
            licensePojo.setLicenseType("1");
            licensePojo.setMaxHeadCount(0);
            licensePojo.setTenantId(tenantId + "");
            licensePojo.setTrialFlag(false);
            licensePojo.setStartTime(System.currentTimeMillis());
            licensePojo.setExpiredTime(4077404340000L);
            licensePojo.setProductName("CRM");
            licensePojo.setProductType("1");
            licensePojo.setProductVersion("extention_package");
            ProductInfoPojo productPojo = new ProductInfoPojo();
            List<ModuleInfoPojo> modulePojo = Lists.newArrayList();
            // 1+N连接企业
            if ("partner_firm_limit".equals(productCode)) {
                ModuleInfoPojo terminalUserFirmLimit = getModuleInfoPojo(I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_1567), "interconnect_app_basic_app", "partner_firm_limit", resourceNum + "");
                modulePojo.add(terminalUserFirmLimit);
            }
            // 渠道用户
            if ("partner_user_firm_limit".equals(productCode)) {
                ModuleInfoPojo partnerFirmLimit = getModuleInfoPojo(I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_1572), "interconnect_app_basic_app", "partner_user_firm_limit", resourceNum + "");
                modulePojo.add(partnerFirmLimit);
            }
            // 终端用户
            if ("terminal_user_firm_limit".equals(productCode)) {
                ModuleInfoPojo partnerUserFirmLimit = getModuleInfoPojo(I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_1577), "interconnect_app_basic_app", "terminal_user_firm_limit", resourceNum + "");
                modulePojo.add(partnerUserFirmLimit);
            }
            // 下游配额:CRM制造业经销商版配额包[20人]
            if ("manufacture_dealer_substitute".equals(productCode)) {
                ModuleInfoPojo partnerUserFirmLimit = getModuleInfoPojo(I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_1582), "manufacture_dealer_substitute", "manufacture_dealer_edition_20_limit", resourceNum + "");
                modulePojo.add(partnerUserFirmLimit);
            }
            // 下游配额：CRM快消行业经销商版配额包[20人]
            if ("dealer_edition_substitute".equals(productCode)) {
                ModuleInfoPojo partnerUserFirmLimit = getModuleInfoPojo(I18nUtil.get(I18nKeyEnum.EPI_LICENSEUPGRADE_ENTERPRISELICENSEUPGRADESERVICE_1587), "dealer_edition_substitute", "dealer_edition_substitute_20_limit", resourceNum + "");
                modulePojo.add(partnerUserFirmLimit);
            }

            productPojo.setModuleInfoPojoList(modulePojo);
            licensePojo.setProductInfoPojo(productPojo);
            arg.setProductLicensePojo(licensePojo);
            com.facishare.paas.license.common.Result productLicense = licenseClient.createProductLicense(arg);
            log.info("tenantId:{},result:{}", tenantId, productLicense);
            Thread.sleep(500L);
        }
    }

    /**
     * 开通行业套件或应用
     *
     * @param productCode 产品编码
     * @param upstreamEaList 企业账号名单
     * @param resourceNum 套件数量
     * @throws InterruptedException
     */
    private void openErAppLicenseQuota(String productCode, List<String> upstreamEaList, Integer resourceNum) throws InterruptedException {
        if (CollectionUtils.isEmpty(upstreamEaList)) {
            log.info("The upstream ea is empty");
            return;
        }
        if (StringUtils.isEmpty(productCode)) {
            log.info("The productCode is empty");
            return;
        }
        for (String ea : upstreamEaList) {
            if (checkEnterpriseIsStop(ea)) {
                continue;
            }
            Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
            CreateIndustryArg arg = new CreateIndustryArg();
            LicenseContext context = new LicenseContext();
            context.setAppId("CRM");
            context.setTenantId(tenantId + "");
            context.setUserId(CrmConstants.SYSTEM_USER + "");
            arg.setContext(context);

            IndustryPojo licensePojo = new IndustryPojo();
            licensePojo.setMaxHeadCount(resourceNum);
            licensePojo.setStartTime(System.currentTimeMillis());
            licensePojo.setExpiredTime(4077404340000L);
            licensePojo.setActualExpiredTime(4077404340000L);
            licensePojo.setGracePeriod(0L);
            licensePojo.setProductName("CRM");
            Set<String> industries = Sets.newHashSet();
            // 互联应用基础包
            if ("interconnect_app_basic_app".equals(productCode)) {
                licensePojo.setLicenseType(LicenseType.OFFICIAL);
                licensePojo.setProductType(ProductType.APPLICATION);
                industries.add("interconnect_app_basic_app");
            }
            licensePojo.setIndustries(industries);
            arg.setIndustryPojo(licensePojo);
            com.facishare.paas.license.common.Result productLicense = licenseClient.createIndustry(arg);
            log.info("tenantId:{},result:{}", tenantId, productLicense);
            Thread.sleep(500L);
        }
    }

    @NotNull
    private ModuleInfoPojo getModuleInfoPojo(String moduleName, String moduleCode, String paraKey, String paraValue) {
        ModuleInfoPojo partnerFirmLimit = new ModuleInfoPojo();
        partnerFirmLimit.setModuleName(moduleName);
        partnerFirmLimit.setModuleCode(moduleCode);
        List<ModuleParaPojo> paraPojos = Lists.newArrayList();
        ModuleParaPojo paraPojo = getModuleParaPojo(moduleName, moduleCode, paraKey, paraValue);
        paraPojos.add(paraPojo);
        partnerFirmLimit.setModuleParaPojos(paraPojos);
        return partnerFirmLimit;
    }

    @NotNull
    private ModuleParaPojo getModuleParaPojo(String licenseName, String moduleCode, String paraKey, String paraValue) {
        ModuleParaPojo paraPojo = new ModuleParaPojo();
        paraPojo.setLicenseName(licenseName);
        paraPojo.setParaKey(paraKey);
        paraPojo.setModuleCode(moduleCode);
        paraPojo.setParaType("0");
        paraPojo.setParaValue(paraValue);
        return paraPojo;
    }

    /**
     * 查询企业所属云环境信息
     *
     * @param allEas 企业账号列表
     */
    public void queryEnterpriseRunEnvInfo(List<String> allEas) {
        if (CollectionUtils.isEmpty(allEas)) {
            allEas = getAllUpstreamEaList();
        }
        List<EnterpriseRunEnvData> dataList = Lists.newArrayList();
        for (String ea : allEas) {
            try {
                GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
                arg.setEnterpriseAccount(ea);
                GetEnterpriseDataResult eaInfo = enterpriseEditionService.getEnterpriseData(arg);
                EnterpriseRunEnvData envData = new EnterpriseRunEnvData();
                envData.setEa(ea);
                int runStatus = eaInfo.getEnterpriseData().getRunStatus();
                if (RunStatus.RUN_STATUS_NORMAL == runStatus) {
                    Integer envInt = eaInfo.getEnterpriseData().getEnv();
                    Integer ei = eieaConverter.enterpriseAccountToId(ea);
                    EnterpriseEnvironment env = null;
                    for (EnterpriseEnvironment value : EnterpriseEnvironment.values()) {
                        if (value.getEnv().equals(envInt)) {
                            env = value;
                        }
                    }
                    envData.setEi(ei + "");
                    envData.setEnvId(envInt + "");
                    envData.setEnvDomain(env == null ? "--" : env.getDomain());
                    envData.setEnvName(env == null ? "--" : env.getDetail());
                    envData.setRunStatus(runStatus + "");
                } else {
                    envData.setEi("--");
                    envData.setEnvId("--");
                    envData.setEnvDomain("--");
                    envData.setEnvName("unknown");
                    envData.setRunStatus("hasStop");
                }
                dataList.add(envData);
            } catch (Exception e) {
                log.warn("{}", e);
            }
        }
        log.info("queryEnterpriseRunEnvInfo--result:{}", dataList.size());
        if (CollectionUtils.isNotEmpty(dataList)) {
            String fileName = String.format("er_enterprise_run_env_list_%s.xlsx", DateUtil.dateFormat(new Date(), "yyyyMMdd_ss"));
            log.info("{}", fileName);
            writeDataToExcelFile(fileName, dataList, EnterpriseRunEnvData.class);
        }
    }

    public void countOrder(String[] upstreamEas) {
        for (String upstreamea : upstreamEas) {
            try {
                Integer tenantId = eieaConverter.enterpriseAccountToId(upstreamea);
                Integer usedEnterpriseRelationQuota = enterpriseLicenseService.countUpstreamRelationEnterpriseHasFsAccountUsers(tenantId);
                Integer usedChannelUserQuota = enterpriseLicenseService.countUpstreamChannelUsers(tenantId);
                Integer usedTerminalUserQuota = enterpriseLicenseService.countUpstreamTerminalUsers(tenantId);
                log.info("countOrder={},{},{},{}", upstreamea, usedEnterpriseRelationQuota, usedChannelUserQuota, usedTerminalUserQuota);
            } catch (Exception e) {
                log.info("countOrder={},error={}", upstreamea, e.getMessage());
                log.warn("", e);
            }
        }
    }
}