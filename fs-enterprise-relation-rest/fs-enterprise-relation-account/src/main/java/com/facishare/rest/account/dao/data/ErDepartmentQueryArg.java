package com.facishare.rest.account.dao.data;

import com.fxiaoke.enterpriserelation.objrest.common.OffsetArg;

import java.util.List;

import lombok.Data;

@Data
public class ErDepartmentQueryArg extends OffsetArg {
    private String name;
    //精确查询名字
    private List<String> names;
    private Long ownerOuterUid;
    private Long assistOuterUid;
    private List<Long> ownerOrAssistOutUids;
    private List<Integer> erDepTypes;
    private List<String> parentIds;
    private List<String> ids;
    private String accountDepartmentId;
    private Integer erDepStatus;
    private List<Long> outerTenantIds;
    // 用于查询该parentIds下面的所有子节点 返回的数据会包含传入的id数据
    private List<String> parentIdsWithCascade;
    /**
     * true  只返回精确总数，不返回数据 false 返回预估的总数，返回数据，列表页用 null 返回精确总数，精确数据
     */
    private Boolean findExplicitTotalNum;//是否精确查询总数
    /**
     * 是否使用数据权限
     */
    private Boolean useDataPermission;
    private Boolean fetchParentName;
    private Boolean searchFromDb;
    private List<String> outerTenantDeptIds;
    private List<String> orgManageTypes;
    private List<String> selectFields;
    //忽略公共对象默认的可见范围
    private boolean ignoreBaseVisibleRange = false;
    //按照ErOrder排序， null 则为不排序，true ASC ，false DESC
    private Boolean ascErOrder;
    private boolean includeInvalid = false;
}
