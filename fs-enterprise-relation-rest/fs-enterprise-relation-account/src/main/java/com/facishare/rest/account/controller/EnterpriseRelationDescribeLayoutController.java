package com.facishare.rest.account.controller;

import static com.facishare.rest.account.util.EnterpriseRelationLayoutUtil.ER_PRESENT_TENANT_CATEGORY_NAME;
import static com.facishare.rest.account.util.PaasControllerUtil.BASE_FIELD_SECTION;

import com.alibaba.fastjson.annotation.JSONField;
import com.beust.jcommander.internal.Lists;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.AccountManageMode;
import com.facishare.enterprise.common.constant.CrmObjectApiNameEnum;
import com.facishare.enterprise.common.constant.EnterpriseRelationObjConstant;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.constant.RegisterType;
import com.facishare.enterprise.common.enums.EnterpriseRelationObjMapperStatusOption;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.enterprise.common.util.ListUtil;
import com.facishare.enterprise.common.util.MapUtil;
import com.facishare.er.api.model.vo.TemplateVo;
import com.facishare.er.api.model.vo.TenantGroupCategoryVo;
import com.facishare.er.api.model.vo.TenantGroupVo;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController.Result;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.pay.business.constant.BusinessCodeEnum;
import com.facishare.enterprise.common.util.I18nUtil;
import com.facishare.rest.account.controller.EnterpriseRelationDescribeLayoutController.Arg;
import com.facishare.rest.account.manager.ActionCallManager;
import com.facishare.rest.account.remote.CrmManager;
import com.facishare.rest.account.remote.FxiaokeAccountManager;
import com.facishare.rest.account.service.EnterpriseLicenseService;
import com.facishare.rest.account.service.EnterpriseRelationService;
import com.facishare.rest.account.service.TenantGroupService;
import com.facishare.rest.account.util.EnterpriseRelationLayoutUtil;
import com.facishare.rest.account.util.PaasControllerUtil;
import com.facishare.rest.account.webrest.data.LicenseQuotaData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.service.PartnerService;
import com.fxiaoke.enterpriserelation.objrest.arg.UpstreamEaArg;
import com.fxiaoke.enterpriserelation.objrest.result.TemplateEnterpriseData;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: jiangxch
 * @date: 2021/10/18 11:39
 */
@Slf4j
public class EnterpriseRelationDescribeLayoutController extends AbstractStandardDescribeLayoutController<Arg> {
    // 移动端主从新建已选择页面显示最简已选字段
    private static final List<String> mobileTypePublicEmployeeObjCreateSelectedIncludeFields = Lists.newArrayList(PublicEmployeeObjConstant.NAME, PublicEmployeeObjConstant.MOBILE,
            PublicEmployeeObjConstant.LOGIN_EMAIL);
    private CrmManager crmManager = SpringUtil.getContext().getBean(CrmManager.class);
    private EIEAConverter eieaConverter = SpringUtil.getContext().getBean(EIEAConverter.class);
    private EnterpriseLicenseService enterpriseLicenseService = SpringUtil.getContext().getBean(EnterpriseLicenseService.class);
    private PartnerService partnerService = SpringUtil.getContext().getBean(PartnerService.class);
    private ActionCallManager actionCallManager = SpringUtil.getContext().getBean(ActionCallManager.class);
    private TenantGroupService tenantGroupService = SpringUtil.getContext().getBean(TenantGroupService.class);
    private EnterpriseRelationService enterpriseRelationService = SpringUtil.getContext().getBean(EnterpriseRelationService.class);
    private EnterpriseRelationObjService enterpriseRelationObjService = SpringUtil.getContext().getBean(EnterpriseRelationObjService.class);
    // 列表页隐藏字段：简称拼音，名称拼音，上游企业简称，上游企业简称拼音，配额国企导致下游企业失效，企业头像，备注
    private static List<String> LIST_REMOVE_FIELDS = Collections.unmodifiableList(new ArrayList<>(
            Arrays.asList(EnterpriseRelationObjConstant.ENTERPRISE_NAME_SPELL, EnterpriseRelationObjConstant.DEST_SHORT_NAME_SPELL, EnterpriseRelationObjConstant.UPSTREAM_SHORT_NAME,
                    EnterpriseRelationObjConstant.UPSTREAM_SHORT_NAME_SPELL, EnterpriseRelationObjConstant.QUOTA_EXPIRED, EnterpriseRelationObjConstant.PROFILE_IMAGE, EnterpriseRelationObjConstant.REMARK)));
    // 编辑页设置只读字段
    private static List<String> EDIT_READ_ONLY_FIELDS = Collections.unmodifiableList(new ArrayList<>(
            Arrays.asList(EnterpriseRelationObjConstant.DEST_OUTER_TENANT_ID, EnterpriseRelationObjConstant.QUOTA_EXPIRED, EnterpriseRelationObjConstant.MAPPER_STATUS,
                    EnterpriseRelationObjConstant.RELATION_TYPE, EnterpriseRelationObjConstant.COPY_STATUS, EnterpriseRelationObjConstant.CRM_OPEN_STATUS, EnterpriseRelationObjConstant.ENTERPRISE_ACCOUNT,
                    EnterpriseRelationObjConstant.TEMPLATE_EA, EnterpriseRelationObjConstant.TENANT_GROUP, EnterpriseRelationObjConstant.LICENSE_NAME, EnterpriseRelationObjConstant.REGISTER_TYPE,
                    EnterpriseRelationObjConstant.HAS_FS_ACCOUNT)));
    private FxiaokeAccountManager fxiaokeAccountManager = SpringUtil.getContext().getBean(FxiaokeAccountManager.class);

    @Data
    public static class Arg extends StandardDescribeLayoutController.Arg {
        @JSONField(name = "er_param")
        @JsonProperty("er_param")
        private Map<String, List<String>> erParam;
    }

//    @Override
//    protected boolean ignoreFunctionPrivilege() {
//        User user = controllerContext.getUser();
//        boolean isRelationAdmin = fxiaokeAccountManager.isRelationAdmin(user.getUserIdInt(), user.getTenantId());
//        if (isRelationAdmin) {
//            return true;
//        }
//        return super.ignoreFunctionPrivilege();
//    }

    @Override
    protected void doFunPrivilegeCheck() {
        User user = controllerContext.getUser();
        boolean isRelationAdmin = fxiaokeAccountManager.isRelationAdmin(user);
        if (isRelationAdmin) {
            return;
        }
        super.doFunPrivilegeCheck();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        User user = this.controllerContext.getUser();
        String layoutType = arg.getLayout_type();
        if (UdobjConstants.LAYOUT_TYPE_ADD.equals(layoutType)) {
            Integer tenantId = user.getTenantIdInt();
            Integer enterpriseRelationRemainQuotaNum = enterpriseLicenseService.getEnterpriseRelationRemainQuotaNum(tenantId);
            Integer channelPublicEmployeeRemainQuotaNum = enterpriseLicenseService.getChannelPublicEmployeeRemainQuotaNum(tenantId);
            Integer terminalPublicEmployeeRemainQuotaNum = enterpriseLicenseService.getTerminalPublicEmployeeRemainQuotaNum(tenantId);
            if (enterpriseRelationRemainQuotaNum <= 0 && channelPublicEmployeeRemainQuotaNum <= 0 && terminalPublicEmployeeRemainQuotaNum <= 0) {
                throw new ValidateException(ResultCode.INSUFFICIENT_QUOTA.getDescription());
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        String ea = controllerContext.getEa();
        int upstreamEi = eieaConverter.enterpriseAccountToId(ea);
        HeaderObj headerObj = new HeaderObj(upstreamEi, -10000);
        String status = partnerService.statusOpen(headerObj).getData().getResult();
        Boolean isOpenPartner = !"open".equals(status);
        ILayout layout = new Layout(result.getLayout());
        LayoutExt layoutExt = LayoutExt.of(layout);
        if (layoutExt.getFormComponent().isPresent()) {
            FormComponent formComponent = (FormComponent) layoutExt.getFormComponent().get().getFormComponent();
            String layoutType = arg.getLayout_type();
            switch (layoutType) {
                // 编辑页
                case UdobjConstants.LAYOUT_TYPE_EDIT: {
                    List<String> readonlyFields = new ArrayList<>();
                    readonlyFields.addAll(EDIT_READ_ONLY_FIELDS);
                    List<String> removeFields = new ArrayList<>();
                    //企业名称:有租户不可修改企业名称
                    EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(this.objectData);
                    Boolean hasFsAccount = enterpriseRelationObj.getHasFsAccount();
                    if (hasFsAccount) {
                        readonlyFields.add(EnterpriseRelationObjConstant.NAME);
                    }
                    //关联客户：关联有效，则不可编辑
                    if (enterpriseRelationObj.getMapperAccountId() != null && crmManager.checkEnterpriseRelationMapperIdObjectDataValid(ea, CrmObjectApiNameEnum.CUSTOMER.getName(),
                            enterpriseRelationObj.getMapperAccountId(), enterpriseRelationObj.getOuterTenantId())) {
                        readonlyFields.add(EnterpriseRelationObjConstant.MAPPER_ACCOUNT_ID);
                    }
                    //企业组
                    addTenantGroupField(ea, formComponent, controllerContext.getRequestContext());
                    // 板企业账号字段
                    addTemplateEaSelectOneField(ea, formComponent, controllerContext.getRequestContext());
                    //关联合作伙伴：关联有效，则不可编辑
                    if (enterpriseRelationObj.getMapperPartnerId() != null && crmManager.checkEnterpriseRelationMapperIdObjectDataValid(ea, CrmObjectApiNameEnum.PARTNER.getName(),
                            enterpriseRelationObj.getMapperPartnerId(), enterpriseRelationObj.getOuterTenantId())) {
                        readonlyFields.add(EnterpriseRelationObjConstant.MAPPER_PARTNER_ID);
                    }
                    EnterpriseRelationLayoutUtil.setFieldsReadOnly(formComponent, readonlyFields);
                    if (isOpenPartner) {
                        removeFields.add(EnterpriseRelationObjConstant.MAPPER_PARTNER_ID);
                    }
                    removeFields.add(EnterpriseRelationObjConstant.START_TIME);
                    removeFields.add(EnterpriseRelationObjConstant.STOP_TIME);
                    EnterpriseRelationLayoutUtil.removeSomeFields(formComponent, removeFields);
                    PaasControllerUtil.fillSelectOptionForBooleanField(upstreamEi, result.getObjectDescribe(), EnterpriseRelationObjConstant.HAS_FS_ACCOUNT, true);
                    handleSystemLayoutRule(layout);
                    break;
                }
                // 列表页
                case UdobjConstants.LAYOUT_TYPE_LIST: {
                    List<String> removeFields = new ArrayList<>();
                    removeFields.addAll(LIST_REMOVE_FIELDS);
                    if (isOpenPartner) {
                        removeFields.add(EnterpriseRelationObjConstant.MAPPER_PARTNER_ID);
                    }
                    EnterpriseRelationLayoutUtil.removeSomeFields(formComponent, removeFields);
                    break;
                }
                // 详情页
                case UdobjConstants.LAYOUT_TYPE_DETAIL: {
                    List<String> removeFields = new ArrayList<>();
                    if (isOpenPartner) {
                        removeFields.add(EnterpriseRelationObjConstant.MAPPER_PARTNER_ID);
                    }
                    EnterpriseRelationLayoutUtil.removeSomeFields(formComponent, removeFields);
                    break;
                }
                // 新建页
                case UdobjConstants.LAYOUT_TYPE_ADD: {
                    List<String> removeFields = new ArrayList<>();
                    List<String> requireFields = new ArrayList<>();
                    if (isOpenPartner) {
                        removeFields.add(EnterpriseRelationObjConstant.MAPPER_PARTNER_ID);
                    }
                    removeFields.add(EnterpriseRelationObjConstant.START_TIME);
                    removeFields.add(EnterpriseRelationObjConstant.STOP_TIME);
                    addTenantGroupField(ea, formComponent, controllerContext.getRequestContext());
                    addTemplateEaSelectOneField(ea, formComponent, controllerContext.getRequestContext());
                    Map<String, List<String>> erParam = arg.getErParam();
                    if (erParam != null) {
                        List<String> argRemoveFields = erParam.get("remove_fields");
                        List<String> argRequireFields = erParam.get("require_fields");

                        ListUtil.addIfNotNull(removeFields, argRemoveFields);
                        ListUtil.addIfNotNull(requireFields, argRequireFields);
                    }
                    EnterpriseRelationLayoutUtil.setFieldsIsRequired(formComponent, requireFields);
                    EnterpriseRelationLayoutUtil.removeSomeFields(formComponent, removeFields);
                    Integer enterpriseRelationRemainQuotaNum = enterpriseLicenseService.getEnterpriseRelationRemainQuotaNum(upstreamEi);
                    LicenseQuotaData hasFsAccountVersionQuota = enterpriseLicenseService.getHasFsAccountVersionQuota(upstreamEi);
                    if (hasFsAccountVersionQuota.getRemainQuota() <= 0 || enterpriseRelationRemainQuotaNum <= 0) {
                        // 企业账号 模板企业只读
                        EnterpriseRelationLayoutUtil.setFieldsReadOnly(formComponent, Lists.newArrayList(EnterpriseRelationObjConstant.HAS_FS_ACCOUNT, EnterpriseRelationObjConstant.TEMPLATE_EA_SELECT_ONE_FIELD));
                    } else {
                        // 企业账号默认是
                        MapUtil.putIfNotNll(result.getObjectData(), EnterpriseRelationObjConstant.HAS_FS_ACCOUNT, true);
                    }
                    PaasControllerUtil.fillSelectOptionForBooleanField(upstreamEi, result.getObjectDescribe(), EnterpriseRelationObjConstant.HAS_FS_ACCOUNT, true);
                    handleSystemLayoutRule(layout);
                    break;
                }
                default:
                    break;
            }
        }
        // 默认选项值设置
        MapUtil.putIfNotNll(result.getObjectData(), EnterpriseRelationObjConstant.MANAGE_MODE, AccountManageMode.ASSIST_MANAGE.getType() + "");
        MapUtil.putIfNotNll(result.getObjectData(), EnterpriseRelationObjConstant.MAPPER_STATUS, EnterpriseRelationObjMapperStatusOption.NORMAL.getType() + "");
        MapUtil.putIfNotNll(result.getObjectData(), EnterpriseRelationObjConstant.REGISTER_TYPE, RegisterType.ADMIN_REGISTER.getType() + "");
        return result;
    }

    private void handleSystemLayoutRule(ILayout layout) {
        try {
            if (layout == null) {
                return;
            }
            List<Map<String, Object>> layoutRules = (List<Map<String, Object>>) layout.get("layout_rule");
            if (CollectionUtil.isEmpty(layoutRules)) {
                return;
            }
            List<Map<String, Object>> systemLayoutRules = layoutRules.stream().filter(x -> EnterpriseRelationObjConstant.SYSTEM_LAYOUT_RULE_API_NAME.equals(x.get("api_name"))).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(systemLayoutRules)) {
                return;
            }
            Map<String, Object> systemLayoutRule = systemLayoutRules.get(0);
            List<Map<String, Object>> mainFieldBranches = (List<Map<String, Object>>) systemLayoutRule.get("main_field_branches");
            for (Map<String, Object> mainFieldBranch : mainFieldBranches) {
                List<Map<String, Object>> branches = (List<Map<String, Object>>) mainFieldBranch.get("branches");
                for (Map<String, Object> branch : branches) {
                    Map<String, Object> branchResult = (Map<String, Object>) branch.get("result");
                    List<Map<String, Object>> requiredField = (List<Map<String, Object>>) branchResult.get("required_field");
                    List<Map<String, Object>> fieldApiName = requiredField.stream().filter(x -> EnterpriseRelationObjConstant.TEMPLATE_EA.equals((String) x.get("field_api_name"))).collect(Collectors.toList());
                    fieldApiName.get(0).put("field_api_name", EnterpriseRelationObjConstant.TEMPLATE_EA_SELECT_ONE_FIELD);
                }
            }
        } catch (Exception e) {
            log.warn("handleSystemLayoutRule error.", e);
        }
    }

    private void addTemplateEaSelectOneField(String ea, FormComponent formComponent, RequestContext requestContext) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(ei + "", SuperUserConstants.USER_ID + "");
        UpstreamEaArg upstreamEaArg = new UpstreamEaArg();
        upstreamEaArg.setEnterpriseAccount(ea);
        com.fxiaoke.enterpriserelation.objrest.result.ListUpdateTemplateEnterprisesVo res = enterpriseRelationObjService.listUpdateTemplateEnterprises(serviceContext, upstreamEaArg);
        List<TemplateEnterpriseData> templateVos = res.getTemplateEnterpriseDataList();
        addTemplateEaSelectOneFieldDescribe(templateVos, requestContext, result.getObjectDescribe());

        IFormField iFormField = PaasControllerUtil.newFormField(EnterpriseRelationObjConstant.TEMPLATE_EA_SELECT_ONE_FIELD, IFieldType.SELECT_ONE, false, false);
        PaasControllerUtil.addFormFields(formComponent, BASE_FIELD_SECTION, Lists.newArrayList(iFormField));
    }

    private void addTenantGroupField(String ea, FormComponent formComponent, RequestContext requestContext) {
        List<TenantGroupVo> tenantGroupVos = Lists.newArrayList();
        com.facishare.enterprise.common.result.Result<List<TenantGroupCategoryVo>> tenantGroupCategoriesResult = tenantGroupService.listTenantGroupCategories(ea);
        if (tenantGroupCategoriesResult.isSuccess() && !CollectionUtil.isEmpty(tenantGroupCategoriesResult.getData())) {
            for (TenantGroupCategoryVo tenantGroupCategoryVo : tenantGroupCategoriesResult.getData()) {
                if (!ER_PRESENT_TENANT_CATEGORY_NAME.equals(tenantGroupCategoryVo.getTenantGroupCategoryName())) {
                    tenantGroupVos.addAll(tenantGroupCategoryVo.getTenantGroups());
                }
            }
        }
        addTenantGroupIdsDescribe(tenantGroupVos, requestContext, result.getObjectDescribe());

        IFormField iFormField = PaasControllerUtil.newFormField(EnterpriseRelationObjConstant.TENANT_GROUP_IDS, IFieldType.SELECT_MANY, false, false);
        PaasControllerUtil.addFormFields(formComponent, BASE_FIELD_SECTION, Lists.newArrayList(iFormField));
    }

    public void addTenantGroupIdsDescribe(List<TenantGroupVo> tenantGroupVos, RequestContext requestContext, ObjectDescribeDocument objectDescribeDocument) {
        if (objectDescribeDocument == null) {
            return;
        }
        SelectManyFieldDescribe selectManyFieldDescribe = new SelectManyFieldDescribe();
        selectManyFieldDescribe.setApiName(EnterpriseRelationObjConstant.TENANT_GROUP_IDS);
        selectManyFieldDescribe.setLabel(
                I18nUtil.get(GetI18nKeyUtil.getFieldLabelKey(EnterpriseRelationObjConstant.API_NAME, EnterpriseRelationObjConstant.TENANT_GROUP_IDS), "企业组", requestContext.getLang().getValue()));//ignoreI18n
        selectManyFieldDescribe.setActive(true);
        selectManyFieldDescribe.setRequired(false);
        selectManyFieldDescribe.setStatus("released");
        selectManyFieldDescribe.setDefineType("package");
        selectManyFieldDescribe.setIndex(true);
        selectManyFieldDescribe.setFieldNum(null);
        selectManyFieldDescribe.setIsExtend(false);
        selectManyFieldDescribe.setSelectOptions(buildTenantGroupIdsOptions(tenantGroupVos));

        String helpTextKey = GetI18nKeyUtil.getFieldHelpTextKey(EnterpriseRelationObjConstant.API_NAME, "select_many", EnterpriseRelationObjConstant.TENANT_GROUP_IDS);
        selectManyFieldDescribe.setHelpText(I18nUtil.get(helpTextKey, "企业组"));//ignoreI18n
        ObjectDescribeExt.of(objectDescribeDocument).addFieldDescribe(selectManyFieldDescribe);
    }

    public void addTemplateEaSelectOneFieldDescribe(List<TemplateEnterpriseData> templateVos, RequestContext requestContext, ObjectDescribeDocument objectDescribeDocument) {
        if (objectDescribeDocument == null) {
            return;
        }

        SelectOneFieldDescribe selectOneFieldDescribe = new SelectOneFieldDescribe();
        selectOneFieldDescribe.setApiName(EnterpriseRelationObjConstant.TEMPLATE_EA_SELECT_ONE_FIELD);
        selectOneFieldDescribe.setLabel(
                I18nUtil.get(GetI18nKeyUtil.getFieldLabelKey(EnterpriseRelationObjConstant.API_NAME, EnterpriseRelationObjConstant.TEMPLATE_EA_SELECT_ONE_FIELD), "模板企业账号",//ignoreI18n
                        requestContext.getLang().getValue()));
        selectOneFieldDescribe.setActive(true);
        selectOneFieldDescribe.setRequired(false);
        selectOneFieldDescribe.setStatus("released");
        selectOneFieldDescribe.setDefineType("package");
        selectOneFieldDescribe.setIndex(true);
        selectOneFieldDescribe.setFieldNum(null);
        selectOneFieldDescribe.setIsExtend(false);
        selectOneFieldDescribe.setSelectOptions(buildTemplateEaSelectOneOptions(templateVos));
        String helpTextKey = GetI18nKeyUtil.getFieldHelpTextKey(EnterpriseRelationObjConstant.API_NAME, "select_one", EnterpriseRelationObjConstant.TEMPLATE_EA_SELECT_ONE_FIELD);

        selectOneFieldDescribe.setHelpText(I18nUtil.get(helpTextKey, "模板企业账号"));//ignoreI18n
        ObjectDescribeExt.of(objectDescribeDocument).addFieldDescribe(selectOneFieldDescribe);
    }

    public static List<ISelectOption> buildTenantGroupIdsOptions(List<TenantGroupVo> tenantGroupVos) {
        List<ISelectOption> options = Lists.newArrayList();
        if (CollectionUtil.isEmpty(tenantGroupVos)) {
            return options;
        }
        options = tenantGroupVos.stream().map(x -> {
            ISelectOption option = new SelectOption();
            option.setValue(x.getTenantGroupId());
            option.setLabel(x.getTenantGroupName());
            return option;
        }).collect(Collectors.toList());
        return options;
    }

    public static List<ISelectOption> buildTemplateEaSelectOneOptions(List<TemplateEnterpriseData> templateVos) {
        List<ISelectOption> options = Lists.newArrayList();
        if (CollectionUtil.isEmpty(templateVos)) {
            return options;
        }
        options = templateVos.stream().map(x -> {
            ISelectOption option = new SelectOption();
            option.setValue(x.getEnterpriseAccount());
            option.setLabel(x.getEnterpriseName());
            return option;
        }).collect(Collectors.toList());
        return options;
    }
}
