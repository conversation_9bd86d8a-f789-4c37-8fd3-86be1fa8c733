package com.facishare.rest.account.outrest;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.EnterpriseRelationObjConstant;
import com.facishare.enterprise.common.constant.OrganizationalModel;
import com.facishare.enterprise.common.model.ErDepartmentObj;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.CommonPoolUtil;
import com.facishare.erdepartment.admin.arg.*;
import com.facishare.erdepartment.admin.result.*;
import com.facishare.erdepartment.service.ErDepartmentObjService;
import com.facishare.global.rest.dao.mongo.FxiaokeEmployeeAssociationEntityDao;
import com.facishare.global.rest.dao.mongo.FxiaokeEnterpriseAssociationEntityDao;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.rest.account.common.GroupControlInfo;
import com.facishare.rest.account.dao.ErDepartmentDao;
import com.facishare.rest.account.dao.data.ErDepartmentQueryArg;
import com.facishare.rest.account.manager.ErDepartmentClientCacheManager;
import com.facishare.rest.account.manager.ErOrgManager;
import com.facishare.rest.account.outrest.arg.*;
import com.facishare.rest.account.outrest.data.BatchGetSubErDepIdsWithCascadeResult;
import com.facishare.rest.account.outrest.data.BatchGetSubErDepIdsWithCascadeResult.ParentErIdDirectSubErDepIdsData;
import com.facishare.rest.account.outrest.data.DirectSubErDepData;
import com.facishare.rest.account.outrest.data.ErDepartmentSimpleData;
import com.facishare.rest.account.outrest.data.PublicEmployeeObjOwnDepOrOrgData;
import com.facishare.rest.account.provider.treepath.TreePathMetadataService;
import com.facishare.rest.account.service.InnerErDepartmentService;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.enterpriserelation.objrest.arg.FindEnterpriseRelationByDownstreamEa;
import com.fxiaoke.enterpriserelation.objrest.arg.FindEnterpriseRelationByNameArg;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentObjConstant;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentStatusEnum;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentTypeEnum;
import com.fxiaoke.enterpriserelation.objrest.result.FindEnterpriseRelationByNameResult;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/outapi/erDepartment")
public class ErDepartmentOutController extends BaseOutController {
    @Autowired
    private ErDepartmentDao erDepartmentDao;
    @Autowired
    private TreePathMetadataService treePathMetadataService;
    @Autowired
    private ErDepartmentClientCacheManager erDepartmentClientCacheManager;
    @Autowired
    private InnerErDepartmentService erDepartmentService;
    @Autowired
    private EnterpriseRelationObjService relationObjService;
    @Autowired
    private ErDepartmentObjService erDepartmentObjService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private FxiaokeEmployeeAssociationEntityDao fxiaokeEmployeeAssociationService;
    @Autowired
    private ErOrgManager erOrgManager;
    @Autowired
    private FxiaokeEnterpriseAssociationEntityDao fxiaokeEnterpriseAssociationEntityDao;
    @Autowired
    private ServiceFacade serviceFacade;

    @RequestMapping(value = "/batchGetErDepartmentSimpleDataByIds", method = RequestMethod.POST)
    public Result<List<ErDepartmentSimpleData>> batchGetErDepartmentSimpleDataByIds(HttpServletRequest request, @RequestBody BatchGetErDepartmentSimpleDataByIdsArg arg) {
        String upstreamEi = request.getHeader("x-fs-ei");
        if (StringUtils.isEmpty(upstreamEi)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (!erDepartmentService.hasOpenErDepartmentObj(Integer.parseInt(upstreamEi))) {
            return Result.newSuccess(new ArrayList<>());
        }
        if (CollectionUtils.isEmpty(arg.getIds())) {
            return Result.newSuccess(new ArrayList<>());
        }
        List<ErDepartmentObj> erDepartmentObjs = erDepartmentDao.findObjectDataByIdsIncludeDeleted(User.systemUser(upstreamEi), arg.getIds());
        List<ErDepartmentSimpleData> res = erDepartmentObjs.stream().map(x -> {
            ErDepartmentSimpleData erDepartmentSimpleData = new ErDepartmentSimpleData();
            erDepartmentSimpleData.setName(x.getName());
            erDepartmentSimpleData.setId(x.getId());
            erDepartmentSimpleData.setPid(x.getParentId());
            erDepartmentSimpleData.setStatus(x.getStatus());
            erDepartmentSimpleData.setOrgManageType(x.getOrgManageType());
            return erDepartmentSimpleData;
        }).filter(x -> {
            if (arg.getStatus() == null) {
                return true;
            }
            return arg.getStatus().equals(x.getStatus());
        }).collect(Collectors.toList());


        Set<String> existsIds = res.stream().map(ErDepartmentSimpleData::getId).collect(Collectors.toSet());
        for (String id : arg.getIds()) {
            if (!existsIds.contains(id)) {
                ErDepartmentSimpleData data = new ErDepartmentSimpleData();
                data.setName("");
                data.setId(id);
                data.setPid("");
                data.setStatus(ErDepartmentStatusEnum.STOP.getType());
                res.add(data);
            }
        }
        CommonPoolUtil.execute(() -> {
            erDepartmentClientCacheManager.loadBatchGetErDepartmentSimpleDataByIdsClientCache(Integer.parseInt(upstreamEi), res);
        });
        return Result.newSuccess(res);
    }

    @RequestMapping(value = "/batchGetPublicEmployeeObjOwnDepOrOrg", method = RequestMethod.POST)
    public Result<List<PublicEmployeeObjOwnDepOrOrgData>> batchGetPublicEmployeeObjOwnDepOrOrg(HttpServletRequest request, @RequestBody BatchGetPublicEmployeeObjOwnDepOrOrgArg arg) {
        String upstreamEi = request.getHeader("x-fs-ei");
        if (StringUtils.isEmpty(upstreamEi)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        return erDepartmentService.batchGetPublicEmployeeObjOwnDepOrOrg(Integer.valueOf(upstreamEi), arg.getOuterAccountVos());
    }

    @RequestMapping(value = "/getOuterUidOwnerAssistErDepIds", method = RequestMethod.POST)
    public Result<List<String>> getOuterUidOwnerAssistErDepIds(HttpServletRequest request, @RequestBody GetOuterUidOwnerAssistErDepIds arg) {
        String upstreamEi = request.getHeader("x-fs-ei");
        if (StringUtils.isEmpty(upstreamEi)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (!erDepartmentService.hasOpenErDepartmentObj(Integer.parseInt(upstreamEi))) {
            return Result.newSuccess(new ArrayList<>());
        }
        ErDepartmentQueryArg erDepartmentQueryArg = new ErDepartmentQueryArg();
        if (BooleanUtils.isTrue(arg.getQueryOwner())) {
            erDepartmentQueryArg.setOwnerOuterUid(arg.getOuterUid());
        } else {
            erDepartmentQueryArg.setAssistOuterUid(arg.getOuterUid());
        }
        List<Integer> depTypes = new ArrayList<>(arg.getDepType());
        if (BooleanUtils.isTrue(arg.getIncludeEnterpriseErDep())) {
            depTypes.add(ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType());
        }
        erDepartmentQueryArg.setErDepStatus(ErDepartmentStatusEnum.NORMAL.getType());
        erDepartmentQueryArg.setUseDataPermission(false);
        erDepartmentQueryArg.setOffset(0);
        erDepartmentQueryArg.setLimit(10000);

        QueryResult<IObjectData> query = erDepartmentDao.getQuery(User.systemUser(upstreamEi), erDepartmentQueryArg);
        List<String> erDepIds = query.getData().stream().map(x -> {
            ErDepartmentObj erDepartmentObj = ErDepartmentObj.newInstance(x);
            return erDepartmentObj.getId();
        }).collect(Collectors.toList());

        CommonPoolUtil.execute(() -> {
            erDepartmentClientCacheManager.loadGetOuterUidOwnerAssistErDepIdsClientCache(Integer.parseInt(upstreamEi), arg.getOuterUid(), arg.getQueryOwner(), erDepIds);
        });
        return Result.newSuccess(erDepIds);
    }

    @RequestMapping(value = "/batchGetSubErDepIdsWithCascade", method = RequestMethod.POST)
    public Result<BatchGetSubErDepIdsWithCascadeResult> batchGetSubErDepIdsWithCascade(HttpServletRequest request, @RequestBody BatchGetSubErDepIdsWithCascadeArg arg) {
        String upstreamEi = request.getHeader("x-fs-ei");
        if (StringUtils.isEmpty(upstreamEi)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        BatchGetSubErDepIdsWithCascadeResult batchGetSubErDepIdsWithCascadeResult = new BatchGetSubErDepIdsWithCascadeResult();
        batchGetSubErDepIdsWithCascadeResult.setParentErDepIdDirectSubErDepIdsDatas(Lists.newArrayList());

        if (!erDepartmentService.hasOpenErDepartmentObj(Integer.parseInt(upstreamEi))) {
            return Result.newSuccess(batchGetSubErDepIdsWithCascadeResult);
        }
        if (CollectionUtils.isEmpty(arg.getParentErDepIds())) {
            return Result.newSuccess(batchGetSubErDepIdsWithCascadeResult);
        }
        ErDepartmentQueryArg erDepartmentQueryArg = new ErDepartmentQueryArg();
        erDepartmentQueryArg.setParentIds(new ArrayList<>(arg.getParentErDepIds()));
        erDepartmentQueryArg.setUseDataPermission(false);
        erDepartmentQueryArg.setOffset(0);
        erDepartmentQueryArg.setLimit(5000);

        QueryResult<IObjectData> directSubErDeps = erDepartmentDao.getQuery(User.systemUser(upstreamEi + ""), erDepartmentQueryArg);
        Set<String> directSubErDepIds = directSubErDeps.getData().stream().map(IObjectData::getId).collect(Collectors.toSet());
        List<IObjectData> allChildErDepartmentObjs = treePathMetadataService.getDataListByIdsInPath(User.systemUser(upstreamEi), ErDepartmentObjConstant.API_NAME, new ArrayList<>(directSubErDepIds));
        List<ErDepartmentObj> erDepartmentObjs = ErDepartmentObj.newObjInstance(allChildErDepartmentObjs);

        batchGetSubErDepIdsWithCascadeResult = convertToTree(erDepartmentObjs);
        List<ParentErIdDirectSubErDepIdsData> parentErDepIdDirectSubErDepIdsDatas = batchGetSubErDepIdsWithCascadeResult.getParentErDepIdDirectSubErDepIdsDatas();

        Set<String> erDepIds = parentErDepIdDirectSubErDepIdsDatas.stream().map(ParentErIdDirectSubErDepIdsData::getParentErDepId).collect(Collectors.toSet());
        for (String parentErDepId : arg.getParentErDepIds()) {
            if (!erDepIds.contains(parentErDepId)) {
                ParentErIdDirectSubErDepIdsData data = new ParentErIdDirectSubErDepIdsData();
                data.setParentErDepId(parentErDepId);
                data.setDirectSubErDepDatas(Lists.newArrayList());
                parentErDepIdDirectSubErDepIdsDatas.add(data);
            }
        }
        CommonPoolUtil.execute(() -> {
            erDepartmentClientCacheManager.loadBatchGetSubErDepIdsWithCascadeClientCache(Integer.parseInt(upstreamEi), parentErDepIdDirectSubErDepIdsDatas);
        });
        return Result.newSuccess(batchGetSubErDepIdsWithCascadeResult);
    }

    /**
     * 检测某个企业是否开通互联部门对象
     *
     * @param request 请求上下文
     * @return 是否开通互联部门
     */
    @RequestMapping(value = "/checkHasOpenErDepartment", method = RequestMethod.POST)
    public Result<Boolean> checkHasOpenErDepartment(HttpServletRequest request) {
        String upstreamEi = request.getHeader("x-fs-ei");
        if (StringUtils.isEmpty(upstreamEi)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        boolean hasOpenedErDepartment = erDepartmentService.isErDepartmentSwitchOpened(Integer.valueOf(upstreamEi));
        log.info("checkHasOpenErDepartment upstreamEi={}, openStatus={}", upstreamEi, hasOpenedErDepartment);
        return Result.newSuccess(hasOpenedErDepartment);
    }

    public static BatchGetSubErDepIdsWithCascadeResult convertToTree(List<ErDepartmentObj> erDepartmentObjs) {
        BatchGetSubErDepIdsWithCascadeResult result = new BatchGetSubErDepIdsWithCascadeResult();
        result.setParentErDepIdDirectSubErDepIdsDatas(new ArrayList<>());
        Map<String, ParentErIdDirectSubErDepIdsData> dataMap = new HashMap<>();

        for (ErDepartmentObj data : erDepartmentObjs) {
            if (StringUtils.isEmpty(data.getParentId())) {
                continue;
            }
            ParentErIdDirectSubErDepIdsData parentData = dataMap.get(data.getParentId());
            if (parentData == null) {
                parentData = new ParentErIdDirectSubErDepIdsData();
                parentData.setParentErDepId(data.getParentId());
                parentData.setDirectSubErDepDatas(new ArrayList<>());
                dataMap.put(data.getParentId(), parentData);
            }

            DirectSubErDepData subDepData = new DirectSubErDepData();
            subDepData.setDepartmentType(data.getDepartmentType());
            subDepData.setErDepId(data.getId());
            subDepData.setIsLeaf(false);
            subDepData.setStatus(data.getStatus());
            subDepData.setOrgManageTypes(data.getOrgManageType());
            parentData.getDirectSubErDepDatas().add(subDepData);
        }

        // 叶子节点无子节点
        for (ParentErIdDirectSubErDepIdsData value : dataMap.values()) {
            for (DirectSubErDepData directSubErDepData : value.getDirectSubErDepDatas()) {
                String erDepId = directSubErDepData.getErDepId();
                ParentErIdDirectSubErDepIdsData parentErIdDirectSubErDepIdsData = dataMap.get(erDepId);
                if (parentErIdDirectSubErDepIdsData == null || CollectionUtils.isEmpty(parentErIdDirectSubErDepIdsData.getDirectSubErDepDatas())) {
                    directSubErDepData.setIsLeaf(true);
                }
            }
        }

        for (ParentErIdDirectSubErDepIdsData parentData : dataMap.values()) {
            result.getParentErDepIdDirectSubErDepIdsDatas().add(parentData);
        }

        return result;
    }


    @PostMapping("/listErDepartmentByUpstreamTenant")
    public Result<ListErDepartmentWithPageResult> listErDepartmentByUpstreamTenant(@RequestHeader("x-fs-ei") String currentTenantId, @RequestBody ListErDepartmentByUpstreamEaArg arg) {
        if (StringUtils.isEmpty(currentTenantId) || ObjectUtils.isEmpty(arg) || StringUtils.isBlank(arg.getUpstreamTenantId())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String downstreamEa = eieaConverter.enterpriseIdToAccount(Integer.valueOf(currentTenantId));
        ServiceContext serviceContext = erDepartmentService.createServiceContext(arg.getUpstreamTenantId(), SuperUserConstants.USER_ID.toString());
        FindEnterpriseRelationByDownstreamEa checkArg = new FindEnterpriseRelationByDownstreamEa();
        checkArg.setDownstreamEa(downstreamEa);

        com.fxiaoke.enterpriserelation.objrest.result.Result<Map<String, Object>> relationObjectResult = relationObjService.findEnterpriseRelationByDownstreamEa(serviceContext, checkArg);
        if (!relationObjectResult.isSuccess()) {
            return Result.newError(relationObjectResult.getErrCode(), relationObjectResult.getErrMessage());
        }

        arg.validate();
        if (arg.getPageSize() > 1000) {
            arg.setPageSize(1000);
        }

        String upstreamEa = eieaConverter.enterpriseIdToAccount(Integer.valueOf(arg.getUpstreamTenantId()));
        ListErDepartmentWithPageArg listErDepartmentWithPageArg = new ListErDepartmentWithPageArg();
        listErDepartmentWithPageArg.setPageSize(arg.getPageSize());
        listErDepartmentWithPageArg.setPageNumber(arg.getPageNumber());
        listErDepartmentWithPageArg.setOuterTenantId(MapUtils.getString(relationObjectResult.getData(), EnterpriseRelationObjConstant.DEST_OUTER_TENANT_ID));
        return erDepartmentObjService.listErDepartmentWithPage(upstreamEa, SuperUserConstants.USER_ID, listErDepartmentWithPageArg);
    }


    @RequestMapping("/updateDeptManagerAssistantAndUpdateEmployeeMainErDept")
    public Result<Void> updateDeptManagerAssistantAndUpdateEmployeeMainErDept(@RequestHeader("x-fs-ei") String tenantId, @RequestBody UpdateDeptManagerAssistantAndUpdateEmployeeMainErDeptArg arg) {
        if (ObjectUtils.isEmpty(tenantId)
                || ObjectUtils.isEmpty(arg)
                || ObjectUtils.isEmpty(arg.getUpstreamTenantId())
                || ObjectUtils.isEmpty(arg.getErDepartmentId())
                || ObjectUtils.isEmpty(arg.getAssistantStrategy())
                || ObjectUtils.isEmpty(arg.getUserId())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        ServiceContext serviceContext = erDepartmentService.createServiceContext(arg.getUpstreamTenantId(), SuperUserConstants.USER_ID.toString());
        FindEnterpriseRelationByDownstreamEa checkArg = new FindEnterpriseRelationByDownstreamEa();
        String downstreamEa = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        checkArg.setDownstreamEa(downstreamEa);
        com.fxiaoke.enterpriserelation.objrest.result.Result<Map<String, Object>> enterpriseRelationResult = relationObjService.findEnterpriseRelationByDownstreamEa(serviceContext, checkArg);
        if (!enterpriseRelationResult.isSuccess()) {
            return Result.newError(enterpriseRelationResult.getErrCode(), enterpriseRelationResult.getErrMessage());
        }

        Long outerUid = fxiaokeEmployeeAssociationService.getOuterUid(downstreamEa, arg.getUserId());
        if (outerUid == null) {
            return Result.newError(ResultCode.EMPLOYEE_NOT_EXIST.getErrorCode(), ResultCode.EMPLOYEE_NOT_EXIST.getDescription());
        }

        com.fxiaoke.enterpriserelation.objrest.result.Result<Void> result = erDepartmentService.updateDeptManagerAssistantAndUpdateEmployeeMainErDept(arg.getUpstreamTenantId(), outerUid, arg.getErDepartmentId(), arg.isUpdateDepartmentManager(), arg.getAssistantStrategy());
        if (result.isSuccess()) {
            return Result.newSuccess();
        }

        return Result.newError(result.getErrCode(), result.getErrMessage());
    }

    /**
     * Paas侧对象数据导入场景使用 @杨华国
     * https://wiki.firstshare.cn/pages/viewpage.action?pageId=543752427
     *
     * @param request 请求来源
     * @param arg     查询参数
     * @return 查询到的互联部门信息
     */
    @PostMapping("/queryErDepartmentByDepartmentNames")
    public Result<ErDepartmentSimpleDataResult> queryErDepartmentByDepartmentNames(HttpServletRequest request, @RequestBody BatchQueryErDepartmentByNames arg) {
        User user = getUserInfo(request);
        String tenantId = user.getTenantId();
        if (arg == null || StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(arg.getNames())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Result<ErDepartmentSimpleDataResult> result = Result.newSuccess();
        try {
            String upstreamEa = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
            if (!CollectionUtils.isEmpty(arg.getOuterTenantNames()) && CollectionUtils.isEmpty(arg.getOuterTenantIds())) {
                Map<Long, String> outerTenantIdsMap = erDepartmentObjService.batGetOuterTenantIdByOuterTenantNames(Integer.valueOf(tenantId), arg.getOuterTenantNames());
                if (outerTenantIdsMap != null && !outerTenantIdsMap.isEmpty()) {
                    arg.setOuterTenantIds(new ArrayList<>(outerTenantIdsMap.keySet()));
                }
            }
            result = erDepartmentObjService.batchQueryErDepartmentByNames(upstreamEa, SuperUserConstants.USER_ID, arg);
        } catch (NumberFormatException | NullPointerException e) {
            // 处理异常，例如记录日志或返回默认值
            return Result.newError(ResultCode.PARAMS_ERROR);
        } catch (Exception ex) {
            return Result.newError(ResultCode.SERVER_BUSY);
        }
        return result;
    }

    @PostMapping("/batchGetErDepartmentInfoByNames")
    public Result<BatchGetErDepartmentInfoByNameResult> batchGetOuterUserByDepartmentName(HttpServletRequest request, @RequestBody BatchGetErDepartmentInfoByNameArg arg) {
        User userInfo = getUserInfo(request);
        if (StringUtils.isBlank(userInfo.getTenantId())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        User user = User.systemUser(userInfo.getTenantId());
        BatchGetErDepartmentInfoByNameResult result = new BatchGetErDepartmentInfoByNameResult();
        result.setOuterErDeptInfoList(ImmutableList.of());

        if (ObjectUtils.isEmpty(arg.getParams())) {
            return Result.newSuccess(result);
        }
        if (!erDepartmentService.hasOpenErDepartmentObj(userInfo.getTenantIdInt())) {
            return Result.newSuccess(result);
        }

        Set<String> outerDepartmentNames = new HashSet<>();
        Set<String> outerTenantNames = new HashSet<>();
        for (BatchGetErDepartmentInfoByNameArg.Param param : arg.getParams()) {
            if (ObjectUtils.isEmpty(param.getOuterDepartmentName())
                    || ObjectUtils.isEmpty(param.getOuterTenantName())) {
                continue;
            }
            outerTenantNames.add(param.getOuterTenantName());
            outerDepartmentNames.add(param.getOuterDepartmentName());
        }

        ServiceContext serviceContext = relationObjService.createServiceContext(user.getTenantId(), user.getUserId());
        FindEnterpriseRelationByNameArg findEnterpriseRelationByNameArg = new FindEnterpriseRelationByNameArg();
        findEnterpriseRelationByNameArg.setNames(Lists.newArrayList(outerTenantNames));
        FindEnterpriseRelationByNameResult findEnterpriseRelationByNameResult = relationObjService.findEnterpriseRelationByName(serviceContext, findEnterpriseRelationByNameArg);
        if (ObjectUtils.isEmpty(findEnterpriseRelationByNameResult.getEnterpriseRelationInfoList())) {
            return Result.newSuccess(result);
        }


        Map<Long, String> outerTenantId2Name = new HashMap<>();
        Set<Long> outerTenantIds = new HashSet<>();
        for (FindEnterpriseRelationByNameResult.EnterpriseRelationInfo enterpriseRelationInfo : findEnterpriseRelationByNameResult.getEnterpriseRelationInfoList()) {
            Long outerTenantId = enterpriseRelationInfo.getOuterTenantId();
            outerTenantIds.add(outerTenantId);
            outerTenantId2Name.put(enterpriseRelationInfo.getOuterTenantId(), enterpriseRelationInfo.getEnterpriseName());
        }
        if (ObjectUtils.isEmpty(outerTenantIds)) {
            return Result.newSuccess(result);
        }

        List<ErDepartmentObj> erDepartmentObjList = erDepartmentService.batchGetErDepartmentByNameAndStatus(user, Lists.newArrayList(outerTenantIds), Lists.newArrayList(outerDepartmentNames), arg.getStatus(), Lists.newArrayList(ErDepartmentObjConstant.ID, ErDepartmentObjConstant.NAME, ErDepartmentObjConstant.OUTER_TENANT_ID, ErDepartmentObjConstant.STATUS));
        if (ObjectUtils.isEmpty(erDepartmentObjList)) {
            return Result.newSuccess(result);
        }

        Map<String, Set<String>> outerTenantName2DepartmentNameList = arg.getParams().stream()
                .filter(it -> ObjectUtils.isNotEmpty(it.getOuterTenantName()) && ObjectUtils.isNotEmpty(it.getOuterDepartmentName()))
                .collect(Collectors.groupingBy(BatchGetErDepartmentInfoByNameArg.Param::getOuterTenantName, Collectors.mapping(BatchGetErDepartmentInfoByNameArg.Param::getOuterDepartmentName, Collectors.toSet())));

        List<BatchGetErDepartmentInfoByNameResult.OuterErDeptInfo> outerErDeptInfoList = new ArrayList<>();
        for (ErDepartmentObj erDepartmentObj : erDepartmentObjList) {
            String outerTenantName = outerTenantId2Name.get(erDepartmentObj.getOuterTenantId());
            if (ObjectUtils.isEmpty(outerTenantName)) {
                continue;
            }
            if (!outerTenantName2DepartmentNameList.getOrDefault(outerTenantName, ImmutableSet.of()).contains(erDepartmentObj.getName())) {
                continue;
            }
            BatchGetErDepartmentInfoByNameResult.OuterErDeptInfo outerErDeptInfo = new BatchGetErDepartmentInfoByNameResult.OuterErDeptInfo();
            outerErDeptInfo.setOuterTenantId(erDepartmentObj.getOuterTenantId());
            outerErDeptInfo.setOuterDepartmentId(erDepartmentObj.getId());
            outerErDeptInfo.setOuterDepartmentName(erDepartmentObj.getName());
            outerErDeptInfo.setStatus(erDepartmentObj.getStatus());
            outerErDeptInfo.setOuterTenantName(outerTenantName);
            outerErDeptInfoList.add(outerErDeptInfo);
        }

        result.setOuterErDeptInfoList(outerErDeptInfoList);

        return Result.newSuccess(result);
    }


    @PostMapping("/findOuterOrgErDepartmentIdByUserInManagerOrAssistant")
    public Result<FindOuterDepartmentIdByUserInManagerOrAssistantResult> findOuterDepartmentIdByUserInManagerOrAssistant(HttpServletRequest request, @RequestBody FindOuterDepartmentIdByUserInManagerOrAssistantArg arg) {
        User userInfo = getUserInfo(request);
        if (StringUtils.isBlank(userInfo.getTenantId()) || CollectionUtils.isEmpty(arg.getOuterUserIds())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        FindOuterDepartmentIdByUserInManagerOrAssistantResult result = new FindOuterDepartmentIdByUserInManagerOrAssistantResult();
        result.setDeptDataMap(ImmutableMap.of());
        if (ObjectUtils.isEmpty(arg.getOuterUserIds())) {
            return Result.newSuccess(result);
        }
        if (!erDepartmentService.isErDepartmentSwitchOpened(userInfo.getTenantIdInt())) {
            return Result.newSuccess(result);
        }

        IObjectDescribe erDepartmentDescribe = serviceFacade.findObjectWithoutCopy(userInfo.getTenantId(), ErDepartmentObjConstant.API_NAME);
        if (!erDepartmentDescribe.isPublicObject()) {
            return Result.newSuccess(result);
        }

        GroupControlInfo groupItControlInfo = erOrgManager.getGroupItControlInfo(erDepartmentDescribe.getUpstreamTenantId());
        if (!groupItControlInfo.isEnable()) {
            return Result.newSuccess(result);
        }
        OrganizationalModel organizationalModel = OrganizationalModel.of(groupItControlInfo.getOrganizationalModel());
        ErDepartmentQueryArg erDepartmentQueryArg = new ErDepartmentQueryArg();
        erDepartmentQueryArg.setOwnerOrAssistOutUids(arg.getOuterUserIds());
        erDepartmentQueryArg.setOffset(0);
        erDepartmentQueryArg.setLimit(Integer.MAX_VALUE);
        erDepartmentQueryArg.setErDepTypes(Lists.newArrayList(ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType(), ErDepartmentTypeEnum.DEPARTMENT.getType()));
        erDepartmentQueryArg.setOrgManageTypes(Lists.newArrayList(organizationalModel.getType()));
        erDepartmentQueryArg.setSelectFields(Lists.newArrayList(ErDepartmentObjConstant.ID, ErDepartmentObjConstant.STATUS, ErDepartmentObjConstant.MANAGER_ID, ErDepartmentObjConstant.ASSISTANT_IDS));

        QueryResult<IObjectData> departmentQueryResult = erDepartmentDao.getQuery(User.systemUser(userInfo.getTenantId()), erDepartmentQueryArg);
        if (ObjectUtils.isEmpty(departmentQueryResult)) {
            return Result.newSuccess(result);
        }

        Map<Long, Set<SimpleErDepartmentData>> deptDataMap = new HashMap<>();
        List<SimpleErDepartmentData> dataList = new ArrayList<>();
        for (IObjectData data : departmentQueryResult.getData()) {
            SimpleErDepartmentData singleDepartmentData = new SimpleErDepartmentData();
            ErDepartmentObj erDepartmentObj = ErDepartmentObj.newInstance(data);
            singleDepartmentData.setErDeptId(erDepartmentObj.getId());
            singleDepartmentData.setStatus(erDepartmentObj.getStatus());
            dataList.add(singleDepartmentData);


            Long managerId = erDepartmentObj.getManagerId();
            if (ObjectUtils.isNotEmpty(managerId)) {
                deptDataMap.computeIfAbsent(managerId, k -> new HashSet<>()).add(singleDepartmentData);
            }
            if (ObjectUtils.isNotEmpty(erDepartmentObj.getAssistantIds())) {
                erDepartmentObj.getAssistantIds().forEach(assistantId -> {
                    deptDataMap.computeIfAbsent(assistantId, k -> new HashSet<>()).add(singleDepartmentData);
                });
            }
        }

        result.setDeptDataMap(deptDataMap);
        CommonPoolUtil.execute(() -> {
            erDepartmentClientCacheManager.loadBatchGetOrgErDepIdsByOwnerOrAssistOuterUid(userInfo.getTenantIdInt(), deptDataMap);
        });

        return Result.newSuccess(result);
    }


    @RequestMapping("/findOuterOrgDepartmentIdByInnerDepartmentId")
    public Result<FindOuterOrgDepartmentIdByInnerDepartmentIdResult> findOuterOrgDepartmentIdByInnerDepartmentId(HttpServletRequest httpServletRequest, @RequestBody FindOuterOrgDepartmentIdByInnerDepartmentIdArg arg) {
        User userInfo = getUserInfo(httpServletRequest);
        if (ObjectUtils.isEmpty(userInfo.getTenantId()) || ObjectUtils.isEmpty(arg.getDepartmentIds())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        FindOuterOrgDepartmentIdByInnerDepartmentIdResult result = new FindOuterOrgDepartmentIdByInnerDepartmentIdResult();
        result.setDeptId2ErDept(ImmutableMap.of());
        if (!erDepartmentService.isErDepartmentSwitchOpened(userInfo.getTenantIdInt())) {
            return Result.newSuccess(result);
        }

        IObjectDescribe erDepartmentDescribe = serviceFacade.findObjectWithoutCopy(userInfo.getTenantId(), ErDepartmentObjConstant.API_NAME);
        if (!erDepartmentDescribe.isPublicObject()) {
            return Result.newSuccess(result);
        }

        GroupControlInfo groupItControlInfo = erOrgManager.getGroupItControlInfo(erDepartmentDescribe.getUpstreamTenantId());
        if (!groupItControlInfo.isEnable()) {
            return Result.newSuccess(result);
        }

        String ea = eieaConverter.enterpriseIdToAccount(userInfo.getTenantIdInt());
        Long outerTenantId = fxiaokeEnterpriseAssociationEntityDao.getOuterTenantIdByEa(ea);

        OrganizationalModel organizationalModel = OrganizationalModel.of(groupItControlInfo.getOrganizationalModel());
        ErDepartmentQueryArg erDepartmentQueryArg = new ErDepartmentQueryArg();
        erDepartmentQueryArg.setOrgManageTypes(Lists.newArrayList(organizationalModel.getType()));
        erDepartmentQueryArg.setOuterTenantDeptIds(arg.getDepartmentIds());
        erDepartmentQueryArg.setOuterTenantIds(Lists.newArrayList(outerTenantId));
        erDepartmentQueryArg.setSelectFields(Lists.newArrayList(ErDepartmentObjConstant.ID, ErDepartmentObjConstant.OUTER_TENANT_DEPT_ID, ErDepartmentObjConstant.STATUS, ErDepartmentObjConstant.OUTER_TENANT_ID));
        QueryResult<IObjectData> query = erDepartmentDao.getQuery(User.systemUser(userInfo.getTenantId()), erDepartmentQueryArg);
        if (ObjectUtils.isEmpty(query.getData())) {
            return Result.newSuccess(result);
        }

        Map<String, SimpleOrgErDepartmentData> deptId2ErDeptId = new HashMap<>();
        for (IObjectData objectData : query.getData()) {
            ErDepartmentObj erDepartmentObj = ErDepartmentObj.newInstance(objectData);
            SimpleOrgErDepartmentData simpleErDepartmentData = new SimpleOrgErDepartmentData();
            simpleErDepartmentData.setErDeptId(erDepartmentObj.getId());
            simpleErDepartmentData.setStatus(erDepartmentObj.getStatus());
            simpleErDepartmentData.setOuterTenantId(erDepartmentObj.getOuterTenantId());
            deptId2ErDeptId.put(erDepartmentObj.getOuterTenantDeptId(), simpleErDepartmentData);
        }
        result.setDeptId2ErDept(deptId2ErDeptId);
        CommonPoolUtil.execute(() -> {
            erDepartmentClientCacheManager.loadFindOuterOrgDepartmentIdByInnerDepartmentId(userInfo.getTenantIdInt(), deptId2ErDeptId);
        });
        return Result.newSuccess(result);
    }

}
