package com.facishare.rest.account.outrest;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.EnterpriseRelationObjConstant;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.ListUtil;
import com.facishare.global.rest.common.HeaderObj;
import com.facishare.global.rest.dao.mongo.FxiaokeEmployeeAssociationEntityDao;
import com.facishare.global.rest.dao.mongo.FxiaokeEnterpriseAssociationEntityDao;
import com.facishare.global.rest.model.entity.FxiaokeEmployeeAssociationEntity;
import com.facishare.global.rest.service.GlobalService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.rest.account.dao.EnterpriseRelationDao;
import com.facishare.rest.account.dao.PublicEmployeeDao;
import com.facishare.rest.account.dao.data.EnterpriseRelationQueryArg;
import com.facishare.rest.account.dao.data.PublicEmployeeQueryArg;
import com.facishare.rest.account.outrest.arg.*;
import com.facishare.rest.account.outrest.data.RelationEmployeeIdInfo;
import com.facishare.rest.account.outrest.data.SimplePublicEmployeeObjData;
import com.facishare.rest.account.outrest.data.UpstreamPublicEmployeeData;
import com.facishare.rest.account.outrest.result.BatchGetOutUserInfoByNameResult;
import com.facishare.rest.account.outrest.result.ListDownstreamEmployeeIdInfo;
import com.facishare.rest.account.outrest.result.SearchSimplePublicEmployeeObjsByNameResult;
import com.facishare.rest.account.outrest.result.SearchUpstreamPublicEmployeesByNameResult;
import com.facishare.rest.account.service.InnerErDepartmentService;
import com.facishare.rest.account.service.PublicEmployeeService;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.enterpriserelation.objrest.arg.FindEnterpriseRelationByDownstreamEa;
import com.fxiaoke.enterpriserelation.objrest.arg.FindEnterpriseRelationByNameArg;
import com.fxiaoke.enterpriserelation.objrest.arg.FindPublicEmployeeByNameArg;
import com.fxiaoke.enterpriserelation.objrest.common.LikeOrQueryArg;
import com.fxiaoke.enterpriserelation.objrest.result.FindEnterpriseRelationByNameResult;
import com.fxiaoke.enterpriserelation.objrest.result.FindPublicEmployeeByNameResult;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.fxiaoke.enterpriserelation.objrest.service.PublicEmployeeObjService;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: jiangxch
 * @date: 2024/1/30 10:19
 */
@Slf4j
@RestController
@RequestMapping("/outapi/publicEmployeeObj/")
public class PublicEmployeeObjOutController extends BaseOutController {
    @Autowired
    private PublicEmployeeDao publicEmployeeDao;
    @Autowired
    private EnterpriseRelationDao enterpriseRelationDao;
    @Autowired
    private GlobalService globalService;
    @Autowired
    private FxiaokeEmployeeAssociationEntityDao fxiaokeEmployeeAssociationService;
    @Autowired
    private FxiaokeEnterpriseAssociationEntityDao fxiaokeEnterpriseAssociationService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EnterpriseRelationObjService relationObjService;
    @Autowired
    private PublicEmployeeService publicEmployeeService;
    @Autowired
    private InnerErDepartmentService erDepartmentService;
    @Autowired
    private PublicEmployeeObjService publicEmployeeObjService;

    /**
     * 根据企业名称和员工名称获取 外部企业id和外部用户id 数据示例：["企业1.员工1","企业2.员工2"] 在导入时使用 张晓峰依赖
     */
    @PostMapping("/batchGetPublicEmployeeIdInfoByName")
    public Result<ListDownstreamEmployeeIdInfo> batchGetPublicEmployeeIdInfoByName(@RequestBody BatchGetPublicEmployeeIdInfoArg arg) {
        ListDownstreamEmployeeIdInfo listDownstreamEmployeeIdInfo = new ListDownstreamEmployeeIdInfo();
        List<RelationEmployeeIdInfo> employeeIdInfos = new ArrayList<>();

        User user = User.systemUser(arg.getUpstreamEi() + "");

        Map<String, List<String>> enterpriseName2EmployeeNamesMap = getEnterpriseName2EmployeeNamesMap(arg.getNames());

        List<String> allEmployeeNameList = new ArrayList<>();

        log.info("getEnterpriseName2EmployeeNamesMap:{}", enterpriseName2EmployeeNamesMap);
        if (enterpriseName2EmployeeNamesMap.size() > 0) {
            EnterpriseRelationQueryArg enterpriseRelationQueryArg = new EnterpriseRelationQueryArg();
            enterpriseRelationQueryArg.setEnterpriseNames(enterpriseName2EmployeeNamesMap.keySet());

            List<IObjectData> iObjectDataList = enterpriseRelationDao.listByQuery(user, enterpriseRelationQueryArg);
            log.info("enterpriseRelationDao.listByQuery:{}", iObjectDataList);

            List<Long> allOuterTenantIds = new ArrayList<>(iObjectDataList.size());

            // 用于区别在一家公司里面的唯一的员工，避免查出A企业的Id，但是B企业的员工name出来。优化性能只调用一次员工远程调用。
            Map<String, String> employeeKeyMap = new HashMap<>();

            Map<Long, String> enterpriseNameMap = new HashMap<>();

            for (IObjectData iObjectData : iObjectDataList) {
                EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(iObjectData);
                List<String> employeeNames = enterpriseName2EmployeeNamesMap.get(enterpriseRelationObj.getName());
                if (CollectionUtils.isEmpty(employeeNames)) {
                    log.warn("employeeNames 为空，enterpriseRelationObj.name:{}", enterpriseRelationObj.getName());
                    continue;
                }

                for (String employeeName : employeeNames) {
                    employeeKeyMap.put(enterpriseRelationObj.getOuterTenantId() + employeeName, "");
                }

                allOuterTenantIds.add(enterpriseRelationObj.getOuterTenantId());
                allEmployeeNameList.addAll(employeeNames);
                enterpriseNameMap.put(enterpriseRelationObj.getOuterTenantId(), enterpriseRelationObj.getName());
            }

            if (CollectionUtils.isNotEmpty(allOuterTenantIds) && CollectionUtils.isNotEmpty(allEmployeeNameList)) {
                PublicEmployeeQueryArg publicEmployeeQueryArg = new PublicEmployeeQueryArg();
                publicEmployeeQueryArg.setOuterTenantIds(allOuterTenantIds);
                publicEmployeeQueryArg.setEmployeeNames(allEmployeeNameList);

                List<IObjectData> objectDatas = publicEmployeeDao.listByQuery(user, publicEmployeeQueryArg);

                List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newObjInstance(objectDatas);
                for (PublicEmployeeObj publicEmployeeObj : publicEmployeeObjs) {
                    // 企业和员工没对应上的数据过滤掉
                    if (!employeeKeyMap.containsKey(publicEmployeeObj.getOuterTenantId() + publicEmployeeObj.getName())) {
                        log.info("not match outerUserId:{}, outerTenantId:{} name:{}", publicEmployeeObj.getOuterUserId(), publicEmployeeObj.getOuterTenantId(), publicEmployeeObj.getName());
                        continue;
                    }

                    RelationEmployeeIdInfo relationEmployeeIdInfo = new RelationEmployeeIdInfo();
                    relationEmployeeIdInfo.setOuterTenantId(publicEmployeeObj.getOuterTenantId());
                    relationEmployeeIdInfo.setOuterUserId(publicEmployeeObj.getOuterUserId());
                    relationEmployeeIdInfo.setUpstreamEi(arg.getUpstreamEi());
                    relationEmployeeIdInfo.setEmployeeName(publicEmployeeObj.getName());
                    relationEmployeeIdInfo.setEnterpriseName(enterpriseNameMap.get(publicEmployeeObj.getOuterTenantId()));
                    employeeIdInfos.add(relationEmployeeIdInfo);
                }
            }
        }

        listDownstreamEmployeeIdInfo.setEmployeeIdInfos(employeeIdInfos);
        return Result.newSuccess(listDownstreamEmployeeIdInfo);
    }

    public static Map<String, List<String>> getEnterpriseName2EmployeeNamesMap(List<String> names) {
        Map<String, List<String>> map = new HashMap<>();

        if (CollectionUtils.isNotEmpty(names)) {
            for (String name : names) {
                String[] nameArray = name.split("\\.");
                if (nameArray.length == 2) {
                    String enterpriseName = nameArray[0];
                    String employeeName = nameArray[1];
                    if (!map.containsKey(enterpriseName)) {
                        map.put(enterpriseName, new ArrayList<>());
                    }
                    map.get(enterpriseName).add(employeeName);
                } else {
                    log.warn("格式不正确：{}", name);
                }
            }
        }

        return map;
    }

    /**
     * 企业会话列表关键词搜索场景 @梁岩超
     * 搜索下游互联用户数据
     *
     * @param arg 上游企业员工身份
     * @return 下游互联用户列表
     */
    @PostMapping("/searchSimplePublicEmployeeObjsByName")
    public Result<SearchSimplePublicEmployeeObjsByNameResult> searchSimplePublicEmployeeObjsByName(@RequestBody SearchSimplePublicEmployeeObjsByNameArg arg) {
        User user = User.builder().tenantId(arg.getTenantId() + "").userId(arg.getUserId()).outUserId(arg.getOutUserId()).outTenantId(arg.getOutTenantId()).build();
        PublicEmployeeQueryArg publicEmployeeQueryArg = new PublicEmployeeQueryArg();
        SearchSimplePublicEmployeeObjsByNameResult res = new SearchSimplePublicEmployeeObjsByNameResult();
        res.setDatas(new ArrayList<>());

        if (CollectionUtils.isEmpty(arg.getSearchNames())) {
            return Result.newSuccess(res);
        }

        List<LikeOrQueryArg> likeOrQueryArgs = arg.getSearchNames().stream().map(x -> {
            LikeOrQueryArg likeOrQueryArg = new LikeOrQueryArg();
            likeOrQueryArg.setFieldApiNames(Lists.newArrayList(PublicEmployeeObjConstant.MOBILE, PublicEmployeeObjConstant.NAME, PublicEmployeeObjConstant.NAME_SPELL,
                    PublicEmployeeObjConstant.OUTER_TENANT_ID + "." + EnterpriseRelationObjConstant.NAME));
            likeOrQueryArg.setLikeStr(x);
            return likeOrQueryArg;
        }).collect(Collectors.toList());

        publicEmployeeQueryArg.setLikeOrQueryArgs(likeOrQueryArgs);
        publicEmployeeQueryArg.setUseDataPermission(true);
        publicEmployeeQueryArg.setFetchEnterpriseName(true);
        if (user.isOutUser()) {
            publicEmployeeQueryArg.setHasOuterPermissionType(true);
        }
        QueryResult<IObjectData> query = publicEmployeeDao.getQuery(user, publicEmployeeQueryArg);
        if (CollectionUtils.isEmpty(query.getData())) {
            return Result.newSuccess(res);
        }
        Set<Long> outerTenantIds = query.getData().stream().map(PublicEmployeeObj::newInstance).map(PublicEmployeeObj::getOuterTenantId).collect(Collectors.toSet());
        final Map<Long, String> outerTenantIdEaMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(outerTenantIds)) {
            EnterpriseRelationQueryArg enterpriseRelationQueryArg = new EnterpriseRelationQueryArg();
            enterpriseRelationQueryArg.setDestOuterTenantIds(ListUtil.toStringList(outerTenantIds));
            enterpriseRelationQueryArg.setUseDataPermission(false);
            List<IObjectData> accountEnterpriseObjs = enterpriseRelationDao.getQueryResult(User.systemUser(user.getTenantId()), enterpriseRelationQueryArg).getData();
            if (CollectionUtils.isNotEmpty(accountEnterpriseObjs)) {
                Map<Long, String> outerTenantIdEaMapTmp = accountEnterpriseObjs.stream().map(EnterpriseRelationObj::newInstance)
                        .collect(Collectors.toMap(EnterpriseRelationObj::getOuterTenantId, x -> x.getEnterpriseAccount() == null ? "" : x.getEnterpriseAccount()));
                outerTenantIdEaMap.putAll(outerTenantIdEaMapTmp);
            }
        }
        List<SimplePublicEmployeeObjData> datas = query.getData().stream().map(x -> {
            PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(x);
            SimplePublicEmployeeObjData data = new SimplePublicEmployeeObjData();
            data.setPublicEmployeeName(publicEmployeeObj.getName());
            data.setOuterUid(publicEmployeeObj.getOuterUid());
            data.setEmployeeId(publicEmployeeObj.getEmployeeId() == null ? null : Integer.parseInt(publicEmployeeObj.getEmployeeId()));
            data.setOuterTenantId(publicEmployeeObj.getOuterTenantId());
            String ea = outerTenantIdEaMap.get(publicEmployeeObj.getOuterTenantId());
            if (StringUtils.isNotBlank(ea)) {
                data.setTenantId(eieaConverter.enterpriseAccountToId(ea));
            }
            return data;
        }).collect(Collectors.toList());
        res.setDatas(datas);
        return Result.newSuccess(res);
    }

    /**
     * 企信会话列表搜索上游企业对接人场景 @梁岩超
     * 查询上游企业对接人信息
     *
     * @param arg 当前登录员工身份
     * @return 上游企业对接人信息
     */
    @PostMapping("/searchUpstreamPublicEmployeesByName")
    public Result<SearchUpstreamPublicEmployeesByNameResult> searchUpstreamPublicEmployeesByName(@RequestBody SearchUpstreamPublicEmployeesByNameArg arg) {
        SearchUpstreamPublicEmployeesByNameResult res = new SearchUpstreamPublicEmployeesByNameResult();
        res.setDatas(new ArrayList<>());

        if (CollectionUtils.isEmpty(arg.getSearchNames())) {
            return Result.newSuccess(res);
        }
        Long downstreamOuterTenantId;
        if (StringUtils.isNotBlank(arg.getOutTenantId())) {
            downstreamOuterTenantId = Long.valueOf(arg.getOutTenantId());
        } else {
            String ea = eieaConverter.enterpriseIdToAccount(arg.getTenantId());
            FxiaokeEmployeeAssociationEntity data = fxiaokeEmployeeAssociationService.getByFixaokeNotCreate(ea, Integer.parseInt(arg.getUserId()));
            if (data == null || data.getOuterTenantId() == null) {
                // fs账号没有外部账号 说明无上游 直接返回空
                return Result.newSuccess(res);
            }
            downstreamOuterTenantId = data.getOuterTenantId();
        }

        com.facishare.global.rest.arg.SearchUpstreamPublicEmployeesByNameArg copyArg = new com.facishare.global.rest.arg.SearchUpstreamPublicEmployeesByNameArg();
        copyArg.setSearchTexts(arg.getSearchNames());
        copyArg.setDownstreamOuterTenantId(downstreamOuterTenantId);

        com.facishare.global.rest.result.Result<com.facishare.global.rest.result.ListUpstreamPublicEmployeesResult> globalResult = globalService.searchUpstreamPublicEmployeesByName(
                HeaderObj.newInstance(arg.getTenantId()), copyArg);
        if (!globalResult.isSuccess()) {
            return Result.newError(globalResult.getCode(), globalResult.getMessage());
        }
        Set<Integer> upstreamEis = globalResult.getData().getUpstreamPublicEmployeeVos().stream().map(x -> x.getTenantId()).filter(Objects::nonNull).collect(Collectors.toSet());
        final Map<String, Long> eaOuterTenantIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(upstreamEis)) {
            Map<Integer, String> eiEaMap = eieaConverter.enterpriseIdToAccount(upstreamEis);
            if (CollectionUtils.isNotEmpty(eiEaMap.values())) {
                Map<String, Long> data = fxiaokeEnterpriseAssociationService.batchGetOuterTenantIds(eiEaMap.values());
                if (data != null) {
                    eaOuterTenantIdMap.putAll(data);
                }
            }
        }
        List<UpstreamPublicEmployeeData> dataList = globalResult.getData().getUpstreamPublicEmployeeVos().stream().map(x -> {
            UpstreamPublicEmployeeData data = new UpstreamPublicEmployeeData();
            String ea = eieaConverter.enterpriseIdToAccount(x.getTenantId());
            data.setUpstreamPublicEmployeeName(x.getEmployeeName());
            data.setUpstreamEi(x.getTenantId());
            data.setUpstreamOuterTenantId(eaOuterTenantIdMap.get(ea));
            data.setUpstreamEmployeeId(x.getEmployeeId());
            data.setUpstreamOuterUid(x.getOuterUid());
            return data;
        }).collect(Collectors.toList());
        res.setDatas(dataList);
        return Result.newSuccess(res);
    }

    /**
     * 流程后动作发CRM提醒时，如果解析出来的人员有作废的，会把作废人员的信息发送给CRM管理员,需要查询互联用户信息 依赖团队:PaaS业务基础平台流程组
     *
     * @param arg 互联用户ID列表
     * @return 互联用户信息
     */
    @PostMapping("/getPublicEmployeeByIds")
    public Result<List<SimplePublicEmployeeObjData>> getPublicEmployeeByIds(@RequestBody BatchGetPublicEmployeeByIdsArg arg) {
        User user = User.builder().tenantId(arg.getTenantId() + "").userId(SuperUserConstants.USER_ID + "").build();
        List<SimplePublicEmployeeObjData> dataList = Lists.newArrayList();
        try {
            PublicEmployeeQueryArg publicEmployeeQueryArg = new PublicEmployeeQueryArg();
            publicEmployeeQueryArg.setUseDataPermission(false);
            publicEmployeeQueryArg.setOuterTenantIds(arg.getOuterTenantIds());
            publicEmployeeQueryArg.setOuterUids(arg.getOuterUids());
            publicEmployeeQueryArg.setOffset(arg.getOffset());
            publicEmployeeQueryArg.setLimit(CollectionUtils.isEmpty(arg.getOuterUids()) ? arg.getLimit() : arg.getOuterUids().size());
            QueryResult<IObjectData> query = publicEmployeeDao.getQuery(user, publicEmployeeQueryArg);
            if (CollectionUtils.isEmpty(query.getData())) {
                return Result.newSuccess(dataList);
            }
            dataList = query.getData().stream().map(x -> {
                PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(x);
                SimplePublicEmployeeObjData data = new SimplePublicEmployeeObjData();
                data.setPublicEmployeeName(publicEmployeeObj.getName());
                data.setOuterUid(publicEmployeeObj.getOuterUid());
                data.setEmployeeId(publicEmployeeObj.getEmployeeId() == null ? null : Integer.parseInt(publicEmployeeObj.getEmployeeId()));
                data.setOuterTenantId(publicEmployeeObj.getOuterTenantId());
                data.setLoginEmail(publicEmployeeObj.getLoginEmail());
                data.setMobile(publicEmployeeObj.getMobile());
                data.setTenantId(Integer.valueOf(publicEmployeeObj.getTenantId()));
                return data;
            }).collect(Collectors.toList());
        } catch (Exception ex) {
            log.warn("getPublicEmployeeByIds error:", ex);
        }
        return Result.newSuccess(dataList);
    }


    @PostMapping("/updateUpstreamPublicEmployeeRoles")
    public Result<Void> updateUpstreamPublicEmployeeRoles(@RequestHeader("x-fs-ei") String tenantId, @RequestBody UpdatePublicEmployeeRolesArg arg) {
        if (ObjectUtils.isEmpty(tenantId) || ObjectUtils.isEmpty(arg.getUpstreamTenantId()) || ObjectUtils.isEmpty(arg.getOuterRoleIds()) || ObjectUtils.isEmpty(arg.getUserId())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        //1 验证互联关系
        String downstreamEa = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        ServiceContext serviceContext = relationObjService.createServiceContext(arg.getUpstreamTenantId(), SuperUserConstants.USER_ID.toString());
        FindEnterpriseRelationByDownstreamEa checkArg = new FindEnterpriseRelationByDownstreamEa();
        checkArg.setDownstreamEa(downstreamEa);
        com.fxiaoke.enterpriserelation.objrest.result.Result<Map<String, Object>> relationObjResult = relationObjService.findEnterpriseRelationByDownstreamEa(serviceContext, checkArg);
        if (!relationObjResult.isSuccess()) {
            return Result.newError(relationObjResult.getErrCode(), relationObjResult.getErrMessage());
        }

        Long outerUid = fxiaokeEmployeeAssociationService.getOuterUid(downstreamEa, arg.getUserId());
        if (outerUid == null) {
            return Result.newError(ResultCode.EMPLOYEE_NOT_EXIST.getErrorCode(), ResultCode.EMPLOYEE_NOT_EXIST.getDescription());
        }

        Result<Void> result = publicEmployeeService.updatePublicEmployeeRoles(arg.getUpstreamTenantId(), outerUid, arg.getOuterRoleIds());
        return result;
    }

    /**
     * 根据互联用户名称查询互联用户信息
     * Paas侧对象数据导入场景使用 @杨华国
     * https://wiki.firstshare.cn/pages/viewpage.action?pageId=543752427
     *
     * @param request  来源为请求头
     * @param queryArg 查询公务员的参数，包含需要查询的公务员名称等信息
     * @return 返回包含查询结果的Result对象，其中包含根据名称查询到的互联用户信息
     */
    @PostMapping("/queryPublicEmployeeByNames")
    public Result<SearchSimplePublicEmployeeObjsByNameResult> queryPublicEmployeeByNames(HttpServletRequest request, @RequestBody QueryPublicEmployeesByNameArg queryArg) {
        User user = getUserInfo(request);
        if (StringUtils.isEmpty(user.getTenantId()) || ObjectUtils.isEmpty(queryArg.getSearchNames())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (queryArg.getSearchNames() == null || queryArg.getSearchNames().size() > 1000) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.PAGE_SIZE_NO_EXCEED_1000.getDescription());
        }
        try {
            if (CollectionUtils.isNotEmpty(queryArg.getOuterTenantNames())) {
                EnterpriseRelationQueryArg enterpriseRelationQueryArg = new EnterpriseRelationQueryArg();
                enterpriseRelationQueryArg.setEnterpriseNames(queryArg.getSearchNames());

                List<IObjectData> iObjectDataList = enterpriseRelationDao.listByQuery(user, enterpriseRelationQueryArg);
                log.info("enterpriseRelationDao.listByQuery:{}", iObjectDataList);
                if (CollectionUtils.isNotEmpty(iObjectDataList)) {
                    Set<Long> outerTenantIds = iObjectDataList.stream().map(x -> EnterpriseRelationObj.newInstance(x).getOuterTenantId()).collect(Collectors.toSet());
                    if (queryArg.getOuterTenantIds() == null) {
                        queryArg.setOuterTenantIds(new ArrayList<>(outerTenantIds));
                    } else {
                        queryArg.getOuterTenantIds().addAll(outerTenantIds);
                    }
                }
            }
            SearchSimplePublicEmployeeObjsByNameResult result = new SearchSimplePublicEmployeeObjsByNameResult();
            PublicEmployeeQueryArg publicEmployeeQueryArg = new PublicEmployeeQueryArg();
            publicEmployeeQueryArg.setEmployeeNames(queryArg.getSearchNames());
            if (queryArg.getOuterTenantIds() != null) {
                publicEmployeeQueryArg.setOuterTenantIds(queryArg.getOuterTenantIds());
            }
            publicEmployeeQueryArg.setUseDataPermission(true);
            publicEmployeeQueryArg.setFetchEnterpriseName(false);
            if (user.isOutUser()) {
                publicEmployeeQueryArg.setHasOuterPermissionType(true);
            }
            publicEmployeeQueryArg.setOffset(0);
            publicEmployeeQueryArg.setLimit(queryArg.getSearchNames().size());
            QueryResult<IObjectData> query = publicEmployeeDao.getQuery(user, publicEmployeeQueryArg);
            if (CollectionUtils.isEmpty(query.getData())) {
                result.setDatas(Lists.newArrayList());
                return Result.newSuccess(result);
            }
            List<SimplePublicEmployeeObjData> datas = query.getData().stream().map(x -> {
                PublicEmployeeObj publicEmployeeObj = PublicEmployeeObj.newInstance(x);
                SimplePublicEmployeeObjData data = new SimplePublicEmployeeObjData();
                data.setPublicEmployeeName(publicEmployeeObj.getName());
                data.setOuterUid(publicEmployeeObj.getOuterUid());
                data.setEmployeeId(publicEmployeeObj.getEmployeeId() == null ? null : Integer.parseInt(publicEmployeeObj.getEmployeeId()));
                data.setOuterTenantId(publicEmployeeObj.getOuterTenantId());
                data.setMobile(publicEmployeeObj.getMobile());
                data.setLoginEmail(publicEmployeeObj.getLoginEmail());
                data.setStatus(publicEmployeeObj.getType() == 1 ? 1 : 0);
                return data;
            }).collect(Collectors.toList());
            result.setDatas(datas);
            return Result.newSuccess(result);
        } catch (Exception ex) {
            log.warn("queryPublicEmployeeByName error:", ex);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), ResultCode.SYSTEM_ERROR.getDescription());
        }
    }


    @RequestMapping(value = "/batchGetOutUserInfoByName", method = RequestMethod.POST)
    public Result<BatchGetOutUserInfoByNameResult> batchGetOutUserInfoByName(HttpServletRequest request, @RequestBody BatchGetOutUserInfoByNameArg arg) {
        User userInfo = getUserInfo(request);
        if (ObjectUtils.isEmpty(userInfo) || ObjectUtils.isEmpty(userInfo.getTenantId())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        BatchGetOutUserInfoByNameResult result = new BatchGetOutUserInfoByNameResult();
        result.setOuterUserInfoList(ImmutableList.of());
        if (ObjectUtils.isEmpty(arg.getParams())) {
            return Result.newSuccess(result);
        }

        User user = User.systemUser(userInfo.getTenantId());
        ServiceContext serviceContext = relationObjService.createServiceContext(user.getTenantId(), user.getUserId());
        Set<String> names = arg.getParams().stream()
                .map(BatchGetOutUserInfoByNameArg.Param::getOuterTenantName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        FindEnterpriseRelationByNameArg findEnterpriseRelationByNameArg = new FindEnterpriseRelationByNameArg();
        findEnterpriseRelationByNameArg.setNames(names);
        FindEnterpriseRelationByNameResult enterpriseRelationByName = relationObjService.findEnterpriseRelationByName(serviceContext, findEnterpriseRelationByNameArg);
        if (ObjectUtils.isEmpty(enterpriseRelationByName.getEnterpriseRelationInfoList())) {
            Result.newSuccess(result);
        }

        Map<Long, String> outerTenantId2Name = new HashMap<>(enterpriseRelationByName.getEnterpriseRelationInfoList().size());
        Map<String, Long> name2OuterTenantId = new HashMap<>(enterpriseRelationByName.getEnterpriseRelationInfoList().size());
        for (FindEnterpriseRelationByNameResult.EnterpriseRelationInfo enterpriseRelationInfo : enterpriseRelationByName.getEnterpriseRelationInfoList()) {
            name2OuterTenantId.put(enterpriseRelationInfo.getEnterpriseName(), enterpriseRelationInfo.getOuterTenantId());
            outerTenantId2Name.put(enterpriseRelationInfo.getOuterTenantId(), enterpriseRelationInfo.getEnterpriseName());
        }

        List<FindPublicEmployeeByNameArg.Param> queryEmployeeParamList = new ArrayList<>();
        for (BatchGetOutUserInfoByNameArg.Param param : arg.getParams()) {
            Long outerTenantId = name2OuterTenantId.get(param.getOuterTenantName());
            if (outerTenantId == null) {
                continue;
            }

            FindPublicEmployeeByNameArg.Param queryUserParam = new FindPublicEmployeeByNameArg.Param();
            queryUserParam.setEmployeeName(param.getOuterUserName());
            queryUserParam.setOuterTenantId(outerTenantId);
            queryEmployeeParamList.add(queryUserParam);
        }

        FindPublicEmployeeByNameArg findPublicEmployeeByNameArg = new FindPublicEmployeeByNameArg();
        findPublicEmployeeByNameArg.setParams(queryEmployeeParamList);
        findPublicEmployeeByNameArg.setType(arg.getType());
        FindPublicEmployeeByNameResult employeeResult = publicEmployeeObjService.findPublicEmployeeByName(serviceContext, findPublicEmployeeByNameArg);

        if (ObjectUtils.isEmpty(employeeResult.getPublicEmployeeInfos())) {
            return Result.newSuccess(result);
        }

        List<BatchGetOutUserInfoByNameResult.OuterUserInfo> outerUserInfoList = new ArrayList<>();
        for (FindPublicEmployeeByNameResult.PublicEmployeeInfo employeeInfo : employeeResult.getPublicEmployeeInfos()) {
            BatchGetOutUserInfoByNameResult.OuterUserInfo outerUserInfo = new BatchGetOutUserInfoByNameResult.OuterUserInfo();
            outerUserInfo.setOuterEmployeeName(employeeInfo.getOuterEmployeeName());
            outerUserInfo.setOuterUserId(employeeInfo.getOuterUserId());
            outerUserInfo.setOuterTenantId(employeeInfo.getOuterTenantId());
            outerUserInfo.setOuterTenantName(outerTenantId2Name.get(employeeInfo.getOuterTenantId()));
            outerUserInfo.setType(employeeInfo.getType());
            outerUserInfoList.add(outerUserInfo);
        }

        result.setOuterUserInfoList(outerUserInfoList);
        return Result.newSuccess(result);
    }
}
