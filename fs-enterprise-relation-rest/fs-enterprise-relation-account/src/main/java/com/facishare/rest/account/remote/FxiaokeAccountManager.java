package com.facishare.rest.account.remote;

import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.FunctionPrivilegeConstant;
import com.facishare.enterprise.common.constant.GenderEnum;
import com.facishare.enterprise.common.exception.RelationException;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.*;
import com.facishare.er.api.model.EmployeeInfo;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NSaveFileFromTempFile;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.organization.adapter.api.model.BasePageControlDto;
import com.facishare.organization.adapter.api.model.biz.employee.MobileStatus;
import com.facishare.organization.adapter.api.model.biz.employee.arg.BatchModifyEmployeeStopArg;
import com.facishare.organization.adapter.api.model.biz.employee.arg.CreateEmployeeArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.CreateEmployeeResult;
import com.facishare.organization.adapter.api.permission.FunctionNo;
import com.facishare.organization.adapter.api.permission.enums.functioncode.SystemFunctionCodeEnum;
import com.facishare.organization.adapter.api.permission.model.CheckFunctionCodeAndGetManageDepartments;
import com.facishare.organization.adapter.api.permission.model.GetPermissionListByFunctionDto;
import com.facishare.organization.adapter.api.permission.service.PermissionService;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.exception.OrganizationException;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentDtoArg;
import com.facishare.organization.api.model.department.arg.GetAllDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.BatchGetDepartmentDtoResult;
import com.facishare.organization.api.model.department.result.GetAllDepartmentDtoResult;
import com.facishare.organization.api.model.employee.BatchGetEmployeeNameMapByDepartmentId;
import com.facishare.organization.api.model.employee.BatchGetSimpleEmployeeDto;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.EmployeeName;
import com.facishare.organization.api.model.employee.EmployeeOption;
import com.facishare.organization.api.model.employee.SimpleEmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeeOptionArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.model.employee.result.GetAllEmployeeOptionResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.appframework.privilege.util.AuthContextExt;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.params.PageInfo;
import com.facishare.paas.auth.model.params.response.RoleUserPageInfoReponse;
import com.facishare.register.api.dto.EnterpriseWebRegisterDto;
import com.facishare.register.api.dto.EnterpriseWebRegisterDto.Argument;
import com.facishare.register.api.dto.EnterpriseWebRegisterDto.EnterpriseRegistrationSource;
import com.facishare.register.api.service.RegisterService;
import com.facishare.rest.account.manager.EnterpriseMetaDataManager;
import com.facishare.rest.account.manager.OrganizationManager;
import com.facishare.rest.account.old.relation.RegisterServiceManagerOldManager;
import com.facishare.rest.common.remote.PermissionManager;
import com.facishare.uc.api.model.employee.EmployeeEditionData;
import com.facishare.uc.api.model.employee.arg.BatchGetEmployeeEditionByMobileArg;
import com.facishare.uc.api.model.employee.result.BatchGetEmployeeEditionByMobileResult;
import com.facishare.uc.api.model.enterprise.arg.ChangeEnterpriseNameArg;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseEnvironment;
import com.facishare.uc.api.model.fscore.SimpleEnterprise;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.service.EmployeeEditionService;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.DepartmentObjContants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.contants.PersonnelFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.AccountData;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Component
public class FxiaokeAccountManager {
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    @Autowired
    private NFileStorageService nfileStorageService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PermissionManager permissionManager;
    @Autowired
    private EmployeeProviderService employeeService;
    @Autowired
    private DepartmentProviderService departmentService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EmployeeService jvEmployeeService;
    @Autowired
    private SmsManager smsManager;
    @Autowired
    private UcManager ucManager;
    @Autowired
    private RegisterService registerService;
    @Autowired
    private RegisterServiceManagerOldManager registerServiceManagerOldManager;
    @Autowired
    private EmployeeEditionService employeeEditionService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private UserRoleInfoService userRoleInfoService;
    @Autowired
    private RoleClient roleClient;
    @Autowired
    private EnterpriseMetaDataManager enterpriseMetaDataManager;
    @Autowired
    private OrganizationManager organizationManager;
    private Cache<String, Boolean> relationAdminCache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(Duration.ofSeconds(10)).build();

    public String createDepartment(int ei, String depName, int parentId) {
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(ei, SuperUserConstants.USER_ID);
        ActionAddArg actionAddArg = new ActionAddArg();
        com.fxiaoke.crmrestapi.common.data.ObjectData objectData = new com.fxiaoke.crmrestapi.common.data.ObjectData();
        objectData.put(DepartmentObjContants.RECORD_TYPE, DepartmentObjContants.DEPARTMENT_RECORD_TYPE);
        objectData.put(DepartmentObjContants.STATUS, "0");
        objectData.put(DepartmentObjContants.NAME, depName);
        objectData.put(DepartmentObjContants.DEPT_CODE, "");
        objectData.put(DepartmentObjContants.MANAGER_ID, new ArrayList<>());
        objectData.put(DepartmentObjContants.ASSISTANT_ID, new ArrayList<>());
        objectData.put(DepartmentObjContants.PARENT_ID, Lists.newArrayList(parentId + ""));
        actionAddArg.setObjectData(objectData);
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResult = metadataActionService.add(headerObj, DepartmentObjContants.API_NAME, false, actionAddArg);
        if (!addResult.isSuccess() || addResult.getData() == null || addResult.getData().getObjectData() == null) {
            log.warn("metadataActionService.add(headerObj, DepartmentObjContants.RECORD_TYPE error");
            return null;
        }
        return addResult.getData().getObjectData().getId();
    }

    public boolean isAdmin(User user) {
        if (userRoleInfoService.isAdmin(user)) {
            return true;
        }
        if (!user.isOutUser() && isRelationAdmin(user)) {
            return true;
        }
        return false;
    }

    public Map<Integer, String> getEmployeeIdNameMap(String ea, Collection<Integer> employeeIds) {
        List<EmployeeDto> employeeDtos = batchGetEmployeeDetail(new ArrayList<>(employeeIds), ea);
        return employeeDtos.stream().collect(Collectors.toMap(EmployeeDto::getEmployeeId, EmployeeDto::getName, (v1, v2) -> v1));
    }

    public String getEnterpriseName(Integer tenantId) {
        GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
        arg.setEnterpriseId(tenantId);
        SimpleEnterprise simpleEnterprise = enterpriseEditionService.getSimpleEnterprise(arg).getSimpleEnterprise();
        if (simpleEnterprise != null) {
            return simpleEnterprise.getEnterpriseName();
        }
        return I18nUtil.get(I18nKeyEnum.EIP_SETTING_SYSTEAM_OPERATOR_UNKNOWN);
    }

    public GetEnterpriseDataResult getEnterpriseDataResult(String ea) {
        String ei = eieaConverter.enterpriseAccountToId(ea) + "";
        GetEnterpriseDataArg getEaDataArg = new GetEnterpriseDataArg();
        getEaDataArg.setEnterpriseAccount(ea);
        return CloudContextUtil.doInCloudContextWithReturn(ei, () -> enterpriseEditionService.getEnterpriseData(getEaDataArg));
    }

    public boolean isCloudEa(String ea) {
        GetEnterpriseDataResult enterpriseDataResult = getEnterpriseDataResult(ea);
        if (enterpriseDataResult == null || enterpriseDataResult.getEnterpriseData() == null) {
            return false;
        }
        Integer env = getEnterpriseDataResult(ea).getEnterpriseData().getEnv();
        if (!EnterpriseEnvironment.FXIAOKE.getEnv().equals(env)) {
            return true;
        }
        return false;
    }

    public void updateEnterpriseName(Integer tenantId, String newName) {
        if (StringUtils.isEmpty(newName)) {
            return;
        }
        ChangeEnterpriseNameArg arg = new ChangeEnterpriseNameArg();
        arg.setEnterpriseId(tenantId);
        arg.setEnterpriseName(newName);
        CloudContextUtil.doInCloudContextWithReturn("" + tenantId, () -> enterpriseEditionService.changeEnterpriseName(arg));
    }

    public String getEmployeeName(String ea, int employeeId) {
        if (employeeId < 0) {
            return I18nUtil.get(I18nKeyEnum.EIP_SETTING_SYSTEAM_OPERATOR_NAME);
        }
        EmployeeDto employeeDto = getEmployeeDetail(employeeId, ea);
        if (employeeDto == null) {

            return I18nUtil.get(I18nKeyEnum.EIP_SETTING_SYSTEAM_OPERATOR_UNKNOWN);
        }
        return employeeDto.getName();
    }

    /**
     * 获取员工详情，部门信息只有ID没有名称
     */
    public EmployeeDto getEmployeeDetail(int employeeId, String ea) {
        List<EmployeeDto> employeeInfos = batchGetEmployeeDetail(Lists.newArrayList(employeeId), ea);
        if (employeeInfos == null || employeeInfos.isEmpty()) {
            log.warn("[batchGetEmployeeDetail] [not found] [employeeId:{}] [ea:{}]", employeeId, ea);
            return null;
        }
        return employeeInfos.get(0);
    }

    /**
     * 批量获取部门信息
     */
    public Map<Integer, String> batchGetDepartmentInfo(String ea, Collection<Integer> departIds) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            BatchGetDepartmentDtoArg arg = new BatchGetDepartmentDtoArg(departIds, RunStatus.ACTIVE);//当前企业互联没有部门停用之类的监听，因此只获取有效部门
            arg.setEnterpriseId(ei);
            //BatchGetDepartmentDtoResult result = departmentService.batchGetDepartmentDto(arg);
            BatchGetDepartmentDtoResult result = CloudContextUtil.doInCloudContextWithReturn("" + ei, () -> departmentService.batchGetDepartmentDto(arg));
            List<DepartmentDto> departList = result.getDepartments();
            return departList.stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, DepartmentDto::getName));
        } catch (OrganizationException r) {
            log.warn("fail to get depart information in batch from remote,departIds:{},ea:{},e:", departIds, ea, r);
            throw new RelationException(ResultCode.ADDRESS_SERVICE_CALL_FAILURE.getErrorCode(), r.getMessage());
        }
    }

    /**
     * 批量获取员工详情
     */
    public List<EmployeeDto> batchGetEmployeeDetail(Collection<Integer> employeeIds, String ea) {
        if (null == employeeIds || employeeIds.isEmpty()) {
            return new ArrayList<>(0);
        }
        try {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            BatchGetEmployeeDtoArg arg = new BatchGetEmployeeDtoArg(employeeIds, RunStatus.ALL);
            arg.setEnterpriseId(ei);
            BatchGetEmployeeDtoResult result = CloudContextUtil.doInCloudContextWithReturn("" + ei, () -> employeeService.batchGetEmployeeDto(arg));
            return Objects.isNull(result) ? new ArrayList<>(0) : result.getEmployeeDtos();
        } catch (OrganizationException r) {
            log.warn("fail to get employee details in batch from remote,employeeIds:{},ea:{},e:", employeeIds, ea, r);
            throw new RelationException(ResultCode.ADDRESS_SERVICE_CALL_FAILURE.getErrorCode(), r.getMessage());
        }
    }

    /**
     * 批量获取员工详情
     */
    public List<EmployeeOption> getAllEmployeeOption(Integer ei) {
        try {
            GetAllEmployeeOptionArg arg = new GetAllEmployeeOptionArg();
            arg.setRunStatus(RunStatus.ALL);
            arg.setEnterpriseId(ei);
            GetAllEmployeeOptionResult result = CloudContextUtil.doInCloudContextWithReturn("" + ei, () -> employeeService.getAllEmployeeOption(arg));
            return Objects.isNull(result) ? new ArrayList<>(0) : result.getEmployeeOptions();
        } catch (OrganizationException r) {
            log.warn("fail to get employee details in batch from remote,ea:{},e:", ei, r);
            throw new RelationException(ResultCode.ADDRESS_SERVICE_CALL_FAILURE.getErrorCode(), r.getMessage());
        }
    }

    /**
     * 检测是否是系统管理员
     */
    public boolean isSystemAdmin(int employeeId, String ea) {
        return checkPermission(ea, employeeId, SystemFunctionCodeEnum.ENTERPRISE_SETTING);
    }

    /**
     * 检测是否是互联管理员
     */
    public boolean isRelationAdmin(Integer employeeId, String tenantId) {
        return relationAdminCache.get(tenantId + "_" + employeeId, (key) -> doIsRelationAdmin(employeeId, tenantId));
    }

    public boolean isRelationAdmin(User user) {
        if (user.isOutUser()) {
            return false;
        }
        Integer employeeId = user.getUserIdInt();
        String tenantId = user.getTenantId();
        return relationAdminCache.get(tenantId + "_" + employeeId, (key) -> doIsRelationAdmin(employeeId, tenantId));
    }

    private boolean doIsRelationAdmin(Integer employeeId, String tenantId) {
        if (employeeId == null || StringUtils.isEmpty(tenantId)) {
            return false;
        }
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        return checkPermission(ea, employeeId, SystemFunctionCodeEnum.ENTERPRISE_INTERCONNECT_SETTING);
    }

    //检查指定员工是否具有指定角色
    private boolean checkPermission(String ea, int employeeId, SystemFunctionCodeEnum systemFunctionCode) {
        try {
            CheckFunctionCodeAndGetManageDepartments.Argument argument = new CheckFunctionCodeAndGetManageDepartments.Argument();
            int ei = eieaConverter.enterpriseAccountToId(ea);
            argument.setEnterpriseId(ei);
            argument.setCurrentEmployeeId(employeeId);
            argument.setEmployeeId(employeeId);
            argument.setFunctionCode(systemFunctionCode.getFunctionCode());
            argument.setAppId(SystemFunctionCodeEnum.APP_ID);
            //CheckFunctionCodeAndGetManageDepartments.Result checkResult = permissionService.checkFunctionCodeAndGetManageDepartments(argument);
            CheckFunctionCodeAndGetManageDepartments.Result checkResult = CloudContextUtil.doInCloudContextWithReturn("" + ei,
                    () -> permissionService.checkFunctionCodeAndGetManageDepartments(argument));
            return (null != checkResult) && (checkResult.getHasAbility());
        } catch (OrganizationException e) {
            log.warn("fail to call permissionService.checkPermission,employeeId:{},ea:{},functionNo:{},err:", employeeId, ea, systemFunctionCode, e);
            throw new RelationException(ResultCode.ADMIN_CHECK_GET_FAILURE.getErrorCode(), e.getMessage());
        }
    }

    /**
     * 获取企业互联管理员列表
     */
    public List<Integer> listRelationAdmins(String ea) {
        return permissionManager.listAdminByFunction(ea, FunctionNo.ENTERPRISE_RELATION_MANAGEMENT);
    }

    /**
     * 获取单个CRM管理员ID
     */
    public Integer getOneCrmAdmin(String ea) {
        return getOneAdminByFunction(ea, FunctionNo.FCUSTOMER_MANAGEMENT);
    }

    public List<Integer> listCrmAdmin(int ei) {
        AuthContext authContext = AuthContextExt.of(User.systemUser(ei + "")).getAuthContext();
        authContext.setAppId(PrivilegeConstants.APP_ID);
        PageInfo pageInfo = new PageInfo(10);
        try {
            RoleUserPageInfoReponse roleUserPageInfoReponse = roleClient.queryUserIdByRole(authContext, FunctionPrivilegeConstant.CRM_ADMIN_ROLE_CODE, pageInfo);
            if (roleUserPageInfoReponse == null || roleUserPageInfoReponse.getUsers() == null) {
                return new ArrayList<>();
            }
            return roleUserPageInfoReponse.getUsers().stream().map(Integer::valueOf).collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("", e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取系统管理员列表
     */
    public List<Integer> listSystemAdmins(String ea) {
        return permissionManager.listAdminByFunction(ea, FunctionNo.SYSTEM_MANAGEMENT);
    }

    //查询指定企业具有指定角色的首页单个成员
    private Integer getOneAdminByFunction(String ea, FunctionNo functionNo) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            int currentPage = 1;
            GetPermissionListByFunctionDto.Argument arg = new GetPermissionListByFunctionDto.Argument();
            arg.setEnterpriseId(ei);
            arg.setFunctionNo(functionNo);
            arg.setPageControl(BasePageControlDto.builder().currentPage(currentPage).pageSize(1).build());
            //GetPermissionListByFunctionDto.Result result = permissionService.getPermissionListByFunction(arg);
            GetPermissionListByFunctionDto.Result result = CloudContextUtil.doInCloudContextWithReturn("" + ei, () -> permissionService.getPermissionListByFunction(arg));
            List<Integer> tmpAdminIds = result.getEmployeeManageDepartments().stream().map(x -> x.getEmployee().getEmployeeId()).collect(Collectors.toList());
            if (tmpAdminIds.isEmpty()) {
                return null;
            }
            return tmpAdminIds.get(0);
        } catch (OrganizationException e) {
            log.warn("fail to call permissionService.getPermissionListByFunction,ea:{},functionNo:{},err:", ea, functionNo, e);
            throw new RelationException(ResultCode.ADMIN_CHECK_GET_FAILURE.getErrorCode(), e.getMessage());
        }
    }

    //查询企业具有指定角色的成员列表
    private List<Integer> listAdminByFunction(String ea, FunctionNo functionNo) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            List<Integer> allAdminIds = new ArrayList<>();
            int currentPage = 1;
            int totalPage;
            do {
                GetPermissionListByFunctionDto.Argument arg = new GetPermissionListByFunctionDto.Argument();
                arg.setEnterpriseId(ei);
                arg.setFunctionNo(functionNo);
                arg.setPageControl(BasePageControlDto.builder().currentPage(currentPage).pageSize(100).build());
                //GetPermissionListByFunctionDto.Result result = permissionService.getPermissionListByFunction(arg);
                GetPermissionListByFunctionDto.Result result = CloudContextUtil.doInCloudContextWithReturn("" + ei, () -> permissionService.getPermissionListByFunction(arg));
                if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getEmployeeManageDepartments())) {
                    break;
                }
                List<Integer> tmpAdminIds = result.getEmployeeManageDepartments().stream().map(x -> x.getEmployee().getEmployeeId()).collect(Collectors.toList());
                allAdminIds.addAll(tmpAdminIds);
                totalPage = result.getPageControl().getTotalPage();
                currentPage++;
            } while (currentPage <= totalPage);
            return allAdminIds;
        } catch (OrganizationException e) {
            log.warn("fail to call permissionService.getPermissionListByFunction,ea:{},functionNo:{},err:", ea, functionNo, e);
            throw new RelationException(ResultCode.ADMIN_CHECK_GET_FAILURE.getErrorCode(), e.getMessage());
        }
    }

    /**
     * 获取企业信息，新注册企业有一定延迟，慎用
     */
    public SimpleEnterpriseData getEnterpriseInfoByEA(String ea) {
        GetSimpleEnterpriseDataArg arg = new GetSimpleEnterpriseDataArg();
        arg.setEnterpriseAccount(ea);
        //GetSimpleEnterpriseDataResult result = enterpriseEditionService.getSimpleEnterpriseData(arg);
        GetSimpleEnterpriseDataResult result = CloudContextUtil.doInCloudContextWithReturn("" + eieaConverter.enterpriseAccountToId(ea), () -> enterpriseEditionService.getSimpleEnterpriseData(arg));

        if ((null == result) || (null == result.getEnterpriseData())) {
            throw new RelationException(ResultCode.EA_NOT_EXIST);
        }
        return result.getEnterpriseData();
    }

    public NSaveFileFromTempFile.Result nSaveFileFromTempFile(String profileImageTempFile, String enterpriseAccount, String extension) {
        NSaveFileFromTempFile.Arg arg = new NSaveFileFromTempFile.Arg();
        arg.setTempFileName(profileImageTempFile);
        arg.setEa(enterpriseAccount);
        arg.setIsFreeFile(true);
        arg.setBusiness("fs-enterprise-relation");
        arg.setFileExt(extension);
        //return nfileStorageService.nSaveFileFromTempFile(arg, enterpriseAccount);
        return CloudContextUtil.doInCloudContextWithReturn("" + eieaConverter.enterpriseAccountToId(enterpriseAccount), () -> nfileStorageService.nSaveFileFromTempFile(arg, enterpriseAccount));
    }

    /**
     * 创建企业员工
     */
    public int createEmployee(String upstreamEa, String enterpriseAccount, String employeeName, String employeeMobile) {
        String password = enterpriseMetaDataManager.generatePasswordString(employeeMobile, enterpriseAccount);
        return createEmployee(upstreamEa, enterpriseAccount, employeeName, employeeMobile, password);
    }

    /**
     * 创建企业员工
     */
    public int createEmployee(String upstreamEa, String enterpriseAccount, String employeeName, String employeeMobile, String password) {
        String nickName = employeeName;//昵称
        if (nickName.length() > 20) { //通讯录人员创建时允许的最大昵称长度
            nickName = nickName.substring(0, 20);
        }
        int ei = eieaConverter.enterpriseAccountToId(enterpriseAccount);
        String account = MobileUtil.getFirstContactMobile(employeeMobile);
        CreateEmployeeArg createEmployeeArg = new CreateEmployeeArg();
        createEmployeeArg.setEnterpriseId(ei);
        createEmployeeArg.setOperatorId(-10000);
        createEmployeeArg.setAccount(MobileUtil.getPhoneWithoutSymbol(account));
        createEmployeeArg.setPassword(password);
        createEmployeeArg.setDepartmentIds(Lists.newArrayList()); //直属部门列表
        createEmployeeArg.setFullName(employeeName);
        createEmployeeArg.setGender(GenderEnum.M.toString());
        createEmployeeArg.setMobile(MobileUtil.getPhoneWithoutSymbol(account));
        createEmployeeArg.setMobileStatus(MobileStatus.PUBLIC);
        createEmployeeArg.setName(nickName);
        createEmployeeArg.setPost("");
        //createEmployeeArg.setMainDepartmentId(999998); // 不需要传部门ID，Paas侧会自动分配部门
        createEmployeeArg.setLastUpdateTime(System.currentTimeMillis());
        createEmployeeArg.setPasswordEncodeType(0);// 明文密码
        int employeeId = createEmployee(enterpriseAccount, createEmployeeArg);
        //发送登录激活短信
        boolean sendSms = organizationManager.enableCreateEmployeeSendSms(ei);
        if (!FsGrayReleaseUtil.disableCreateEmployeeSendMsg(upstreamEa) && sendSms) {
            smsManager.sendLocalLoadMessage(enterpriseAccount, employeeMobile, employeeMobile, password);
        }
        return employeeId;
    }

    public Integer createEmployeeByLoginEmail(String ea, String employeeName, String loginEmail) {
        String nickName = employeeName;//昵称
        if (nickName.length() > 20) { //通讯录人员创建时允许的最大昵称长度
            nickName = nickName.substring(0, 20);
        }
        int ei = eieaConverter.enterpriseAccountToId(ea);
        HeaderObj headerObj = new HeaderObj(ei, SuperUserConstants.USER_ID);
        ObjectData objectData = new ObjectData();
        objectData.put(ObjectDescribeContants.DESCRIBE_API_NAME, PersonnelFieldContants.API_NAME);
        objectData.put(PersonnelFieldContants.NAME, nickName);
        objectData.put(PersonnelFieldContants.EMAIL, loginEmail);
        objectData.put(ObjectDescribeContants.OWNER, Lists.newArrayList("" + SuperUserConstants.USER_ID));
        ActionAddArg actionAddArg = new ActionAddArg();
        actionAddArg.setObjectData(objectData);
        Result<ActionAddResult> actionAddResultResult = metadataActionService.add(headerObj, PersonnelFieldContants.API_NAME, false, true, true, actionAddArg);
        if (!actionAddResultResult.isSuccess()) {
            log.warn("createEmployeeByLoginEmail error,res={}", actionAddResultResult);
            return null;
        }
        ObjectData personnelObjectData = AccountData.wrap(actionAddResultResult.getData().getObjectData());
        return Integer.valueOf((String) personnelObjectData.get(PersonnelFieldContants.USER_ID));
    }

    private int createEmployee(String enterpriseAccount, CreateEmployeeArg createEmployeeArg) {
        CreateEmployeeResult createResult = null;
        try {
            createEmployeeArg.setOperatorId(-12);
            createResult = CloudContextUtil.doInCloudContextWithReturn("" + createEmployeeArg.getEnterpriseId(), () -> jvEmployeeService.createEmployee(createEmployeeArg));
            log.info("FxiaokeAccountManager.createEmployee success!, arg={}, result={}", JSON.toJSONString(createEmployeeArg), JSON.toJSONString(createResult));
        } catch (OrganizationException e) {
            log.warn("FxiaokeAccountManager.createEmployee fail!, arg:{}, result:{}", JSON.toJSONString(createEmployeeArg), JSON.toJSONString(createResult));
            int errCode = e.getErrorCode();
            if (errCode == AddressBookResultCodeConstant.NOT_ENOUGH_EMPLOYEE_ACCOUNT) {//户额不足
                log.warn("FxiaokeAccountManager.createEmployee fail!, arg:{}, err:NOT_ENOUGH_EMPLOYEE_ACCOUNT", JSON.toJSONString(createEmployeeArg));
                throw new RelationException(ResultCode.EMPLOYEE_ACCOUNT_NOT_ENOUGH);
            }
            if (errCode == AddressBookResultCodeConstant.EMPLOYEE_NAME_DUPLICATE) { //昵称冲突
                String newNickName = createEmployeeArg.getName();
                if (newNickName.length() > 15) {
                    newNickName = newNickName.substring(0, 15);
                }
                log.warn("FxiaokeAccountManager.createEmployee fail!, arg:{}, err:EMPLOYEE_NAME_DUPLICATE", JSON.toJSONString(createEmployeeArg));
                createEmployeeArg.setName(newNickName.concat(RandomStringUtils.random(5, true, true)));
                return createEmployee(enterpriseAccount, createEmployeeArg);
            }
            if (errCode == AddressBookResultCodeConstant.EMPLOYEE_ACCOUNT_DUPLICATE) { //帐号冲突
                log.warn("FxiaokeAccountManager.createEmployee fail!, arg:{}, err:EMPLOYEE_ACCOUNT_DUPLICATE", JSON.toJSONString(createEmployeeArg));
                createEmployeeArg.setAccount(createEmployeeArg.getAccount().concat(RandomStringUtils.random(6, true, true)));
                return createEmployee(enterpriseAccount, createEmployeeArg);
            }
            if (errCode == AddressBookResultCodeConstant.EMPLOYEE_MOBILE_DUPLICATE) {//手机号已存在,todo 此处可能return null，返回类型为int，会出错
                log.warn("FxiaokeAccountManager.createEmployee fail!, arg:{}, err:EMPLOYEE_MOBILE_DUPLICATE", JSON.toJSONString(createEmployeeArg));
                return ucManager.getMobileUsers(enterpriseAccount, Lists.newArrayList(createEmployeeArg.getMobile()), false).get(createEmployeeArg.getMobile());
            }
            throw new RelationException(AddressBookResultCodeConstant.EMPLOYEE_CREATE_EXCEPTION, e.getMessage());
        } catch (Exception ex) {
            log.warn("FxiaokeAccountManager.createEmployee exception!,arg:{},err:", JSON.toJSONString(createEmployeeArg), ex);
            throw new RelationException(ResultCode.ADDRESS_SERVICE_CALL_FAILURE);
        }
        return createResult.getEmployee().getEmployeeId();
    }

    public void stopOrStartEmployees(String enterpriseAccount, List<Integer> employeeIds, boolean isStop) {
        int ei = eieaConverter.enterpriseAccountToId(enterpriseAccount);

        try {
            BatchModifyEmployeeStopArg batchModifyEmployeeStopArg = new BatchModifyEmployeeStopArg();
            batchModifyEmployeeStopArg.setEmployeeIds(employeeIds);
            batchModifyEmployeeStopArg.setEnterpriseId(ei);
            batchModifyEmployeeStopArg.setOperatorId(CrmConstants.SYSTEM_USER);
            batchModifyEmployeeStopArg.setLastUpdateTime(System.currentTimeMillis());
            batchModifyEmployeeStopArg.setStop(isStop);

            //jvEmployeeService.batchModifyEmployeeStop(batchModifyEmployeeStopArg);
            CloudContextUtil.doInCloudContextWithReturn("" + batchModifyEmployeeStopArg.getEnterpriseId(), () -> jvEmployeeService.batchModifyEmployeeStop(batchModifyEmployeeStopArg));
        } catch (Exception e) {
            log.warn("", e);
        }
    }

    public EnterpriseWebRegisterDto.Result regiestEnterprise(String enterpriseName, String adminName, String adminMobile, String adminPassword, String clientIp) {
        //生成待注册企业管理员加密密码
        String password = SecurityUtil.getMD5(adminPassword);
        //注册企业
        Argument argument = new Argument();
        argument.setEnterpriseName(enterpriseName);
        argument.setManagerFullName(adminName);
        argument.setManagerMobile(adminMobile);
        argument.setManagerPassword(password);
        argument.setClientIp(clientIp);
        argument.setSource(EnterpriseRegistrationSource.Qiyehulian);
        if (CloudUtil.isInCloud()) {
            return registerServiceManagerOldManager.registerFromSelf(argument);
        } else {
            return registerService.registerFromSelf(argument);
        }
    }

    /**
     * 获取手机号已关联的用户列表
     *
     * @param enterpriseId 企业ID
     * @param mobiles      手机号列表
     * @param filterActive 是否只过滤未停用员工
     */
    public List<EmployeeEditionData> listMobileUsers(Integer enterpriseId, List<String> mobiles, boolean filterActive) {
        try {
            BatchGetEmployeeEditionByMobileArg arg = new BatchGetEmployeeEditionByMobileArg();
            arg.setEnterpriseId(enterpriseId);
            arg.setMobiles(mobiles);
            //BatchGetEmployeeEditionByMobileResult phoneCheckResult = employeeEditionService.batchGetEmployeeEditionByMobile(arg);
            BatchGetEmployeeEditionByMobileResult phoneCheckResult = CloudContextUtil.doInCloudContextWithReturn("" + enterpriseId, () -> employeeEditionService.batchGetEmployeeEditionByMobile(arg));
            if (Objects.isNull(phoneCheckResult) || CollectionUtils.isEmpty(phoneCheckResult.getEmployeeEditionDataList())) {
                return new ArrayList<>(0);
            }
            if (filterActive) {
                return phoneCheckResult.getEmployeeEditionDataList().stream().filter(x -> x.getIsStop() != 1).collect(Collectors.toList());
            } else {
                return phoneCheckResult.getEmployeeEditionDataList();
            }
        } catch (Exception e) {
            log.warn("checking mobiles whether used by employees catches exception,enterpriseId:{},mobiles:{},err:", enterpriseId, mobiles, e);
            return new ArrayList<>(0);
        }
    }

    public List<ObjectData> listEmailUsers(Integer ei, List<String> loginEmails, boolean filterActive) {
        if (CollectionUtils.isEmpty(loginEmails)) {
            return new ArrayList<>();
        }
        HeaderObj headerObj = new HeaderObj(ei, SuperUserConstants.USER_ID);

        ControllerListArg controllerListArg = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter(PersonnelFieldContants.EMAIL, loginEmails, FilterOperatorEnum.IN);
        if (filterActive) {
            searchQuery.addFilter(PersonnelFieldContants.IS_ACTIVE, Lists.newArrayList("true"), FilterOperatorEnum.EQ);
        }
        controllerListArg.setObjectDescribeApiName(PersonnelFieldContants.API_NAME);
        searchQuery.setLimit(loginEmails.size());
        controllerListArg.setSearchQueryInfo(JsonUtil.toJson(searchQuery));
        Page<ObjectData> page = metadataControllerService.list(headerObj, PersonnelFieldContants.API_NAME, controllerListArg).getData();
        List<ObjectData> dataList = page.getDataList();
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        return dataList;
    }

    /**
     * 批量获取人员信息，带有部门层级名称
     */
    public List<EmployeeInfo> batchGetSimpleEmployees(Collection<Integer> employeeIds, String enterpriseAccount) {
        //查询基本信息
        List<EmployeeDto> employDetailFromRemote = this.batchGetEmployeeDetail(employeeIds, enterpriseAccount);
        if (null == employDetailFromRemote || employDetailFromRemote.isEmpty()) {
            return new ArrayList<>(0);
        }
        Set<Integer> departIds = new HashSet<>();
        List<EmployeeInfo> employeeInfos = employDetailFromRemote.stream().map(employeeDto -> {
            EmployeeInfo employeeInfo = new EmployeeInfo();
            initSimpleEmployee(employeeDto, employeeInfo);
            if (Objects.nonNull(employeeDto.getDepartmentIds())) {
                departIds.addAll(employeeDto.getDepartmentIds());
            }
            return employeeInfo;
        }).collect(Collectors.toList());
        //组合部门
        if (!departIds.isEmpty()) {
            Map<Integer, String> departIdNameMap = batchGetDepartmentInfo(enterpriseAccount, departIds);
            if (!departIdNameMap.isEmpty()) {
                employeeInfos.forEach(emp -> {
                    if (Objects.nonNull(emp.getDepartIds())) {
                        emp.setDepartment(emp.getDepartIds().stream().map(departIdNameMap::get).filter(Objects::nonNull).collect(Collectors.joining(",")));
                    }
                });
            }
        }
        return employeeInfos;
    }

    /**
     * 将通讯录查询到的员工信息组织为企业互联员工信息
     */
    private void initSimpleEmployee(EmployeeDto remoteEmployee, EmployeeInfo employeeInfo) {
        employeeInfo.setProfileImage(remoteEmployee.getProfileImage());
        employeeInfo.setEmployeeId(remoteEmployee.getEmployeeId());
        employeeInfo.setDepartIds(remoteEmployee.getDepartmentIds());
        employeeInfo.setNameSpell(remoteEmployee.getNameSpell());
        employeeInfo.setNameOrder(remoteEmployee.getNameOrder());
        employeeInfo.setEmployeeName(remoteEmployee.getName());
        employeeInfo.setMobile(remoteEmployee.getMobile());
        employeeInfo.setGender(String.valueOf(remoteEmployee.getGender()));
        employeeInfo.setEmail(remoteEmployee.getEmail());
        employeeInfo.setStop(!remoteEmployee.isActive());
        employeeInfo.setPost(remoteEmployee.getPost());
        employeeInfo.setLeaderID(remoteEmployee.getLeaderId());
    }

    /**
     * 批量获取员工详情
     */
    public List<SimpleEmployeeDto> batchGetSimpleEmployeeInfo(Collection<Integer> employeeIds, Integer ei) {
        if (null == employeeIds || employeeIds.isEmpty()) {
            return new ArrayList<>(0);
        }
        try {
            BatchGetSimpleEmployeeDto.Arg arg = new BatchGetSimpleEmployeeDto.Arg();
            arg.setEmployeeIds(employeeIds);
            arg.setRunStatus(RunStatus.ALL);
            arg.setEnterpriseId(ei);

            BatchGetSimpleEmployeeDto.Result result = employeeService.batchGetSimpleEmployeeDto(arg);
            return Objects.isNull(result) ? new ArrayList<>(0) : result.getEmployeeDtos();
        } catch (OrganizationException r) {
            log.warn("fail to get employee details in batch from remote,employeeIds:{},ei:{},e:", employeeIds, ei, r);
            throw new RelationException(ResultCode.ADDRESS_SERVICE_CALL_FAILURE.getErrorCode(), r.getMessage());
        }
    }

    public Map<String, String> batchGetEmployeeIdNameByDepartmentIds(Integer tenantId, Collection<Integer> departIds) {
        if (CollectionUtils.isEmpty(departIds)) {
            return new HashMap<>();
        }
        try {
            BatchGetEmployeeNameMapByDepartmentId.Arg arg = new BatchGetEmployeeNameMapByDepartmentId.Arg();//当前企业互联没有部门停用之类的监听，因此只获取有效部门
            arg.setDepartmentIds(new ArrayList<>(departIds));
            arg.setIncludeLowDepartment(true);
            arg.setRunStatus(RunStatus.ACTIVE);
            arg.setEnterpriseId(tenantId);

            BatchGetEmployeeNameMapByDepartmentId.Result result = employeeService.batchGetEmployeeNameMapByDepartmentId(arg);
            return result.getEmployeeMap().values().stream().flatMap(List::stream).collect(Collectors.toMap(x -> x.getEmployeeId() + "", EmployeeName::getName, (v1, v2) -> v1));
        } catch (OrganizationException r) {
            log.warn("fail to get depart information in batch from remote,departIds:{},ei:{},e:", departIds, tenantId, r);
            return new HashMap<>();
        }
    }

    /**
     *
     */
    private static class AddressBookResultCodeConstant {
        /**
         * 贵企业购买的帐号数不足，不能开通
         */
        public static int NOT_ENOUGH_EMPLOYEE_ACCOUNT = 30022 + ********;
        /**
         * 员工昵称已被他人使用，请尝试其他昵称
         */
        public static int EMPLOYEE_NAME_DUPLICATE = 30023 + ********;
        /**
         * 员工帐号已被他人使用，请尝试其他帐号
         */
        public static int EMPLOYEE_ACCOUNT_DUPLICATE = 30020 + ********;
        /**
         * 员工手机号码已被其他员工绑定，请尝试其他号码
         */
        public static int EMPLOYEE_MOBILE_DUPLICATE = 30024 + ********;
        /**
         * 手机号码注册的企业上线超出
         */
        public static int EMPLOYEE_CREATE_EXCEPTION = 1 + ********;
    }

    public Map<String, SimpleEnterpriseData> batchGetEnterpriseInfoByEas(Collection<String> eas) {
        Map<String, SimpleEnterpriseData> res = new HashMap<>();
        for (String ea : eas) {
            res.put(ea, getEnterpriseInfoByEA(ea));
        }
        return res;
    }

    public Map<Integer, DepartmentDto> getAllDepartmentDto(String ea) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            GetAllDepartmentDtoArg arg = new GetAllDepartmentDtoArg();
            arg.setEnterpriseId(ei);
            //GetAllDepartmentDtoResult result = departmentService.getAllDepartmentDto(arg);
            GetAllDepartmentDtoResult result = CloudContextUtil.doInCloudContextWithReturn("" + ei, () -> departmentService.getAllDepartmentDto(arg));
            List<DepartmentDto> departList = result.getDepartments();
            return departList.stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, Function.identity()));
        } catch (OrganizationException r) {
            log.warn("fail to get depart information in batch from remote,ea:{},e:", ea, r);
            throw new RelationException(ResultCode.ADDRESS_SERVICE_CALL_FAILURE.getErrorCode(), r.getMessage());
        }
    }
}