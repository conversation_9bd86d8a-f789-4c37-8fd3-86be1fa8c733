[
	{
		
		"callDatas":{
			"com.fxiaoke.crmrestapi.service.ObjectDataServiceV3#findByQuery":[
				{
					
					"args":[
						{
							"@type":"com.fxiaoke.crmrestapi.common.data.HeaderObj",
							"X-fs-Employee-Id":-10000,
							"client_info":"rest-api",
							"x-fs-userInfo":-10000,
							"X-fs-Enterprise-Id":78057,
							"X-fs-ei":78057
						},
						{
							"@type":"com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg",
							"calculateFormula":false,
							"calculateQuote":false,
							"describeApiName":"PublicEmployeeObj",
							"fillExtendInfo":false,
							"includeInvalid":true,
							"includeRelevantTeam":false,
							"needCount":false,
							"paginationOptimization":false,
							"searchQueryInfo":"{\"limit\":10,\"offset\":0,\"filters\":[{\"field_name\":\"outer_uid\",\"field_values\":[\"300401099\",\"300405026\"],\"operator\":\"IN\"}],\"orders\":[]}",
							"selectFields":["outer_uid","outer_tenant_id","name"]
						}
					],
					"result":{
						"@type":"com.fxiaoke.crmrestapi.common.result.Result",
						"code":0,
						"data":{
							"@type":"com.fxiaoke.crmrestapi.result.QueryBySearchTemplateV3Result",
							"queryResult":{
								"dataList":[
									{
										"outer_tenant_id":"300111710",
										"name":"范旭东1",
										"outer_uid":"300401099",
										"_id":"300401099"
									},
									{
										"outer_tenant_id":"200071698",
										"name":"李慧慧",
										"outer_uid":"300405026",
										"_id":"300405026"
									}
								],
								"totalNumber":0
							}
						},
						"message":"OK"
					}
				}
			],
			"com.facishare.linkapp.api.service.HttpLinkAppService#queryLinkAppByEa":[
				{
					
					"args":[
						"obj0509"
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":[
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_a71a2994a29342e783446ab8244ef5d8.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
								"appSessionIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"disablePrompt":"停用后，合作伙伴将不能再看到网盘文件",
								"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限1",
								"enabledByDefault":1,
								"icon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c82",
								"internalVisibleRangePrompt":"管理员和对接人默认可使用",
								"introduction":"可以共享文件给合作伙伴。",
								"introductionI18nKey":"eip.app.introduction_hlwp",
								"isDefaultSelect":true,
								"managerIcon":"https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter",
								"name":"互联网盘",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":0,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
								"appSessionIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_07_0cc5c36d318e4803bd872c8642282de7.png&size=150_150&ea=appCenter",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":2,
								"createTime":*************,
								"disablePrompt":"停用后，合作伙伴将不能再看到通知公告",
								"enablePrompt":"开启后，请注意设置管理员和合作伙伴的使用权限",
								"enabledByDefault":1,
								"icon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_07_0cc5c36d318e4803bd872c8642282de7.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c83",
								"internalVisibleRangePrompt":"管理员和对接人默认可使用",
								"introduction":"群发通知给合作伙伴，支持评论，掌握已读状态，对未读联系人一键群发提醒。",
								"introductionI18nKey":"eip.app.introduction_tzgg",
								"isDefaultSelect":true,
								"managerIcon":"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_eae91ff6ed3c4f23b161beb7bbc1a908.png&size=150_150&ea=appCenter",
								"name":"通知公告",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":0,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201907_05_09b4f697a79847e8a0c72957be4bc6dc.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80",
								"appSessionIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"dataAuthType":1,
								"disablePrompt":"停用后，CRM管理中【订货通管理】菜单将屏蔽，订单/回款/开票等编号规则变为可人工调整",
								"enablePrompt":"启用订货通后，CRM管理中会增加【订货通管理】菜单，订单规则由订货通锁定、不可人工调整，且订单/回款/开票等编号规则也将锁定为自动编号模式。",
								"enabledByDefault":1,
								"icon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490c84",
								"internalVisibleRangePrompt":"订货通管理员及订单处理流程相关人员可使用。可在订货通管理中设置",
								"introduction":"合作伙伴可在订货通界面浏览上游企业发布的产品，可直接下订单并跟踪交易状态。",
								"introductionI18nKey":"eip.app.introduction_dht",
								"managerIcon":"https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_941f7c796e4f45d49cd32c4b87bf4cb2.png&size=150_150&ea=appCenter",
								"name":"订货通",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":false,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"CRM管理员可在【CRM】-【设置】-【订货通管理】中管理订货通，无需在此设置订货通管理员。",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"dataAuthType":1,
								"disablePrompt":"",
								"enablePrompt":"",
								"enabledByDefault":2,
								"icon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"id":"FSAID_11490d40",
								"internalVisibleRangePrompt":"",
								"introduction":"面向合作伙伴的在线学习考试移动应用",
								"introductionI18nKey":"eip.app.introduction_hbxt",
								"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app11.svg",
								"name":"伙伴学堂",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a9.fspage.com/FSR/link/eservice/app_icon/appIcon_downstreamOrderList.svg?",
								"appSessionIcon":"https://a9.fspage.com/FSR/link/eservice/app_icon/appIcon_downstreamOrderList.svg?",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"disablePrompt":"",
								"enablePrompt":"",
								"enabledByDefault":2,
								"icon":"https://a9.fspage.com/FSR/link/eservice/app_icon/appIcon_downstreamOrderList.svg?",
								"id":"FSAID_11490d93",
								"internalVisibleRangePrompt":"",
								"introduction":"用于承载服务商与厂家侧之间，基于工单的预设业务往来，包括工单流转、配件申领/使用/退还等。",
								"introductionI18nKey":"eip.app.introduction_pgd",
								"managerIcon":"https://a9.fspage.com/FSR/link/eservice/app_icon/appIcon_downstreamOrderList.svg?",
								"name":"服务商门户",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":false,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_c2e2699ad3604f12970118ceadbb69da.png&size=150_150&ea=appCenter",
								"appSessionIcon":"https://www.ceshi112.com/FSC/EM/File/GetByPath?path=A_201804_25_c7f1ce5d79a54ef5891d8b355167bbb4.png",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"disablePrompt":"",
								"enablePrompt":"",
								"enabledByDefault":2,
								"icon":"https://www.ceshi112.com/FSC/EM/File/GetByPath?path=A_201804_25_c7f1ce5d79a54ef5891d8b355167bbb4.png",
								"id":"FSAID_11490d97",
								"internalVisibleRangePrompt":"",
								"introduction":"扫描查看设备档案，以及设备巡检工作",
								"introductionI18nKey":"eip.app.introduction_sbt",
								"managerIcon":"https://www.ceshi112.com/FSC/EM/File/GetByPath?path=A_201804_25_c7f1ce5d79a54ef5891d8b355167bbb4.png",
								"name":"设备通",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":false,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/webapp/%E4%BB%A3%E7%90%86%E9%80%9A.svg",
								"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/webapp/%E4%BB%A3%E7%90%86%E9%80%9A.svg",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"dataAuthType":1,
								"disablePrompt":"",
								"enablePrompt":"",
								"enabledByDefault":1,
								"icon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=../../../../WEB-INF/web.xml;https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a9.fspage.com/FSR/fs-qixin/static/webapp/代理通.svg",
								"id":"FSAID_11490d9e",
								"internalVisibleRangePrompt":"",
								"introduction":"PRM",
								"introductionI18nKey":"",
								"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/webapp/%E4%BB%A3%E7%90%86%E9%80%9A.svg",
								"name":"代理通",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201910_10_7de9fa48519f4cce8466cc558bc99d42.png&size=150_150&ea=appCenter",
								"appSessionIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201910_10_7de9fa48519f4cce8466cc558bc99d42.png&size=150_150&ea=appCenter",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"disablePrompt":"停用后，下游伙伴将不会有服务业务的聚合入口，请确保已为其他服务具体应用设置了操作入口。",
								"enablePrompt":"服务通仅作为下游伙伴对于服务业务的聚合入口，请注意同时开启服务相关的具体应用，如派工单、设备资产等。",
								"enabledByDefault":1,
								"icon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201910_10_7de9fa48519f4cce8466cc558bc99d42.png&size=150_150&ea=appCenter",
								"id":"FSAID_11490ea7",
								"internalVisibleRangePrompt":"",
								"introduction":"该应用将不再维护，如需使用其他能力，请联系纷享客户经理，将业务对象和数据权限迁移到服务商服务通应用中。",
								"introductionI18nKey":"",
								"managerIcon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201910_10_7de9fa48519f4cce8466cc558bc99d42.png&size=150_150&ea=appCenter",
								"name":"服务通-下游服务商（旧）",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"appSessionIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"disablePrompt":"",
								"enablePrompt":"",
								"enabledByDefault":1,
								"icon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"id":"FSAID_11491008",
								"internalVisibleRangePrompt":"",
								"introduction":"",
								"managerIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"name":"纷享导入",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"appSessionIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"appVisibility":true,
								"associationCrmObject":false,
								"authByEa":false,
								"authType":1,
								"createTime":*************,
								"disablePrompt":"",
								"enablePrompt":"",
								"enabledByDefault":2,
								"icon":"https://crm.ceshi112.com/FSC/EM/File/GetByPath?path=https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"id":"FSAID_11491009",
								"internalVisibleRangePrompt":"",
								"introduction":"",
								"introductionI18nKey":"",
								"managerIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"name":"渠道门户",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":false,
								"supportGuestor":false,
								"supportIdentityTypes":"1,2",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"appSessionIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"disablePrompt":"",
								"enablePrompt":"",
								"enabledByDefault":1,
								"icon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"id":"FSAID_11491016",
								"internalVisibleRangePrompt":"",
								"introduction":"",
								"introductionI18nKey":"",
								"managerIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter",
								"name":"hh主2",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon29.svg",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"disablePrompt":"",
								"enablePrompt":"",
								"id":"FSAID_1149127b",
								"internalVisibleRangePrompt":"应用内部使用权限提示",
								"introduction":"",
								"name":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"type":2,
								"unVisibleInManager":false,
								"unableSetManagerText":"不能设置管理员",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"disablePrompt":"",
								"enablePrompt":"",
								"icon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"id":"FSAID_1149131f",
								"internalVisibleRangePrompt":"应用内部使用权限提示",
								"introduction":"23",
								"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"name":"新建互联应用",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"type":2,
								"unVisibleInManager":false,
								"unableSetManagerText":"不能设置管理员",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app98.svg",
								"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app98.svg",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"disablePrompt":"",
								"enablePrompt":"",
								"icon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app98.svg",
								"id":"FSAID_11491339",
								"internalVisibleRangePrompt":"应用内部使用权限提示",
								"introduction":"fby测试使用的...",
								"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app98.svg",
								"name":"fby-中文",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"type":2,
								"unVisibleInManager":false,
								"unableSetManagerText":"不能设置管理员",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"disablePrompt":"",
								"enablePrompt":"",
								"icon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"id":"FSAID_1149135f",
								"internalVisibleRangePrompt":"应用内部使用权限提示",
								"introduction":"11",
								"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"name":"lzhh",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"type":2,
								"unVisibleInManager":false,
								"unableSetManagerText":"不能设置管理员",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"appSessionIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"disablePrompt":"",
								"enablePrompt":"",
								"icon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"id":"FSAID_114913aa",
								"internalVisibleRangePrompt":"应用内部使用权限提示",
								"introduction":"我是一个应用呀呀呀呀",
								"managerIcon":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon1.svg",
								"name":"你好呀呀呀呀",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"1",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"type":2,
								"unVisibleInManager":false,
								"unableSetManagerText":"不能设置管理员",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a5.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_04_bdc427c930394630b891570ef9326606.png&size=150_150&ea=appCenter",
								"appSessionIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201711_15_0ff9ba0f97814611b059ec21e2625e52.png&size=150_150&ea=appCenter",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"dataAuthType":2,
								"disablePrompt":"",
								"enablePrompt":"",
								"enabledByDefault":2,
								"icon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201711_15_0ff9ba0f97814611b059ec21e2625e52.png&size=150_150&ea=appCenter",
								"id":"FSAID_127a3981",
								"internalVisibleRangePrompt":"",
								"introduction":"实现客户自助提交故障图片或视频，可视化设备故障，并支持用户自助进度查询",
								"introductionI18nKey":"eip.app.introduction_fwt",
								"managerIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201711_15_0ff9ba0f97814611b059ec21e2625e52.png&size=150_150&ea=appCenter",
								"name":"终端用户服务通",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":3,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"2",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppData",
								"appIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201711_15_0ff9ba0f97814611b059ec21e2625e52.png&size=150_150&ea=appCenter",
								"appSessionIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201711_15_0ff9ba0f97814611b059ec21e2625e52.png&size=150_150&ea=appCenter",
								"appVisibility":true,
								"associationCrmObject":true,
								"authByEa":false,
								"authType":0,
								"createTime":*************,
								"dataAuthType":2,
								"disablePrompt":"",
								"enablePrompt":"",
								"enabledByDefault":2,
								"icon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201711_15_0ff9ba0f97814611b059ec21e2625e52.png&size=150_150&ea=appCenter",
								"id":"FSAID_127a3982",
								"internalVisibleRangePrompt":"",
								"introduction":"扫码查看设备档案",
								"introductionI18nKey":"eip.app.introduction_sbcx",
								"managerIcon":"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201711_15_0ff9ba0f97814611b059ec21e2625e52.png&size=150_150&ea=appCenter",
								"name":"设备查询",
								"nameI18nKey":"",
								"openValidatePostUrl":"",
								"showPaasPage":1,
								"supportExtendObject":true,
								"supportGuestor":false,
								"supportIdentityTypes":"2",
								"supportNoFsAccount":false,
								"supportRoleExt":true,
								"supportSetManager":true,
								"type":1,
								"unVisibleInManager":false,
								"unableSetManagerText":"",
								"updateTime":*************
							}
						],
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-biz/66de6149788ceb7d2c4430e0"
					}
				}
			],
			"com.facishare.converter.EIEAConverter#enterpriseIdToAccount":[
				{
					
					"args":[
						78057
					],
					"result":"obj0509"
				}
			],
			"com.facishare.linkapp.api.service.LinkAppAssociationObjectService#listAssociationObjectsByLinkAppIds":[
				{
					
					"args":[
						"obj0509",
						[
							"FSAID_11490c82",
							"FSAID_11490c83",
							"FSAID_11490c84",
							"FSAID_11490d40",
							"FSAID_11490d93",
							"FSAID_11490d97",
							"FSAID_11490d9e",
							"FSAID_11490ea7",
							"FSAID_11491008",
							"FSAID_11491009",
							"FSAID_11491016",
							"FSAID_1149127b",
							"FSAID_1149131f",
							"FSAID_11491339",
							"FSAID_1149135f",
							"FSAID_114913aa",
							"FSAID_127a3981",
							"FSAID_127a3982"
						]
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"data":[
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CampaignMembersObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CasesObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"DeliveryNoteObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"DeliveryNoteProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"EnterpriseInfoObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"IkunObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"MarketingEventObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ShoppingCartObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ShoppingCartSummaryObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_63jxG__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_6OHm3__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_72o1R__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_Ce152__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_InternetLayoutTest__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_K3G8n__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_T6jtZ__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_TestInterconnectLayout__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_UKrgv__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_ccc_long_text__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_shenqing__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_tJbHx__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d93",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"BehaviorRecordObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d93",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"PartnerObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d93",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_nl59L__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d93",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_tJbHx__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d93",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_z0GZI__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ActiveRecordObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"BehaviorRecordObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CampaignMembersObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CasesObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CheckinsObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ContactObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CreditFileObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CustomerAccountObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"DeliveryNoteObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"DeliveryNoteProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"GoodsReceivedNoteObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"GoodsReceivedNoteProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"InvoiceApplicationLinesObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"LeadsFlowRecordObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"LeadsObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"Object_master_geOtD__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"OutboundDeliveryNoteObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"OutboundDeliveryNoteProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"PartnerObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"PrepayDetailObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"RebateIncomeDetailObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"RebateUseRuleObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"RequisitionNoteObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"RequisitionNoteProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ReturnedGoodsInvoiceProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"SaleContractObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"SalesOrderProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"SpecificationObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"SpecificationValueObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"StockCheckNoteObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"StockCheckNoteProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"StockDetailsObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"StockObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"WarehouseObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_08y31__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_0LXl2__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_0q605__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_0r1Tl__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_11Mun__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_13xGk__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_15Qyb__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_1gB1z__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_1hFy1__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_1kFK6__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_1uYvV__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_1w7x5__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_1xv4o__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_20qq6__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_2830v__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_2Hilj__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_2eAAx__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_3k61K__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_42DX2__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_497yb__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_4ZcMy__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_4cI02__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_50Cjk__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_63jxG__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_63nDt__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_69s9w__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_6B2y2__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_6RfV9__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_6mWx2__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_707E8__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_71Wtp__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_72fW1__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_72o1R__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_76is3__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_7sVOl__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_7sk5c__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_85rZM__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_8HGma__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_90Lvu__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_93098__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_9yXAw__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_AS1n1__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_C0vxo__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_C2Zm5__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_CRen2__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_CWe4t__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_CbEgn__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_Cgiv1__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_E6mOR__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_FcGm1__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_G8fh1__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_H1brV__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_InternetLayoutTest__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_Jf6yP__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_Jrwa6__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_K3G8n__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_Kh37y__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_L2k2I__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_MO77s__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_NeWWm__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_Npvqm__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_OU699__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_Oj61n__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_PbY23__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_QCf97__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_T6jtZ__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_TXz6s__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_Wf2ot__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_Wm4y7__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_bRCh5__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_bRivD__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_c616h__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_cX76o__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_cZ2A6__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_cf3x3__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_contract__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_e1Fe4__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_e3jJl__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_gaGT2__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_hNgKo__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_hb_business_flow_test_object__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_ikml2__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_jKqk0__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_k21Ai__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_kJa7y__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_ki71k__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_ma4FF__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_mr1FM__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_mx4tq__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_n8uTn__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_nzufh__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_omk22__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_pjy2L__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_r17a4__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_s1z2E__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_s3hjO__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_sIYbf__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_scdB5__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_szFPV__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_tJbHx__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_test__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_ti13X__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_uW82g__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_vKAm8__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_vgskN__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_xA280__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_xE7Dx__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_xLcPb__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_yA7cC__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_yR1Y9__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_z0GZI__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_z2K27__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"scancodelog__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490ea7",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"MarketingEventObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490ea7",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_28K21__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490ea7",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_3k61K__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490ea7",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_50Cjk__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490ea7",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_9yXAw__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490ea7",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_bRivD__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490ea7",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_nl59L__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490ea7",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_tJbHx__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11490ea7",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_vKa79__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11491008",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_50Cjk__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11491008",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_69s9w__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11491008",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_9yXAw__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11491016",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_vKa79__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_1149131f",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_ccc_long_text__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11491339",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11491339",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"SalesOrderObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11491339",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_C0vxo__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11491339",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_CRen2__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11491339",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_bYg5S__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11491339",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_ccc_long_text__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_11491339",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"object_z2K27__c",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":false,
								"linkAppId":"FSAID_127a3981",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AppraiseObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":true,
								"inLay":false,
								"linkAppId":"FSAID_127a3981",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"PartnerObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c83",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d93",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d93",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CasesObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d93",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ContactObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d93",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490ea7",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CasesObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d97",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"DeviceObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountAddrObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountFinInfoObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AdvertisementObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"BIBlankTemplate",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"BIDEV",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"BIDashBoard",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"BIStatisticalAgg",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ContactObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"InvoiceApplicationObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"OrderPaymentObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"PartnerObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"PaymentObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"SalesOrderObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490c84",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"SalesOrderProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_127a3981",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_127a3981",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CasesObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_127a3981",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ContactObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_127a3981",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d40",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d40",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"SalesOrderObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountAddrObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountFinInfoObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"AccountMainDataObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"BOMObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CompetitorObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ContractObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"EnterpriseInfoObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"InvoiceApplicationObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"MarketingEventObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"NewOpportunityContactsObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"NewOpportunityLinesObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"NewOpportunityObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"OpportunityObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"OrderPaymentObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"PaymentObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"PaymentPlanObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"PriceBookObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"PriceBookProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ProductObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"QuoteLinesObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"QuoteObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"RefundObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ReturnedGoodsInvoiceObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"SPUObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"SalesOrderObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ScheduleObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ScheduleRepeatObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_11490d9e",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"ServiceLogObj",
								"ownerAllocateRule":"auto"
							},
							{
								"@type":"com.facishare.linkapp.api.result.LinkAppAssociationObjectData",
								"allowRemove":false,
								"inLay":true,
								"linkAppId":"FSAID_127a3982",
								"needAllocate":true,
								"needAllocateCreateEditLayout":false,
								"needAllocateMobileSummaryLayout":false,
								"objectApiName":"CasesObj",
								"ownerAllocateRule":"auto"
							}
						],
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-biz/66de6149788ceb7d2c4430e0"
					}
				}
			],
			"com.fxiaoke.enterpriserelation.objrest.service.MigrateToolService#createMigrateOutOwner":[
				{
					
					"args":[
						{
							"@type":"java.util.HashMap",
							"x-tenant-id":78057,
							"x-user-id":-10000
						},
						{
							"@type":"com.fxiaoke.enterpriserelation.objrest.arg.CreateMigrateOutOwnerArg",
							"actionCategory":4,
							"actionTarget":1,
							"executeType":1,
							"extraData":{
								"@type":"java.util.HashMap",
								"receiverId":300405026L,
								"fromOuterUserName":"范旭东1",
								"toOuterUserName":"李慧慧"
							},
							"migrateObjectList":[
								{
									"apiName":"object_s3hjO__c",
									"describeApiName":"zm-从对象2"
								},
								{
									"apiName":"object_C2Zm5__c",
									"describeApiName":"wkl关联B2主"
								},
								{
									"apiName":"object_mx4tq__c",
									"describeApiName":"lihh对象E"
								},
								{
									"apiName":"object_E6mOR__c",
									"describeApiName":"wkl-关联A被关联"
								},
								{
									"apiName":"object_Npvqm__c",
									"describeApiName":"上下游-lm-1"
								},
								{
									"apiName":"SPUObj",
									"describeApiName":"商品"
								},
								{
									"apiName":"object_NeWWm__c",
									"describeApiName":"HG全字段-主"
								},
								{
									"apiName":"DeliveryNoteProductObj",
									"describeApiName":"发货单产品"
								},
								{
									"apiName":"object_vKa79__c",
									"describeApiName":"zj-对象D"
								},
								{
									"apiName":"object_cX76o__c",
									"describeApiName":"【删除】wkl导入"
								},
								{
									"apiName":"object_71Wtp__c",
									"describeApiName":"zm-从对象1"
								},
								{
									"apiName":"object_e1Fe4__c",
									"describeApiName":"fj-测试更改集"
								},
								{
									"apiName":"OutboundDeliveryNoteProductObj",
									"describeApiName":"出库单产品"
								},
								{
									"apiName":"object_9yXAw__c",
									"describeApiName":"lihh主从1-从1"
								},
								{
									"apiName":"PriceBookProductObj",
									"describeApiName":"价目表明细"
								},
								{
									"apiName":"object_15Qyb__c",
									"describeApiName":"lihh对象J"
								},
								{
									"apiName":"object_Jrwa6__c",
									"describeApiName":"主对象"
								},
								{
									"apiName":"QuoteLinesObj",
									"describeApiName":"报价单明细"
								},
								{
									"apiName":"NewOpportunityContactsObj",
									"describeApiName":"商机联系人"
								},
								{
									"apiName":"ScheduleObj",
									"describeApiName":"日程"
								},
								{
									"apiName":"NewOpportunityLinesObj",
									"describeApiName":"商机2.0明细"
								},
								{
									"apiName":"object_K3G8n__c",
									"describeApiName":"汇率-从对象"
								},
								{
									"apiName":"object_Wm4y7__c",
									"describeApiName":"fby-22"
								},
								{
									"apiName":"object_1kFK6__c",
									"describeApiName":"hb权限测试对象"
								},
								{
									"apiName":"object_6OHm3__c",
									"describeApiName":"布局mj"
								},
								{
									"apiName":"CreditFileObj",
									"describeApiName":"信用"
								},
								{
									"apiName":"object_76is3__c",
									"describeApiName":"zm-所有字段"
								},
								{
									"apiName":"object_Cgiv1__c",
									"describeApiName":"fby-导入导出-从2"
								},
								{
									"apiName":"object_mr1FM__c",
									"describeApiName":"lihh主从1-从2"
								},
								{
									"apiName":"ServiceLogObj",
									"describeApiName":"服务记录"
								},
								{
									"apiName":"ScheduleRepeatObj",
									"describeApiName":"日程循环"
								},
								{
									"apiName":"object_72o1R__c",
									"describeApiName":"wbqx-new-B"
								},
								{
									"apiName":"PaymentPlanObj",
									"describeApiName":"回款计划"
								},
								{
									"apiName":"object_z2K27__c",
									"describeApiName":"瓜瓜国家省市区（勿动）"
								},
								{
									"apiName":"ShoppingCartObj",
									"describeApiName":"购物车"
								},
								{
									"apiName":"object_ki71k__c",
									"describeApiName":"fby-22从"
								},
								{
									"apiName":"object_shenqing__c",
									"describeApiName":"申请单"
								},
								{
									"apiName":"object_UKrgv__c",
									"describeApiName":"lhh配置包C (订货通-对象1)"
								},
								{
									"apiName":"AppraiseObj",
									"describeApiName":"服务评价"
								},
								{
									"apiName":"SpecificationObj",
									"describeApiName":"规格"
								},
								{
									"apiName":"StockDetailsObj",
									"describeApiName":"出入库明细"
								},
								{
									"apiName":"object_TestInterconnectLayout__c",
									"describeApiName":"测试互联布局"
								},
								{
									"apiName":"PriceBookObj",
									"describeApiName":"价目表"
								},
								{
									"apiName":"object_50Cjk__c",
									"describeApiName":"lihh主从1-主"
								},
								{
									"apiName":"object_pjy2L__c",
									"describeApiName":"zm-函数测试"
								},
								{
									"apiName":"object_szFPV__c",
									"describeApiName":"wkl审批从"
								},
								{
									"apiName":"GoodsReceivedNoteObj",
									"describeApiName":"入库单"
								},
								{
									"apiName":"StockObj",
									"describeApiName":"库存"
								},
								{
									"apiName":"ShoppingCartSummaryObj",
									"describeApiName":"购物车摘要"
								},
								{
									"apiName":"AccountObj",
									"describeApiName":"客户AccountObj"
								},
								{
									"apiName":"StockCheckNoteProductObj",
									"describeApiName":"盘点单产品"
								},
								{
									"apiName":"object_2830v__c",
									"describeApiName":"lihh主从2-从1"
								},
								{
									"apiName":"object_n8uTn__c",
									"describeApiName":"自行车(INAG)"
								},
								{
									"apiName":"object_4ZcMy__c",
									"describeApiName":"lihh对象G"
								},
								{
									"apiName":"object_8HGma__c",
									"describeApiName":"业务链测试-从1"
								},
								{
									"apiName":"object_AS1n1__c",
									"describeApiName":"HG全字段-从"
								},
								{
									"apiName":"object_0q605__c",
									"describeApiName":"zxf"
								},
								{
									"apiName":"object_TXz6s__c",
									"describeApiName":"lihh主从2-从4"
								},
								{
									"apiName":"OrderPaymentObj",
									"describeApiName":"回款明细"
								},
								{
									"apiName":"object_Jf6yP__c",
									"describeApiName":"zm-导入导出（4W+大数据）"
								},
								{
									"apiName":"object_c616h__c",
									"describeApiName":"wkl_what"
								},
								{
									"apiName":"object_2eAAx__c",
									"describeApiName":"fby-导入导出-主"
								},
								{
									"apiName":"SpecificationValueObj",
									"describeApiName":"规格值"
								},
								{
									"apiName":"object_63jxG__c",
									"describeApiName":"汇率测试"
								},
								{
									"apiName":"object_90Lvu__c",
									"describeApiName":"fby-自增编号"
								},
								{
									"apiName":"object_xA280__c",
									"describeApiName":"xA开头打印报错bug验证"
								},
								{
									"apiName":"RefundObj",
									"describeApiName":"退款"
								},
								{
									"apiName":"object_Wf2ot__c",
									"describeApiName":"wkl三流组件"
								},
								{
									"apiName":"CheckinsObj",
									"describeApiName":"高级外勤"
								},
								{
									"apiName":"object_yR1Y9__c",
									"describeApiName":"hb-阶段推进器测试对象"
								},
								{
									"apiName":"object_bYg5S__c",
									"describeApiName":"zzxui事件"
								},
								{
									"apiName":"RebateIncomeDetailObj",
									"describeApiName":"返利"
								},
								{
									"apiName":"CompetitorObj",
									"describeApiName":"竞争对手"
								},
								{
									"apiName":"object_6mWx2__c",
									"describeApiName":"wkl主"
								},
								{
									"apiName":"object_k21Ai__c",
									"describeApiName":"zm-770版本测试2"
								},
								{
									"apiName":"WarehouseObj",
									"describeApiName":"仓库"
								},
								{
									"apiName":"object_1hFy1__c",
									"describeApiName":"fby-导入导出-从1"
								},
								{
									"apiName":"object_H1brV__c",
									"describeApiName":"wb-外部人员主"
								},
								{
									"apiName":"object_xLcPb__c",
									"describeApiName":"ccc-主"
								},
								{
									"apiName":"GoodsReceivedNoteProductObj",
									"describeApiName":"入库单产品"
								},
								{
									"apiName":"MarketingEventObj",
									"describeApiName":"市场活动"
								},
								{
									"apiName":"object_MO77s__c",
									"describeApiName":"hb-审批流测试对象"
								},
								{
									"apiName":"object_xE7Dx__c",
									"describeApiName":"lihh对象D"
								},
								{
									"apiName":"OutboundDeliveryNoteObj",
									"describeApiName":"出库单"
								},
								{
									"apiName":"object_s1z2E__c",
									"describeApiName":"测试-fby-zhu"
								},
								{
									"apiName":"object_7sVOl__c",
									"describeApiName":"对象M1"
								},
								{
									"apiName":"object_T6jtZ__c",
									"describeApiName":"wbqx-关闭相关团队"
								},
								{
									"apiName":"CampaignMembersObj",
									"describeApiName":"活动成员"
								},
								{
									"apiName":"object_tJbHx__c",
									"describeApiName":"zj-自定义对象"
								},
								{
									"apiName":"object_CbEgn__c",
									"describeApiName":"HG全字段-主-关联对象"
								},
								{
									"apiName":"object_sIYbf__c",
									"describeApiName":"hb测试权限对象"
								},
								{
									"apiName":"IkunObj",
									"describeApiName":"IKun"
								},
								{
									"apiName":"object_7sk5c__c",
									"describeApiName":"prm主"
								},
								{
									"apiName":"StockCheckNoteObj",
									"describeApiName":"盘点单"
								},
								{
									"apiName":"scancodelog__c",
									"describeApiName":"保利乐加扫码日志"
								},
								{
									"apiName":"object_13xGk__c",
									"describeApiName":"业务链测试-主"
								},
								{
									"apiName":"object_nl59L__c",
									"describeApiName":"wbqx-hh-name重复"
								},
								{
									"apiName":"object_omk22__c",
									"describeApiName":"fby-引用字段"
								},
								{
									"apiName":"object_OU699__c",
									"describeApiName":"qaz-大量数据"
								},
								{
									"apiName":"ReturnedGoodsInvoiceProductObj",
									"describeApiName":"退货单产品"
								},
								{
									"apiName":"NewOpportunityObj",
									"describeApiName":"商机2.0改名88"
								},
								{
									"apiName":"EnterpriseInfoObj",
									"describeApiName":"企业库"
								},
								{
									"apiName":"QuoteObj",
									"describeApiName":"报价单"
								},
								{
									"apiName":"Object_master_geOtD__c",
									"describeApiName":"112自动化测试主geOtD"
								},
								{
									"apiName":"object_ti13X__c",
									"describeApiName":"lihh对象C"
								},
								{
									"apiName":"object_6B2y2__c",
									"describeApiName":"币种没有默认值和缓存"
								},
								{
									"apiName":"object_1gB1z__c",
									"describeApiName":"fj-测试负责人非必输"
								},
								{
									"apiName":"object_69s9w__c",
									"describeApiName":"lihh对象B-审批"
								},
								{
									"apiName":"object_FcGm1__c",
									"describeApiName":"lmh-主"
								},
								{
									"apiName":"LeadsFlowRecordObj",
									"describeApiName":"线索跟进过程记录"
								},
								{
									"apiName":"object_11Mun__c",
									"describeApiName":"wkl01-关联别人"
								},
								{
									"apiName":"object_42DX2__c",
									"describeApiName":"lhh配置包A"
								},
								{
									"apiName":"object_contract__c",
									"describeApiName":"保利乐加合同"
								},
								{
									"apiName":"object_vgskN__c",
									"describeApiName":"zm-企业互联"
								},
								{
									"apiName":"object_4cI02__c",
									"describeApiName":"zzx群策打分器"
								},
								{
									"apiName":"object_6RfV9__c",
									"describeApiName":"kakaxi-多时区主"
								},
								{
									"apiName":"object_28K21__c",
									"describeApiName":"zj-对象C-函数插件"
								},
								{
									"apiName":"object_nzufh__c",
									"describeApiName":"zm-主对象"
								},
								{
									"apiName":"RequisitionNoteProductObj",
									"describeApiName":"调拨单产品"
								},
								{
									"apiName":"object_1w7x5__c",
									"describeApiName":"上下游执行函数"
								},
								{
									"apiName":"object_yA7cC__c",
									"describeApiName":"对象OP"
								},
								{
									"apiName":"object_uW82g__c",
									"describeApiName":"swj布局测试对象001"
								},
								{
									"apiName":"object_bRivD__c",
									"describeApiName":"zj-对象A"
								},
								{
									"apiName":"object_20qq6__c",
									"describeApiName":"zwr-主"
								},
								{
									"apiName":"ActiveRecordObj",
									"describeApiName":"销售记录"
								},
								{
									"apiName":"ReturnedGoodsInvoiceObj",
									"describeApiName":"退货单"
								},
								{
									"apiName":"object_63nDt__c",
									"describeApiName":"wkl从可以单独新建"
								},
								{
									"apiName":"DeliveryNoteObj",
									"describeApiName":"发货单"
								},
								{
									"apiName":"object_1xv4o__c",
									"describeApiName":"附件格式限制"
								},
								{
									"apiName":"object_G8fh1__c",
									"describeApiName":"qaz-测试1-M"
								},
								{
									"apiName":"object_vKAm8__c",
									"describeApiName":"URl-Action"
								},
								{
									"apiName":"AccountFinInfoObj",
									"describeApiName":"客户财务信息"
								},
								{
									"apiName":"object_kJa7y__c",
									"describeApiName":"zm-关联主对象"
								},
								{
									"apiName":"object_0r1Tl__c",
									"describeApiName":"lihh对象A"
								},
								{
									"apiName":"object_cf3x3__c",
									"describeApiName":"kakaxi-多时区A"
								},
								{
									"apiName":"object_72fW1__c",
									"describeApiName":"zxf-ocr"
								},
								{
									"apiName":"object_r17a4__c",
									"describeApiName":"小程序测试"
								},
								{
									"apiName":"object_3k61K__c",
									"describeApiName":"lihh主从2-主"
								},
								{
									"apiName":"object_z0GZI__c",
									"describeApiName":"zj-多时区"
								},
								{
									"apiName":"SalesOrderObj",
									"describeApiName":"销售订单"
								},
								{
									"apiName":"ContractObj",
									"describeApiName":"合同"
								},
								{
									"apiName":"CustomerAccountObj",
									"describeApiName":"客户账户"
								},
								{
									"apiName":"OpportunityObj",
									"describeApiName":"商机2"
								},
								{
									"apiName":"object_C0vxo__c",
									"describeApiName":"fby-11主"
								},
								{
									"apiName":"PaymentObj",
									"describeApiName":"回款"
								},
								{
									"apiName":"object_CRen2__c",
									"describeApiName":"fby-11从"
								},
								{
									"apiName":"object_93098__c",
									"describeApiName":"场景测试"
								},
								{
									"apiName":"object_PbY23__c",
									"describeApiName":"zm-通用对象1"
								},
								{
									"apiName":"object_ikml2__c",
									"describeApiName":"多维度-test"
								},
								{
									"apiName":"object_Kh37y__c",
									"describeApiName":"自定义对象1"
								},
								{
									"apiName":"object_hb_business_flow_test_object__c",
									"describeApiName":"hb-业务流测试对象"
								},
								{
									"apiName":"AccountAddrObj",
									"describeApiName":"客户地址"
								},
								{
									"apiName":"InvoiceApplicationObj",
									"describeApiName":"开票申请333"
								},
								{
									"apiName":"object_scdB5__c",
									"describeApiName":"kakaxi-多时区从"
								},
								{
									"apiName":"object_Ce152__c",
									"describeApiName":"字段导入"
								},
								{
									"apiName":"PrepayDetailObj",
									"describeApiName":"预存款"
								},
								{
									"apiName":"object_1uYvV__c",
									"describeApiName":"testA"
								},
								{
									"apiName":"object_2Hilj__c",
									"describeApiName":"hb-工作流测试对象"
								},
								{
									"apiName":"BehaviorRecordObj",
									"describeApiName":"行为记录"
								},
								{
									"apiName":"PartnerObj",
									"describeApiName":"合作伙伴"
								},
								{
									"apiName":"object_InternetLayoutTest__c",
									"describeApiName":"互联布局测试"
								},
								{
									"apiName":"object_08y31__c",
									"describeApiName":"zm-770版本测试"
								},
								{
									"apiName":"RequisitionNoteObj",
									"describeApiName":"调拨单"
								},
								{
									"apiName":"ProductObj",
									"describeApiName":"产品1"
								},
								{
									"apiName":"SalesOrderProductObj",
									"describeApiName":"订单产品"
								},
								{
									"apiName":"object_CWe4t__c",
									"describeApiName":"币种没有默认值和缓存从"
								},
								{
									"apiName":"LeadsObj",
									"describeApiName":"销售线索"
								},
								{
									"apiName":"object_L2k2I__c",
									"describeApiName":"zxf-主"
								},
								{
									"apiName":"InvoiceApplicationLinesObj",
									"describeApiName":"开票申请明细"
								},
								{
									"apiName":"ContactObj",
									"describeApiName":"联系人"
								},
								{
									"apiName":"object_cZ2A6__c",
									"describeApiName":"qaz-测试3-B"
								},
								{
									"apiName":"object_497yb__c",
									"describeApiName":"wkl审批主"
								},
								{
									"apiName":"CasesObj",
									"describeApiName":"工单"
								},
								{
									"apiName":"RebateUseRuleObj",
									"describeApiName":"返利使用规则"
								},
								{
									"apiName":"object_test__c",
									"describeApiName":"工具创建测试"
								},
								{
									"apiName":"object_bRCh5__c",
									"describeApiName":"导入-主"
								},
								{
									"apiName":"SaleContractObj",
									"describeApiName":"销售合同"
								},
								{
									"apiName":"object_ccc_long_text__c",
									"describeApiName":"ccc-长文本"
								},
								{
									"apiName":"object_e3jJl__c",
									"describeApiName":"讲师库"
								}
							],
							"migrateSourceList":[
								{
									"apiName":"out_owner",
									"dataName":"范旭东1",
									"dataValue":"300401099",
									"outTenantId":"300111710"
								}
							],
							"migrateTarget":{
								"apiName":"out_owner",
								"dataName":"李慧慧",
								"dataValue":"300405026",
								"outTenantId":"200071698"
							},
							"needSendMqMessage":true,
							"postId":"fjyAfterTest2"
						}
					],
					"result":{
						"@type":"com.fxiaoke.enterpriserelation.objrest.result.CreateMigrateOutOwnerResult",
						"code":200,
						"data":{
							"errorCode":-1,
							"errorMessage":"Duplicate"
						},
						"message":"OK"
					}
				}
			],
			"com.fxiaoke.crmrestapi.service.ObjectDescribeService#queryDisplayNameByApiNames":[
				{
					
					"args":[
						{
							"@type":"com.fxiaoke.crmrestapi.common.data.HeaderObj",
							"X-fs-Employee-Id":-10000,
							"client_info":"rest-api",
							"x-fs-userInfo":-10000,
							"X-fs-Enterprise-Id":78057,
							"X-fs-ei":78057
						},
						{
							"@type":"com.fxiaoke.crmrestapi.arg.QueryDisplayNameByApiNamesArg",
							"apiNames":["CampaignMembersObj","CasesObj","DeliveryNoteObj","DeliveryNoteProductObj","EnterpriseInfoObj","IkunObj","MarketingEventObj","ShoppingCartObj","ShoppingCartSummaryObj","object_63jxG__c","object_6OHm3__c","object_72o1R__c","object_Ce152__c","object_InternetLayoutTest__c","object_K3G8n__c","object_T6jtZ__c","object_TestInterconnectLayout__c","object_UKrgv__c","object_ccc_long_text__c","object_shenqing__c","object_tJbHx__c","BehaviorRecordObj","PartnerObj","object_nl59L__c","object_tJbHx__c","object_z0GZI__c","AccountObj","ActiveRecordObj","BehaviorRecordObj","CampaignMembersObj","CasesObj","CheckinsObj","ContactObj","CreditFileObj","CustomerAccountObj","DeliveryNoteObj","DeliveryNoteProductObj","GoodsReceivedNoteObj","GoodsReceivedNoteProductObj","InvoiceApplicationLinesObj","LeadsFlowRecordObj","LeadsObj","Object_master_geOtD__c","OutboundDeliveryNoteObj","OutboundDeliveryNoteProductObj","PartnerObj","PrepayDetailObj","RebateIncomeDetailObj","RebateUseRuleObj","RequisitionNoteObj","RequisitionNoteProductObj","ReturnedGoodsInvoiceProductObj","SaleContractObj","SalesOrderProductObj","SpecificationObj","SpecificationValueObj","StockCheckNoteObj","StockCheckNoteProductObj","StockDetailsObj","StockObj","WarehouseObj","object_08y31__c","object_0LXl2__c","object_0q605__c","object_0r1Tl__c","object_11Mun__c","object_13xGk__c","object_15Qyb__c","object_1gB1z__c","object_1hFy1__c","object_1kFK6__c","object_1uYvV__c","object_1w7x5__c","object_1xv4o__c","object_20qq6__c","object_2830v__c","object_2Hilj__c","object_2eAAx__c","object_3k61K__c","object_42DX2__c","object_497yb__c","object_4ZcMy__c","object_4cI02__c","object_50Cjk__c","object_63jxG__c","object_63nDt__c","object_69s9w__c","object_6B2y2__c","object_6RfV9__c","object_6mWx2__c","object_707E8__c","object_71Wtp__c","object_72fW1__c","object_72o1R__c","object_76is3__c","object_7sVOl__c","object_7sk5c__c","object_85rZM__c","object_8HGma__c","object_90Lvu__c","object_93098__c","object_9yXAw__c","object_AS1n1__c","object_C0vxo__c","object_C2Zm5__c","object_CRen2__c","object_CWe4t__c","object_CbEgn__c","object_Cgiv1__c","object_E6mOR__c","object_FcGm1__c","object_G8fh1__c","object_H1brV__c","object_InternetLayoutTest__c","object_Jf6yP__c","object_Jrwa6__c","object_K3G8n__c","object_Kh37y__c","object_L2k2I__c","object_MO77s__c","object_NeWWm__c","object_Npvqm__c","object_OU699__c","object_Oj61n__c","object_PbY23__c","object_QCf97__c","object_T6jtZ__c","object_TXz6s__c","object_Wf2ot__c","object_Wm4y7__c","object_bRCh5__c","object_bRivD__c","object_c616h__c","object_cX76o__c","object_cZ2A6__c","object_cf3x3__c","object_contract__c","object_e1Fe4__c","object_e3jJl__c","object_gaGT2__c","object_hNgKo__c","object_hb_business_flow_test_object__c","object_ikml2__c","object_jKqk0__c","object_k21Ai__c","object_kJa7y__c","object_ki71k__c","object_ma4FF__c","object_mr1FM__c","object_mx4tq__c","object_n8uTn__c","object_nzufh__c","object_omk22__c","object_pjy2L__c","object_r17a4__c","object_s1z2E__c","object_s3hjO__c","object_sIYbf__c","object_scdB5__c","object_szFPV__c","object_tJbHx__c","object_test__c","object_ti13X__c","object_uW82g__c","object_vKAm8__c","object_vgskN__c","object_xA280__c","object_xE7Dx__c","object_xLcPb__c","object_yA7cC__c","object_yR1Y9__c","object_z0GZI__c","object_z2K27__c","scancodelog__c","MarketingEventObj","object_28K21__c","object_3k61K__c","object_50Cjk__c","object_9yXAw__c","object_bRivD__c","object_nl59L__c","object_tJbHx__c","object_vKa79__c","object_50Cjk__c","object_69s9w__c","object_9yXAw__c","object_vKa79__c","object_ccc_long_text__c","AccountObj","SalesOrderObj","object_C0vxo__c","object_CRen2__c","object_bYg5S__c","object_ccc_long_text__c","object_z2K27__c","AppraiseObj","PartnerObj","AccountObj","AccountObj","CasesObj","ContactObj","ProductObj","CasesObj","DeviceObj","AccountAddrObj","AccountFinInfoObj","AccountObj","AdvertisementObj","BIBlankTemplate","BIDEV","BIDashBoard","BIStatisticalAgg","ContactObj","InvoiceApplicationObj","OrderPaymentObj","PartnerObj","PaymentObj","ProductObj","SalesOrderObj","SalesOrderProductObj","AccountObj","CasesObj","ContactObj","ProductObj","AccountObj","SalesOrderObj","AccountAddrObj","AccountFinInfoObj","AccountMainDataObj","BOMObj","CompetitorObj","ContractObj","EnterpriseInfoObj","InvoiceApplicationObj","MarketingEventObj","NewOpportunityContactsObj","NewOpportunityLinesObj","NewOpportunityObj","OpportunityObj","OrderPaymentObj","PaymentObj","PaymentPlanObj","PriceBookObj","PriceBookProductObj","ProductObj","QuoteLinesObj","QuoteObj","RefundObj","ReturnedGoodsInvoiceObj","SPUObj","SalesOrderObj","ScheduleObj","ScheduleRepeatObj","ServiceLogObj","CasesObj"]
						}
					],
					"result":{
						"@type":"com.fxiaoke.crmrestapi.common.result.Result",
						"code":0,
						"data":{
							"@type":"com.fxiaoke.crmrestapi.result.QueryDisplayNameByApiNamesResult",
							"displayNames":{
								"@type":"java.util.HashMap",
								"object_s3hjO__c":"zm-从对象2",
								"object_C2Zm5__c":"wkl关联B2主",
								"object_mx4tq__c":"lihh对象E",
								"object_E6mOR__c":"wkl-关联A被关联",
								"object_Npvqm__c":"上下游-lm-1",
								"SPUObj":"商品",
								"object_NeWWm__c":"HG全字段-主",
								"DeliveryNoteProductObj":"发货单产品",
								"object_vKa79__c":"zj-对象D",
								"object_cX76o__c":"【删除】wkl导入",
								"object_71Wtp__c":"zm-从对象1",
								"object_e1Fe4__c":"fj-测试更改集",
								"OutboundDeliveryNoteProductObj":"出库单产品",
								"object_9yXAw__c":"lihh主从1-从1",
								"PriceBookProductObj":"价目表明细",
								"object_15Qyb__c":"lihh对象J",
								"object_Jrwa6__c":"主对象",
								"QuoteLinesObj":"报价单明细",
								"NewOpportunityContactsObj":"商机联系人",
								"ScheduleObj":"日程",
								"NewOpportunityLinesObj":"商机2.0明细",
								"object_K3G8n__c":"汇率-从对象",
								"object_Wm4y7__c":"fby-22",
								"object_1kFK6__c":"hb权限测试对象",
								"object_6OHm3__c":"布局mj",
								"CreditFileObj":"信用",
								"object_76is3__c":"zm-所有字段",
								"object_Cgiv1__c":"fby-导入导出-从2",
								"object_mr1FM__c":"lihh主从1-从2",
								"ServiceLogObj":"服务记录",
								"ScheduleRepeatObj":"日程循环",
								"object_72o1R__c":"wbqx-new-B",
								"PaymentPlanObj":"回款计划",
								"object_z2K27__c":"瓜瓜国家省市区（勿动）",
								"ShoppingCartObj":"购物车",
								"object_ki71k__c":"fby-22从",
								"object_shenqing__c":"申请单",
								"object_UKrgv__c":"lhh配置包C (订货通-对象1)",
								"AppraiseObj":"服务评价",
								"SpecificationObj":"规格",
								"StockDetailsObj":"出入库明细",
								"object_TestInterconnectLayout__c":"测试互联布局",
								"PriceBookObj":"价目表",
								"object_50Cjk__c":"lihh主从1-主",
								"object_pjy2L__c":"zm-函数测试",
								"object_szFPV__c":"wkl审批从",
								"GoodsReceivedNoteObj":"入库单",
								"StockObj":"库存",
								"ShoppingCartSummaryObj":"购物车摘要",
								"AccountObj":"客户AccountObj",
								"StockCheckNoteProductObj":"盘点单产品",
								"object_2830v__c":"lihh主从2-从1",
								"object_n8uTn__c":"自行车(INAG)",
								"object_4ZcMy__c":"lihh对象G",
								"object_8HGma__c":"业务链测试-从1",
								"object_AS1n1__c":"HG全字段-从",
								"object_0q605__c":"zxf",
								"object_TXz6s__c":"lihh主从2-从4",
								"OrderPaymentObj":"回款明细",
								"object_Jf6yP__c":"zm-导入导出（4W+大数据）",
								"object_c616h__c":"wkl_what",
								"object_2eAAx__c":"fby-导入导出-主",
								"SpecificationValueObj":"规格值",
								"object_63jxG__c":"汇率测试",
								"object_90Lvu__c":"fby-自增编号",
								"object_xA280__c":"xA开头打印报错bug验证",
								"RefundObj":"退款",
								"object_Wf2ot__c":"wkl三流组件",
								"CheckinsObj":"高级外勤",
								"object_yR1Y9__c":"hb-阶段推进器测试对象",
								"object_bYg5S__c":"zzxui事件",
								"RebateIncomeDetailObj":"返利",
								"CompetitorObj":"竞争对手",
								"object_6mWx2__c":"wkl主",
								"object_k21Ai__c":"zm-770版本测试2",
								"WarehouseObj":"仓库",
								"object_1hFy1__c":"fby-导入导出-从1",
								"object_H1brV__c":"wb-外部人员主",
								"object_xLcPb__c":"ccc-主",
								"GoodsReceivedNoteProductObj":"入库单产品",
								"MarketingEventObj":"市场活动",
								"object_MO77s__c":"hb-审批流测试对象",
								"object_xE7Dx__c":"lihh对象D",
								"OutboundDeliveryNoteObj":"出库单",
								"object_s1z2E__c":"测试-fby-zhu",
								"object_7sVOl__c":"对象M1",
								"object_T6jtZ__c":"wbqx-关闭相关团队",
								"CampaignMembersObj":"活动成员",
								"object_tJbHx__c":"zj-自定义对象",
								"object_CbEgn__c":"HG全字段-主-关联对象",
								"object_sIYbf__c":"hb测试权限对象",
								"IkunObj":"IKun",
								"object_7sk5c__c":"prm主",
								"StockCheckNoteObj":"盘点单",
								"scancodelog__c":"保利乐加扫码日志",
								"object_13xGk__c":"业务链测试-主",
								"object_nl59L__c":"wbqx-hh-name重复",
								"object_omk22__c":"fby-引用字段",
								"object_OU699__c":"qaz-大量数据",
								"ReturnedGoodsInvoiceProductObj":"退货单产品",
								"NewOpportunityObj":"商机2.0改名88",
								"EnterpriseInfoObj":"企业库",
								"QuoteObj":"报价单",
								"Object_master_geOtD__c":"112自动化测试主geOtD",
								"object_ti13X__c":"lihh对象C",
								"object_6B2y2__c":"币种没有默认值和缓存",
								"object_1gB1z__c":"fj-测试负责人非必输",
								"object_69s9w__c":"lihh对象B-审批",
								"object_FcGm1__c":"lmh-主",
								"LeadsFlowRecordObj":"线索跟进过程记录",
								"object_11Mun__c":"wkl01-关联别人",
								"object_42DX2__c":"lhh配置包A",
								"object_contract__c":"保利乐加合同",
								"object_vgskN__c":"zm-企业互联",
								"object_4cI02__c":"zzx群策打分器",
								"object_6RfV9__c":"kakaxi-多时区主",
								"object_28K21__c":"zj-对象C-函数插件",
								"object_nzufh__c":"zm-主对象",
								"RequisitionNoteProductObj":"调拨单产品",
								"object_1w7x5__c":"上下游执行函数",
								"object_yA7cC__c":"对象OP",
								"object_uW82g__c":"swj布局测试对象001",
								"object_bRivD__c":"zj-对象A",
								"object_20qq6__c":"zwr-主",
								"ActiveRecordObj":"销售记录",
								"ReturnedGoodsInvoiceObj":"退货单",
								"object_63nDt__c":"wkl从可以单独新建",
								"DeliveryNoteObj":"发货单",
								"object_1xv4o__c":"附件格式限制",
								"object_G8fh1__c":"qaz-测试1-M",
								"object_vKAm8__c":"URl-Action",
								"AccountFinInfoObj":"客户财务信息",
								"object_kJa7y__c":"zm-关联主对象",
								"object_0r1Tl__c":"lihh对象A",
								"object_cf3x3__c":"kakaxi-多时区A",
								"object_72fW1__c":"zxf-ocr",
								"object_r17a4__c":"小程序测试",
								"object_3k61K__c":"lihh主从2-主",
								"object_z0GZI__c":"zj-多时区",
								"SalesOrderObj":"销售订单",
								"ContractObj":"合同",
								"CustomerAccountObj":"客户账户",
								"OpportunityObj":"商机2",
								"object_C0vxo__c":"fby-11主",
								"PaymentObj":"回款",
								"object_CRen2__c":"fby-11从",
								"object_93098__c":"场景测试",
								"object_PbY23__c":"zm-通用对象1",
								"object_ikml2__c":"多维度-test",
								"object_Kh37y__c":"自定义对象1",
								"object_hb_business_flow_test_object__c":"hb-业务流测试对象",
								"AccountAddrObj":"客户地址",
								"InvoiceApplicationObj":"开票申请333",
								"object_scdB5__c":"kakaxi-多时区从",
								"object_Ce152__c":"字段导入",
								"PrepayDetailObj":"预存款",
								"object_1uYvV__c":"testA",
								"object_2Hilj__c":"hb-工作流测试对象",
								"BehaviorRecordObj":"行为记录",
								"PartnerObj":"合作伙伴",
								"object_InternetLayoutTest__c":"互联布局测试",
								"object_08y31__c":"zm-770版本测试",
								"RequisitionNoteObj":"调拨单",
								"ProductObj":"产品1",
								"SalesOrderProductObj":"订单产品",
								"object_CWe4t__c":"币种没有默认值和缓存从",
								"LeadsObj":"销售线索",
								"object_L2k2I__c":"zxf-主",
								"InvoiceApplicationLinesObj":"开票申请明细",
								"ContactObj":"联系人",
								"object_cZ2A6__c":"qaz-测试3-B",
								"object_497yb__c":"wkl审批主",
								"CasesObj":"工单",
								"RebateUseRuleObj":"返利使用规则",
								"object_test__c":"工具创建测试",
								"object_bRCh5__c":"导入-主",
								"SaleContractObj":"销售合同",
								"object_ccc_long_text__c":"ccc-长文本",
								"object_e3jJl__c":"讲师库"
							}
						},
						"message":"OK"
					}
				}
			],
			"com.fxiaoke.enterpriserelation.objrest.service.MigrateToolService#getHeader":[
				{
					
					"args":[
						78057
					],
					"result":{
						"@type":"java.util.HashMap",
						"x-tenant-id":78057,
						"x-user-id":-10000
					}
				}
			]
		},
		"id":1,
		"method":"com.facishare.rest.account.service.TransferDataPermissionService#transferOutOwner",
		"methodData":{
			"args":[
				78057,
				300111710L,
				300401099L,
				200071698L,
				300405026L,
				"fjyAfterTest2"
			],
			"result":true
		}
	},
	{
		
		"callDatas":{
			"com.facishare.converter.EIEAConverter#enterpriseIdToAccount":[
				{
					
					"args":[
						78057
					],
					"result":"obj0509"
				}
			],
			"com.facishare.rest.account.service.UnionMessageService#sendMessageByApp":[
				{
					
					"args":[
						{
							"@type":"com.facishare.er.api.model.arg.message.UnionMessageSendVo",
							"channels":Set[
								"OUTER_UID"
							],
							"fsAppId":"FSAID_2",
							"message":{
								"@type":"com.facishare.er.api.model.arg.message.UnionTextMessage",
								"content":"【fromUser】被合并到【toUser】，迁移成功数据1条，迁移失败数据1条。"
							},
							"receiverIds":Set[
								300410831L
							],
							"senderEa":"obj0509",
							"traceId":"a63025f56f864be2a9ad69ce3ec6c027"
						}
					],
					"result":{
						"@type":"com.facishare.enterprise.common.result.Result",
						"errCode":0,
						"errMsg":"成功",
						"traceId":"fs-enterprise-relation-biz/66de7025788ceb71f01d7e91"
					}
				}
			]
		},
		"id":101,
		"method":"com.facishare.rest.account.service.TransferDataPermissionService#sendTransferDataPermissionMessage",
		"methodData":{
			"args":[
				78057,
				1L,
				1L,
				{
					"@type":"com.google.common.collect.RegularImmutableMap",
					"receiverId":300410831L,
					"fromOuterUserName":"fromUser",
					"toOuterUserName":"toUser"
				}
			],
			"result":true
		}
	}
]