package com.facishare.erdepartment.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.AccountManageMode;
import com.facishare.enterprise.common.constant.EnterpriseRelationObjConstant;
import com.facishare.enterprise.common.constant.GlobalRoleConstants;
import com.facishare.enterprise.common.constant.KeyBuilder;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.exception.RelationException;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.model.OuterAccountVo;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.DistributeLock;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.enterprise.common.util.ListUtil;
import com.facishare.enterprise.common.util.OffsetUtil;
import com.facishare.erdepartment.admin.arg.*;
import com.facishare.erdepartment.admin.arg.CreateErDepartmentByDepartmentOrOrgs.DepartmentOrgArg;
import com.facishare.erdepartment.admin.result.*;
import com.facishare.erdepartment.admin.result.ListErDepartmentsUnderOneLevelWithPageResult.ErDepartmentData;
import com.facishare.erdepartment.admin.result.ListErDepartmentsUnderOneLevelWithPageResult.ParentPath;
import com.facishare.erdepartment.constant.PaasConstant;
import com.facishare.erdepartment.remote.PaasShareObjectManager;
import com.facishare.erdepartment.service.data.ErDepartmentObjData;
import com.facishare.erdepartment.service.data.ErDepartmentObjData.AssistantData;
import com.facishare.global.rest.dao.mongo.FxiaokeEmployeeAssociationEntityDao;
import com.facishare.global.rest.dao.mongo.FxiaokeEnterpriseAssociationEntityDao;
import com.facishare.global.rest.model.entity.FxiaokeEmployeeAssociationEntity;
import com.facishare.linkapp.api.service.LinkAppOuterRoleService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeMapByDepartmentIdArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeMapByDepartmentIdResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.rest.common.remote.PermissionManager;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.bizconf.arg.ConfigArg;
import com.fxiaoke.bizconf.arg.QueryConfigByRankArg;
import com.fxiaoke.bizconf.bean.Rank;
import com.fxiaoke.bizconf.bean.ValueType;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionBulkDeleteArg;
import com.fxiaoke.crmrestapi.arg.ActionBulkInvalidArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.AggregateQueryArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.PartnerFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.GroupByParameter;
import com.fxiaoke.crmrestapi.common.data.GroupByParameter.AggFunction;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.OrderBy;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.result.ActionBulkDeleteResult;
import com.fxiaoke.crmrestapi.result.ActionBulkInvalidResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.AggregateQueryResult;
import com.fxiaoke.crmrestapi.result.FindNameByIdsResult;
import com.fxiaoke.crmrestapi.result.FindNameByIdsResult.IdName;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.crmrestapi.service.PartnerService;
import com.fxiaoke.enterpriserelation.objrest.arg.BatchCreatePublicEmployeesArg;
import com.fxiaoke.enterpriserelation.objrest.arg.CreateEnterpriseRelationWithFsAccountArg;
import com.fxiaoke.enterpriserelation.objrest.arg.IdsLongArg;
import com.fxiaoke.enterpriserelation.objrest.arg.ListEnterpriseRelationObjByConditionArg;
import com.fxiaoke.enterpriserelation.objrest.arg.ListPublicEmployeeObjByConditionArg;
import com.fxiaoke.enterpriserelation.objrest.common.BaseObjectData;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentObj;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentObjConstant;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentStatusEnum;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentTypeEnum;
import com.fxiaoke.enterpriserelation.objrest.common.IdentityTypeEnum;
import com.fxiaoke.enterpriserelation.objrest.result.BatchCreatePublicEmployeesResult;
import com.fxiaoke.enterpriserelation.objrest.result.ListByConditionResult;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.fxiaoke.enterpriserelation.objrest.service.PublicEmployeeObjService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: jiangxch
 * @date: 2023/7/10 17:28
 */
@Service
@Slf4j
public class ErDepartmentObjService {
    public static final String GROUP_IT_CONTROL_SWITCH_KEY = "group_it_control_switch_key";
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private FxiaokeEnterpriseAssociationEntityDao fxiaokeEnterpriseAssociationEntityDaoRemoteManager;
    @Autowired
    private FxiaokeEmployeeAssociationEntityDao fxiaokeEmployeeAssociationEntityDaoRemoteManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private BizConfClient bizConfClient;
    @Autowired
    private DistributeLock distributeLock;
    @Autowired
    private PaasShareObjectManager paasShareObjectManager;
    @Autowired
    private PermissionManager permissionManager;
    @Autowired
    private LinkAppOuterRoleService linkAppOuterRoleService;
    @Autowired
    private EnterpriseRelationObjService enterpriseRelationObjService;
    @Autowired
    private PublicEmployeeObjService publicEmployeeObjService;
    @Autowired
    private EmployeeProviderService employeeService;
    @Autowired
    private PartnerService partnerService;

    public Result<GetAllErDepartmentResult> getAllErDepartment(String upstreamEa, Integer userId, String currentEa) {
        return getAllErDepartment(upstreamEa, userId, currentEa, null);
    }

    public Result<GetAllErDepartmentResult> getAllErDepartment(String upstreamEa, Integer userId, String currentEa, Boolean onlyQueryShareObjectData) {
        // 取集团名称作为根节点(erDepartmentId=null,parentDepartmentId=null)
        GetAllErDepartmentResult getAllErDepartmentResult = new GetAllErDepartmentResult();
        getAllErDepartmentResult.setErDepartmentObjDatas(Lists.newArrayList());

        List<ErDepartmentObjData> allErDepartmentObjDatas = getAllErDepartmentObjDatas(upstreamEa, userId, onlyQueryShareObjectData);
        getAllErDepartmentResult.setErDepartmentObjDatas(allErDepartmentObjDatas);
        if (!CollectionUtils.isEmpty(allErDepartmentObjDatas)) {
            for (ErDepartmentObjData departmentObjData : allErDepartmentObjDatas) {
                if (currentEa.equals(departmentObjData.getEa())) {
                    getAllErDepartmentResult.setCurrentEnterpriseErDepartmentId(departmentObjData.getErDepartmentId());
                    break;
                }
            }
        }
        return Result.newSuccess(getAllErDepartmentResult);
    }

    private List<ErDepartmentObjData> getAllErDepartmentObjDatas(String upstreamEa, Integer userId, Boolean onlyQueryShareObjectData) {
        List<ErDepartmentObj> erDepartmentObjs = listAllErDepartmentObjs(upstreamEa, userId, onlyQueryShareObjectData);
        if (CollectionUtils.isEmpty(erDepartmentObjs)) {
            return Lists.newArrayList();
        }

        List<Long> outerTenantIds = erDepartmentObjs.stream().filter(x -> x.getDepartmentType() != null && x.getDepartmentType() == ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType())
                .map(ErDepartmentObj::getOuterTenantId).collect(Collectors.toList());
        Map<Long, String> outerTenantIdNameMap = getOuterTenantIdNameMap(upstreamEa, userId, outerTenantIds);

        List<Long> managerOuterUids = erDepartmentObjs.stream().filter(x -> x.getManagerId() != null).map(ErDepartmentObj::getManagerId).collect(Collectors.toList());
        List<Long> assistantOuterUids = Lists.newArrayList();
        for (ErDepartmentObj erDepartmentObj : erDepartmentObjs) {
            if (CollectionUtils.isEmpty(erDepartmentObj.getAssistantIds())) {
                continue;
            }
            assistantOuterUids.addAll(erDepartmentObj.getAssistantIds());
        }
        managerOuterUids.addAll(assistantOuterUids);
        Map<Long, String> outerUidNameMap = getOuterUidNameMap(upstreamEa, userId, managerOuterUids);

        Map<String, List<String>> eaDeptIdsMap = erDepartmentObjs.stream().filter(x -> !StringUtils.isEmpty(x.getOuterTenantFsAccount()) && !StringUtils.isEmpty(x.getOuterTenantDeptId()))
                .collect(Collectors.groupingBy(ErDepartmentObj::getOuterTenantFsAccount, Collectors.mapping(ErDepartmentObj::getOuterTenantDeptId, Collectors.toList())));
        Map<String, Map<String, String>> eaDeptIdNameMap = getEaDeptIdNameMap(eaDeptIdsMap);
        return toErDepartmentObjDatas(erDepartmentObjs, outerTenantIdNameMap, outerUidNameMap, eaDeptIdNameMap);
    }

    public void editErDepManagerOrAssist(String upstreamEa, Integer userId, String erDepId, Long managerOuterUid, List<Long> assistOuterUids, String parentErDepObjId) {
        int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        ObjectData objectData = new ObjectData();
        objectData.put(ErDepartmentObjConstant.ID, erDepId);
        if (managerOuterUid != null) {
            objectData.put(ErDepartmentObjConstant.MANAGER_ID, managerOuterUid + "");
        }
        if (CollectionUtils.isNotEmpty(assistOuterUids)) {
            objectData.put(ErDepartmentObjConstant.ASSISTANT_IDS, ListUtil.toStringList(assistOuterUids));
        }
        if (StringUtils.isNotBlank(parentErDepObjId)) {
            objectData.put(ErDepartmentObjConstant.PARENT_ID, parentErDepObjId);
        }
        objectData.setOwner(userId);
        objectData.setCreateBy(userId);
        objectData.setTenantId(tenantId);

        ActionEditArg editArg = new ActionEditArg();
        editArg.setObjectData(objectData);
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(tenantId, userId);
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> res = metadataActionService.edit(headerObj, ErDepartmentObjConstant.API_NAME, false, false, editArg);
        if (!res.isSuccess() || res.getData() != null) {
            log.warn("metadataActionService.edit error");
        }
    }

    public Result<Void> createEnterpriseErDepartment(String upstreamEa, Integer userId, CreateEnterpriseErDepartmentArg arg) {
        int upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
        Map<String, Set<String>> changePrivateObjectDatas2PublicApiNameDataIdsMap = new HashMap<>();

        boolean isPublic = paasShareObjectManager.checkIsOpenShareObjectConfigByApiName(upstreamEi, userId, ErDepartmentObjConstant.API_NAME);

        List<EnterpriseRelationObj> enterpriseRelationObjs = batchGetEnterpriseRelationObjs(upstreamEa, userId, arg.getOuterTenantIds());
        if (CollectionUtils.isEmpty(enterpriseRelationObjs)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        List<PublicEmployeeObj> relationOwners = batchGetRelationOwners(upstreamEa, userId, arg.getOuterTenantIds());
        Map<Long, List<Long>> outerTenantIdRelationOwnersMap = relationOwners.stream()
                .collect(Collectors.groupingBy(PublicEmployeeObj::getOuterTenantId, Collectors.mapping(PublicEmployeeObj::getOuterUid, Collectors.toList())));

        List<PublicEmployeeObj> dataManagers = batchGetDataManagers(upstreamEa, userId, arg.getOuterTenantIds());
        Map<Long, List<Long>> outerTenantIdDataManagersMap = dataManagers.stream()
                .collect(Collectors.groupingBy(PublicEmployeeObj::getOuterTenantId, Collectors.mapping(PublicEmployeeObj::getOuterUid, Collectors.toList())));
        Map<Long, ErDepartmentObj> outerTenantIdErDepObjMap = batchGetErDepartmentObjByOuterTenantIds(upstreamEa, userId, arg.getOuterTenantIds());
        for (Long outerTenantId : arg.getOuterTenantIds()) {
            changePrivateObjectDatas2PublicApiNameDataIdsMap.computeIfAbsent(EnterpriseRelationObjConstant.API_NAME, k -> new HashSet<>()).add(outerTenantId + "");
            Long managerOuterUid = null;
            List<Long> dataManagerIds = null;
            if (BooleanUtils.isTrue(arg.getEnterpriseOwner2ErDepartmentOwner())) {
                List<Long> relationOwnerIds = outerTenantIdRelationOwnersMap.get(outerTenantId);
                if (!CollectionUtils.isEmpty(relationOwnerIds)) {
                    managerOuterUid = relationOwnerIds.get(0);
                    changePrivateObjectDatas2PublicApiNameDataIdsMap.computeIfAbsent(PublicEmployeeObjConstant.API_NAME, k -> new HashSet<>()).add(managerOuterUid + "");
                }
                dataManagerIds = outerTenantIdDataManagersMap.get(outerTenantId);
                if (CollectionUtils.isNotEmpty(dataManagerIds)) {
                    changePrivateObjectDatas2PublicApiNameDataIdsMap.computeIfAbsent(PublicEmployeeObjConstant.API_NAME, k -> new HashSet<>()).addAll(ListUtil.toStringList(dataManagerIds));
                }
            }

            ErDepartmentObj erDepartmentObjByOuterTenantId = outerTenantIdErDepObjMap.get(outerTenantId);
            if (erDepartmentObjByOuterTenantId == null) {
                log.warn("erDepartmentObjByOuterTenantId == null");
                continue;
            }
            changePrivateObjectDatas2PublicApiNameDataIdsMap.computeIfAbsent(ErDepartmentObjConstant.API_NAME, k -> new HashSet<>()).add(erDepartmentObjByOuterTenantId.getId());
            changePrivateObjectDatas2PublicApiNameDataIdsMap.computeIfAbsent(ErDepartmentObjConstant.API_NAME, k -> new HashSet<>())
                    .addAll(getAllIdByTreePath(erDepartmentObjByOuterTenantId.getTreePath()));

            editErDepManagerOrAssist(upstreamEa, userId, erDepartmentObjByOuterTenantId.getId(), managerOuterUid, dataManagerIds, arg.getParentErDepartmentId());
        }
        if (isPublic) {
            List<String> downstreamEas = enterpriseRelationObjs.stream().filter(x -> !StringUtils.isEmpty(x.getEnterpriseAccount())).map(EnterpriseRelationObj::getEnterpriseAccount)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(downstreamEas)) {
                List<Integer> downstreamEis = eieaConverter.enterpriseAccountToId(downstreamEas).values().stream().collect(Collectors.toList());
                paasShareObjectManager.openDownstreamShareObjectConfig(upstreamEi, downstreamEis);
                addErAdminRole2RelationAdmins(downstreamEas, upstreamEa);
            }
            // 以上数据设置为共享数据
            paasShareObjectManager.changePrivateObjectDatas2Public(upstreamEi, userId, changePrivateObjectDatas2PublicApiNameDataIdsMap);
        }
        return Result.newSuccess();
    }

    public Set<String> getAllIdByTreePath(List<String> treePaths) {
        Set<String> res = new HashSet<>();
        if (CollectionUtils.isEmpty(treePaths)) {
            return res;
        }
        for (String treePath : treePaths) {
            if (StringUtils.isBlank(treePath)) {
                continue;
            }
            res.addAll(Lists.newArrayList(treePath.split("\\.")));
        }
        return res;
    }

    public Result<Void> createErDepartmentByDepartmentOrOrgs(RequestContext context, String currentEa, CreateErDepartmentByDepartmentOrOrgs arg) {
        User user = context.getUser();
        int upstreamEi = user.getTenantIdInt();
        String upstreamEa = eieaConverter.enterpriseIdToAccount(upstreamEi);
        // 校验是否已存在关联这些部门id的互联部门
        List<String> departmentIds = arg.getDepartmentOrgArgs().stream().map(DepartmentOrgArg::getDepartmentId).collect(Collectors.toList());
        SearchQuery query = new SearchQuery();
        query.addFilter(ErDepartmentObjConstant.OUTER_TENANT_DEPT_ID, departmentIds, FilterOperatorEnum.IN);
        query.addFilter(ErDepartmentObjConstant.DEPARTMENT_TYPE, Lists.newArrayList(ErDepartmentTypeEnum.DEPARTMENT.getType() + ""), FilterOperatorEnum.EQ);
        List<ObjectData> existsErDepartmentObjectDatas = queryObjectDatas(upstreamEa, user.getUserIdInt(), ErDepartmentObjConstant.API_NAME, query);
        if (!CollectionUtils.isEmpty(existsErDepartmentObjectDatas)) {
            StringBuilder stringBuilder = new StringBuilder();
            for (ObjectData existsErDepartmentObjectData : existsErDepartmentObjectDatas) {
                stringBuilder.append("[");
                stringBuilder.append(existsErDepartmentObjectData.getName());
                stringBuilder.append("]");
            }
            String errMsg = String.format(ResultCode.FS_DEPARTMENT_ER_DEPARTMENT_EXISTED.getDescription(), stringBuilder.toString());
            return Result.newError(ResultCode.FS_DEPARTMENT_ER_DEPARTMENT_EXISTED.getErrorCode(), errMsg);
        }

        //boolean isPublic = paasShareObjectManager.checkIsOpenShareObjectConfigByApiName(upstreamEi, user.getUserIdInt(), ErDepartmentObjConstant.API_NAME);

        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = buildHeaderObj(context);
        Long outerTenantId = fxiaokeEnterpriseAssociationEntityDaoRemoteManager.getOuterTenantIdByEa(currentEa);

        List<String> deptIds = arg.getDepartmentOrgArgs().stream().map(DepartmentOrgArg::getDepartmentId).collect(Collectors.toList());
        List<ObjectData> deptObjectDatas = batchGetDepartments(currentEa, -10000, deptIds);
        Map<String, ObjectData> idDeptObjectDataMap = deptObjectDatas.stream().collect(Collectors.toMap(ObjectData::getId, Function.identity()));

        double levelMaxErOrder = getLevelMaxErOrder(context, arg.getParentErDepartmentId());
        if (levelMaxErOrder < 0) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        for (DepartmentOrgArg departmentOrgArg : arg.getDepartmentOrgArgs()) {
            ObjectData deptObjectData = idDeptObjectDataMap.get(departmentOrgArg.getDepartmentId());
            if (deptObjectData == null) {
                continue;
            }

            // 部门负责人
            List<String> managerIds = (List<String>) deptObjectData.get("manager_id");
            List<Long> managerOuterUids = batchCreatePublicEmployeeObjByEmployeeIdStr(upstreamEi, user.getUserIdInt(), outerTenantId, managerIds);

            // 部门助理
            List<String> assistantIds = (List<String>) deptObjectData.get("assistant_id");
            List<Long> assistantOuterUids = batchCreatePublicEmployeeObjByEmployeeIdStr(upstreamEi, user.getUserIdInt(), outerTenantId, assistantIds);

            levelMaxErOrder++;
            int erDeptType = "organization__c".equals(deptObjectData.getRecordType()) ? ErDepartmentTypeEnum.ORGANIZATION.getType() : ErDepartmentTypeEnum.DEPARTMENT.getType();
            ObjectData objectData = new ObjectData();
            objectData.put(ErDepartmentObjConstant.NAME, deptObjectData.getName());
            objectData.put(ErDepartmentObjConstant.PARENT_ID, arg.getParentErDepartmentId());
            objectData.put(ErDepartmentObjConstant.OUTER_TENANT_ID, outerTenantId + "");
            objectData.put(ErDepartmentObjConstant.OUTER_TENANT_FS_ACCOUNT, currentEa);
            objectData.put(ErDepartmentObjConstant.OUTER_TENANT_DEPT_ID, departmentOrgArg.getDepartmentId());
            objectData.put(ErDepartmentObjConstant.DEPARTMENT_TYPE, erDeptType);
            objectData.put(ErDepartmentObjConstant.DEPARTMENT_CODE, StringUtils.isEmpty((String) deptObjectData.get("dept_code")) ? null : deptObjectData.get("dept_code"));
            objectData.put(ErDepartmentObjConstant.STATUS, ErDepartmentStatusEnum.NORMAL.getType());
            objectData.put(ErDepartmentObjConstant.ER_ORDER, levelMaxErOrder);
            if (!CollectionUtils.isEmpty(managerOuterUids)) {
                objectData.put(ErDepartmentObjConstant.MANAGER_ID, String.valueOf(managerOuterUids.get(0)));
            }
            if (!CollectionUtils.isEmpty(assistantOuterUids)) {
                List<String> assistantOuterUidStrs = assistantOuterUids.stream().map(String::valueOf).collect(Collectors.toList());
                objectData.put(ErDepartmentObjConstant.ASSISTANT_IDS, assistantOuterUidStrs);
            }
            objectData.setOwner(user.getUserIdInt());
            objectData.setCreateBy(user.getUserIdInt());

            ActionAddArg actionAddArg = new ActionAddArg();
            actionAddArg.setObjectData(objectData);
            metadataActionService.add(headerObj, ErDepartmentObjConstant.API_NAME, false, false, false, actionAddArg);
        }
        return Result.newSuccess();
    }

    private List<Long> batchCreatePublicEmployeeObjByEmployeeIdStr(int tenantId, Integer userId, Long outerTenantId, List<String> employeeIdStrs) {
        if (CollectionUtils.isEmpty(employeeIdStrs)) {
            return Lists.newArrayList();
        }
        List<Integer> employeeIds = employeeIdStrs.stream().map(Integer::parseInt).collect(Collectors.toList());
        return batchCreatePublicEmployeeObjForSelfEmployee4Uids(tenantId, userId, outerTenantId, employeeIds);
    }

    private List<Long> batchCreatePublicEmployeeObjForSelfEmployee4Uids(int tenantId, Integer userId, Long outerTenantId, List<Integer> employeeIds) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return Lists.newArrayList();
        }
        List<Map<String, Object>> maps = batchCreatePublicEmployeeObjForSelfEmployee(tenantId, userId, outerTenantId, employeeIds);
        return PublicEmployeeObj.newInstance(maps).stream().map(PublicEmployeeObj::getOuterUid).collect(Collectors.toList());
    }

    public Result<Void> createPublicEmployeeObjByDownstreamEmployeeOrDeps(RequestContext context, String currentEa, CreatePublicEmployeeObjByDownstreamEmployeeOrDepsArg arg) {
        ErDepartmentObj erDepartmentObj = getErDepartmentObjById(context, arg.getErDepartmentId());
        if (erDepartmentObj == null) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        User user = context.getUser();
        // 租户私有对象，没有上下游关系，不支持添加下游员工
        boolean isPublicObj = paasShareObjectManager.checkIsOpenShareObjectConfigByApiName(user.getTenantIdInt(), user.getUserIdInt(), ErDepartmentObjConstant.API_NAME);
        if (!isPublicObj) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        // 创建互联用户
        int currentTenantId = eieaConverter.enterpriseAccountToId(currentEa);
        List<Integer> allEmployeeIds = arg.getDownstreamEmpoyeeIds().stream().map(Integer::parseInt).collect(Collectors.toList());
        List<Integer> deptIds = arg.getDownstreamDepartmentIds().stream().map(Integer::parseInt).collect(Collectors.toList());
        List<Integer> orgDeptIds = arg.getDownstreamOrgIds().stream().map(Integer::parseInt).collect(Collectors.toList());
        deptIds.addAll(orgDeptIds);
        BatchGetEmployeeMapByDepartmentIdArg batchGetEmployeeMapByDepartmentIdArg = new BatchGetEmployeeMapByDepartmentIdArg();
        batchGetEmployeeMapByDepartmentIdArg.setEnterpriseId(currentTenantId);
        batchGetEmployeeMapByDepartmentIdArg.setDepartmentIds(deptIds);
        batchGetEmployeeMapByDepartmentIdArg.setRunStatus(RunStatus.ALL);
        BatchGetEmployeeMapByDepartmentIdResult batchGetEmployeesDtoByDepartmentIdResult = employeeService.batchGetEmployeeMapByDepartmentId(batchGetEmployeeMapByDepartmentIdArg);
        if (batchGetEmployeesDtoByDepartmentIdResult != null && !MapUtils.isEmpty(batchGetEmployeesDtoByDepartmentIdResult.getEmployeeMap())) {
            for (List<EmployeeDto> dtos : batchGetEmployeesDtoByDepartmentIdResult.getEmployeeMap().values()) {
                List<Integer> employeeIds = dtos.stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toList());
                allEmployeeIds.addAll(employeeIds);
            }
        }
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(user.getTenantId(), SuperUserConstants.USER_ID + "");
        BatchCreatePublicEmployeesArg batchCreatePublicEmployeesArg = new BatchCreatePublicEmployeesArg();
        batchCreatePublicEmployeesArg.setDownstreamEmployeeIds(allEmployeeIds);
        batchCreatePublicEmployeesArg.setErDepartmentId(arg.getErDepartmentId());
        batchCreatePublicEmployeesArg.setDownstreamOuterTenantId(erDepartmentObj.getOuterTenantId());
        com.fxiaoke.enterpriserelation.objrest.result.Result<BatchCreatePublicEmployeesResult> batchCreatePublicEmployeesResult = publicEmployeeObjService
                .batchCreatePublicEmployeesForErAccountInner(serviceContext, batchCreatePublicEmployeesArg);
        if (batchCreatePublicEmployeesResult.isSuccess() && !CollectionUtils.isEmpty(batchCreatePublicEmployeesResult.getData().getObjectDatas())) {
            Map<String, Set<String>> apiNameIdSetMap = new HashMap<>();
            Set<String> ids = batchCreatePublicEmployeesResult.getData().getObjectDatas().stream().map(x -> (String) x.get("_id")).collect(Collectors.toSet());
            apiNameIdSetMap.put(PublicEmployeeObjConstant.API_NAME, ids);
            paasShareObjectManager.changePrivateObjectDatas2Public(user.getTenantIdInt(), user.getUserIdInt(), apiNameIdSetMap);
        }
        return Result.newResult(batchCreatePublicEmployeesResult.getErrCode(), batchCreatePublicEmployeesResult.getErrMsg());
    }

    public Result<Void> deleteErDepartment(String upstreamEa, Integer userId, DeleteErDepartmentArg arg) {
        int upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);

        // 非共享数据，则操作租户库
        // 校验是否包含下级部门
        SearchQuery query = new SearchQuery();
        query.addFilter(ErDepartmentObjConstant.PARENT_ID, Lists.newArrayList(arg.getErDepartmentId()), FilterOperatorEnum.EQ);
        query.setOffset(0);
        query.setLimit(1);
        List<ObjectData> objectDatas = queryObjectDatas(upstreamEa, userId, ErDepartmentObjConstant.API_NAME, query);
        if (!CollectionUtils.isEmpty(objectDatas)) {
            return Result.newError(ResultCode.ER_DEPARETMENT_HAS_LOWER_LEVEL_CAN_NOT_BE_DELETE);
        }

        SearchQuery searchTemplateQuery = new SearchQuery();
        searchTemplateQuery.addFilter(ErDepartmentObjConstant.ID, Lists.newArrayList(arg.getErDepartmentId()), FilterOperatorEnum.EQ);
        List<ObjectData> deleteErDepartmentObjs = queryObjectDatas(upstreamEa, userId, ErDepartmentObjConstant.API_NAME, searchTemplateQuery);
        if (CollectionUtils.isEmpty(deleteErDepartmentObjs)) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.CANNOT_FIND_DATA_OF_INTERCONNECTED_DEPARTMENT.getDescription());
        }
        // 禁用公共对象
        List<ErDepartmentObj> erDepartmentObjs = ErDepartmentObj.newInstances(ObjectData.convert2MapList(deleteErDepartmentObjs));
        List<ErDepartmentObj> relationErDepartmentObjs = erDepartmentObjs.stream().filter(x -> x.getDepartmentType() == ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType())
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(relationErDepartmentObjs) && !StringUtils.isEmpty(relationErDepartmentObjs.get(0).getOuterTenantFsAccount())) {
            int downstreamEi = eieaConverter.enterpriseAccountToId(relationErDepartmentObjs.get(0).getOuterTenantFsAccount());

            // 互联部门、互联部门对应的互联企业 从公共数据转为私有数据
            Map<String, Set<String>> apiNameIdSetMap = new HashMap<>();
            apiNameIdSetMap.computeIfAbsent(EnterpriseRelationObjConstant.API_NAME, k -> new HashSet<>()).add(relationErDepartmentObjs.get(0).getOuterTenantId() + "");
            apiNameIdSetMap.computeIfAbsent(ErDepartmentObjConstant.API_NAME, k -> new HashSet<>()).add(relationErDepartmentObjs.get(0).getId());
            paasShareObjectManager.changePublicObjectDatas2Private(upstreamEi, userId, apiNameIdSetMap);

            paasShareObjectManager.closeDownstreamShareObjectConfig(upstreamEi, downstreamEi);
        }
        return Result.newSuccess();
    }

    private com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> stopErDepartmentObj(int tenantId, int userId, String id) {
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(tenantId, userId);

        ObjectData objectData = new ObjectData();
        objectData.put(ErDepartmentObjConstant.ID, id);
        objectData.put(ErDepartmentObjConstant.STATUS, ErDepartmentStatusEnum.STOP.getType());

        ActionEditArg actionEditArg = new ActionEditArg();
        actionEditArg.setObjectData(objectData);
        return metadataActionService.edit(headerObj, ErDepartmentObjConstant.API_NAME, false, false, actionEditArg);
    }

    private com.fxiaoke.crmrestapi.common.result.Result<ActionBulkInvalidResult> invalidErDepartmentObj(int tenantId, int userId, String id) {
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(tenantId, userId);
        ActionBulkInvalidArg invalidArg = new ActionBulkInvalidArg(ErDepartmentObjConstant.API_NAME, Lists.newArrayList(id));
        return metadataActionService.bulkInvalid(headerObj, ErDepartmentObjConstant.API_NAME, invalidArg);
    }

    private com.fxiaoke.crmrestapi.common.result.Result<ActionBulkDeleteResult> deleteErDepartmentObj(int tenantId, int userId, String id) {
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(tenantId, userId);
        ActionBulkDeleteArg deleteArg = new ActionBulkDeleteArg();
        deleteArg.setDescribe_api_name(ErDepartmentObjConstant.API_NAME);
        deleteArg.setIdList(Lists.newArrayList(id));
        return metadataActionService.bulkDelete(headerObj, ErDepartmentObjConstant.API_NAME, deleteArg);
    }

    public List<ErDepartmentObj> listAllErDepartmentObjs(String upstreamEa, Integer userId, Boolean onlyQueryShareObjectData) {
        SearchQuery query = new SearchQuery();
        OrderBy orderBy = new OrderBy(ErDepartmentObjConstant.ER_ORDER, true);
        query.setOrders(Lists.newArrayList(orderBy));
        if (BooleanUtils.isTrue(onlyQueryShareObjectData)) {
            query.addFilter(PaasConstant.DOWNSTREAM_TENANT_ID, Lists.newArrayList(PaasConstant.SHARE_OBJECT_DATA_D_TENANT_ID), "HASANYOF");
        }
        List<ObjectData> objectDatas = queryObjectDatas(upstreamEa, userId, ErDepartmentObjConstant.API_NAME, query);
        return ErDepartmentObj.newInstances(ObjectData.convert2MapList(objectDatas));
    }

    public List<ErDepartmentObj> listErDepartmentObjsByParentId(String upstreamEa, Integer userId, String parentId) {
        SearchQuery query = new SearchQuery();
        query.addFilter(ErDepartmentObjConstant.PARENT_ID, Lists.newArrayList(parentId), FilterOperatorEnum.EQ.getValue());
        List<ObjectData> objectDatas = queryObjectDatas(upstreamEa, userId, ErDepartmentObjConstant.API_NAME, query);
        return ErDepartmentObj.newInstances(ObjectData.convert2MapList(objectDatas));
    }

    public List<ErDepartmentObjData> getParentErDepartmentListByType(String upstreamEa, Integer userId, List<ErDepartmentObj> selectDeptList) {
        List<ErDepartmentObjData> dataResult = Lists.newArrayList();
        if (CollectionUtils.isEmpty(selectDeptList)) {
            return dataResult;
        }
        List<Long> outerTenantIds = selectDeptList.stream().filter(x -> x.getOuterTenantId() != null).map(x -> x.getOuterTenantId()).collect(Collectors.toList());
        List<Long> managerOuterUids = selectDeptList.stream().filter(x -> x.getManagerId() != null).map(ErDepartmentObj::getManagerId).collect(Collectors.toList());
        Map<Long, String> outerTenantIdNameMap = getOuterTenantIdNameMap(upstreamEa, userId, outerTenantIds);
        Map<Long, String> outerUidNameMap = getOuterUidNameMap(upstreamEa, userId, managerOuterUids);
        Map<String, List<String>> eaDeptIdsMap = selectDeptList.stream().filter(x -> !StringUtils.isEmpty(x.getOuterTenantFsAccount()) && !StringUtils.isEmpty(x.getOuterTenantDeptId()))
                .collect(Collectors.groupingBy(ErDepartmentObj::getOuterTenantFsAccount, Collectors.mapping(ErDepartmentObj::getOuterTenantDeptId, Collectors.toList())));
        Map<String, Map<String, String>> eaDeptIdNameMap = getEaDeptIdNameMap(eaDeptIdsMap);
        dataResult = toErDepartmentObjDatas(selectDeptList, outerTenantIdNameMap, outerUidNameMap, eaDeptIdNameMap);
        return dataResult;
    }

    public List<ErDepartmentObj> listErDepartmentObjs(String upstreamEa, Integer userId, List<String> ids) {
        SearchQuery query = new SearchQuery();
        OrderBy orderBy = new OrderBy(ErDepartmentObjConstant.ER_ORDER, true);
        query.setOrders(Lists.newArrayList(orderBy));
        query.addFilter(ErDepartmentObjConstant.ID, ids, FilterOperatorEnum.IN);
        List<ObjectData> objectDatas = queryObjectDatas(upstreamEa, userId, ErDepartmentObjConstant.API_NAME, query);
        return ErDepartmentObj.newInstances(ObjectData.convert2MapList(objectDatas));
    }

    public ErDepartmentObj getErDepartmentObjById(RequestContext context, String id) {
        SearchQuery query = new SearchQuery();
        query.addFilter(ErDepartmentObjConstant.ID, Lists.newArrayList(id), FilterOperatorEnum.EQ.getValue());
        List<ObjectData> objectDatas = queryObjectDatas(context, ErDepartmentObjConstant.API_NAME, query);
        if (CollectionUtils.isEmpty(objectDatas)) {
            return null;
        }
        return ErDepartmentObj.newInstance(ObjectData.convert2MapList(objectDatas).get(0));
    }

    private double getLevelMaxErOrder(RequestContext context, String parentId) {
        // 获取父节点下第一层节点最大order
        ErDepartmentObj parentErDepartmentObj = getErDepartmentObjById(context, parentId);
        if (parentErDepartmentObj == null) {
            return -10000;
        }
        double erOrder = parentErDepartmentObj.getErOrder();

        SearchQuery query = new SearchQuery();
        query.addFilter(ErDepartmentObjConstant.PARENT_ID, Lists.newArrayList(parentId), FilterOperatorEnum.EQ);
        OrderBy orderBy = new OrderBy(ErDepartmentObjConstant.ER_ORDER, false);
        query.setOrders(Lists.newArrayList(orderBy));
        query.setOffset(0);
        query.setLimit(1);
        List<ObjectData> objectDatas = queryObjectDatas(context, ErDepartmentObjConstant.API_NAME, query);
        if (CollectionUtils.isEmpty(objectDatas)) {
            return erOrder;
        }
        return ErDepartmentObj.newInstance(objectDatas.get(0)).getErOrder();
    }

    private Map<Long, String> getOuterTenantIdNameMap(String upstreamEa, Integer userId, List<Long> outerTenantIds) {
        if (CollectionUtils.isEmpty(outerTenantIds)) {
            return Maps.newHashMap();
        }
        outerTenantIds = outerTenantIds.stream().distinct().collect(Collectors.toList());
        List<EnterpriseRelationObj> enterpriseRelationObjs = batchGetEnterpriseRelationObjs(upstreamEa, userId, outerTenantIds);
        return enterpriseRelationObjs.stream().collect(Collectors.toMap(EnterpriseRelationObj::getOuterTenantId, EnterpriseRelationObj::getName));
    }

    private List<EnterpriseRelationObj> batchGetEnterpriseRelationObjs(String upstreamEa, Integer userId, List<Long> outerTenantIds) {
        List<String> outerTenantIdStrs = outerTenantIds.stream().map(String::valueOf).collect(Collectors.toList());
        SearchQuery query = new SearchQuery();
        query.addFilter(EnterpriseRelationObjConstant.DEST_OUTER_TENANT_ID, outerTenantIdStrs, FilterOperatorEnum.IN.getValue());
        List<ObjectData> objectDatas = queryObjectDatas(upstreamEa, userId, EnterpriseRelationObjConstant.API_NAME, query);
        return EnterpriseRelationObj.newInstance(ObjectData.convert2MapList(objectDatas));
    }

    protected List<EnterpriseRelationObj> batchGetEnterpriseRelationByOuterTenantId(String tenantId, List<Long> outerTenantIds, List<String> selectFields) {
        if (ObjectUtils.isEmpty(outerTenantIds)) {
            return Lists.newArrayList();
        }
        ListEnterpriseRelationObjByConditionArg relationObjByConditionArg = ListEnterpriseRelationObjByConditionArg.newStartRelationInstance();
        relationObjByConditionArg.setIdentityType(IdentityTypeEnum.CORP.getType());
        relationObjByConditionArg.setDestOuterTenantIds(ListUtil.toStringList(outerTenantIds));
        relationObjByConditionArg.setHasFsAccount(true);
        relationObjByConditionArg.setOffset(0);
        relationObjByConditionArg.setIgnoreBaseVisibleRange(true);
        relationObjByConditionArg.setIgnoreSelfFilter(true);
        relationObjByConditionArg.setSelectFields(selectFields);
        relationObjByConditionArg.setLimit(outerTenantIds.size());
        ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(tenantId, SuperUserConstants.USER_ID + "");
        List<Map<String, Object>> enterpriseRelationObjectDatas = enterpriseRelationObjService.listByCondition(serviceContext, relationObjByConditionArg).getObjectDatas();
        List<EnterpriseRelationObj> enterpriseRelationObjList = org.apache.commons.collections4.CollectionUtils.emptyIfNull(enterpriseRelationObjectDatas).stream().map(EnterpriseRelationObj::newInstance).collect(Collectors.toList());
        return enterpriseRelationObjList;
    }

    private Map<Long, String> getOuterUidNameMap(String upstreamEa, Integer userId, List<Long> outerUids) {
        if (CollectionUtils.isEmpty(outerUids)) {
            return Maps.newHashMap();
        }
        List<String> outerUidStrs = outerUids.stream().distinct().map(String::valueOf).collect(Collectors.toList());
        SearchQuery query = new SearchQuery();
        query.addFilter(PublicEmployeeObjConstant.OUTER_UID, outerUidStrs, FilterOperatorEnum.EQ.getValue());
        // filters.add(buildFilter(PublicEmployeeObjConstant.TYPE, Lists.newArrayList(PublicEmployeeTypeEnum.TYPE_IS_PUBLIC_EMPLOYEE.getType() + ""), FilterOperatorEnum.IN.getValue()));
        List<ObjectData> objectDatas = queryObjectDatas(upstreamEa, userId, PublicEmployeeObjConstant.API_NAME, query);
        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newInstance(ObjectData.convert2MapList(objectDatas));
        return publicEmployeeObjs.stream().collect(Collectors.toMap(PublicEmployeeObj::getOuterUid, com.facishare.paas.metadata.impl.ObjectData::getName));
    }

    private Map<Long, PublicEmployeeObj> batchGetPublicEmployeeObjsMapByOuterUids(String upstreamEa, Integer userId, List<Long> outerUids) {
        if (outerUids == null || outerUids.isEmpty()) {
            return Maps.newHashMap();
        }
        SearchQuery query = new SearchQuery();
        List<String> outerUidStrs = outerUids.stream().distinct().map(String::valueOf).collect(Collectors.toList());
        query.addFilter(PublicEmployeeObjConstant.OUTER_UID, outerUidStrs, FilterOperatorEnum.EQ.getValue());
        // filters.add(buildFilter(PublicEmployeeObjConstant.TYPE, Lists.newArrayList(PublicEmployeeTypeEnum.TYPE_IS_PUBLIC_EMPLOYEE.getType() + ""), FilterOperatorEnum.IN.getValue()));
        List<ObjectData> objectDatas = queryObjectDatas(upstreamEa, userId, PublicEmployeeObjConstant.API_NAME, query);
        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newInstance(ObjectData.convert2MapList(objectDatas));
        return publicEmployeeObjs.stream().collect(Collectors.toMap(PublicEmployeeObj::getOuterUid, Function.identity()));
    }

    private Map<String, PublicEmployeeObj> batchGetPublicEmployeeObjsByEmployeeIds(String upstreamEa, Integer userId, List<String> employeeIds) {
        if (employeeIds == null || employeeIds.isEmpty()) {
            return Maps.newHashMap();
        }
        SearchQuery query = new SearchQuery();
        query.addFilter(PublicEmployeeObjConstant.EMPLOYEE_ID, employeeIds, FilterOperatorEnum.IN.getValue());
        // filters.add(buildFilter(PublicEmployeeObjConstant.TYPE, Lists.newArrayList(PublicEmployeeTypeEnum.TYPE_IS_PUBLIC_EMPLOYEE.getType() + ""), FilterOperatorEnum.IN.getValue()));
        List<ObjectData> objectDatas = queryObjectDatas(upstreamEa, SuperUserConstants.USER_ID, PublicEmployeeObjConstant.API_NAME, query);
        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newInstance(ObjectData.convert2MapList(objectDatas));
        return publicEmployeeObjs.stream().collect(Collectors.toMap(PublicEmployeeObj::getEmployeeId, Function.identity()));
    }

    private Map<String, Map<String, String>> getEaDeptIdNameMap(Map<String, List<String>> eaDeptIdsMap) {
        if (MapUtils.isEmpty(eaDeptIdsMap)) {
            return Maps.newHashMap();
        }
        Map<String, Map<String, String>> eaDeptIdNameMap = Maps.newHashMap();
        for (Entry<String, List<String>> entry : eaDeptIdsMap.entrySet()) {
            int tenantId = eieaConverter.enterpriseAccountToId(entry.getKey());
            List<String> deptIds = entry.getValue().stream().distinct().collect(Collectors.toList());
            com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(tenantId, -10000);
            com.fxiaoke.crmrestapi.common.result.Result<FindNameByIdsResult> findNameResult = objectDataService.findNameByIds(headerObj, "DepartmentObj", deptIds);
            if (!findNameResult.isSuccess() || findNameResult.getData() == null || CollectionUtils.isEmpty(findNameResult.getData().getNameList())) {
                continue;
            }
            List<IdName> nameList = findNameResult.getData().getNameList();
            Map<String, String> deptIdNameMap = nameList.stream().filter(x -> !StringUtils.isEmpty(x.getName())).collect(Collectors.toMap(IdName::getId, IdName::getName));
            eaDeptIdNameMap.put(entry.getKey(), deptIdNameMap);
        }
        return eaDeptIdNameMap;
    }

    public List<ErDepartmentObjData> toErDepartmentObjDatas(List<ErDepartmentObj> erDepartmentObjs, Map<Long, String> outerTenantIdEaMap, Map<Long, String> outerUidNameMap,
                                                            Map<String, Map<String, String>> eaDeptIdNameMap) {
        List<ErDepartmentObjData> erDepartmentObjDatas = Lists.newArrayList();
        if (CollectionUtils.isEmpty(erDepartmentObjs)) {
            return erDepartmentObjDatas;
        }
        for (ErDepartmentObj erDepartmentObj : erDepartmentObjs) {
            ErDepartmentObjData data = toErDepartmentObjData(erDepartmentObj, outerTenantIdEaMap, outerUidNameMap, eaDeptIdNameMap);
            erDepartmentObjDatas.add(data);
        }
        return erDepartmentObjDatas;
    }

    private ErDepartmentObjData toErDepartmentObjData(ErDepartmentObj erDepartmentObj, Map<Long, String> outerTenantIdNameMap, Map<Long, String> outerUidNameMap,
                                                      Map<String, Map<String, String>> eaDeptIdNameMap) {
        if (erDepartmentObj == null) {
            return null;
        }
        ErDepartmentObjData data = new ErDepartmentObjData();
        data.setName(erDepartmentObj.getName());
        data.setErDepartmentId(erDepartmentObj.getId());
        data.setParentErDepartmentId(erDepartmentObj.getParentId());
//        data.setType(String.valueOf(erDepartmentObj.getDepartmentType()));
        data.setType(erDepartmentObj.getDepartmentType() == null ? ErDepartmentTypeEnum.DEPARTMENT.getType() + "" : String.valueOf(erDepartmentObj.getDepartmentType()));// TODO 仅测试
        if (erDepartmentObj.getDepartmentType() != null) {
            // 互联企业节点返回ea数据
            if (erDepartmentObj.getDepartmentType() == ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType() && erDepartmentObj.getOuterTenantId() != null) {
                data.setEa(erDepartmentObj.getOuterTenantFsAccount());
                if (!MapUtils.isEmpty(outerTenantIdNameMap)) {
                    data.setEnterpriseOrDepOrOrgName(outerTenantIdNameMap.get(erDepartmentObj.getOuterTenantId()));
                }
            }
            if ((erDepartmentObj.getDepartmentType() == ErDepartmentTypeEnum.ORGANIZATION.getType() || erDepartmentObj.getDepartmentType() == ErDepartmentTypeEnum.DEPARTMENT.getType())
                    && erDepartmentObj.getOuterTenantDeptId() != null && erDepartmentObj.getOuterTenantFsAccount() != null) {
                if (!MapUtils.isEmpty(eaDeptIdNameMap)) {
                    Map<String, String> deptIdNameMap = eaDeptIdNameMap.get(erDepartmentObj.getOuterTenantFsAccount());
                    if (!MapUtils.isEmpty(deptIdNameMap)) {
                        data.setEnterpriseOrDepOrOrgName(deptIdNameMap.get(erDepartmentObj.getOuterTenantDeptId()));
                    }
                }
            }
        }
        if (erDepartmentObj.getManagerId() != null) {
            data.setManagerOuterUid(String.valueOf(erDepartmentObj.getManagerId()));
            if (!MapUtils.isEmpty(outerUidNameMap)) {
                data.setManagerName(outerUidNameMap.get(erDepartmentObj.getManagerId()));
            }
        }
        if (!CollectionUtils.isEmpty(erDepartmentObj.getAssistantIds())) {
            List<AssistantData> assistantDatas = Lists.newArrayList();
            for (Long assistantId : erDepartmentObj.getAssistantIds()) {
                AssistantData assistantData = new AssistantData();
                assistantData.setAssistantOuterUid(String.valueOf(assistantId));
                if (!MapUtils.isEmpty(outerUidNameMap)) {
                    assistantData.setAssistantName(outerUidNameMap.get(assistantId));
                }
                assistantDatas.add(assistantData);
            }
            data.setAssistantDatas(assistantDatas);
        }
        data.setOrder(erDepartmentObj.getErOrder());
        return data;
    }

    private List<PublicEmployeeObj> batchGetRelationOwners(String upstreamEa, Integer userId, List<Long> outerTenantIds) {
        List<String> outerTenantIdStrs = outerTenantIds.stream().map(String::valueOf).collect(Collectors.toList());
        SearchQuery query = new SearchQuery();
        query.addFilter(PublicEmployeeObjConstant.OUTER_TENANT_ID, outerTenantIdStrs, FilterOperatorEnum.IN.getValue());
        query.addFilter(PublicEmployeeObjConstant.RELATION_OWNER, Lists.newArrayList("true"), FilterOperatorEnum.EQ.getValue());
        List<ObjectData> objectDatas = queryObjectDatas(upstreamEa, userId, PublicEmployeeObjConstant.API_NAME, query);
        return PublicEmployeeObj.newInstance(ObjectData.convert2MapList(objectDatas));
    }

    private List<PublicEmployeeObj> batchGetDataManagers(String upstreamEa, Integer userId, List<Long> outerTenantIds) {
        List<String> outerTenantIdStrs = outerTenantIds.stream().map(String::valueOf).collect(Collectors.toList());
        SearchQuery query = new SearchQuery();
        query.addFilter(PublicEmployeeObjConstant.OUTER_TENANT_ID, outerTenantIdStrs, FilterOperatorEnum.IN.getValue());
        query.addFilter(PublicEmployeeObjConstant.DATA_MANAGER, Lists.newArrayList("true"), FilterOperatorEnum.EQ.getValue());
        List<ObjectData> objectDatas = queryObjectDatas(upstreamEa, userId, PublicEmployeeObjConstant.API_NAME, query);
        return PublicEmployeeObj.newInstance(ObjectData.convert2MapList(objectDatas));
    }

    private List<ObjectData> batchGetDepartments(String upstreamEa, Integer userId, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        SearchQuery query = new SearchQuery();
        query.addFilter("_id", ids, FilterOperatorEnum.IN.getValue());
        return queryObjectDatas(upstreamEa, userId, "DepartmentObj", query);
    }

    private List<ObjectData> queryObjectDatas(String upstreamEa, Integer userId, String apiName, SearchQuery query) {
        return queryObjectDatasResult(upstreamEa, userId, apiName, query, true).getDataList();
    }

    private List<ObjectData> queryObjectDatas(RequestContext context, String apiName, SearchQuery query) {
        Page<ObjectData> result = queryObjectDatasResult(context, apiName, query, true);
        if (CollectionUtils.isEmpty(result.getDataList())) {
            return Lists.newArrayList();
        }
        return result.getDataList();
    }

    private Page<ObjectData> queryObjectDatasResult(String upstreamEa, Integer userId, String apiName, SearchQuery query, boolean checkPermission) {
        HeaderObj headerObj = HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(upstreamEa), userId);
        return queryObjectDatasResult(headerObj, apiName, query, checkPermission);
    }

    private Page<ObjectData> queryObjectDatasResult(RequestContext context, String apiName, SearchQuery query, boolean checkPermission) {
        HeaderObj headerObj = buildHeaderObj(context);
        Page<ObjectData> result = queryObjectDatasResult(headerObj, apiName, query, checkPermission);
        return result;
    }

    private Page<ObjectData> queryObjectDatasResult(HeaderObj headerObj, String apiName, SearchQuery query, boolean checkPermission) {
        Page<ObjectData> pageData = new Page<>();
        SearchQuery templateQuery = new SearchQuery();
        templateQuery.setFilters(query.getFilters());
        templateQuery.setWheres(query.getWheres());
        templateQuery.setLimit(query.getLimit());
        templateQuery.setOffset(query.getOffset());

        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setCheckPermission(checkPermission);
        if ((headerObj.getInt("x-fs-userInfo") != null && -10000 == headerObj.getInt("x-fs-userInfo")) || (headerObj.getInt("X-fs-Employee-Id") != null && -10000 == headerObj
                .getInt("X-fs-Employee-Id"))) {
            controllerListArg.setCheckPermission(false);
        }
        controllerListArg.setSearchQuery(templateQuery);

        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> result = metadataControllerService.list(headerObj, apiName, controllerListArg);
        if (result.isSuccess() && result.getData() != null && !CollectionUtils.isEmpty(result.getData().getDataList())) {
            pageData.setDataList(result.getData().getDataList());
            pageData.setTotal(result.getData().getTotal());
        } else {
            pageData.setDataList(Lists.newArrayList());
            pageData.setTotal(0);
        }
        return pageData;
    }

    private Filter buildFilter(String fieldName, List<String> values, String operator) {
        Filter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setFieldValues(values);
        filter.setOperator(operator);
        return filter;
    }

    public Map<Long, String> batGetEaByOuterTenantIds(Integer tenantId, List<Long> outerTenantIds) {
        if (CollectionUtils.isEmpty(outerTenantIds)) {
            return new HashMap<>();
        }
        ListEnterpriseRelationObjByConditionArg relationObjByConditionArg = ListEnterpriseRelationObjByConditionArg.newStartRelationInstance();
        relationObjByConditionArg.setIdentityType(IdentityTypeEnum.CORP.getType());
        relationObjByConditionArg.setDestOuterTenantIds(ListUtil.toStringList(outerTenantIds));
        relationObjByConditionArg.setHasFsAccount(true);
        ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(tenantId + "", SuperUserConstants.USER_ID + "");
        List<Map<String, Object>> enterpriseRelationObjectDatas = enterpriseRelationObjService.listByCondition(serviceContext, relationObjByConditionArg).getObjectDatas();

        return enterpriseRelationObjectDatas.stream().map(EnterpriseRelationObj::newInstance).collect(Collectors.toMap(x -> x.getOuterTenantId(), y -> y.getEnterpriseAccount()));
    }

    public Map<Long, String> batGetOuterTenantIdByOuterTenantNames(Integer tenantId, List<String> outerTenantNames) {
        if (CollectionUtils.isEmpty(outerTenantNames)) {
            return new HashMap<>();
        }
        ListEnterpriseRelationObjByConditionArg queryArg = ListEnterpriseRelationObjByConditionArg.newStartRelationInstance();
        queryArg.setEnterpriseNames(outerTenantNames);
        queryArg.setLimit(outerTenantNames.size());
        ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(tenantId + "", SuperUserConstants.USER_ID + "");
        List<Map<String, Object>> enterpriseRelationObjectDatas = enterpriseRelationObjService.listByCondition(serviceContext, queryArg).getObjectDatas();

        return enterpriseRelationObjectDatas.stream().map(EnterpriseRelationObj::newInstance).collect(Collectors.toMap(x -> x.getOuterTenantId(), y -> y.getName()));
    }

    public Result<Void> initRootErDepartmentObj(String upstreamEa, Integer userId, CreateRootErDepartmentObjArg arg) {
        // 如果未开启合作伙伴 不允许初始化
        if (!partnerStatusIsOpen(upstreamEa)) {
            return Result.newResult(ResultCode.NEED_OPEN_PARTNER.getErrorCode(), ResultCode.NEED_OPEN_PARTNER.getErrorMessage());
        }
        if (this.getGroupItControlSwitch(upstreamEa).getData().getEnableStatus()) {
            return Result.newResult(ResultCode.PARAMS_TIMESTAMP_ILLEGAL.getErrorCode(), ResultCode.ALREADY_START_PLEASE_REFRESH.getDescription());
        }
        String lockKey = KeyBuilder.builderCreateRootErDepartmentObjLockKey(upstreamEa);
        try {
            boolean lock = distributeLock.lockWeakly(lockKey, 5, TimeUnit.MINUTES);
            if (!lock) {
                return Result.newError(ResultCode.ENTERPRISE_IS_OPENING);
            }
            int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
            Collection<String> downstreamEas = batGetEaByOuterTenantIds(tenantId, arg.getOuterTenantIds()).values();
            List<Integer> downstreamEis = new ArrayList<>(eieaConverter.enterpriseAccountToId(downstreamEas).values());

            // 将互联用户、互联企业、合作伙伴、互联部门设置为共享对象
            paasShareObjectManager.openUpstreamShareObjectConfig(tenantId);

            // 将选中的下游租户的几个共享对象给禁用
            paasShareObjectManager.openDownstreamShareObjectConfig(tenantId, downstreamEis);

            Map<String, Set<String>> changePrivateObjectDatas2PublicApiNameDataIdsMap = new HashMap<>();
            // 将参数中的合作伙伴设置为共享数据
            changePrivateObjectDatas2PublicApiNameDataIdsMap.computeIfAbsent(PartnerFieldContants.API_NAME, k -> new HashSet<>()).add(arg.getMapperPartnerId());

            // 将自己创建为互联企业共享数据
            Long enterpriseRelationObjForSelfOuterTenantId = createEnterpriseRelationObjForSelfEnterprise(userId, arg.getMapperPartnerId(), tenantId);
            String rootErDepartmentId = this.updateRootErDepartmentObjByEnterpriseRelationObj(tenantId, SuperUserConstants.USER_ID, enterpriseRelationObjForSelfOuterTenantId);
            changePrivateObjectDatas2PublicApiNameDataIdsMap.computeIfAbsent(EnterpriseRelationObjConstant.API_NAME, k -> new HashSet<>()).add(enterpriseRelationObjForSelfOuterTenantId + "");

            List<String> queryUserIds = new ArrayList<>();
            ListUtil.addIfNotNull(queryUserIds, arg.getManagerEmployeeId());
            ListUtil.addIfNotNull(queryUserIds, arg.getAssistantEmployeeIds());
            // 本企业员工 创建为互联用户共享对象
            batchCreatePublicEmployeeObjForSelfEmployee(tenantId, userId, enterpriseRelationObjForSelfOuterTenantId, ListUtil.toIntegerList(queryUserIds));
            List<FxiaokeEmployeeAssociationEntity> fxiaokeEmployeeAssociationEntities = fxiaokeEmployeeAssociationEntityDaoRemoteManager
                    .batchGetBySingleEa(upstreamEa, ListUtil.toIntegerList(queryUserIds));
            Map<Integer, Long> employeeIdOuterUidMap = fxiaokeEmployeeAssociationEntities.stream()
                    .collect(Collectors.toMap(FxiaokeEmployeeAssociationEntity::getEmployeeId, FxiaokeEmployeeAssociationEntity::getOuterUid));
            if (CollectionUtils.isNotEmpty(employeeIdOuterUidMap.values())) {
                changePrivateObjectDatas2PublicApiNameDataIdsMap.computeIfAbsent(PublicEmployeeObjConstant.API_NAME, k -> new HashSet<>())
                        .addAll(ListUtil.toStringList(employeeIdOuterUidMap.values()));
            }

            Long managerOuterUid = employeeIdOuterUidMap.remove(Integer.parseInt(arg.getManagerEmployeeId()));
            List<Long> assisOuterUids = new ArrayList<>(employeeIdOuterUidMap.values());
            // 非共享对象，查租户库
            this.editErDepManagerOrAssist(upstreamEa, userId, rootErDepartmentId, managerOuterUid, assisOuterUids, null);
            if (StringUtils.isBlank(rootErDepartmentId)) {
                return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), "create root erDepartmentObj error");
            }
            changePrivateObjectDatas2PublicApiNameDataIdsMap.computeIfAbsent(ErDepartmentObjConstant.API_NAME, k -> new HashSet<>()).add(rootErDepartmentId);

            // 将数据转为共享数据
            paasShareObjectManager.changePrivateObjectDatas2Public(tenantId, userId, changePrivateObjectDatas2PublicApiNameDataIdsMap);

            // 下游企业创建为下级企业节点
            CreateEnterpriseErDepartmentArg createEnterpriseErDepartmentArg = new CreateEnterpriseErDepartmentArg();
            createEnterpriseErDepartmentArg.setParentErDepartmentId(rootErDepartmentId);
            createEnterpriseErDepartmentArg.setOuterTenantIds(arg.getOuterTenantIds());
            createEnterpriseErDepartmentArg.setEnterpriseOwner2ErDepartmentOwner(arg.getEnterpriseOwner2ErDepartmentOwner());

            this.createEnterpriseErDepartment(upstreamEa, SuperUserConstants.USER_ID, createEnterpriseErDepartmentArg);
            this.addErAdminRole2RelationAdmins(new ArrayList<>(downstreamEas), upstreamEa);
            updateGroupItControlSwitch(upstreamEa, true);
            return Result.newSuccess();
        } finally {
            distributeLock.unlock(lockKey);
        }
    }

    /**
     * 将自己企业下的员工创建为互联用户
     */
    private List<Map<String, Object>> batchCreatePublicEmployeeObjForSelfEmployee(int tenantId, Integer userId, Long outerTenantId, List<Integer> employeeIds) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return Lists.newArrayList();
        }
        com.fxiaoke.enterpriserelation.objrest.arg.BatchCreatePublicEmployeesArg batchCreatePublicEmployeesArg = new com.fxiaoke.enterpriserelation.objrest.arg.BatchCreatePublicEmployeesArg();
        batchCreatePublicEmployeesArg.setDownstreamOuterTenantId(outerTenantId);
        batchCreatePublicEmployeesArg.setDownstreamEmployeeIds(employeeIds);
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(tenantId + "", SuperUserConstants.USER_ID.toString());
        com.fxiaoke.enterpriserelation.objrest.result.Result<BatchCreatePublicEmployeesResult> result = publicEmployeeObjService
                .batchCreatePublicEmployeesForErAccountInner(serviceContext, batchCreatePublicEmployeesArg);
        if (!result.isSuccess()) {
            return Lists.newArrayList();
        }
        return result.getData().getObjectDatas();
    }

    /**
     * 将互联部门根节点名称改为协助节点名称
     */
    private Long createEnterpriseRelationObjForSelfEnterprise(Integer userId, String mapperPartnerId, int tenantId) {
        ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(tenantId + "", userId + "");
        CreateEnterpriseRelationWithFsAccountArg createArg = new CreateEnterpriseRelationWithFsAccountArg();
        createArg.setDownstreamTenantId(tenantId);
        createArg.setMapperApiName(PartnerFieldContants.API_NAME);
        createArg.setMapperObjectId(mapperPartnerId);
        createArg.setManagerMode(AccountManageMode.ASSIST_MANAGE.getType());
        return enterpriseRelationObjService.createEnterpriseRelationWithFsAccount(serviceContext, createArg);
    }

    public void setPublicEmployeeContactId2Null(Integer tenantId, List<String> outerUids) {
        if (CollectionUtils.isEmpty(outerUids)) {
            return;
        }
        for (String outerUid : outerUids) {
            try {
                ObjectData objectData = new ObjectData();
                objectData.put(PublicEmployeeObjConstant.ID, outerUid);
                objectData.put(PublicEmployeeObjConstant.CONTRACT_ID, null);
                objectData.setTenantId(tenantId);
                ActionEditArg editArg = new ActionEditArg();
                editArg.setObjectData(objectData);
                com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(tenantId, SuperUserConstants.USER_ID);
                com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> res = metadataActionService.edit(headerObj, PublicEmployeeObjConstant.API_NAME, false, false, editArg);
                if (!res.isSuccess() || res.getData() != null) {
                    log.warn("metadataActionService.edit error");
                }
            } catch (Exception e) {
                log.warn("", e);
            }
        }
    }

    private void addErAdminRole2RelationAdmins(List<String> eas, String upstreamEa) {
        if (CollectionUtils.isEmpty(eas)) {
            return;
        }
        for (String ea : eas) {
            List<Integer> relationAdminUserIds = permissionManager.listRelationAdmins(ea);
            if (CollectionUtils.isEmpty(relationAdminUserIds)) {
                continue;
            }
            List<FxiaokeEmployeeAssociationEntity> fxiaokeEmployeeAssociationEntities = fxiaokeEmployeeAssociationEntityDaoRemoteManager.batchGetBySingleEa(ea, relationAdminUserIds);
            if (CollectionUtils.isEmpty(fxiaokeEmployeeAssociationEntities)) {
                continue;
            }
            List<OuterAccountVo> outerAccountVos = fxiaokeEmployeeAssociationEntities.stream().map(x -> {
                OuterAccountVo outerAccountVo = new OuterAccountVo();
                outerAccountVo.setOuterTenantId(x.getOuterTenantId());
                outerAccountVo.setOuterUid(x.getOuterUid());
                return outerAccountVo;
            }).collect(Collectors.toList());
            try {
                Result<Void> addResult = linkAppOuterRoleService.batchAddAppRoleAssigns(upstreamEa, GlobalRoleConstants.ER_ORG_ADMIN_ROLE_ID, outerAccountVos);
                if (!addResult.isSuccess()) {
                    log.warn("linkAppOuterRoleService.batchAddAppRoleAssigns addResult={}", addResult);
                }
            } catch (Exception e) {
                log.warn("linkAppOuterRoleService.batchAddAppRoleAssigns", e);
            }
        }
    }

    public String updateRootErDepartmentObjByEnterpriseRelationObj(int tenantId, Integer userId, Long outerTenantId) {
        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        SearchQuery query = new SearchQuery();
        query.addFilter(ErDepartmentObjConstant.DEPARTMENT_TYPE, Lists.newArrayList(ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType() + ""), FilterOperatorEnum.EQ);
        query.addFilter(ErDepartmentObjConstant.PARENT_ID, Lists.newArrayList(), FilterOperatorEnum.IS);
        List<ObjectData> rootDatas = queryObjectDatasResult(ea, userId, ErDepartmentObjConstant.API_NAME, query, false).getDataList();
        if (rootDatas.isEmpty()) {
            log.warn("rootDatas is null");
            throw new RelationException(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.CANNOT_FIND_DATA_OF_INTERCONNECTED_DEPARTMENT.getDescription());
        }
        ObjectData root = rootDatas.get(0);

        ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(tenantId + "", SuperUserConstants.USER_ID + "");
        Map<String, Object> erObj = enterpriseRelationObjService.getObjectData(serviceContext, outerTenantId).getObjectData();
        if (erObj == null) {
            log.warn("enterpriseRelationObjService.getObjectData(serviceContext, outerTenantId).getObjectData() is null");
            throw new RelationException(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.CANNOT_FIND_DATA_OF_INTERCONNECTED_ENTERPRISE.getDescription());
        }
        EnterpriseRelationObj relationObj = EnterpriseRelationObj.newInstance(erObj);
        ObjectData objectData = new ObjectData();
        objectData.put(ErDepartmentObjConstant.ID, root.getId());
        objectData.put(ErDepartmentObjConstant.NAME, relationObj.getName());
        objectData.put(ErDepartmentObjConstant.OUTER_TENANT_ID, relationObj.getOuterTenantId() + "");
        objectData.put(ErDepartmentObjConstant.OUTER_TENANT_FS_ACCOUNT, relationObj.getEnterpriseAccount());
        this.updateErDepartmentObj(tenantId, SuperUserConstants.USER_ID, objectData);
        return root.getId();
    }

    public Result<GetGroupItControlSwitchResult> getGroupItControlSwitch(String ea) {
        GetGroupItControlSwitchResult res = new GetGroupItControlSwitchResult();
        boolean openGroupItControlSwitch = isOpenGroupItControlSwitch(ea);
        res.setEnableStatus(openGroupItControlSwitch);
        return Result.newResult(ResultCode.SUCCESS.getErrorCode(), ResultCode.SUCCESS.getErrorMessage(), res);
    }

    public boolean isOpenGroupItControlSwitch(String ea) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        QueryConfigByRankArg queryArg = new QueryConfigByRankArg();
        queryArg.setRank(Rank.TENANT);
        queryArg.setKey(GROUP_IT_CONTROL_SWITCH_KEY);
        queryArg.setPkg("CRM");
        queryArg.setTenantId(ei + "");
        queryArg.setUserId(SuperUserConstants.USER_ID + "");
        try {
            String isOpen = bizConfClient.queryConfigByRank(queryArg);
            if (StringUtils.isNotEmpty(isOpen)) {
                return "true".equals(isOpen);
            }
        } catch (FRestClientException e) {
            log.error("getErDepartmentSwitchStatus--error:{}", e);
        }
        return false;
    }

    public boolean updateGroupItControlSwitch(String ea, boolean status) {
        ConfigArg configArg = new ConfigArg();
        configArg.setKey(GROUP_IT_CONTROL_SWITCH_KEY);
        configArg.setValueType(ValueType.STRING);
        configArg.setValue(status + "");
        configArg.setRank(Rank.TENANT);
        configArg.setTenantId(eieaConverter.enterpriseAccountToId(ea) + "");
        configArg.setUserId("");
        configArg.setPkg("CRM");
        configArg.setOperator(SuperUserConstants.USER_ID + "");
        try {
            int i = bizConfClient.upsertConfig(configArg);
            if (i == -1) {
                log.warn("can't get create/update updateErDepartmentSwitchStatus status,ea={},config={}", ea, configArg);
                return false;
            }
            return true;
        } catch (FRestClientException e) {
            log.error("can't get create/update updateErDepartmentSwitchStatus status,ea={},config={}", ea, configArg, e);
        }
        return false;
    }

    public Result<GetErDepartmentInfoResult> getErDepartmentInfo(RequestContext context, GetErDepartmentInfoArg arg) {
        User user = context.getUser();
        ErDepartmentObj erDepartmentObj = getErDepartmentObjById(context, arg.getErDepartmentId());
        if (erDepartmentObj == null) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(user.getTenantId(), user.getUserId());
        GetErDepartmentInfoResult result = new GetErDepartmentInfoResult();
        result.setEa(erDepartmentObj.getOuterTenantFsAccount());
        result.setName(erDepartmentObj.getName());
        result.setType(String.valueOf(erDepartmentObj.getDepartmentType()));
        if (erDepartmentObj.getDepartmentType() != null) {
            if (erDepartmentObj.getDepartmentType() == ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType() && erDepartmentObj.getOuterTenantId() != null) {
                List<EnterpriseRelationObj> enterpriseRelationObjs = batchGetEnterpriseRelationByOuterTenantId(user.getTenantId(), Lists.newArrayList(erDepartmentObj.getOuterTenantId()), Lists.newArrayList(EnterpriseRelationObj.NAME, EnterpriseRelationObj.ID));
                if (!CollectionUtils.isEmpty(enterpriseRelationObjs)) {
                    result.setEnterpriseOrDepOrOrgName(enterpriseRelationObjs.get(0).getName());
                }
            }
            if ((erDepartmentObj.getDepartmentType() == ErDepartmentTypeEnum.ORGANIZATION.getType() || erDepartmentObj.getDepartmentType() == ErDepartmentTypeEnum.DEPARTMENT.getType())
                    && erDepartmentObj.getOuterTenantDeptId() != null && erDepartmentObj.getOuterTenantFsAccount() != null) {
                Map<String, List<String>> eaDeptsMap = Maps.newHashMap();
                eaDeptsMap.put(erDepartmentObj.getOuterTenantFsAccount(), Lists.newArrayList(erDepartmentObj.getOuterTenantDeptId()));
                Map<String, Map<String, String>> eaDeptIdNameMap = getEaDeptIdNameMap(eaDeptsMap);
                Map<String, String> deptIdNameMap = eaDeptIdNameMap.get(erDepartmentObj.getOuterTenantFsAccount());
                if (!MapUtils.isEmpty(deptIdNameMap)) {
                    result.setEnterpriseOrDepOrOrgName(deptIdNameMap.get(erDepartmentObj.getOuterTenantDeptId()));
                }
            }
        }
        if (!StringUtils.isEmpty(erDepartmentObj.getParentId())) {
            ErDepartmentObj parentErDeptObj = getErDepartmentObjById(context, erDepartmentObj.getParentId());
            result.setParentErDepartmentId(erDepartmentObj.getParentId());
            result.setParentErDepartmentName(parentErDeptObj.getName());
        }
        if (erDepartmentObj.getManagerId() != null) {
            ListPublicEmployeeObjByConditionArg conditionArg = new ListPublicEmployeeObjByConditionArg();
            conditionArg.setOuterUids(Lists.newArrayList(erDepartmentObj.getManagerId()));
            conditionArg.setIgnoreSelfFilter(true);
            ListByConditionResult listByConditionResult = publicEmployeeObjService.listByCondition(serviceContext, conditionArg);
            if (!CollectionUtils.isEmpty(listByConditionResult.getObjectDatas())) {
                List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newInstance(listByConditionResult.getObjectDatas());
                result.setEmployeeType(publicEmployeeObjs.get(0).getType());
                result.setManagerEmployeeId(publicEmployeeObjs.get(0).getEmployeeId());
            }
            result.setManagerOuterUid(String.valueOf(erDepartmentObj.getManagerId()));
        }
        List<GetErDepartmentInfoResult.AssistantData> assistantDatas = Lists.newArrayList();
        result.setAssistantDatas(assistantDatas);
        if (CollectionUtils.isEmpty(erDepartmentObj.getAssistantIds())) {
            return Result.newSuccess(result);
        }

        IdsLongArg assistantIdArgs = new IdsLongArg();
        assistantIdArgs.setIds(erDepartmentObj.getAssistantIds());
        List<Map<String, Object>> assistantPublicEmployeeObjs = publicEmployeeObjService.batchGetObjectDatas(serviceContext, assistantIdArgs).getData();
        Map<Long, PublicEmployeeObj> assistantPublicEmployeeObjsMap = new HashMap<>();
        for (Map<String, Object> assistantPublicEmployeeObj : assistantPublicEmployeeObjs) {
            PublicEmployeeObj obj = PublicEmployeeObj.newInstance(assistantPublicEmployeeObj);
            assistantPublicEmployeeObjsMap.put(obj.getOuterUid(), obj);
        }
        for (Long assistantId : erDepartmentObj.getAssistantIds()) {
            PublicEmployeeObj obj = assistantPublicEmployeeObjsMap.get(assistantId);
            if (obj != null) {
                GetErDepartmentInfoResult.AssistantData assistantData = new GetErDepartmentInfoResult.AssistantData();
                assistantData.setAssistantOuterUid(String.valueOf(assistantId));
                assistantData.setAssistantEmployeeUid(obj.getEmployeeId());
                assistantData.setAssistantEmployeeName(obj.getName());
                assistantData.setEmployeeType(obj.getType());
                assistantDatas.add(assistantData);
            }
        }
        return Result.newSuccess(result);
    }

    public Result<Void> updateErDepartmentInfo(RequestContext context, UpdateErDepartmentInfoArg arg) {
        ErDepartmentObj erDepartmentObj = getErDepartmentObjById(context, arg.getErDepartmentId());
        if (erDepartmentObj == null) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (CollectionUtils.isEmpty(arg.getManagerAndAssistantEmployeeIds())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        int ei = context.getUser().getTenantIdInt();
        int userId = context.getUser().getUserIdInt();
        String upstreamEa = eieaConverter.enterpriseIdToAccount(ei);
        Map<String, Long> argEmployeeIdOuterUidMap = fxiaokeEmployeeAssociationEntityDaoRemoteManager.batchGetBySingleEa(upstreamEa, ListUtil.toIntegerList(arg.getManagerAndAssistantEmployeeIds()))
                .stream().collect(Collectors.toMap(x -> String.valueOf(x.getEmployeeId()), FxiaokeEmployeeAssociationEntity::getOuterUid));
        Long managerOuterUid = argEmployeeIdOuterUidMap.get(arg.getManagerEmployeeId());

        // 创建为互联用户
        List<String> needCreatePublicEmployeeObjEmployeeIds = new ArrayList<>();
        if (StringUtils.isNotBlank(arg.getManagerEmployeeId()) && (managerOuterUid == null || !managerOuterUid.equals(erDepartmentObj.getManagerId()))) {
            needCreatePublicEmployeeObjEmployeeIds.add(arg.getManagerEmployeeId());
        }
        if (CollectionUtils.isNotEmpty(arg.getAssistantEmployeeIds())) {
            Set<Long> assistantIds = new HashSet<>(erDepartmentObj.getAssistantIds());
            for (String assistantEmployeeId : arg.getAssistantEmployeeIds()) {
                Long outerUid = argEmployeeIdOuterUidMap.get(assistantEmployeeId);
                if (!assistantIds.contains(outerUid)) {
                    needCreatePublicEmployeeObjEmployeeIds.add(assistantEmployeeId);
                }
            }
        }

        Long outerTenantId = fxiaokeEnterpriseAssociationEntityDaoRemoteManager.getOuterTenantIdByEa(upstreamEa);
        List<Map<String, Object>> createOuterUsers = this.batchCreatePublicEmployeeObjForSelfEmployee(ei, userId, outerTenantId, ListUtil.toIntegerList(needCreatePublicEmployeeObjEmployeeIds));
        List<PublicEmployeeObj> publicEmployeeObjs = PublicEmployeeObj.newInstance(createOuterUsers);
        Map<String, Long> newArgEmployeeIdOuterUidMap = publicEmployeeObjs.stream().collect(Collectors.toMap(PublicEmployeeObj::getEmployeeId, PublicEmployeeObj::getOuterUid));
        List<Long> createOuterUids = publicEmployeeObjs.stream().map(PublicEmployeeObj::getOuterUid).collect(Collectors.toList());

        Map<String, Set<String>> changePrivateObjectDatas2PublicApiNameDataIdsMap = new HashMap<>();
        // 将参数中的合作伙伴设置为共享数据
        changePrivateObjectDatas2PublicApiNameDataIdsMap.computeIfAbsent(PublicEmployeeObjConstant.API_NAME, k -> new HashSet<>()).addAll(ListUtil.toStringList(createOuterUids));
        paasShareObjectManager.changePrivateObjectDatas2Public(ei, SuperUserConstants.USER_ID, changePrivateObjectDatas2PublicApiNameDataIdsMap);

        // 跟新数据
        ObjectData objectData = new ObjectData();
        objectData.put(ErDepartmentObjConstant.ID, arg.getErDepartmentId());
        if (StringUtils.isNotBlank(arg.getManagerEmployeeId())) {
            Long outerUid = newArgEmployeeIdOuterUidMap.get(arg.getManagerEmployeeId());
            if (outerUid != null) {
                objectData.put(ErDepartmentObjConstant.MANAGER_ID, outerUid + "");
            } else {
                log.warn("this.batchGetOrCreatePublicEmployeeByEmployeeId error outerUid = null");
            }
        }

        if (CollectionUtils.isNotEmpty(arg.getAssistantEmployeeIds())) {
            List<Long> assisOuterUids = arg.getAssistantEmployeeIds().stream().map(newArgEmployeeIdOuterUidMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(assisOuterUids)) {
                objectData.put(ErDepartmentObjConstant.ASSISTANT_IDS, ListUtil.toStringList(assisOuterUids));
            }
        }
        updateErDepartmentObj(ei, userId, objectData);
        return Result.newSuccess();
    }

    private com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> updateErDepartmentObj(int tenantId, int userId, ObjectData objectData) {
        ActionEditArg actionEditArg = new ActionEditArg();
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(tenantId, userId);
        actionEditArg.setObjectData(objectData);
        return metadataActionService.edit(headerObj, ErDepartmentObjConstant.API_NAME, false, false, actionEditArg);
    }

    /**
     * 1、企业节点才可以移动 2、如果被挂载节点是组织或者部门节点，需要判断部门是否是当前企业的部门 3、被挂载节点如果是企业节点，要判断被挂载节点是否是移动节点的子节点（避免循环）
     */
    public Result<Void> updateErDepartmentOrderOrParentErDepartmentId(String upstreamEa, Integer userId, UpdateErDepartmentOrderOrParentErDepartmentIdArg arg) {
        int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        List<String> erDeptIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(arg.getPreErDepartmentId())) {
            erDeptIds.add(arg.getPreErDepartmentId());
        }
        if (!StringUtils.isEmpty(arg.getNextErDepartmentId())) {
            erDeptIds.add(arg.getNextErDepartmentId());
        }
        if (!StringUtils.isEmpty(arg.getParentErDepartmentId())) {
            erDeptIds.add(arg.getParentErDepartmentId());
        }
        erDeptIds.add(arg.getUpdateErDepartmentId());
        List<ErDepartmentObj> erDepartmentObjs = listErDepartmentObjs(upstreamEa, userId, erDeptIds);
        if (CollectionUtils.isEmpty(erDepartmentObjs)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Map<String, ErDepartmentObj> erDepartmentObjMap = erDepartmentObjs.stream().collect(Collectors.toMap(BaseObjectData::getId, Function.identity()));
        ErDepartmentObj updateErDepartmentObj = erDepartmentObjMap.get(arg.getUpdateErDepartmentId());
        if (updateErDepartmentObj == null) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (updateErDepartmentObj.getDepartmentType() == ErDepartmentTypeEnum.DEPARTMENT.getType() || updateErDepartmentObj.getDepartmentType() == ErDepartmentTypeEnum.ORGANIZATION.getType()) {
            if (!upstreamEa.equals(updateErDepartmentObj.getOuterTenantFsAccount())) {
                return Result.newError(ResultCode.DO_NOT_MOVE_NOT_OWN_ER_DEPARTMENT);
            }
        }
        // 必须是上游企业才能移动
        if (updateErDepartmentObj.getDepartmentType() == ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType() && !StringUtils.isEmpty(upstreamEa)) {
            return Result.newError(ResultCode.DO_NOT_MOVE_ENTERPRISE_RELATION_ER_DEPARTMENT_IN_DOWNSTREAM);
        }

        double newErOrder = updateErDepartmentObj.getErOrder();
        double newNextErOrder = -1;
        DecimalFormat df = new DecimalFormat("#.####");
        if (StringUtils.isEmpty(arg.getPreErDepartmentId()) && StringUtils.isEmpty(arg.getNextErDepartmentId())) {
            newErOrder = 0;// 父节点下没有其他节点，则说明是第一个
        }
        if (StringUtils.isEmpty(arg.getPreErDepartmentId()) && !StringUtils.isEmpty(arg.getNextErDepartmentId())) {
            ErDepartmentObj nextEerDepartmentObj = erDepartmentObjMap.get(arg.getNextErDepartmentId());
            // 如果nextEerDepartmentObj的order是0，且其父节点下只有1个子节点，则nextEerDepartmentObj的order+10000，newOrder = 0
            // 如果nextEerDepartmentObj的order是0，且其父节点下只有多个子节点，则找下一个节点的order/2，并更新nextEerDepartmentObj的order，newErOrder = 0
            if (nextEerDepartmentObj.getErOrder() != null && nextEerDepartmentObj.getErOrder() <= 0) {
                OrderBy orderBy = new OrderBy(ErDepartmentObjConstant.ER_ORDER, true);
                SearchQuery query = new SearchQuery();
                query.addFilter(ErDepartmentObjConstant.PARENT_ID, Lists.newArrayList(arg.getParentErDepartmentId()), FilterOperatorEnum.EQ);
                query.setOrders(Lists.newArrayList(orderBy));
                query.setOffset(0);
                query.setLimit(2);
                List<ObjectData> objectDatas = queryObjectDatas(upstreamEa, userId, ErDepartmentObjConstant.API_NAME, query);
                List<ErDepartmentObj> childErDepartmentObjs = ErDepartmentObj.newInstances(ObjectData.convert2MapList(objectDatas));
                if (childErDepartmentObjs.size() <= 1) {
                    newErOrder = 0;
                    newNextErOrder = newErOrder + 10000;
                }
                if (childErDepartmentObjs.size() > 1) {
                    newErOrder = 0;
                    newNextErOrder = Double.parseDouble(df.format(childErDepartmentObjs.get(1).getErOrder() / 2));
                }
            } else {
                newErOrder = Double.parseDouble(df.format(nextEerDepartmentObj.getErOrder() / 2));
            }
        }
        if (!StringUtils.isEmpty(arg.getPreErDepartmentId()) && StringUtils.isEmpty(arg.getNextErDepartmentId())) {
            ErDepartmentObj perEerDepartmentObj = erDepartmentObjMap.get(arg.getPreErDepartmentId());
            newErOrder = perEerDepartmentObj.getErOrder() + 10000;
        }
        if (!StringUtils.isEmpty(arg.getPreErDepartmentId()) && !StringUtils.isEmpty(arg.getNextErDepartmentId())) {
            ErDepartmentObj nextEerDepartmentObj = erDepartmentObjMap.get(arg.getNextErDepartmentId());
            ErDepartmentObj perEerDepartmentObj = erDepartmentObjMap.get(arg.getPreErDepartmentId());
            newErOrder = perEerDepartmentObj.getErOrder() + (nextEerDepartmentObj.getErOrder() - perEerDepartmentObj.getErOrder()) / 2;
        }
        // 下移nextErDepartmentObj
        if (newNextErOrder != -1) {
            ObjectData objectData = new ObjectData();
            objectData.put(ErDepartmentObjConstant.ID, arg.getNextErDepartmentId());
            objectData.put(ErDepartmentObjConstant.ER_ORDER, newNextErOrder);
            updateErDepartmentObj(tenantId, userId, objectData);
        }
        // 移动当前拖拽节点
        ObjectData objectData = new ObjectData();
        objectData.put(ErDepartmentObjConstant.ID, updateErDepartmentObj.getId());
        objectData.put(ErDepartmentObjConstant.ER_ORDER, newErOrder);
        if (!StringUtils.isEmpty(arg.getParentErDepartmentId()) && !updateErDepartmentObj.getParentId().equals(arg.getParentErDepartmentId())) {// 移动父节点
            objectData.put(ErDepartmentObjConstant.PARENT_ID, arg.getParentErDepartmentId());
        }
        updateErDepartmentObj(tenantId, userId, objectData);
        return Result.newSuccess();
    }

    public Boolean partnerStatusIsOpen(String upstreamEa) {
        int upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
        HeaderObj headerObj = new HeaderObj(upstreamEi, -10000);
        String status = partnerService.statusOpen(headerObj).getData().getResult();
        return "open".equals(status);
    }

    public Result<ListErDepartmentsUnderOneLevelWithPageResult> listErDepartmentsUnderOneLevelWithPage(RequestContext context, ListErDepartmentsUnderOneLevelWithPageArg arg) {
        ListErDepartmentsUnderOneLevelWithPageResult result = new ListErDepartmentsUnderOneLevelWithPageResult();
        result.setDataList(Lists.newArrayList());
        result.setCurrentParentPaths(Lists.newArrayList());
        result.setPageNumber(arg.getPageNumber());
        result.setPageSize(arg.getPageSize());
        result.setTotal(0);
        if (StringUtils.isEmpty(arg.getParentId()) && arg.getOuterTenantId() != null) {
            ErDepartmentObj erDepartmentObj = getErDepartmentObjByOuterTenantId(context, arg.getOuterTenantId());
            if (erDepartmentObj != null) {
                arg.setParentId(erDepartmentObj.getId());
            } else {
                return Result.newSuccess(result);
            }
        }

        SearchQuery query = new SearchQuery();
        if (ObjectUtils.isEmpty(arg.getParentId()) && ObjectUtils.isEmpty(arg.getOuterTenantId())) {
            query.addFilter(ErDepartmentObjConstant.PARENT_ID, Lists.newArrayList(""), FilterOperatorEnum.IS);
        }
        if (!StringUtils.isEmpty(arg.getParentId())) {
            query.addFilter(ErDepartmentObjConstant.PARENT_ID, Lists.newArrayList(arg.getParentId()), FilterOperatorEnum.EQ);
        }
        if (CollectionUtils.isNotEmpty(arg.getDepartmentTypes())) {
            query.addFilter(ErDepartmentObjConstant.DEPARTMENT_TYPE, arg.getDepartmentTypes(), FilterOperatorEnum.IN);
        }
        if (!StringUtils.isEmpty(arg.getSearchText())) {
            query.addFilter(ErDepartmentObjConstant.NAME, Lists.newArrayList(arg.getSearchText()), FilterOperatorEnum.LIKE);
        }
        query.addFilter(ErDepartmentObjConstant.STATUS, Lists.newArrayList(ErDepartmentStatusEnum.NORMAL.getType() + ""), FilterOperatorEnum.EQ);
        query.setOffset(arg.getOffset());
        query.setLimit(arg.getLimit());

        HeaderObj headerObj = HeaderObj.newInstance(context.getUser().getTenantIdInt(), -10000);
        Page<ObjectData> pageResult = queryObjectDatasResult(headerObj, ErDepartmentObjConstant.API_NAME, query, false);
        if (CollectionUtils.isEmpty(pageResult.getDataList())) {
            return Result.newSuccess(result);
        }

        List<ErDepartmentObj> erDepartmentObjs = ErDepartmentObj.newInstances(ObjectData.convert2MapList(pageResult.getDataList()));
        List<String> deptIds = erDepartmentObjs.stream().map(BaseObjectData::getId).collect(Collectors.toList());
        Map<String, Integer> deptIdChildrenCountMap = countErDepartmentChildren(context, deptIds);
        List<ErDepartmentData> erDepartmentDatas = toErDepartmentDatas(erDepartmentObjs, deptIdChildrenCountMap);
        List<ParentPath> parentPaths = getParentPaths(context, arg.getParentId());

        result.setDataList(erDepartmentDatas);
        result.setCurrentParentPaths(parentPaths);
        result.setTotal(pageResult.getTotal());
        return Result.newSuccess(result);
    }

    private ErDepartmentObj getErDepartmentObjByOuterTenantId(RequestContext context, Long outerTenantId) {
        HeaderObj headerObj = buildHeaderObj(context);
        SearchQuery query = new SearchQuery();
        query.addFilter(ErDepartmentObjConstant.OUTER_TENANT_ID, Lists.newArrayList(outerTenantId + ""), FilterOperatorEnum.EQ);
        query.addFilter(ErDepartmentObjConstant.DEPARTMENT_TYPE, Lists.newArrayList(ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType() + ""), FilterOperatorEnum.EQ);
        query.setOffset(0);
        query.setLimit(1);

        Page<ObjectData> result = queryObjectDatasResult(headerObj, ErDepartmentObjConstant.API_NAME, query, true);
        if (CollectionUtils.isEmpty(result.getDataList())) {
            return null;
        }
        return ErDepartmentObj.newInstance(result.getDataList().get(0));
    }

    private Map<Long, ErDepartmentObj> batchGetErDepartmentObjByOuterTenantIds(String ea, Integer userId, List<Long> outerTenantIds) {
        if (CollectionUtils.isEmpty(outerTenantIds)) {
            return new HashMap<>();
        }
        SearchQuery query = new SearchQuery();
        query.addFilter(ErDepartmentObjConstant.OUTER_TENANT_ID, ListUtil.toStringList(outerTenantIds), FilterOperatorEnum.IN);
        query.addFilter(ErDepartmentObjConstant.DEPARTMENT_TYPE, Lists.newArrayList(ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType() + ""), FilterOperatorEnum.EQ);
        query.setLimit(2000);
        query.setOffset(0);
        List<ObjectData> objectDatas = queryObjectDatas(ea, userId, ErDepartmentObjConstant.API_NAME, query);
        Map<Long, ErDepartmentObj> res = objectDatas.stream().map(ErDepartmentObj::newInstance).collect(Collectors.toMap(ErDepartmentObj::getOuterTenantId, Function.identity(), (o1, o2) -> {
            return o1;
        }));
        return res;
    }

    private List<ParentPath> getParentPaths(RequestContext context, String parentId) {
        if (StringUtils.isEmpty(parentId)) {
            return Lists.newArrayList();
        }
        HeaderObj headerObj = buildHeaderObj(context);
        ErDepartmentObj parentErDepartmentObj = getErDepartmentObjById(context, parentId);
        if (parentErDepartmentObj == null || CollectionUtils.isEmpty(parentErDepartmentObj.getTreePath())) {
            return Lists.newArrayList();
        }
        List<String> treePath = parentErDepartmentObj.getTreePath();
        com.fxiaoke.crmrestapi.common.result.Result<FindNameByIdsResult> nameByIds = objectDataService.findNameByIds(headerObj, ErDepartmentObjConstant.API_NAME, treePath);
        if (!nameByIds.isSuccess() || CollectionUtils.isEmpty(nameByIds.getData().getNameList())) {
            return Lists.newArrayList();
        }
        List<ParentPath> parentPaths = Lists.newArrayList();
        for (IdName idName : nameByIds.getData().getNameList()) {
            ParentPath parentPath = new ParentPath();
            parentPath.setId(idName.getId());
            parentPath.setName(idName.getName());
            parentPaths.add(parentPath);
        }
        return parentPaths;
    }

    private Map<String, Integer> countErDepartmentChildren(RequestContext context, List<String> parentIds) {
        Map<String, Integer> idCountMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(parentIds)) {
            return idCountMap;
        }

        AggFunction aggFunction = new AggFunction();
        aggFunction.setAggField("_id");
        aggFunction.setAggFunction("count");

        List<AggFunction> aggFunctions = Lists.newArrayList();
        aggFunctions.add(aggFunction);

        GroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setGroupBy(Lists.newArrayList("parent_id"));
        groupByParameter.setAggFunctions(aggFunctions);

        Filter filter = new Filter();
        filter.setFieldName("parent_id");
        filter.setFieldValues(parentIds);
        filter.setOperator(FilterOperatorEnum.IN.getValue());

        AggregateQueryArg.SearchQueryInfo searchQueryInfo = new AggregateQueryArg.SearchQueryInfo();
        searchQueryInfo.setGroupByParameter(groupByParameter);
        searchQueryInfo.getFilters().add(filter);

        AggregateQueryArg arg = new AggregateQueryArg();
        arg.setDescribeApiName("ErDepartmentObj");
        arg.setSearchQueryInfo(JsonUtil.toJson(searchQueryInfo));
        HeaderObj headerObj = buildHeaderObj(context);
        com.fxiaoke.crmrestapi.common.result.Result<AggregateQueryResult> result = objectDataServiceV3.aggregateQuery(headerObj, arg);
        List<Map<String, Object>> dataList = result.getData().getDataList();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (Map<String, Object> map : dataList) {
                Double count = (Double) map.get(AggregateQueryResult.RESULT_FIELD_GROUP_BY_COUNT);
                if (count == null) {
                    count = 0d;
                }
                idCountMap.put((String) map.get("parent_id"), count.intValue());
            }
        }
        return idCountMap;
    }

    private List<ErDepartmentData> toErDepartmentDatas(List<ErDepartmentObj> erDepartmentObjs, Map<String, Integer> idChildrenCountMap) {
        if (CollectionUtils.isEmpty(erDepartmentObjs)) {
            return Lists.newArrayList();
        }
        List<ErDepartmentData> erDepartmentDatas = Lists.newArrayList();
        for (ErDepartmentObj erDepartmentObj : erDepartmentObjs) {
            ErDepartmentData data = new ErDepartmentData();
            data.setId(erDepartmentObj.getId());
            data.setParentId(erDepartmentObj.getParentId());
            data.setName(erDepartmentObj.getName());
            data.setType(erDepartmentObj.getDepartmentType());
            data.setHasChildren(idChildrenCountMap.getOrDefault(erDepartmentObj.getId(), 0) > 0);
            data.setOrder(erDepartmentObj.getErOrder());
            erDepartmentDatas.add(data);
        }
        return erDepartmentDatas;
    }

    public Result<List<ErDepartmentData>> batchGetErDepartmentNames(String upstreamEa, Integer userId, BatchGetErDepartmentNamesArg arg) {
        List<ErDepartmentObj> erDepartmentObjs = listErDepartmentObjs(upstreamEa, userId, arg.getIds());
        if (CollectionUtils.isEmpty(erDepartmentObjs)) {
            return Result.newSuccess(Lists.newArrayList());
        }
        List<ErDepartmentData> erDepartmentDatas = Lists.newArrayList();
        for (ErDepartmentObj erDepartmentObj : erDepartmentObjs) {
            ErDepartmentData data = new ErDepartmentData();
            data.setId(erDepartmentObj.getId());
            data.setName(erDepartmentObj.getName());
            data.setType(erDepartmentObj.getDepartmentType());
            data.setStatus(erDepartmentObj.getStatus());
            erDepartmentDatas.add(data);
        }
        return Result.newSuccess(erDepartmentDatas);
    }

    public Result<ErDepartmentSimpleDataResult> batchQueryErDepartmentByNames(String upstreamEa, Integer userId, BatchQueryErDepartmentByNames arg) {
        ErDepartmentSimpleDataResult result = new ErDepartmentSimpleDataResult();
        SearchQuery query = new SearchQuery();
        if (!CollectionUtils.isEmpty(arg.getNames()) && arg.getNames().size() > 1000) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.PAGE_SIZE_NO_EXCEED_1000.getDescription());
        }
        query.addFilter(ErDepartmentObjConstant.NAME, Lists.newArrayList(arg.getNames()), FilterOperatorEnum.IN);
        if (!CollectionUtils.isEmpty(arg.getOuterTenantIds())) {
            List<String> outTenantIds = arg.getOuterTenantIds().stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList());
            query.addFilter(ErDepartmentObjConstant.OUTER_TENANT_ID, outTenantIds, FilterOperatorEnum.IN);
        }
        query.addFilter(ErDepartmentObjConstant.STATUS, Lists.newArrayList(ErDepartmentStatusEnum.NORMAL.getType() + "", ErDepartmentStatusEnum.STOP.getType() + ""), FilterOperatorEnum.IN);
        query.addFilter(ErDepartmentObjConstant.LIFE_STATUS, Lists.newArrayList("normal"), FilterOperatorEnum.EQ);
        query.setOffset(0);
        query.setLimit(arg.getNames().size());
        Page<ObjectData> queryResult = queryObjectDatasResult(upstreamEa, userId, ErDepartmentObjConstant.API_NAME, query, true);

        if (CollectionUtils.isEmpty(queryResult.getDataList())) {
            result.setDataList(Lists.newArrayList());
            return Result.newSuccess(result);
        }
        List<ErDepartmentObj> erDepartmentObjs = ErDepartmentObj.newInstances(ObjectData.convert2MapList(queryResult.getDataList()));
        List<ErDepartmentSimpleDataResult.ErDepartmentSimpleData> erDepartmentSimpleDataList = erDepartmentObjs.stream().map(x -> {
            ErDepartmentSimpleDataResult.ErDepartmentSimpleData data = new ErDepartmentSimpleDataResult.ErDepartmentSimpleData();
            data.setId(x.getId());
            data.setName(x.getName());
            data.setDepartmentCode(x.getDepartmentCode());
            data.setType(x.getDepartmentType());
            data.setOuterTenantId(x.getOuterTenantId());
            data.setParentId(x.getParentId());
            data.setOrder(x.getErOrder());
            data.setStatus(x.getStatus());
            return data;
        }).collect(Collectors.toList());
        result.setDataList(erDepartmentSimpleDataList);
        return Result.newSuccess(result);
    }

    public Result<ListErDepartmentWithPageResult> listErDepartmentWithPage(String upstreamEa, Integer userId, ListErDepartmentWithPageArg arg) {
        ListErDepartmentWithPageResult result = new ListErDepartmentWithPageResult();
        result.setDataList(Lists.newArrayList());
        result.setPageNumber(arg.getPageNumber());
        result.setPageSize(arg.getPageSize());
        result.setTotal(0);

        SearchQuery query = new SearchQuery();
        if (!StringUtils.isEmpty(arg.getSearchText())) {
            query.addFilter(ErDepartmentObjConstant.NAME, Lists.newArrayList(arg.getSearchText()), FilterOperatorEnum.LIKE);
        }
        if (!StringUtils.isEmpty(arg.getOuterTenantId())) {
            query.addFilter(ErDepartmentObjConstant.OUTER_TENANT_ID, Lists.newArrayList(arg.getOuterTenantId()), FilterOperatorEnum.EQ);
        }
        query.addFilter(ErDepartmentObjConstant.STATUS, Lists.newArrayList(ErDepartmentStatusEnum.NORMAL.getType() + ""), FilterOperatorEnum.EQ);
        query.setOffset(arg.getOffset());
        query.setLimit(arg.getLimit());
        Page<ObjectData> objectDatasResult = queryObjectDatasResult(upstreamEa, userId, ErDepartmentObjConstant.API_NAME, query, true);

        if (CollectionUtils.isEmpty(objectDatasResult.getDataList())) {
            return Result.newSuccess(result);
        }
        List<ErDepartmentObj> erDepartmentObjs = ErDepartmentObj.newInstances(ObjectData.convert2MapList(objectDatasResult.getDataList()));
        List<ErDepartmentObjData> erDepartmentObjDatas = toErDepartmentObjDatas(erDepartmentObjs, Maps.newHashMap(), Maps.newHashMap(), Maps.newHashMap());
        result.setDataList(erDepartmentObjDatas);
        result.setTotal(objectDatasResult.getTotal());
        return Result.newSuccess(result);
    }

    /**
     * 查询可选的上游互联部门（要排除当前互联企业关联的互联部门及所有子部门节点数据）
     *
     * @param upstreamEa 上游企业Ea
     * @param userId     当前用户ID
     * @param arg        查询条件
     * @return 互联部门列表
     */
    public Result<ListErDepartmentWithPageResult> listParentErDepartmentWithPage(String upstreamEa, Integer userId, ListParentErDepartmentArg arg) {
        List<String> ids = Lists.newArrayList();
        OffsetUtil.offsetFunction(1000, (offset, limit) -> {
            SearchQuery query = new SearchQuery();
            query.addFilter(ErDepartmentObjConstant.OUTER_TENANT_ID, Lists.newArrayList(arg.getOuterTenantId()), FilterOperatorEnum.EQ);
            query.addFilter(ErDepartmentObjConstant.STATUS, Lists.newArrayList(ErDepartmentStatusEnum.NORMAL.getType() + ""), FilterOperatorEnum.EQ);
            Page<ObjectData> result = queryObjectDatasResult(upstreamEa, userId, ErDepartmentObjConstant.API_NAME, query, true);
            if (!CollectionUtils.isEmpty(result.getDataList())) {
                List<String> deptIds = result.getDataList().stream().map(map -> map.getId()).collect(Collectors.toList());
                ids.addAll(deptIds);
                return result.getDataList().size();
            }
            return 0;
        });

        ListErDepartmentWithPageResult result = new ListErDepartmentWithPageResult();
        SearchQuery query2 = new SearchQuery();
        if (!StringUtils.isEmpty(arg.getSearchText())) {
            query2.addFilter(ErDepartmentObjConstant.NAME, Lists.newArrayList(arg.getSearchText()), FilterOperatorEnum.LIKE);
        }
        if (!CollectionUtils.isEmpty(ids)) {
            query2.addFilter(ErDepartmentObjConstant.ID, ids, FilterOperatorEnum.NIN);
        }
        query2.addFilter(ErDepartmentObjConstant.STATUS, Lists.newArrayList(ErDepartmentStatusEnum.NORMAL.getType() + ""), FilterOperatorEnum.EQ);
        query2.setOffset(arg.getOffset());
        query2.setLimit(arg.getLimit());
        Page<ObjectData> queryResult = queryObjectDatasResult(upstreamEa, userId, ErDepartmentObjConstant.API_NAME, query2, true);

        if (!CollectionUtils.isEmpty(queryResult.getDataList())) {
            List<ErDepartmentObj> erDepartmentObjs = ErDepartmentObj.newInstances(ObjectData.convert2MapList(queryResult.getDataList()));
            List<ErDepartmentObjData> erDepartmentObjDatas = toErDepartmentObjDatas(erDepartmentObjs, Maps.newHashMap(), Maps.newHashMap(), Maps.newHashMap());
            result.setDataList(erDepartmentObjDatas);
            result.setTotal(queryResult.getTotal());
            return Result.newSuccess(result);
        }

        result.setDataList(Lists.newArrayList());
        result.setPageNumber(arg.getPageNumber());
        result.setPageSize(arg.getPageSize());
        result.setTotal(0);
        return Result.newSuccess(result);
    }

    private HeaderObj buildHeaderObj(User user) {
        HeaderObj headerObj = new HeaderObj(user.getTenantIdInt(), user.getUserIdInt(), "fs-enterprise-relation-biz", true);
        if (user.getOutTenantId() != null) {
            headerObj.put("X-out-tenant-id", user.getOutTenantId());
        }
        if (user.getOutUserId() != null) {
            headerObj.put("X-out-user-id", user.getOutUserId());
        }
        return headerObj;
    }

    private HeaderObj buildHeaderObj(RequestContext context) {
        User user = context.getUser();
        HeaderObj headerObj = new HeaderObj(user.getTenantIdInt());
        if (user.getUserIdInt() != null) {
            headerObj.setOperatorId(user.getUserIdInt());
        }
        if (user.getOutTenantId() != null) {
            headerObj.put("X-out-tenant-id", user.getOutTenantId());
        }
        if (user.getOutUserId() != null) {
            headerObj.put("X-out-user-id", user.getOutUserId());
        }
        if (StringUtils.isNotEmpty(context.getAppId())) {
            headerObj.put("X-app-id", context.getAppId());
        }
        if (StringUtils.isNotEmpty(context.getThirdType())) {
            headerObj.put("x-fs-thirdType", context.getThirdType());
        }
        if (StringUtils.isNotEmpty(context.getThirdAppId())) {
            headerObj.put("x-fs-thirdAppId", context.getThirdAppId());
        }
        if (StringUtils.isNotEmpty(context.getThirdUserId())) {
            headerObj.put("x-fs-thirdUserId", context.getThirdUserId());
        }
        if (StringUtils.isNotEmpty(context.getOutIdentityType())) {
            headerObj.put("x-out-identity-type", context.getOutIdentityType());
        }
        if (StringUtils.isNotEmpty(context.getUpstreamOwnerId())) {
            headerObj.put("X-fs-upstream-owner-id", context.getUpstreamOwnerId());
        }
        if (StringUtils.isNotEmpty(context.getEa())) {
            headerObj.put("X-fs-Enterprise-Account", context.getEa());
        }
        if (StringUtils.isNotEmpty(context.getLang().getValue())) {
            headerObj.put("x-fs-locale", context.getLang().getValue());
        }
        return headerObj;
    }
}
