package com.facishare.orgerdepartment.service.impl;

import com.facishare.enterprise.common.constant.EnterpriseRelationObjConstant;
import com.facishare.enterprise.common.constant.ErDepartmentObjConstant;
import com.facishare.enterprise.common.constant.PublicEmployeeObjConstant;
import com.facishare.enterprise.common.enums.PublicEmployeeObjTypeOption;
import com.facishare.enterprise.common.exception.RelationException;
import com.facishare.enterprise.common.model.EnterpriseRelationObj;
import com.facishare.enterprise.common.model.ErDepartmentObj;
import com.facishare.enterprise.common.model.PublicEmployeeObj;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.enterprise.common.util.ListUtil;
import com.facishare.er.api.model.EnterpriseRelation;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.orgerdepartment.admin.arg.*;
import com.facishare.orgerdepartment.admin.data.ConnectEnterpriseData;
import com.facishare.orgerdepartment.constant.DepartmentType;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;
import com.facishare.rest.account.common.GroupControlInfo;
import com.facishare.orgerdepartment.admin.data.ListIsolationSettingData;
import com.facishare.orgerdepartment.admin.result.ListConnectEnterpriseResult;
import com.facishare.enterprise.common.constant.OrganizationalModel;
import com.facishare.orgerdepartment.admin.result.ListIsolationSettingResult;
import com.facishare.orgerdepartment.constant.QueryTypeEnum;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectLogicService;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicFieldDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobParamDTO;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicFieldType;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobType;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectStatusResult;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectStatusType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.rest.account.dao.data.EnterpriseRelationQueryArg;
import com.facishare.rest.account.dao.data.ErDepartmentQueryArg;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.rest.account.dao.data.PublicEmployeeQueryArg;
import com.facishare.wechat.union.core.api.constant.ErrorCode;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentStatusEnum;
import com.fxiaoke.enterpriserelation.objrest.common.ErDepartmentTypeEnum;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.io.Resources;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 基于公共对象互联组织架构服实现
 */
@Service
@Slf4j
public class PublicErOrganizationalManagementObjectServiceImpl extends AbstractErOrganizationalManagementObjectService {


    @Autowired
    private PublicObjectLogicService publicObjectLogicService;
    private static Map<String, Map<String, Set<String>>> PUBLIC_OBJECT_FIELD_CONFIG = new HashMap<>();
    //公共字段私有数据
    private static final String PUBLIC_FIELD_PRIVATE_DATA = "public_field_private_data_api_names";
    //私有字段
    private static final String PRIVATE_FIELD = "private_field_api_names";
    //公共字段公共数据
    private static final String PUBLIC_FIELD_PUBLIC_DATA = "public_field_public_data_api_names";

    static {
        try (InputStream resource = Resources.getResourceAsStream("object/PublicObjectFieldConfig.json")) {
            PUBLIC_OBJECT_FIELD_CONFIG = JsonUtil.fromJson(StreamUtils.copyToString(resource, Charset.defaultCharset()), new com.google.gson.reflect.TypeToken<Map<String, Map<String, Set<String>>>>() {
            }.getType());
        } catch (Exception e) {
            log.error("load object/PublicObjectFieldConfig.json from file error ", e);
        }
    }

    @Override
    protected Result<Void> doInitRootErDepartmentObj(String tenantId, Integer userId, CreateRootErDepartmentObjArg arg, List<EnterpriseRelationObj> enterpriseRelationObjs, List<ErDepartmentObj> erDepartmentObjs, List<PublicEmployeeObj> publicEmployeeObjs, String rootDepartmentId) {
        User user = new User(tenantId, userId.toString());
        //验证互联企业+互联用户+互联部门是否是公共对象
        List<String> describeApiNames = Lists.newArrayList(EnterpriseRelationObjConstant.API_NAME, ErDepartmentObjConstant.API_NAME, PublicEmployeeObjConstant.API_NAME);
        Map<String, IObjectDescribe> objects = serviceFacade.findObjectsWithoutCopy(tenantId, describeApiNames);
        List<String> failObjectDisplayName = openPublicObject(tenantId, userId, objects.values());
        if (ObjectUtils.isNotEmpty(failObjectDisplayName)) {
            String displayNames = failObjectDisplayName.stream().collect(Collectors.joining(","));
            return Result.newError(ResultCode.ENTERPRISE_NEED_OPEN_PUBLIC_OBJECT.getErrorCode(), ResultCode.ENTERPRISE_NEED_OPEN_PUBLIC_OBJECT.getDescription("", displayNames));
        }

        //验证下游是否开启公共对象, 如果没开启公共对象，直接提示异常，在后台管理页进行开启
        for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationObjs) {
            String downstreamEa = enterpriseRelationObj.getEnterpriseAccount();
            int downstreamTenantId = eieaConverter.enterpriseAccountToId(downstreamEa);
            if (Objects.equals(downstreamTenantId, tenantId)) {
                continue;
            }
            Map<String, IObjectDescribe> downstreamDescribes = serviceFacade.findObjectsWithoutCopy(String.valueOf(downstreamTenantId), describeApiNames);
            for (Map.Entry<String, IObjectDescribe> describeEntry : downstreamDescribes.entrySet()) {
                IObjectDescribe describe = describeEntry.getValue();
                if (!describe.isPublicObject()) {
                    return Result.newError(ResultCode.ENTERPRISE_NEED_OPEN_PUBLIC_OBJECT.getErrorCode(), ResultCode.ENTERPRISE_NEED_OPEN_PUBLIC_OBJECT.getDescription(enterpriseRelationObj.getName(), describe.getDisplayName()));
                }
            }
        }

        OrganizationalModel organizationalModel = OrganizationalModel.of(arg.getOrganizationalModel());
        Map<String, String> id2Ea = enterpriseRelationObjs.stream().collect(Collectors.toMap(it -> it.getId(), it -> it.getEnterpriseAccount(), (x, y) -> x));
        Map<String, Integer> ea2TenantId = eieaConverter.enterpriseAccountToId(id2Ea.values());

        List<String> enterpriseRelationIds = enterpriseRelationObjs.stream().map(EnterpriseRelationObj::getId).collect(Collectors.toList());
        List<String> departmentIds = erDepartmentObjs.stream().map(ErDepartmentObj::getId).collect(Collectors.toList());
        departmentIds.add(rootDepartmentId);

        List<String> employeeIds = publicEmployeeObjs.stream().map(PublicEmployeeObj::getId).collect(Collectors.toList());

        List<IObjectData> updateEnterpriseRelationObj = buildUpdateObjectData(enterpriseRelationIds, tenantId, EnterpriseRelationObjConstant.API_NAME);
        List<IObjectData> updateErDepartmentObj = buildUpdateObjectData(departmentIds, tenantId, ErDepartmentObjConstant.API_NAME);
        List<IObjectData> updatePublicEmployeeObj = buildUpdateObjectData(employeeIds, tenantId, PublicEmployeeObjConstant.API_NAME);
        //集团新增企业
        Map<String, Object> updateMap = new HashMap<>();
        if (organizationalModel == OrganizationalModel.GROUP) {
            Set<String> allTenantId = ea2TenantId.values().stream().map(String::valueOf).collect(Collectors.toSet());
            allTenantId.add(user.getTenantId());

            updateMap.put(IObjectData.DOWNSTREAM_TENANT_ID, allTenantId.stream().collect(Collectors.toList()));
            updateMap.put(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PUBLIC_DATA);
            serviceFacade.batchUpdateWithMap(user, updateEnterpriseRelationObj, updateMap);

            if (ObjectUtils.isNotEmpty(updateErDepartmentObj)) {
                serviceFacade.batchUpdateWithMap(user, updateErDepartmentObj, updateMap);
            }
            if (ObjectUtils.isNotEmpty(publicEmployeeObjs)) {
                serviceFacade.batchUpdateWithMap(user, updatePublicEmployeeObj, updateMap);
            }
        }


        //品牌新增企业
        if (organizationalModel == OrganizationalModel.BRAND) {
            updateMap.put(IObjectData.DOWNSTREAM_TENANT_ID, Lists.newArrayList(tenantId));
            updateMap.put(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PUBLIC_DATA);
            serviceFacade.batchUpdateWithMap(user, updateEnterpriseRelationObj, updateMap);

            if (ObjectUtils.isNotEmpty(updateErDepartmentObj)) {
                serviceFacade.batchUpdateWithMap(user, updateErDepartmentObj, updateMap);
            }
            if (ObjectUtils.isNotEmpty(updateErDepartmentObj)) {
                serviceFacade.batchUpdateWithMap(user, updateErDepartmentObj, updateMap);
            }
            if (ObjectUtils.isNotEmpty(publicEmployeeObjs)) {
                serviceFacade.batchUpdateWithMap(user, updatePublicEmployeeObj, updateMap);
            }
        }

        return Result.newSuccess();
    }

    @Override
    protected List<PublicEmployeeObj> doCreatePublicEmployeeByEmployeeIds(RequestContext context, String upstreamTenantId, Long outerTenantId, List<String> employeeIds, String orgManageType, String departmentId) {
        List<String> downstreanTenantId = new ArrayList<>();
        List<EnterpriseRelationObj> enterpriseRelationObjList = batchGetEnterpriseRelationByOuterTenantId(context.getTenantId(), Lists.newArrayList(outerTenantId), Lists.newArrayList(EnterpriseRelationObjConstant.DOWNSTREAM_TENANT_ID));
        if (ObjectUtils.isNotEmpty(enterpriseRelationObjList)) {
            downstreanTenantId = enterpriseRelationObjList.get(0).getDownstreamTenantIds();
        }
        List<PublicEmployeeObj> publicEmployeeObjList = batchCreatePublicEmployeeObjByEmployeeIdStr(Integer.valueOf(context.getTenantId()), context.getUser().getUserIdInt(), outerTenantId, employeeIds, downstreanTenantId, orgManageType, departmentId);
        return publicEmployeeObjList;
    }


    /**
     * 为1 企业开启公共对象
     */
    private List<String> openPublicObject(String tenantId, Integer userId, Collection<IObjectDescribe> objectDescribes) {
        User user = new User(tenantId, userId.toString());

        List<String> unOpenPublicObjectName = new ArrayList<>();
        for (IObjectDescribe objectDescribe : objectDescribes) {
            if (objectDescribe.isPublicObject()) {
                continue;
            }

            PublicObjectStatusResult publicObjectStatusResult = publicObjectLogicService.queryStatus(user, objectDescribe.getApiName());
            if (PublicObjectStatusType.OPENED.equals(publicObjectStatusResult.getPublicObjectStatus())) {
                continue;
            }
            if (PublicObjectStatusType.OPENING.equals(publicObjectStatusResult.getPublicObjectStatus())) {
                unOpenPublicObjectName.add(objectDescribe.getDisplayName());
            }
            Map<String, Set<String>> publicObjectFieldConfig = PUBLIC_OBJECT_FIELD_CONFIG.getOrDefault(objectDescribe.getApiName(), MapUtils.EMPTY_SORTED_MAP);
            Set<String> publicFieldPrivateDataApiNames = publicObjectFieldConfig.getOrDefault(PUBLIC_FIELD_PRIVATE_DATA, SetUtils.EMPTY_SORTED_SET);
            Set<String> publicFieldPublicDataApiNames = publicObjectFieldConfig.getOrDefault(PUBLIC_FIELD_PUBLIC_DATA, SetUtils.EMPTY_SORTED_SET);
            Set<String> privateFieldApiNames = publicObjectFieldConfig.getOrDefault(PRIVATE_FIELD, SetUtils.EMPTY_SORTED_SET);


            List<PublicFieldDTO> publicFieldDTOS = new ArrayList<>();
            for (IFieldDescribe fieldDescribe : objectDescribe.getFieldDescribes()) {
                if (publicFieldPrivateDataApiNames.contains(fieldDescribe.getApiName())) {
                    publicFieldDTOS.add(PublicFieldDTO.of(fieldDescribe.getApiName(), PublicFieldType.PUBLIC_FIELD_PRIVATE_DATA.getType()));
                }
                if (publicFieldPublicDataApiNames.contains(fieldDescribe.getApiName())) {
                    publicFieldDTOS.add(PublicFieldDTO.of(fieldDescribe.getApiName(), PublicFieldType.PUBLIC_FIELD_PUBLIC_DATA.getType()));
                }
                if (privateFieldApiNames.contains(fieldDescribe.getApiName())) {
                    publicFieldDTOS.add(PublicFieldDTO.of(fieldDescribe.getApiName(), PublicFieldType.PRIVATE_FIELD.getType()));
                }
            }

            PublicObjectJobParamDTO arg = new PublicObjectJobParamDTO();
            arg.setObjectApiName(objectDescribe.getApiName());
            arg.setJobType(PublicObjectJobType.OPEN_JOB.getType());
            arg.setFields(publicFieldDTOS);
            String jobId = publicObjectLogicService.createJob(user, arg);
            log.info("create public object {} {} {}", tenantId, objectDescribe.getApiName(), jobId);
            unOpenPublicObjectName.add(objectDescribe.getDisplayName());
        }

        return unOpenPublicObjectName;
    }

    @Override
    public Result<ListConnectEnterpriseResult> listConnectEnterprise(String tenantId, String upstreamTenantId, ListConnectEnterpriseArg arg) {
        ListConnectEnterpriseResult listConnectEnterpriseResult = new ListConnectEnterpriseResult();
        listConnectEnterpriseResult.setPageSize(arg.getPageSize());
        listConnectEnterpriseResult.setPageNumber(arg.getPageNumber());
        listConnectEnterpriseResult.setDataList(new ArrayList<>());
        User upstreamUser = User.systemUser(tenantId);

        GroupControlInfo groupItControlInfo = getGroupItControlInfo(upstreamTenantId);
        if (!groupItControlInfo.isEnable()) {
            return Result.newSuccess(listConnectEnterpriseResult);
        }
        List<String> orgManageTypes = Collections.singletonList(OrganizationalModel.of(groupItControlInfo.getOrganizationalModel()).getType());
        ErDepartmentObj erDepartmentObj = erDepartmentDao.getById(upstreamUser, arg.getErDepartmentId());


        EnterpriseRelationQueryArg queryArg = new EnterpriseRelationQueryArg();
        queryArg.setDestOuterTenantIds(Collections.singletonList(String.valueOf(erDepartmentObj.getOuterTenantId())));
        queryArg.setIgnoreBaseVisibleRange(true);
        queryArg.setIgnoreSelfFilter(true);
        queryArg.setOrgManageTypes(orgManageTypes);
        queryArg.setHasFsAccount(true);

        QueryResult<IObjectData> dataQueryResult = enterpriseRelationDao.getQueryResult(upstreamUser, queryArg);
        if (CollectionUtils.isEmpty(dataQueryResult.getData())) {
            return Result.newError(ResultCode.NOT_FOUND);
        }

        EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(dataQueryResult.getData().get(0));
        EnterpriseRelationQueryArg enterpriseRelationQueryArg = new EnterpriseRelationQueryArg();
        enterpriseRelationQueryArg.setHasFsAccount(true);
        enterpriseRelationQueryArg.setOrgManageTypes(orgManageTypes);
        enterpriseRelationQueryArg.setLimit(arg.getLimit());
        enterpriseRelationQueryArg.setOffset(arg.getOffset());
        if (ObjectUtils.isNotEmpty(arg.getSearchText())) {
            enterpriseRelationQueryArg.setName(arg.getSearchText());
        }
        if (ObjectUtils.isNotEmpty(arg.getTargetOuterTenantId())) {
            enterpriseRelationQueryArg.setDestOuterTenantIds(Lists.newArrayList(arg.getTargetOuterTenantId() + ""));
        }


        if (Objects.equals(arg.getQueryType(), QueryTypeEnum.UPSTREAM_ENTERPRISE.getType())) {
            List<String> downstreamTenantIds = enterpriseRelationObj.getDownstreamTenantIds();
            if (ObjectUtils.isEmpty(downstreamTenantIds)) {
                enterpriseRelationObj.setDownstreamTenantIds(Lists.newArrayList(upstreamTenantId));
            }

            Map<Integer, String> eaMap = eieaConverter.enterpriseIdToAccount(enterpriseRelationObj.getOnlyTenantIdDTenantIdList().stream().map(Integer::valueOf).collect(Collectors.toList()));
            enterpriseRelationQueryArg.setEnterpriseAccounts(eaMap.values());
            enterpriseRelationQueryArg.setIgnoreBaseVisibleRange(true);
        }

        String downstreamTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(enterpriseRelationObj.getEnterpriseAccount()));
        enterpriseRelationQueryArg.setSelectFields(Lists.newArrayList(EnterpriseRelationObjConstant.NAME, EnterpriseRelationObjConstant.DEST_OUTER_TENANT_ID, EnterpriseRelationObjConstant.ID));
        QueryResult<IObjectData> result = enterpriseRelationDao.getQueryResult(User.systemUser(downstreamTenantId), enterpriseRelationQueryArg);
        if (ObjectUtils.isEmpty(result.getData())) {
            return Result.newSuccess(listConnectEnterpriseResult);
        }

        listConnectEnterpriseResult.setTotal(result.getTotalNumber());
        List<EnterpriseRelationObj> enterpriseRelationObjList = EnterpriseRelationObj.newInstanceList(result.getData());
        List<Long> outerTenantIds = enterpriseRelationObjList.stream().map(EnterpriseRelationObj::getOuterTenantId).collect(Collectors.toList());
        Map<Long, String> erDepartmentIdMap = getErDepartmentIdMap(tenantId, outerTenantIds);

        List<ConnectEnterpriseData> connectEnterpriseDataList = new ArrayList<>();
        for (EnterpriseRelationObj enterpriseRelationData : enterpriseRelationObjList) {
            ConnectEnterpriseData connectEnterpriseData = new ConnectEnterpriseData();
            if (Objects.equals(arg.getQueryType(), QueryTypeEnum.ER_ENTERPRISE.getType())) {
                connectEnterpriseData.setEnterpriseRelationName(enterpriseRelationData.getName());
                connectEnterpriseData.setUpstreamEnterpriseName(enterpriseRelationObj.getName());
            } else {
                connectEnterpriseData.setEnterpriseRelationName(enterpriseRelationObj.getName());
                connectEnterpriseData.setUpstreamEnterpriseName(enterpriseRelationData.getName());
            }
            connectEnterpriseData.setErDepartmentId(erDepartmentIdMap.get(enterpriseRelationData.getOuterTenantId()));
            PublicEmployeeObj publicEmployeeObj = publicEmployeeDao.getRelationOwnerIgnorePublicObjectFilter(upstreamUser, enterpriseRelationData.getOuterTenantId());
            if (publicEmployeeObj != null) {
                connectEnterpriseData.setRelationOwnerName(publicEmployeeObj.getName());
            }
            connectEnterpriseDataList.add(connectEnterpriseData);
        }

        listConnectEnterpriseResult.setDataList(connectEnterpriseDataList);
        return Result.newSuccess(listConnectEnterpriseResult);
    }


    @SneakyThrows
    @Override
    @MetadataTransactional
    public Result<Void> addEnterpriseConnection(RequestContext requestContext, String upstreamTenantId, AddEnterpriseConnectionArg arg) {
        User user = User.systemUser(requestContext.getTenantId());

        GroupControlInfo groupItControlInfo = getGroupItControlInfo(upstreamTenantId);
        if (!groupItControlInfo.isEnable()) {
            return Result.newSuccess();
        }

        OrganizationalModel organizationalModel = OrganizationalModel.of(groupItControlInfo.getOrganizationalModel());
        if (StringUtils.isAnyBlank(arg.getSourceDepartmentId(), arg.getTargetDepartmentId())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (Objects.equals(arg.getSourceDepartmentId(), arg.getTargetDepartmentId())) {
            return Result.newError(ResultCode.CONNECTION_ISOLATED_PARAM_ERROR);
        }

        List<ErDepartmentObj> departmentObjs = erDepartmentDao.getByIds(user, Lists.newArrayList(arg.getSourceDepartmentId(), arg.getTargetDepartmentId()));
        if (ObjectUtils.isEmpty(departmentObjs)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        //查询来源和目标部门
        ErDepartmentObj sourceErDepartmentObj = null;
        ErDepartmentObj targetErDepartmentObj = null;
        for (ErDepartmentObj departmentObj : departmentObjs) {
            if (Objects.equals(departmentObj.getId(), arg.getSourceDepartmentId())) {
                sourceErDepartmentObj = departmentObj;
                continue;
            }
            if (Objects.equals(departmentObj.getId(), arg.getTargetDepartmentId())) {
                targetErDepartmentObj = departmentObj;
            }
        }
        if (sourceErDepartmentObj == null || targetErDepartmentObj == null || Objects.equals(sourceErDepartmentObj.getOuterTenantId(), targetErDepartmentObj.getOuterTenantId())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (sourceErDepartmentObj.getDepartmentType() != ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType() || targetErDepartmentObj.getDepartmentType() != ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType()) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        ErDepartmentObj finalSourceErDepartmentObj = sourceErDepartmentObj;
        ErDepartmentObj finalTargetErDepartmentObj = targetErDepartmentObj;
        boolean exists = metadataTransactionService.executeWithOutMetadataTransaction(() -> isConnectEnterpriseExists(requestContext.getTenantId(), upstreamTenantId, finalSourceErDepartmentObj, finalTargetErDepartmentObj));
        if (exists) {
            return Result.newError(ResultCode.CONNECTION_EXISTS_ERROR);
        }
        //查看互联企业
        List<Long> outerTenantIds = Lists.newArrayList(sourceErDepartmentObj.getOuterTenantId(), targetErDepartmentObj.getOuterTenantId());
        List<EnterpriseRelationObj> enterpriseRelationObjs = enterpriseRelationDao.getByIdsIncludeSelf(User.systemUser(requestContext.getTenantId()), outerTenantIds);
        if (ObjectUtils.isEmpty(enterpriseRelationObjs) || enterpriseRelationObjs.stream().anyMatch(enterpriseRelationObj -> StringUtils.isEmpty(enterpriseRelationObj.getEnterpriseAccount()))) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        EnterpriseRelationObj targetEnterpriseRelationObj = null;
        String appendTenantId = null;
        for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationObjs) {
            if (Objects.equals(enterpriseRelationObj.getOuterTenantId(), sourceErDepartmentObj.getOuterTenantId())) {
                int enterpriseAccountToId = eieaConverter.enterpriseAccountToId(enterpriseRelationObj.getEnterpriseAccount());
                appendTenantId = enterpriseAccountToId + "";
                continue;
            }
            targetEnterpriseRelationObj = enterpriseRelationObj;
        }

        if (ObjectUtils.isEmpty(targetEnterpriseRelationObj)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (ObjectUtils.isEmpty(appendTenantId)) {
            log.warn("not found append tenants");
            return Result.newSuccess();
        }

        //修改互联企业+互联部门+互联用户的可见范围
        //修改互联企业的可见范围

        List<String> appendTenantIds = Lists.newArrayList(appendTenantId);
        List<IObjectData> updateEnterpriseRelation = buildAppendDownstreamTenantObject(Lists.newArrayList(targetEnterpriseRelationObj), EnterpriseRelationObjConstant.API_NAME, appendTenantIds, requestContext.getTenantId());
        serviceFacade.batchUpdateByFields(requestContext.getUser(), updateEnterpriseRelation, Lists.newArrayList(PublicEmployeeObjConstant.DOWNSTREAM_TENANT_ID, IObjectData.PUBLIC_DATA_TYPE));

        //修改互联用户的可见范围
        List<PublicEmployeeObj> publicEmployeeObjs = batchGetPublicEmployeeByOuterTenantIds(requestContext.getTenantId(), Lists.newArrayList(targetEnterpriseRelationObj.getOuterTenantId()), Lists.newArrayList(PublicEmployeeObjConstant.ID, PublicEmployeeObjConstant.DOWNSTREAM_TENANT_ID));
        if (ObjectUtils.isNotEmpty(publicEmployeeObjs)) {
            List<IObjectData> updatePublicEmployeeObj = buildAppendDownstreamTenantObject(publicEmployeeObjs, PublicEmployeeObjConstant.API_NAME, appendTenantIds, requestContext.getTenantId());
            serviceFacade.batchUpdateByFields(requestContext.getUser(), updatePublicEmployeeObj, Lists.newArrayList(PublicEmployeeObjConstant.DOWNSTREAM_TENANT_ID, IObjectData.PUBLIC_DATA_TYPE));
        }

        //如果是品牌模式，修改互联部门的可见范围
        if (organizationalModel == OrganizationalModel.BRAND) {
            List<IObjectData> updateErDepartmentObjs = buildAppendDownstreamTenantObject(Lists.newArrayList(targetErDepartmentObj), ErDepartmentObjConstant.API_NAME, appendTenantIds, requestContext.getTenantId());
            serviceFacade.batchUpdateByFields(requestContext.getUser(), updateErDepartmentObjs, Lists.newArrayList(PublicEmployeeObjConstant.DOWNSTREAM_TENANT_ID, IObjectData.PUBLIC_DATA_TYPE));
        }

        return Result.newSuccess();
    }

    //验证互联关系是否存在
    private boolean isConnectEnterpriseExists(String tenantId, String upstreamTenantId, ErDepartmentObj sourceErDepartmentObj, ErDepartmentObj targetErDepartmentObj) {
        ListConnectEnterpriseArg arg = new ListConnectEnterpriseArg();
        arg.setPageNumber(1);
        arg.setPageSize(1);
        arg.setErDepartmentId(sourceErDepartmentObj.getId());
        arg.setTargetOuterTenantId(targetErDepartmentObj.getOuterTenantId());
        arg.setQueryType(QueryTypeEnum.ER_ENTERPRISE.getType());

        Result<ListConnectEnterpriseResult> result = listConnectEnterprise(tenantId, upstreamTenantId, arg);
        if (result.isSuccess() && ObjectUtils.isNotEmpty(result.getData()) && ObjectUtils.isNotEmpty(result.getData().getDataList())) {
            return true;
        }
        return false;
    }

    @Override
    public Result<ListIsolationSettingResult> listIsolationSetting(String tenantId, String upstreamTenantId, ListIsolationSettingArg arg) {
        ListIsolationSettingResult listConnectEnterpriseResult = new ListIsolationSettingResult();
        listConnectEnterpriseResult.setDataList(ImmutableList.of());
        listConnectEnterpriseResult.setPageNumber(arg.getPageNumber());
        listConnectEnterpriseResult.setPageSize(arg.getPageSize());

        GroupControlInfo groupItControlInfo = getGroupItControlInfo(upstreamTenantId);
        if (!groupItControlInfo.isEnable()) {
            return Result.newSuccess(listConnectEnterpriseResult);
        }

        User upstreamUser = User.systemUser(upstreamTenantId);
        List<String> orgManageTypes = Collections.singletonList(OrganizationalModel.of(groupItControlInfo.getOrganizationalModel()).getType());

        ErDepartmentObj erDepartmentObj = erDepartmentDao.getById(upstreamUser, arg.getErDepartmentId());
        EnterpriseRelationQueryArg queryArg = new EnterpriseRelationQueryArg();
        queryArg.setDestOuterTenantIds(Collections.singletonList(String.valueOf(erDepartmentObj.getOuterTenantId())));
        queryArg.setIgnoreBaseVisibleRange(true);
        queryArg.setOrgManageTypes(orgManageTypes);
        queryArg.setHasFsAccount(true);
        queryArg.setIgnoreSelfFilter(true);
        queryArg.setSelectFields(Lists.newArrayList(EnterpriseRelationObjConstant.ID, EnterpriseRelationObjConstant.ENTERPRISE_ACCOUNT, EnterpriseRelationObjConstant.DOWNSTREAM_TENANT_ID, EnterpriseRelationObjConstant.NAME));

        QueryResult<IObjectData> dataQueryResult = enterpriseRelationDao.getQueryResult(upstreamUser, queryArg);
        if (CollectionUtils.isEmpty(dataQueryResult.getData())) {
            return Result.newError(ResultCode.NOT_FOUND);
        }

        EnterpriseRelationObj enterpriseRelationObj = EnterpriseRelationObj.newInstance(dataQueryResult.getData().get(0));
        String downstreamTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(enterpriseRelationObj.getEnterpriseAccount()));

        EnterpriseRelationQueryArg enterpriseRelationQueryArg = new EnterpriseRelationQueryArg();
        enterpriseRelationQueryArg.setHasFsAccount(true);
        enterpriseRelationQueryArg.setOrgManageTypes(orgManageTypes);
        enterpriseRelationQueryArg.setLimit(arg.getLimit());
        enterpriseRelationQueryArg.setOffset(arg.getOffset());
        enterpriseRelationQueryArg.setIgnoreBaseVisibleRange(true);
        if (ObjectUtils.isNotEmpty(arg.getOuterTenantIds())) {
            enterpriseRelationQueryArg.setDestOuterTenantIds(ListUtil.toStringList(arg.getOuterTenantIds()));
        }


        if (Objects.equals(arg.getQueryType(), QueryTypeEnum.ER_ENTERPRISE.getType())) {
            enterpriseRelationQueryArg.setNotInDTenantIds(getNotInDTenantIds(downstreamTenantId, enterpriseRelationObj.getId()));
        } else if (Objects.equals(arg.getQueryType(), QueryTypeEnum.UPSTREAM_ENTERPRISE.getType())) {
            List<String> downstreamTenantIds = enterpriseRelationObj.getDownstreamTenantIds();
            if (ObjectUtils.isEmpty(downstreamTenantIds)) {
                downstreamTenantIds = Lists.newArrayList(upstreamTenantId);
            }
            List<Integer> visibleTenantIds = downstreamTenantIds.stream().filter(StringUtils::isNumeric).map(Integer::valueOf).collect(Collectors.toList());
            Map<Integer, String> tenantId2Ea = eieaConverter.enterpriseIdToAccount(visibleTenantIds);
            enterpriseRelationQueryArg.setExcludeEnterpriseAccounts(tenantId2Ea.values());
        }

        if (ObjectUtils.isNotEmpty(arg.getSearchText())) {
            enterpriseRelationQueryArg.setName(arg.getSearchText());
        }
        QueryResult<IObjectData> result = enterpriseRelationDao.getQueryResult(User.systemUser(downstreamTenantId), enterpriseRelationQueryArg);

        if (result.getTotalNumber() <= 0) {
            listConnectEnterpriseResult.setDataList(new ArrayList<>());
            return Result.newSuccess(listConnectEnterpriseResult);
        }

        List<EnterpriseRelationObj> enterpriseRelationObjList = EnterpriseRelationObj.newInstanceList(result.getData());

        List<Long> outerTenantIds = enterpriseRelationObjList.stream().map(EnterpriseRelationObj::getOuterTenantId).collect(Collectors.toList());
        Map<Long, String> erDepartmentIdMap = getErDepartmentIdMap(upstreamTenantId, outerTenantIds);

        List<ListIsolationSettingData> connectEnterpriseDataList = new ArrayList<>();
        for (EnterpriseRelationObj enterpriseRelationData : enterpriseRelationObjList) {
            ListIsolationSettingData isolationSettingData = new ListIsolationSettingData();

            isolationSettingData.setEnterpriseRelationName(enterpriseRelationData.getName());
            isolationSettingData.setUpstreamEnterpriseName(enterpriseRelationObj.getName());
            isolationSettingData.setErDepartmentId(erDepartmentIdMap.get(enterpriseRelationData.getOuterTenantId()));
            PublicEmployeeObj publicEmployeeObj = publicEmployeeDao.getRelationOwnerIgnorePublicObjectFilter(upstreamUser, enterpriseRelationData.getOuterTenantId());
            if (publicEmployeeObj != null) {
                isolationSettingData.setRelationOwnerName(publicEmployeeObj.getName());
            }
            connectEnterpriseDataList.add(isolationSettingData);
        }

        listConnectEnterpriseResult.setDataList(connectEnterpriseDataList);
        return Result.newSuccess(listConnectEnterpriseResult);
    }

    private List<String> getNotInDTenantIds(String tenantId, String outTenantId) {
        List<String> notInDTenantIds = new ArrayList<>();
        // 不是全部可见
        notInDTenantIds.add(IObjectData.PUBLIC_DATA_DOWNSTREAM_TENANT_ID);
        // 不包含当前企业
        notInDTenantIds.add(tenantId);
        // 当前企业所在的企业组也不包含
        List<String> groupIds = orgTenantGroupMapper.setTenantId(tenantId).findGroupIdByOutTenantId(tenantId, outTenantId);
        if (CollectionUtils.isNotEmpty(groupIds)) {
            notInDTenantIds.addAll(groupIds.stream().map(it -> "tg_" + it).collect(Collectors.toList()));
        }

        return notInDTenantIds;
    }

    private List<String> getNotInDTenantIds(String upstreamTenantId, List<String> tenantIds, List<String> outTenantIds) {
        List<String> notInDTenantIds = new ArrayList<>();

        // 不是全部可见
        notInDTenantIds.add(IObjectData.PUBLIC_DATA_DOWNSTREAM_TENANT_ID);
        // 不包含当前企业
        notInDTenantIds.addAll(tenantIds);

        // 当前企业所在的企业组也不包含
        for (String outTenantId : outTenantIds) {
            List<String> groupIds = orgTenantGroupMapper.setTenantId(upstreamTenantId).findGroupIdByOutTenantId(upstreamTenantId, outTenantId);
            if (CollectionUtils.isNotEmpty(groupIds)) {
                notInDTenantIds.addAll(groupIds.stream().map(it -> "tg_" + it).collect(Collectors.toList()));
            }
        }

        return notInDTenantIds;
    }


    @Override
    @MetadataTransactional
    public Result<Void> addIsolationSetting(RequestContext requestContext, String upstreamTenantId, AddIsolationSettingArg arg) {
        if (ObjectUtils.isEmpty(arg.getSourceDepartmentId()) || ObjectUtils.isEmpty(arg.getTargetDepartmentIds())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        GroupControlInfo groupItControlInfo = getGroupItControlInfo(upstreamTenantId);
        if (!groupItControlInfo.isEnable()) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        OrganizationalModel organizationalModel = OrganizationalModel.of(groupItControlInfo.getOrganizationalModel());
        List<String> departmentIds = Lists.newArrayList(arg.getSourceDepartmentId());
        departmentIds.addAll(arg.getTargetDepartmentIds());

        if (arg.getTargetDepartmentIds().stream().anyMatch(it -> Objects.equals(it, arg.getSourceDepartmentId()))) {
            return Result.newError(ResultCode.CONNECTION_ISOLATED_PARAM_ERROR);
        }
        List<ErDepartmentObj> erDepartmentObjList = erDepartmentDao.getByIds(requestContext.getUser(), departmentIds, Lists.newArrayList(ErDepartmentObjConstant.OUTER_TENANT_ID, ErDepartmentObjConstant.DEPARTMENT_TYPE, ErDepartmentObjConstant.ID, ErDepartmentObjConstant.DOWNSTREAM_TENANT_ID));
        if (ObjectUtils.isEmpty(erDepartmentObjList) || erDepartmentObjList.stream().anyMatch(it -> !ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType().equals(it.getDepartmentType()))) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }


        if (Objects.equals(arg.getQueryType(), QueryTypeEnum.ER_ENTERPRISE.getType())) {
            return addIsolationSettingForDownstream(requestContext, upstreamTenantId, organizationalModel, erDepartmentObjList, arg);
        }

        if (Objects.equals(arg.getQueryType(), QueryTypeEnum.UPSTREAM_ENTERPRISE.getType())) {
            return addIsolationSettingForUpstream(requestContext, upstreamTenantId, organizationalModel, erDepartmentObjList, arg);
        }


        return Result.newError(ResultCode.PARAMS_ERROR);
    }

    private Result<Void> addIsolationSettingForUpstream(RequestContext requestContext, String upstreamTenantId, OrganizationalModel organizationalModel, List<ErDepartmentObj> erDepartmentObjList, AddIsolationSettingArg arg) {
        ErDepartmentObj sourceDepartment = null;
        List<Long> outerTenantIds = new ArrayList<>();
        for (ErDepartmentObj erDepartmentObj : erDepartmentObjList) {
            outerTenantIds.add(erDepartmentObj.getOuterTenantId());
            if (Objects.equals(erDepartmentObj.getId(), arg.getSourceDepartmentId())) {
                sourceDepartment = erDepartmentObj;
                continue;
            }
        }

        if (ObjectUtils.isEmpty(sourceDepartment)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        List<EnterpriseRelationObj> enterpriseRelationObjs = enterpriseRelationDao.getByIdsIncludeSelf(requestContext.getUser(), outerTenantIds);
        if (ObjectUtils.isEmpty(enterpriseRelationObjs)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        List<String> removeEas = new ArrayList<>();
        EnterpriseRelationObj updateEnterpriseRelation = null;
        for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationObjs) {
            if (Objects.equals(enterpriseRelationObj.getOuterTenantId(), sourceDepartment.getOuterTenantId())) {
                updateEnterpriseRelation = enterpriseRelationObj;
                continue;
            }
            removeEas.add(enterpriseRelationObj.getEnterpriseAccount());
        }

        if (ObjectUtils.isEmpty(updateEnterpriseRelation) || ObjectUtils.isEmpty(removeEas)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        Map<String, Integer> ea2ei = eieaConverter.enterpriseAccountToId(removeEas);
        List<String> removeTenantIds = ea2ei.values().stream().map(String::valueOf).collect(Collectors.toList());
        if (removeTenantIds.contains(upstreamTenantId)) {
            return Result.newError(ResultCode.ISOLATED_TENANT_ERROR);
        }

        List<String> enterpriseRelationDownstreamTenantIds = updateEnterpriseRelation.getDownstreamTenantIds();
        if (ObjectUtils.isEmpty(enterpriseRelationDownstreamTenantIds)) {
            enterpriseRelationDownstreamTenantIds = Lists.newArrayList(upstreamTenantId);
        }
        enterpriseRelationDownstreamTenantIds.removeAll(removeTenantIds);

        IObjectData enterpriseRelationObjectData = buildUpdateObjectDataForData(updateEnterpriseRelation, requestContext.getTenantId(), EnterpriseRelationObjConstant.API_NAME);
        enterpriseRelationObjectData.set(IObjectData.DOWNSTREAM_TENANT_ID, enterpriseRelationDownstreamTenantIds);
        serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), Lists.newArrayList(enterpriseRelationObjectData), Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));

        List<IObjectData> updatePublicEmployee = new ArrayList<>();
        List<PublicEmployeeObj> publicEmployeeObjs = batchGetPublicEmployeeByOuterTenantIds(requestContext.getTenantId(), Lists.newArrayList(updateEnterpriseRelation.getOuterTenantId()), Lists.newArrayList(PublicEmployeeObjConstant.ID, PublicEmployeeObjConstant.DOWNSTREAM_TENANT_ID));
        for (PublicEmployeeObj publicEmployeeObj : publicEmployeeObjs) {
            List<String> downstreamTenantIds = publicEmployeeObj.getDownstreamTenantIds();
            if (ObjectUtils.isEmpty(downstreamTenantIds)) {
                downstreamTenantIds = Lists.newArrayList(upstreamTenantId);
            }
            downstreamTenantIds.removeAll(removeTenantIds);
            IObjectData data = buildUpdateObjectDataForData(updateEnterpriseRelation, requestContext.getTenantId(), PublicEmployeeObjConstant.API_NAME);
            data.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds);
            updatePublicEmployee.add(data);
        }
        serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), updatePublicEmployee, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));

        //如果是品牌模式，部门的可见范围和企业保持一致
        if (organizationalModel == OrganizationalModel.BRAND) {
            List<String> downstreamTenantIds = sourceDepartment.getDownstreamTenantIds();
            if (ObjectUtils.isEmpty(downstreamTenantIds)) {
                downstreamTenantIds = Lists.newArrayList(upstreamTenantId);
            }
            downstreamTenantIds.removeAll(removeTenantIds);
            IObjectData data = buildUpdateObjectDataForData(sourceDepartment, requestContext.getTenantId(), ErDepartmentObjConstant.API_NAME);
            data.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds);
            serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), updatePublicEmployee, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));
        }


        return Result.newSuccess();
    }

    private Result<Void> addIsolationSettingForDownstream(RequestContext requestContext, String upstreamTenantId, OrganizationalModel organizationalModel, List<ErDepartmentObj> erDepartmentObjList, AddIsolationSettingArg arg) {
        List<Long> outerTenantIds = new ArrayList<>();
        List<Long> targetOuterTenantIds = new ArrayList<>();
        List<ErDepartmentObj> updateErDepartmentList = new ArrayList<>();
        ErDepartmentObj sourceDepartment = null;
        for (ErDepartmentObj erDepartmentObj : erDepartmentObjList) {
            outerTenantIds.add(erDepartmentObj.getOuterTenantId());
            if (Objects.equals(erDepartmentObj.getId(), arg.getSourceDepartmentId())) {
                sourceDepartment = erDepartmentObj;
                continue;
            }
            updateErDepartmentList.add(erDepartmentObj);
            targetOuterTenantIds.add(erDepartmentObj.getOuterTenantId());
        }
        if (ObjectUtils.isEmpty(sourceDepartment)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        ErDepartmentObj finalSourceDepartment = sourceDepartment;
        boolean exists = metadataTransactionService.executeWithOutTransaction(() -> isIsolationSettingExist(requestContext.getTenantId(), upstreamTenantId, finalSourceDepartment, targetOuterTenantIds));
        if (exists) {
            return Result.newError(ResultCode.ISOLATED_EXISTS_ERROR);
        }


        List<EnterpriseRelationObj> enterpriseRelationObjs = enterpriseRelationDao.getByIdsIncludeSelf(requestContext.getUser(), outerTenantIds);
        if (ObjectUtils.isEmpty(enterpriseRelationObjs)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String removeEa = null;
        List<EnterpriseRelationObj> removeEnterpriseRelationObjs = new ArrayList<>();
        for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationObjs) {
            if (Objects.equals(enterpriseRelationObj.getOuterTenantId(), sourceDepartment.getOuterTenantId())) {
                removeEa = enterpriseRelationObj.getEnterpriseAccount();
                continue;
            }
            removeEnterpriseRelationObjs.add(enterpriseRelationObj);
        }

        if (ObjectUtils.isEmpty(removeEnterpriseRelationObjs) || ObjectUtils.isEmpty(removeEa)) {
            return Result.newSuccess();
        }

        String removeTenantId = eieaConverter.enterpriseAccountToId(removeEa) + "";
        if (Objects.equals(removeTenantId, upstreamTenantId)) {
            return Result.newError(ResultCode.ISOLATED_TENANT_ERROR);
        }

        List<IObjectData> updateEnterpriseRelationData = new ArrayList<>();
        for (EnterpriseRelationObj removeEnterpriseRelationObj : removeEnterpriseRelationObjs) {
            List<String> downstreamTenantIds = removeEnterpriseRelationObj.getDownstreamTenantIds();
            if (CollectionUtils.emptyIfNull(downstreamTenantIds).contains(removeTenantId)) {
                downstreamTenantIds.remove(removeTenantId);
                IObjectData data = buildUpdateObjectDataForData(removeEnterpriseRelationObj, requestContext.getTenantId(), EnterpriseRelationObjConstant.API_NAME);
                data.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds);
                updateEnterpriseRelationData.add(data);
            }
        }
        if (ObjectUtils.isNotEmpty(updateEnterpriseRelationData)) {
            serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), updateEnterpriseRelationData, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));
        }


        List<IObjectData> updateEmployeeData = new ArrayList<>();
        List<Long> updateOuterTenantId = removeEnterpriseRelationObjs.stream().map(EnterpriseRelationObj::getOuterTenantId).collect(Collectors.toList());
        List<PublicEmployeeObj> employeeObjList = batchGetPublicEmployeeByOuterTenantIds(requestContext.getTenantId(), updateOuterTenantId, Lists.newArrayList(PublicEmployeeObjConstant.ID, PublicEmployeeObjConstant.DOWNSTREAM_TENANT_ID));
        for (PublicEmployeeObj publicEmployeeObj : employeeObjList) {
            List<String> downstreamTenantIds = publicEmployeeObj.getDownstreamTenantIds();
            if (CollectionUtils.emptyIfNull(downstreamTenantIds).contains(removeTenantId)) {
                downstreamTenantIds.remove(removeTenantId);
                IObjectData data = buildUpdateObjectDataForData(publicEmployeeObj, requestContext.getTenantId(), PublicEmployeeObjConstant.API_NAME);
                data.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds);
                updateEmployeeData.add(data);
            }
        }

        if (ObjectUtils.isNotEmpty(updateEmployeeData)) {
            serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), updateEmployeeData, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));
        }

        //如果是品牌模式，部门的可见范围和企业保持一致
        if (organizationalModel == OrganizationalModel.BRAND) {
            List<IObjectData> updatedData = new ArrayList<>();
            for (ErDepartmentObj erDepartmentObj : updateErDepartmentList) {
                List<String> downstreamTenantIds = erDepartmentObj.getDownstreamTenantIds();
                if (CollectionUtils.emptyIfNull(downstreamTenantIds).contains(removeTenantId)) {
                    downstreamTenantIds.remove(removeTenantId);
                    IObjectData data = buildUpdateObjectDataForData(erDepartmentObj, requestContext.getTenantId(), ErDepartmentObjConstant.API_NAME);
                    data.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds);
                    updatedData.add(data);
                }
            }
            if (ObjectUtils.isNotEmpty(updatedData)) {
                serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), updatedData, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));
            }
        }
        return Result.newSuccess();
    }

    private boolean isIsolationSettingExist(String tenantId, String upstreamTenantId, ErDepartmentObj sourceDepartment, List<Long> targetOuterTenantIds) {
        if (ObjectUtils.isEmpty(targetOuterTenantIds)) {
            return false;
        }
        ListIsolationSettingArg arg = new ListIsolationSettingArg();
        arg.setErDepartmentId(sourceDepartment.getId());
        arg.setPageNumber(1);
        arg.setPageSize(1);
        arg.setQueryType(QueryTypeEnum.ER_ENTERPRISE.getType());
        arg.setOuterTenantIds(targetOuterTenantIds);
        Result<ListIsolationSettingResult> result = listIsolationSetting(tenantId, upstreamTenantId, arg);
        if (result.isSuccess() && ObjectUtils.isNotEmpty(result.getData()) && ObjectUtils.isNotEmpty(result.getData().getDataList())) {
            return true;
        }
        return false;
    }

    @Override
    @MetadataTransactional
    public Result<Void> removeIsolationSetting(RequestContext requestContext, String upstreamTenantId, RemoveIsolationSettingArg arg) {
        if (ObjectUtils.isEmpty(arg.getSourceDepartmentId()) || ObjectUtils.isEmpty(arg.getTargetDepartmentIds())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        GroupControlInfo groupItControlInfo = getGroupItControlInfo(upstreamTenantId);
        if (!groupItControlInfo.isEnable()) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        OrganizationalModel organizationalModel = OrganizationalModel.of(groupItControlInfo.getOrganizationalModel());
        List<String> departmentIds = Lists.newArrayList(arg.getSourceDepartmentId());
        departmentIds.addAll(arg.getTargetDepartmentIds());
        List<ErDepartmentObj> erDepartmentObjList = erDepartmentDao.getByIds(requestContext.getUser(), departmentIds, Lists.newArrayList(ErDepartmentObjConstant.OUTER_TENANT_ID, ErDepartmentObjConstant.DEPARTMENT_TYPE, ErDepartmentObjConstant.ID, ErDepartmentObjConstant.DOWNSTREAM_TENANT_ID));
        if (ObjectUtils.isEmpty(erDepartmentObjList) || erDepartmentObjList.stream().anyMatch(it -> !ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType().equals(it.getDepartmentType()))) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        List<Long> outerTenantIds = new ArrayList<>();
        ErDepartmentObj sourceDepartment = null;
        for (ErDepartmentObj erDepartmentObj : erDepartmentObjList) {
            outerTenantIds.add(erDepartmentObj.getOuterTenantId());
            if (Objects.equals(erDepartmentObj.getId(), arg.getSourceDepartmentId())) {
                sourceDepartment = erDepartmentObj;
            }
        }
        if (ObjectUtils.isEmpty(sourceDepartment)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        List<EnterpriseRelationObj> enterpriseRelationObjs = enterpriseRelationDao.getByIdsIncludeSelf(requestContext.getUser(), outerTenantIds);
        if (ObjectUtils.isEmpty(enterpriseRelationObjs)) {
            return Result.newSuccess();
        }

        if (Objects.equals(arg.getQueryType(), QueryTypeEnum.ER_ENTERPRISE.getType())) {
            return removeDownstreamIsolationSetting(requestContext, upstreamTenantId, enterpriseRelationObjs, sourceDepartment, organizationalModel);
        }
        if (Objects.equals(arg.getQueryType(), QueryTypeEnum.UPSTREAM_ENTERPRISE.getType())) {
            return removeUpstreamIsolationSetting(requestContext, upstreamTenantId, enterpriseRelationObjs, sourceDepartment, organizationalModel);
        }

        return Result.newError(ResultCode.PARAMS_ERROR);
    }

    private Result<Void> removeUpstreamIsolationSetting(RequestContext requestContext, String upstreamTenantId, List<EnterpriseRelationObj> enterpriseRelationObjs, ErDepartmentObj sourceDepartment, OrganizationalModel organizationalModel) {
        EnterpriseRelationObj updateEnterpriseRelationObj = null;
        List<String> appendEas = new ArrayList<>();
        for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationObjs) {
            if (Objects.equals(enterpriseRelationObj.getOuterTenantId(), sourceDepartment.getOuterTenantId())) {
                updateEnterpriseRelationObj = enterpriseRelationObj;
                continue;
            }
            appendEas.add(enterpriseRelationObj.getEnterpriseAccount());
        }

        if (ObjectUtils.isEmpty(updateEnterpriseRelationObj) || ObjectUtils.isEmpty(appendEas)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        Map<String, Integer> ea2ei = eieaConverter.enterpriseAccountToId(appendEas);
        List<String> appendTenantIds = ea2ei.values().stream().map(String::valueOf).collect(Collectors.toList());

        //互联企业，可见范围增加对应企业
        List<String> enterpriseRelationDownstreamTenantIds = updateEnterpriseRelationObj.getDownstreamTenantIds();
        if (ObjectUtils.isEmpty(enterpriseRelationDownstreamTenantIds)) {
            enterpriseRelationDownstreamTenantIds = Lists.newArrayList(upstreamTenantId);
        }
        enterpriseRelationDownstreamTenantIds.addAll(appendTenantIds);
        IObjectData updateEnterpriseRelationObjectData = buildUpdateObjectDataForData(updateEnterpriseRelationObj, requestContext.getTenantId(), EnterpriseRelationObjConstant.API_NAME);
        updateEnterpriseRelationObjectData.set(IObjectData.DOWNSTREAM_TENANT_ID, enterpriseRelationDownstreamTenantIds.stream().distinct().collect(Collectors.toList()));
        serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), Lists.newArrayList(updateEnterpriseRelationObjectData), Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));


        //对应互联用户可见范围增加企业
        List<PublicEmployeeObj> employeeObjList = batchGetPublicEmployeeByOuterTenantIds(requestContext.getTenantId(), Lists.newArrayList(updateEnterpriseRelationObj.getOuterTenantId()), Lists.newArrayList(PublicEmployeeObjConstant.ID, PublicEmployeeObjConstant.DOWNSTREAM_TENANT_ID));
        List<IObjectData> updatePublicEmployeeObjDataList = new ArrayList<>();
        for (PublicEmployeeObj publicEmployeeObj : employeeObjList) {
            List<String> downstreamTenantIds = publicEmployeeObj.getDownstreamTenantIds();
            if (ObjectUtils.isEmpty(downstreamTenantIds)) {
                downstreamTenantIds = Lists.newArrayList(upstreamTenantId);
            }
            downstreamTenantIds.addAll(appendTenantIds);
            IObjectData data = buildUpdateObjectDataForData(publicEmployeeObj, requestContext.getTenantId(), PublicEmployeeObjConstant.API_NAME);
            data.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds.stream().distinct().collect(Collectors.toList()));
            updatePublicEmployeeObjDataList.add(data);
        }
        if (ObjectUtils.isNotEmpty(updatePublicEmployeeObjDataList)) {
            serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), updatePublicEmployeeObjDataList, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));
        }

        //如果是品牌模式，部门的可见范围和企业保持一致
        if (organizationalModel == OrganizationalModel.BRAND) {
            List<String> downstreamTenantIds = sourceDepartment.getDownstreamTenantIds();
            if (ObjectUtils.isEmpty(downstreamTenantIds)) {
                downstreamTenantIds = Lists.newArrayList(upstreamTenantId);
            }
            downstreamTenantIds.addAll(appendTenantIds);
            IObjectData updateErDepartmentObj = buildUpdateObjectDataForData(sourceDepartment, requestContext.getTenantId(), ErDepartmentObjConstant.API_NAME);
            updateErDepartmentObj.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds.stream().distinct().collect(Collectors.toList()));
            serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), Lists.newArrayList(updateErDepartmentObj), Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));
        }

        return Result.newSuccess();
    }

    private Result<Void> removeDownstreamIsolationSetting(RequestContext requestContext, String upstreamTenantId, List<EnterpriseRelationObj> enterpriseRelationObjs, ErDepartmentObj sourceDepartment, OrganizationalModel organizationalModel) {
        String appendEa = null;
        List<EnterpriseRelationObj> updateEnterpriseRelationObjs = new ArrayList<>();
        for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationObjs) {
            if (Objects.equals(enterpriseRelationObj.getOuterTenantId(), sourceDepartment.getOuterTenantId())) {
                appendEa = enterpriseRelationObj.getEnterpriseAccount();
                continue;
            }
            updateEnterpriseRelationObjs.add(enterpriseRelationObj);
        }
        if (ObjectUtils.isEmpty(updateEnterpriseRelationObjs) || ObjectUtils.isEmpty(appendEa)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String appendTenantId = eieaConverter.enterpriseAccountToId(appendEa) + "";
        List<IObjectData> updateEnterpriseRelationDataList = new ArrayList<>();
        for (EnterpriseRelationObj enterpriseRelationObj : updateEnterpriseRelationObjs) {
            List<String> downstreamTenantIds = enterpriseRelationObj.getDownstreamTenantIds();
            if (!CollectionUtils.emptyIfNull(downstreamTenantIds).contains(appendTenantId)) {
                if (ObjectUtils.isEmpty(downstreamTenantIds)) {
                    downstreamTenantIds = Lists.newArrayList(upstreamTenantId);
                }
                downstreamTenantIds.add(appendTenantId);
                IObjectData data = buildUpdateObjectDataForData(enterpriseRelationObj, requestContext.getTenantId(), EnterpriseRelationObjConstant.API_NAME);
                data.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds.stream().distinct().collect(Collectors.toList()));
                updateEnterpriseRelationDataList.add(data);
            }
        }
        if (ObjectUtils.isNotEmpty(updateEnterpriseRelationDataList)) {
            serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), updateEnterpriseRelationDataList, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));
        }

        List<Long> updateOuterTenantIds = updateEnterpriseRelationObjs.stream().map(EnterpriseRelationObj::getOuterTenantId).collect(Collectors.toList());
        List<PublicEmployeeObj> employeeObjList = batchGetPublicEmployeeByOuterTenantIds(requestContext.getTenantId(), updateOuterTenantIds, Lists.newArrayList(PublicEmployeeObjConstant.ID, PublicEmployeeObjConstant.DOWNSTREAM_TENANT_ID));
        List<IObjectData> updatePublicEmployeeObjDataList = new ArrayList<>();
        for (PublicEmployeeObj publicEmployeeObj : employeeObjList) {
            List<String> downstreamTenantIds = publicEmployeeObj.getDownstreamTenantIds();
            if (!CollectionUtils.emptyIfNull(downstreamTenantIds).contains(appendTenantId)) {
                if (ObjectUtils.isEmpty(downstreamTenantIds)) {
                    downstreamTenantIds = Lists.newArrayList(upstreamTenantId);
                }
                downstreamTenantIds.add(appendTenantId);
                IObjectData data = buildUpdateObjectDataForData(publicEmployeeObj, requestContext.getTenantId(), PublicEmployeeObjConstant.API_NAME);
                data.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds.stream().distinct().collect(Collectors.toList()));
                updatePublicEmployeeObjDataList.add(data);
            }
        }
        if (ObjectUtils.isNotEmpty(updatePublicEmployeeObjDataList)) {
            serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), updatePublicEmployeeObjDataList, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));
        }

        //如果是品牌模式，部门的可见范围和企业保持一致
        if (organizationalModel == OrganizationalModel.BRAND) {
            List<ErDepartmentObj> erDepartmentObjs = batchGetErDepartmentByOuterTenantId(requestContext.getTenantId(), updateOuterTenantIds, Lists.newArrayList(ErDepartmentObjConstant.ID, ErDepartmentObjConstant.DOWNSTREAM_TENANT_ID));
            List<IObjectData> updateErDepartmentObjDataList = new ArrayList<>();
            for (ErDepartmentObj erDepartmentObj : erDepartmentObjs) {
                List<String> downstreamTenantIds = erDepartmentObj.getDownstreamTenantIds();
                if (!CollectionUtils.emptyIfNull(downstreamTenantIds).contains(appendTenantId)) {
                    if (ObjectUtils.isEmpty(downstreamTenantIds)) {
                        downstreamTenantIds = Lists.newArrayList(upstreamTenantId);
                    }
                    downstreamTenantIds.add(appendTenantId);
                    IObjectData data = buildUpdateObjectDataForData(erDepartmentObj, requestContext.getTenantId(), ErDepartmentObjConstant.API_NAME);
                    data.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds.stream().distinct().collect(Collectors.toList()));
                    updateErDepartmentObjDataList.add(data);
                }
            }
            serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(requestContext.getUser()), updateErDepartmentObjDataList, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID));
        }

        return Result.newSuccess();
    }


    private Map<Long, String> getErDepartmentIdMap(String tenantId, List<Long> outTenantIds) {
        Map<Long, String> map = new HashMap<>();

        ErDepartmentQueryArg erDepartmentQueryArg = new ErDepartmentQueryArg();
        erDepartmentQueryArg.setOuterTenantIds(outTenantIds);
        erDepartmentQueryArg.setErDepTypes(Collections.singletonList(ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType()));
        erDepartmentQueryArg.setSelectFields(Lists.newArrayList(ErDepartmentObjConstant.OUTER_TENANT_ID, ErDepartmentObjConstant.ID));
        QueryResult<IObjectData> result = erDepartmentDao.getQuery(User.systemUser(tenantId), erDepartmentQueryArg);

        List<ErDepartmentObj> erDepartmentObjList = ErDepartmentObj.newObjInstance(result.getData());
        for (ErDepartmentObj erDepartmentObj : erDepartmentObjList) {
            map.put(erDepartmentObj.getOuterTenantId(), erDepartmentObj.getId());
        }

        return map;
    }

    @Override
    public Result<Void> createEnterpriseErDepartment(RequestContext requestContext, String upstreamTenantId, CreateEnterpriseErDepartmentArg arg) {
        GroupControlInfo groupItControlInfo = getGroupItControlInfo(upstreamTenantId);
        if (!groupItControlInfo.isEnable()) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        String tenantId = requestContext.getTenantId();
        List<EnterpriseRelationObj> enterpriseRelationByOuterTenantId = batchGetEnterpriseRelationByOuterTenantId(tenantId, arg.getOuterTenantIds(), Lists.newArrayList(EnterpriseRelationObjConstant.NAME, EnterpriseRelationObjConstant.ID, EnterpriseRelationObjConstant.DEST_OUTER_TENANT_ID, EnterpriseRelationObjConstant.ENTERPRISE_ACCOUNT, EnterpriseRelationObjConstant.DOWNSTREAM_TENANT_ID, EnterpriseRelationObjConstant.ORG_MANAGE_TYPE));
        if (CollectionUtils.isEmpty(enterpriseRelationByOuterTenantId)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        //验证是否重复加入组织架构树，重复添加给出提示
        for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationByOuterTenantId) {
            if (ObjectUtils.isNotEmpty(enterpriseRelationObj.getOrgManageType())) {
                return Result.newError(ResultCode.ER_ORG_EXISTS.getErrorCode(), ResultCode.ER_ORG_EXISTS.getDescription(enterpriseRelationObj.getName()));
            }
        }

        //暂不支持无租户的互联企业
        boolean hasNotFsAccountEnterprise = enterpriseRelationByOuterTenantId.stream().allMatch(it -> ObjectUtils.isEmpty(it.getEnterpriseAccount()));
        if (hasNotFsAccountEnterprise) {
            return Result.newError(ResultCode.NOT_SUPPORT_NOT_TENANT_ENTERPRISE_RELATION);
        }

        Result<Void> publicObjectEnableResult = checkErDepartmentOpenAndPublicObjectEnable(enterpriseRelationByOuterTenantId);
        if (!publicObjectEnableResult.isSuccess()) {
            return publicObjectEnableResult;
        }
        User user = requestContext.getUser();
        List<Long> outerTenantIds = enterpriseRelationByOuterTenantId.stream().map(EnterpriseRelationObj::getOuterTenantId).collect(Collectors.toList());

        //更新对应互联部门的信息
        List<PublicEmployeeObj> publicEmployeeByOuterTenantId = batchGetPublicEmployeeByOuterTenantIds(tenantId, outerTenantIds, Lists.newArrayList(PublicEmployeeObjConstant.ID, PublicEmployeeObjConstant.RELATION_OWNER, PublicEmployeeObjConstant.DOWNSTREAM_TENANT_ID, PublicEmployeeObjConstant.OUTER_TENANT_ID, PublicEmployeeObjConstant.TYPE, PublicEmployeeObjConstant.OUTER_UID, PublicEmployeeObjConstant.ORG_MANAGE_TYPE));
        Map<Long, List<Long>> outerTenantIdRelationOwnersMap = publicEmployeeByOuterTenantId.stream()
                .filter(it -> BooleanUtils.isTrue(it.getRelationOwner()))
                .filter(it -> Objects.equals(it.getType(), PublicEmployeeObjTypeOption.NORMAL.getType()))
                .collect(Collectors.groupingBy(PublicEmployeeObj::getOuterTenantId, Collectors.mapping(PublicEmployeeObj::getOuterUid, Collectors.toList())));
        Map<Long, List<Long>> outerTenantIdDataManagersMap = publicEmployeeByOuterTenantId.stream()
                .filter(it -> BooleanUtils.isTrue(it.getDataManager()))
                .filter(it -> Objects.equals(it.getType(), PublicEmployeeObjTypeOption.NORMAL.getType()))
                .collect(Collectors.groupingBy(PublicEmployeeObj::getOuterTenantId, Collectors.mapping(PublicEmployeeObj::getOuterUid, Collectors.toList())));

        List<ErDepartmentObj> departmentErDepartmentList = batchGetEnterpriseDepartmentByOuterTenantId(tenantId, outerTenantIds, Lists.newArrayList(ErDepartmentObjConstant.ID, ErDepartmentObjConstant.OUTER_TENANT_ID, ErDepartmentObjConstant.DOWNSTREAM_TENANT_ID, ErDepartmentObjConstant.ORG_MANAGE_TYPE));
        if (ObjectUtils.isEmpty(departmentErDepartmentList)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        for (ErDepartmentObj erDepartmentObj : departmentErDepartmentList) {
            Long managerOuterUid = null;
            List<Long> dataManagerIds = null;
            Long outerTenantId = erDepartmentObj.getOuterTenantId();
            if (BooleanUtils.isTrue(arg.getEnterpriseOwner2ErDepartmentOwner())) {
                List<Long> managerOuterUids = outerTenantIdRelationOwnersMap.get(outerTenantId);
                if (ObjectUtils.isNotEmpty(managerOuterUids)) {
                    managerOuterUid = managerOuterUids.get(0);
                }
                dataManagerIds = outerTenantIdDataManagersMap.get(outerTenantId);
            }
            this.editErDepManagerOrAssist(requestContext.getUser(), erDepartmentObj.getId(), managerOuterUid, dataManagerIds, arg.getParentErDepartmentId());
        }

        OrganizationalModel organizationalModel = OrganizationalModel.of(groupItControlInfo.getOrganizationalModel());
        Set<String> eas = enterpriseRelationByOuterTenantId
                .stream().map(EnterpriseRelationObj::getEnterpriseAccount)
                .filter(ObjectUtils::isNotEmpty)
                .collect(Collectors.toSet());
        //集团模式
        if (organizationalModel == OrganizationalModel.GROUP) {
            metadataTransactionService.execute(() -> {
                //如果有新增的企业，所有数据可见范围，添加新建的企业ei,当前企业的数据，添加历史的企业
                Set<String> tenantIds = eieaConverter.enterpriseAccountToId(eas).values().stream().map(String::valueOf).collect(Collectors.toSet());
                List<EnterpriseRelationObj> enterpriseRelationObjList = batchGetAllEnterpriseRelationByType(tenantId, organizationalModel.getType(), Lists.newArrayList(EnterpriseRelationObjConstant.ID, EnterpriseRelationObjConstant.ENTERPRISE_ACCOUNT, EnterpriseRelationObjConstant.DOWNSTREAM_TENANT_ID), false);
                Set<String> existsEa = CollectionUtils.emptyIfNull(enterpriseRelationObjList).stream().map(EnterpriseRelationObj::getEnterpriseAccount).filter(ObjectUtils::isNotEmpty).collect(Collectors.toSet());

                Set<String> allTenantIds = tenantIds;
                if (ObjectUtils.isNotEmpty(existsEa)) {
                    Set<String> existsTenantIds = eieaConverter.enterpriseAccountToId(existsEa).values().stream().map(String::valueOf).collect(Collectors.toSet());
                    allTenantIds = new HashSet<>(tenantIds);
                    allTenantIds.addAll(existsTenantIds);
                }
                List<IObjectData> updateEnterpriseRelation = buildAppendDownstreamTenantObject(enterpriseRelationObjList, EnterpriseRelationObjConstant.API_NAME, tenantIds, upstreamTenantId);
                List<IObjectData> updateEnterpriseRelationForArg = buildAppendDownstreamTenantObject(enterpriseRelationByOuterTenantId, EnterpriseRelationObjConstant.API_NAME, allTenantIds, upstreamTenantId);
                updateEnterpriseRelation.addAll(updateEnterpriseRelationForArg);
                if (ObjectUtils.isNotEmpty(updateEnterpriseRelation)) {
                    updateEnterpriseRelationForArg.forEach(it -> appendOrgManageModule(it, organizationalModel.getType()));
                    serviceFacade.batchUpdateWithData(User.systemUser(upstreamTenantId), updateEnterpriseRelation, Lists.newArrayList(EnterpriseRelationObjConstant.ORG_MANAGE_TYPE, EnterpriseRelationObjConstant.DOWNSTREAM_TENANT_ID, IObjectData.PUBLIC_DATA_TYPE));
                }

                List<ErDepartmentObj> erDepartmentObjList = batchGetAllErDepartmentByType(upstreamTenantId, organizationalModel.getType(), Lists.newArrayList(ErDepartmentObjConstant.ID, ErDepartmentObjConstant.DOWNSTREAM_TENANT_ID));
                List<IObjectData> updateErDepartment = buildAppendDownstreamTenantObject(erDepartmentObjList, ErDepartmentObjConstant.API_NAME, tenantIds, upstreamTenantId);
                List<IObjectData> updateErDepartmentByArg = buildAppendDownstreamTenantObject(departmentErDepartmentList, ErDepartmentObjConstant.API_NAME, allTenantIds, upstreamTenantId);
                updateErDepartment.addAll(updateErDepartmentByArg);
                if (ObjectUtils.isNotEmpty(updateErDepartment)) {
                    updateErDepartmentByArg.forEach(it -> appendOrgManageModule(it, organizationalModel.getType()));
                    serviceFacade.batchUpdateWithData(User.systemUser(upstreamTenantId), updateErDepartment, Lists.newArrayList(PublicEmployeeObjConstant.ORG_MANAGE_TYPE, PublicEmployeeObjConstant.DOWNSTREAM_TENANT_ID, IObjectData.PUBLIC_DATA_TYPE));
                }

                List<PublicEmployeeObj> publicEmployeeObjsList = batchGetAllPublicEmployeeByType(upstreamTenantId, organizationalModel.getType(), Lists.newArrayList(PublicEmployeeObjConstant.ID, PublicEmployeeObjConstant.DOWNSTREAM_TENANT_ID));
                List<IObjectData> updatePublicEmployee = buildAppendDownstreamTenantObject(publicEmployeeObjsList, PublicEmployeeObjConstant.API_NAME, tenantIds, upstreamTenantId);
                List<IObjectData> updateEmployeeByArg = buildAppendDownstreamTenantObject(publicEmployeeByOuterTenantId, PublicEmployeeObjConstant.API_NAME, allTenantIds, upstreamTenantId);
                updatePublicEmployee.addAll(updateEmployeeByArg);

                if (ObjectUtils.isNotEmpty(updatePublicEmployee)) {
                    updateEmployeeByArg.forEach(it -> appendOrgManageModule(it, organizationalModel.getType()));
                    serviceFacade.batchUpdateWithData(User.systemUser(upstreamTenantId), updatePublicEmployee, Lists.newArrayList(PublicEmployeeObjConstant.ORG_MANAGE_TYPE, EnterpriseRelationObjConstant.DOWNSTREAM_TENANT_ID, IObjectData.PUBLIC_DATA_TYPE));
                }
                return null;
            });
        }

        //品牌模式
        if (organizationalModel == OrganizationalModel.BRAND) {
            User upstreamUser = User.systemUser(upstreamTenantId);
            ErDepartmentObj parentErDepartment = erDepartmentDao.getById(upstreamUser, arg.getParentErDepartmentId());
            if (parentErDepartment == null) {
                log.warn("parent department not found {} {}", upstreamUser, arg.getParentErDepartmentId());
                throw new RelationException(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.PARAMS_ERROR.getDescription());
            }

            Map<Long, String> outerTenantId2Eas = enterpriseRelationByOuterTenantId.stream()
                    .collect(Collectors.toMap(EnterpriseRelationObj::getOuterTenantId, EnterpriseRelationObj::getEnterpriseAccount, (x, y) -> x));
            Map<String, Integer> ea2ei = eieaConverter.enterpriseAccountToId(outerTenantId2Eas.values());

            //当前节点可见范围 保证有自己
            for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationByOuterTenantId) {
                String ea = outerTenantId2Eas.getOrDefault(enterpriseRelationObj.getOuterTenantId(), "");
                Integer tenantIdInt = ea2ei.get(ea);
                List<String> appendUpstreamTenantIds = tenantIdInt != null ? Collections.singletonList(tenantIdInt.toString()) :
                        Collections.singletonList(upstreamTenantId);
                enterpriseRelationObj.set(IObjectData.DOWNSTREAM_TENANT_ID, appendUpstreamTenantIds);
            }

            for (ErDepartmentObj erDepartmentObj : departmentErDepartmentList) {
                String ea = outerTenantId2Eas.getOrDefault(erDepartmentObj.getOuterTenantId(), "");
                Integer tenantIdInt = ea2ei.get(ea);
                List<String> appendUpstreamTenantIds = tenantIdInt != null ? Collections.singletonList(tenantIdInt.toString()) :
                        Collections.singletonList(upstreamTenantId);
                erDepartmentObj.set(IObjectData.DOWNSTREAM_TENANT_ID, appendUpstreamTenantIds);
            }

            for (PublicEmployeeObj publicEmployeeObj : publicEmployeeByOuterTenantId) {
                String ea = outerTenantId2Eas.getOrDefault(publicEmployeeObj.getOuterTenantId(), "");
                Integer tenantIdInt = ea2ei.get(ea);
                List<String> appendUpstreamTenantIds = tenantIdInt != null ? Collections.singletonList(tenantIdInt.toString()) :
                        Collections.singletonList(upstreamTenantId);
                publicEmployeeObj.set(IObjectData.DOWNSTREAM_TENANT_ID, appendUpstreamTenantIds);
            }

            //添加上游的可见范围
            List<String> appendUpstreamTenantIds = Collections.singletonList(upstreamTenantId);
            List<IObjectData> updateEnterpriseRelation = buildAppendDownstreamTenantObject(enterpriseRelationByOuterTenantId, EnterpriseRelationObjConstant.API_NAME, appendUpstreamTenantIds, upstreamTenantId);
            List<IObjectData> updateErDepartment = buildAppendDownstreamTenantObject(departmentErDepartmentList, ErDepartmentObjConstant.API_NAME, appendUpstreamTenantIds, upstreamTenantId);
            List<IObjectData> updatePublicEmployee = buildAppendDownstreamTenantObject(publicEmployeeByOuterTenantId, PublicEmployeeObjConstant.API_NAME, appendUpstreamTenantIds, upstreamTenantId);

            //设置新增的互联企业+互联部门+互联用户的绑定模式
            updateEnterpriseRelation.forEach(it -> appendOrgManageModule(it, organizationalModel.getType()));
            updateErDepartment.forEach(it -> appendOrgManageModule(it, organizationalModel.getType()));
            updatePublicEmployee.forEach(it -> appendOrgManageModule(it, organizationalModel.getType()));


            //1 直接挂在根节点下面 更新当前节点的互联企业，互联用户，互联部门可见范围 增加根节点的企业
            if (parentErDepartment.isRootDepartment()) {
                return metadataTransactionService.execute(() -> {
                    serviceFacade.batchUpdateWithData(user, updateErDepartment, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID, ErDepartmentObjConstant.ORG_MANAGE_TYPE, IObjectData.PUBLIC_DATA));
                    serviceFacade.batchUpdateWithData(user, updateEnterpriseRelation, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID, EnterpriseRelationObjConstant.ORG_MANAGE_TYPE, IObjectData.PUBLIC_DATA));
                    serviceFacade.batchUpdateWithData(user, updatePublicEmployee, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID, PublicEmployeeObjConstant.ORG_MANAGE_TYPE, IObjectData.PUBLIC_DATA));
                    return Result.newSuccess();
                });
            }

            Tuple<ErDepartmentObj, ErDepartmentObj> parentDepartmentTuple = findTheEnterpriseNode(upstreamTenantId, parentErDepartment);

            ErDepartmentObj parentEnterpriseDepartment = parentDepartmentTuple.getKey();
            ErDepartmentObj grandpaDepartment = parentDepartmentTuple.getValue();
            Map<Long, String> outerTenantId2Ea = fxiaokeEnterpriseAssociationEntityDao.batchGetEaByOuterTenantId(Lists.newArrayList(parentEnterpriseDepartment.getOuterTenantId(), grandpaDepartment.getOuterTenantId()));

            //上上级节点d_tenant_id 增加上级企业的ei
            List<EnterpriseRelationObj> grandpaEnterpriseRelationObjList = batchGetEnterpriseRelationByOuterTenantId(upstreamTenantId, Lists.newArrayList(grandpaDepartment.getOuterTenantId()), Lists.newArrayList(IObjectData.ID, IObjectData.DOWNSTREAM_TENANT_ID));
            List<ErDepartmentObj> grandpaErDepartmentObjs = batchGetErDepartmentByOuterTenantId(upstreamTenantId, Lists.newArrayList(grandpaDepartment.getOuterTenantId()), Lists.newArrayList(IObjectData.ID, IObjectData.DOWNSTREAM_TENANT_ID));
            List<PublicEmployeeObj> grandpaPublicEmployeeObjs = batchGetPublicEmployeeByOuterTenantIds(upstreamTenantId, Lists.newArrayList(grandpaDepartment.getOuterTenantId()), Lists.newArrayList(IObjectData.ID, IObjectData.DOWNSTREAM_TENANT_ID));
            int parentTenantId = eieaConverter.enterpriseAccountToId(outerTenantId2Ea.get(parentEnterpriseDepartment.getOuterTenantId()));
            String parentTenantIdStr = String.valueOf(parentTenantId);

            List<String> parentTenantIdList = Lists.newArrayList(String.valueOf(parentTenantId));
            List<IObjectData> updateGrandpaEnterpriseRelation = buildAppendDownstreamTenantObject(grandpaEnterpriseRelationObjList, EnterpriseRelationObjConstant.API_NAME, parentTenantIdList, user.getTenantId());
            List<IObjectData> updateGrandpaErDepartment = buildAppendDownstreamTenantObject(grandpaErDepartmentObjs, ErDepartmentObjConstant.API_NAME, parentTenantIdList, user.getTenantId());
            List<IObjectData> updateGrandpaEmployee = buildAppendDownstreamTenantObject(grandpaPublicEmployeeObjs, PublicEmployeeObjConstant.API_NAME, parentTenantIdList, user.getTenantId());


            //当前节点增加上级节点的企业id
            appendDownstreamTenantId(parentTenantIdStr, upstreamTenantId, updateEnterpriseRelation);
            appendDownstreamTenantId(parentTenantIdStr, upstreamTenantId, updateErDepartment);
            appendDownstreamTenantId(parentTenantIdStr, upstreamTenantId, updatePublicEmployee);

            //更新数据的可见范围
            updateEnterpriseRelation.addAll(updateGrandpaEnterpriseRelation);
            updateErDepartment.addAll(updateGrandpaErDepartment);
            updatePublicEmployee.addAll(updateGrandpaEmployee);

            metadataTransactionService.execute(() -> {
                IActionContext context = ActionContextExt.of(user).getContext();
                context.put(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, true);
                context.put(ActionContextKey.IGNORE_PUBLIC_EMPLOYEE_ENTERPRISE_RELATION_DEFAULT_FILTER, true);
                serviceFacade.batchUpdateWithData(context, updateErDepartment, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID, ErDepartmentObjConstant.ORG_MANAGE_TYPE, IObjectData.PUBLIC_DATA));

                context = ActionContextExt.of(user).getContext();
                context.put(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, true);
                context.put(ActionContextKey.IGNORE_PUBLIC_EMPLOYEE_ENTERPRISE_RELATION_DEFAULT_FILTER, true);
                serviceFacade.batchUpdateWithData(context, updateEnterpriseRelation, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID, EnterpriseRelationObjConstant.ORG_MANAGE_TYPE, IObjectData.PUBLIC_DATA));

                context = ActionContextExt.of(user).getContext();
                context.put(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, true);
                context.put(ActionContextKey.IGNORE_PUBLIC_EMPLOYEE_ENTERPRISE_RELATION_DEFAULT_FILTER, true);
                serviceFacade.batchUpdateWithData(context, updatePublicEmployee, Lists.newArrayList(IObjectData.DOWNSTREAM_TENANT_ID, PublicEmployeeObjConstant.ORG_MANAGE_TYPE, IObjectData.PUBLIC_DATA));
                return null;
            });
        }


        return Result.newSuccess();
    }

    @Override
    public Result<Void> createErDepartmentByDepartmentOrOrgs(RequestContext requestContext, String upstreamTenantId, CreateErDepartmentByDepartmentOrOrgs arg) {
        GroupControlInfo groupItControlInfo = getGroupItControlInfo(upstreamTenantId);
        if (!groupItControlInfo.isEnable()) {
            return Result.newResult(ResultCode.ALREADY_START_PLEASE_REFRESH.getErrorCode(), ResultCode.ALREADY_START_PLEASE_REFRESH.getDescription());
        }
        String tenantId = requestContext.getTenantId();
        Result<Void> checkResult = checkErDepartmentOpen(tenantId, null);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        if (ObjectUtils.isEmpty(arg.getDepartmentOrgArgs())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        List<String> departmentIds = arg.getDepartmentOrgArgs().stream()
                .map(CreateErDepartmentByDepartmentOrOrgs.DepartmentOrgArg::getDepartmentId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(departmentIds)) {
            log.warn("department id is empty {}", arg);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }


        ErDepartmentObj parentErDepartmentObj = erDepartmentDao.getById(User.systemUser(tenantId), arg.getParentErDepartmentId());
        if (ObjectUtils.isEmpty(parentErDepartmentObj)) {
            log.warn("parent department is empty {} {}", tenantId, arg.getParentErDepartmentId());
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        List<ErDepartmentObj> existsErDepartmentObject = new ArrayList<>();

        Long outerTenantId = fxiaokeEnterpriseAssociationEntityDao.getOuterTenantIdByEa(requestContext.getEa());
        Map<String, ErDepartmentObj> existsErDepartmentObjectDatas = Collections.EMPTY_MAP;
        DepartmentType employeeType = DepartmentType.of(arg.getDepartmentType());
        if (employeeType == DepartmentType.INNER) {
            //根据内部部门添加互联部门，只能添加在本企业下，互联部门添加只能添加到指定的互联企业下
            if (!Objects.equals(outerTenantId, parentErDepartmentObj.getOuterTenantId())) {
                return Result.newError(ResultCode.ER_ORG_DEPARTMENT_ENTERPRISE_NOT_MATCH);
            }
        } else {
            //如果添加的是外部部门，验证添加的外部部门是否为当前的互联企业
            List<ErDepartmentObj> erDepartmentByArg = erDepartmentDao.getByIds(requestContext.getUser(), departmentIds, Lists.newArrayList(ErDepartmentObjConstant.ID, ErDepartmentObjConstant.OUTER_TENANT_ID, ErDepartmentObjConstant.NAME));
            if (ObjectUtils.isEmpty(erDepartmentByArg)) {
                return Result.newError(ResultCode.ER_ORG_DEPARTMENT_ENTERPRISE_NOT_MATCH);
            }
            for (ErDepartmentObj erDepartmentObj : erDepartmentByArg) {
                if (!Objects.equals(erDepartmentObj.getOuterTenantId(), parentErDepartmentObj.getOuterTenantId())) {
                    return Result.newError(ResultCode.ER_ORG_DEPARTMENT_ENTERPRISE_NOT_MATCH);
                }
                if (ObjectUtils.isNotEmpty(erDepartmentObj.getOrgManageType())) {
                    return Result.newError(ResultCode.ER_ORG_EXISTS.getErrorCode(), ResultCode.ER_ORG_EXISTS.getDescription(erDepartmentObj.getName()));
                }
            }
            existsErDepartmentObject = erDepartmentByArg;
        }

        List<IObjectData> innerDepartmentObj = Collections.EMPTY_LIST;
        if (employeeType == DepartmentType.INNER) {
            // 验证内部部门是否存在
            innerDepartmentObj = findInnerDepartmentInfoByIds(tenantId, departmentIds, Lists.newArrayList(IObjectData.ID, IObjectData.RECORD_TYPE, IObjectData.NAME, "dept_code", ErDepartmentObjConstant.MANAGER_ID, ErDepartmentObjConstant.ASSISTANT_IDS));
            if (ObjectUtils.isEmpty(innerDepartmentObj)) {
                return Result.newError(ResultCode.PARAMS_ERROR);
            }

            departmentIds = innerDepartmentObj.stream().map(IObjectData::getId).collect(Collectors.toList());
            ErDepartmentQueryArg queryArg = new ErDepartmentQueryArg();
            queryArg.setErDepTypes(Lists.newArrayList(ErDepartmentTypeEnum.DEPARTMENT.getType()));
            queryArg.setOuterTenantDeptIds(departmentIds);
            queryArg.setOuterTenantIds(Lists.newArrayList(outerTenantId));
            queryArg.setSelectFields(Lists.newArrayList(ErDepartmentObjConstant.ID, ErDepartmentObjConstant.NAME, ErDepartmentObjConstant.ORG_MANAGE_TYPE, ErDepartmentObjConstant.OUTER_TENANT_DEPT_ID));
            queryArg.setOffset(0);
            queryArg.setIgnoreBaseVisibleRange(true);
            queryArg.setLimit(departmentIds.size());
            QueryResult<IObjectData> queryResult = erDepartmentDao.getQuery(User.systemUser(tenantId), queryArg);
            if (ObjectUtils.isNotEmpty(queryResult.getData())) {
                existsErDepartmentObjectDatas = queryResult.getData().stream()
                        .map(ErDepartmentObj::newInstance)
                        .collect(Collectors.toMap(ErDepartmentObj::getOuterTenantDeptId, Function.identity(), (x, y) -> x));
            }

            //如果已经加入组织架构树当中，给出异常
            if (ObjectUtils.isNotEmpty(existsErDepartmentObjectDatas)) {
                StringBuilder stringBuilder = new StringBuilder();
                for (ErDepartmentObj existsErDepartmentObjectData : existsErDepartmentObjectDatas.values()) {
                    if (ObjectUtils.isEmpty(existsErDepartmentObjectData.getOrgManageType())) {
                        existsErDepartmentObject.add(existsErDepartmentObjectData);
                        continue;
                    }
                    stringBuilder.append("[");
                    stringBuilder.append(existsErDepartmentObjectData.getName());
                    stringBuilder.append("]");
                }
                if (stringBuilder.length() != 0) {
                    String errMsg = String.format(ResultCode.FS_DEPARTMENT_ER_DEPARTMENT_EXISTED.getDescription(), stringBuilder);
                    return Result.newError(ResultCode.FS_DEPARTMENT_ER_DEPARTMENT_EXISTED.getErrorCode(), errMsg);
                }
            }
        }


        OrganizationalModel organizationalModel = OrganizationalModel.of(groupItControlInfo.getOrganizationalModel());
        List<String> downstreamTenantIds = null;
        //组织类型查询所有的互联企业，建立两两互联
        if (organizationalModel == OrganizationalModel.GROUP) {
            List<EnterpriseRelationObj> enterpriseRelationObjList = batchGetAllEnterpriseRelationByType(tenantId, organizationalModel.getType(), Lists.newArrayList(EnterpriseRelationObjConstant.ID, EnterpriseRelationObjConstant.ENTERPRISE_ACCOUNT), true);
            Set<String> eas = enterpriseRelationObjList.stream().map(EnterpriseRelationObj::getEnterpriseAccount).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            Map<String, Integer> tenantIds = eieaConverter.enterpriseAccountToId(eas);
            downstreamTenantIds = tenantIds.values().stream().map(String::valueOf).collect(Collectors.toList());
        }

        //品牌模式，找到挂载的上级企业，将可见范围设置上级企业一致
        if (organizationalModel == OrganizationalModel.BRAND) {
            if (parentErDepartmentObj.isRootDepartment()) {
                List<EnterpriseRelationObj> enterpriseRelationObjList = batchGetEnterpriseRelationByOuterTenantId(upstreamTenantId, Lists.newArrayList(parentErDepartmentObj.getOuterTenantId()), Lists.newArrayList(EnterpriseRelationObjConstant.DOWNSTREAM_TENANT_ID));
                downstreamTenantIds = enterpriseRelationObjList.get(0).getDownstreamTenantIds();
            } else {
                List<String> treePathList = parentErDepartmentObj.getTreePathList();
                treePathList.remove(treePathList.size() - 1);
                List<ErDepartmentObj> departmentType = erDepartmentDao.getByIds(User.systemUser(upstreamTenantId), treePathList, Lists.newArrayList(ErDepartmentObjConstant.ID, ErDepartmentObjConstant.DEPARTMENT_TYPE, ErDepartmentObjConstant.OUTER_TENANT_ID));
                for (int i = treePathList.size() - 1; i >= 0; i--) {
                    ErDepartmentObj erDepartmentObj = departmentType.get(i);
                    if (ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType().equals(erDepartmentObj.getDepartmentType())) {
                        List<EnterpriseRelationObj> enterpriseRelationObjList = batchGetEnterpriseRelationByOuterTenantId(upstreamTenantId, Lists.newArrayList(erDepartmentObj.getOuterTenantId()), Lists.newArrayList(EnterpriseRelationObjConstant.DOWNSTREAM_TENANT_ID));
                        downstreamTenantIds = enterpriseRelationObjList.get(0).getDownstreamTenantIds();
                        break;
                    }
                }
            }
        }

        User user = requestContext.getUser();
        double levelMaxErOrder = getLevelMaxErOrder(upstreamTenantId, parentErDepartmentObj);
        if (levelMaxErOrder < 0) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        Map<String, Double> existsErOrder = new HashMap<>();
        //如果是内部部门，创建不存在的内部部门
        if (employeeType == DepartmentType.INNER) {
            String currentEa = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
            if (ObjectUtils.isNotEmpty(innerDepartmentObj)) {
                for (IObjectData deptObjectData : innerDepartmentObj) {
                    ErDepartmentObj existErDepartmentObj = existsErDepartmentObjectDatas.get(deptObjectData.getId());
                    if (existErDepartmentObj != null) {
                        levelMaxErOrder++;
                        existsErOrder.put(existErDepartmentObj.getId(), levelMaxErOrder);
                        continue;
                    }

                    List<String> managerOuterUids = null;
                    List<String> assistantOuterUids = null;
                    if (arg.isEnterpriseOwner2ErDepartmentOwner()) {
                        // 部门负责人
                        List<String> managerIds = deptObjectData.get(ErDepartmentObjConstant.MANAGER_ID, List.class);
                        Set<String> createEmployeeIds = new HashSet<>();
                        if (ObjectUtils.isNotEmpty(managerIds)) {
                            createEmployeeIds.addAll(managerIds);
                        }
                        List<String> assistantIds = deptObjectData.get(ErDepartmentObjConstant.ASSISTANT_IDS, List.class);
                        if (ObjectUtils.isNotEmpty(assistantIds)) {
                            createEmployeeIds.addAll(assistantIds);
                        }

                        List<String> employeeIds = createEmployeeIds.stream().collect(Collectors.toList());
                        List<PublicEmployeeObj> publicEmployeeObjs = doCreatePublicEmployeeByEmployeeIds(requestContext, upstreamTenantId, outerTenantId, employeeIds, organizationalModel.getType(), null);
                        Map<String, Long> employeeId2OuterUserId = publicEmployeeObjs.stream().collect(Collectors.toMap(PublicEmployeeObj::getEmployeeId, PublicEmployeeObj::getOuterUid));

                        if (ObjectUtils.isNotEmpty(managerIds)) {
                            managerOuterUids = managerIds.stream().map(employeeId2OuterUserId::get).filter(ObjectUtils::isNotEmpty).map(String::valueOf).collect(Collectors.toList());
                        }
                        if (ObjectUtils.isNotEmpty(assistantIds)) {
                            assistantOuterUids = assistantIds.stream().map(employeeId2OuterUserId::get).filter(ObjectUtils::isNotEmpty).map(String::valueOf).collect(Collectors.toList());
                        }
                    }


                    levelMaxErOrder++;

                    int erDeptType = DepartmentDto.RECORD_TYPE_ORGANIZATION.equals(deptObjectData.getRecordType()) ? ErDepartmentTypeEnum.ORGANIZATION.getType() : ErDepartmentTypeEnum.DEPARTMENT.getType();
                    ObjectDataDocument objectData = new ObjectDataDocument();
                    objectData.put(ErDepartmentObjConstant.NAME, deptObjectData.getName());
                    objectData.put(ErDepartmentObjConstant.PARENT_ID, arg.getParentErDepartmentId());
                    objectData.put(ErDepartmentObjConstant.OUTER_TENANT_ID, outerTenantId + "");
                    objectData.put(ErDepartmentObjConstant.OUTER_TENANT_FS_ACCOUNT, currentEa);
                    objectData.put(ErDepartmentObjConstant.OUTER_TENANT_DEPT_ID, deptObjectData.getId());
                    objectData.put(ErDepartmentObjConstant.DEPARTMENT_TYPE, erDeptType);
                    objectData.put(ErDepartmentObjConstant.DEPARTMENT_CODE, StringUtils.isEmpty((String) deptObjectData.get("dept_code")) ? null : deptObjectData.get("dept_code"));
                    objectData.put(ErDepartmentObjConstant.STATUS, ErDepartmentStatusEnum.NORMAL.getType());
                    objectData.put(ErDepartmentObjConstant.ER_ORDER, levelMaxErOrder);
                    objectData.put(ErDepartmentObjConstant.DOWNSTREAM_TENANT_ID, downstreamTenantIds);
                    if (!CollectionUtils.isEmpty(managerOuterUids)) {
                        objectData.put(ErDepartmentObjConstant.MANAGER_ID, String.valueOf(managerOuterUids.get(0)));
                    }
                    if (!CollectionUtils.isEmpty(assistantOuterUids)) {
                        List<String> assistantOuterUidStrs = assistantOuterUids.stream().map(String::valueOf).collect(Collectors.toList());
                        objectData.put(ErDepartmentObjConstant.ASSISTANT_IDS, assistantOuterUidStrs);
                    }
                    objectData.put(IObjectData.OWNER, Lists.newArrayList(user.getUserId()));
                    objectData.put(IObjectData.CREATED_BY, user.getUserId());
                    objectData.put(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PUBLIC_DATA);
                    objectData.put(ErDepartmentObjConstant.ORG_MANAGE_TYPE, Lists.newArrayList(organizationalModel.getType()));
                    actionCallManager.triggerAddAction(user, objectData, ErDepartmentObjConstant.API_NAME, true);
                }
            }
        }


        if (ObjectUtils.isNotEmpty(existsErDepartmentObject)) {
            List<String> updateDeptId = existsErDepartmentObject.stream().map(ErDepartmentObj::getId).collect(Collectors.toList());
            List<IObjectData> updateDepartmentData = buildUpdateObjectData(updateDeptId, tenantId, ErDepartmentObjConstant.API_NAME);
            for (IObjectData objectData : updateDepartmentData) {
                Double erOrder = existsErOrder.get(objectData.getId());
                if (erOrder != null) {
                    objectData.set(ErDepartmentObjConstant.ER_ORDER, erOrder);
                }
                objectData.set(ErDepartmentObjConstant.PARENT_ID, arg.getParentErDepartmentId());
                objectData.set(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PUBLIC_DATA);
                objectData.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds);
                objectData.set(ErDepartmentObjConstant.ORG_MANAGE_TYPE, Lists.newArrayList(organizationalModel.getType()));
            }

            List<PublicEmployeeObj> publicEmployeeObjList = batchGetPublicEmployeeByDepartmentIds(tenantId, departmentIds, Lists.newArrayList(PublicEmployeeObjConstant.ID));
            List<IObjectData> updatePublicEmployee = buildUpdateObjectDataForData(publicEmployeeObjList, tenantId, ErDepartmentObjConstant.API_NAME);
            for (IObjectData objectData : updatePublicEmployee) {
                objectData.set(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PUBLIC_DATA);
                objectData.set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds);
                objectData.set(ErDepartmentObjConstant.ORG_MANAGE_TYPE, Lists.newArrayList(organizationalModel.getType()));
            }


            metadataTransactionService.execute(() -> {
                serviceFacade.batchUpdateWithData(user, updateDepartmentData, Lists.newArrayList(IObjectData.PUBLIC_DATA_TYPE, IObjectData.DOWNSTREAM_TENANT_ID, ErDepartmentObjConstant.ORG_MANAGE_TYPE, ErDepartmentObjConstant.PARENT_ID, ErDepartmentObjConstant.ER_ORDER));
                serviceFacade.batchUpdateWithData(user, updatePublicEmployee, Lists.newArrayList(IObjectData.PUBLIC_DATA_TYPE, IObjectData.DOWNSTREAM_TENANT_ID, PublicEmployeeObjConstant.ORG_MANAGE_TYPE));
                return null;
            });
            treePathMetadataService.saveTreePath(user, ErDepartmentObjConstant.API_NAME, Lists.newArrayList(updateDepartmentData));
        }


        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteErDepartment(RequestContext requestContext, String upstreamTenantId, DeleteErDepartmentArg arg) {
        if (ObjectUtils.isEmpty(arg.getErDepartmentId())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        String tenantId = requestContext.getTenantId();
        List<ErDepartmentObj> erDepartmentObjs = erDepartmentDao.getByIds(User.systemUser(tenantId), Lists.newArrayList(arg.getErDepartmentId()), Lists.newArrayList(ErDepartmentObjConstant.ID, ErDepartmentObjConstant.ORG_MANAGE_TYPE, ErDepartmentObj.CREATE_ENTERPRISE, ErDepartmentObjConstant.OUTER_TENANT_ID, ErDepartmentObjConstant.DEPARTMENT_TYPE));
        if (ObjectUtils.isEmpty(erDepartmentObjs)) {
            log.warn("not found er department {} {}", tenantId, arg.getErDepartmentId());
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        GroupControlInfo groupItControlInfo = getGroupItControlInfo(upstreamTenantId);
        if (!groupItControlInfo.isEnable()) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        OrganizationalModel organizationalModel = OrganizationalModel.of(groupItControlInfo.getOrganizationalModel());
        ErDepartmentQueryArg queryArg = new ErDepartmentQueryArg();
        queryArg.setLimit(1);
        queryArg.setOffset(0);
        queryArg.setParentIds(Lists.newArrayList(arg.getErDepartmentId()));
        queryArg.setSelectFields(Lists.newArrayList(ErDepartmentObjConstant.ID));
        queryArg.setOrgManageTypes(Lists.newArrayList(organizationalModel.getType()));
        QueryResult<IObjectData> child = erDepartmentDao.getQuery(User.systemUser(upstreamTenantId), queryArg);
        if (ObjectUtils.isNotEmpty(child.getData())) {
            log.info("{} {} has child not support delete", tenantId, arg.getErDepartmentId());
            return Result.newError(ResultCode.ER_DEPARETMENT_HAS_LOWER_LEVEL_CAN_NOT_BE_DELETE);
        }

        ErDepartmentObj deleteDepartment = erDepartmentObjs.get(0);
        String createTenantId = ObjectUtils.isEmpty(deleteDepartment.getCreateEnterprise()) ? upstreamTenantId : deleteDepartment.getCreateEnterprise();
        //如果删除的是企业，找到所有数据的可见范围包含当前企业的，进行去除
        if (ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType().equals(deleteDepartment.getDepartmentType())) {
            List<EnterpriseRelationObj> enterpriseRelationObjs = batchGetEnterpriseRelationByOuterTenantId(requestContext.getTenantId(), Lists.newArrayList(deleteDepartment.getOuterTenantId()), Lists.newArrayList(IObjectData.ID, EnterpriseRelationObjConstant.ENTERPRISE_ACCOUNT));
            String ea = enterpriseRelationObjs.get(0).getEnterpriseAccount();
            String deleteTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(ea));

            List<IObjectData> updateEnterpriseRelation = new ArrayList<>();
            List<EnterpriseRelationObj> enterpriseRelationObjList = batchGetAllEnterpriseRelationByType(upstreamTenantId, organizationalModel.getType(), Lists.newArrayList(IObjectData.ID, IObjectData.DOWNSTREAM_TENANT_ID, EnterpriseRelationObjConstant.DEST_OUTER_TENANT_ID), false);
            if (ObjectUtils.isNotEmpty(enterpriseRelationObjList)) {
                //1 如果是当前数据，变成私有数据
                for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationObjList) {
                    if (Objects.equals(enterpriseRelationObj.getOuterTenantId(), deleteDepartment.getOuterTenantId())) {
                        List<IObjectData> resetEnterpriseRelation = batchResetPublicObject(Lists.newArrayList(enterpriseRelationObj), EnterpriseRelationObjConstant.API_NAME, createTenantId);
                        updateEnterpriseRelation.addAll(resetEnterpriseRelation);
                        continue;
                    }

                    //如果可见范围内包含当前企业，修改可见范围去除当前企业
                    List<String> downstreamTenantIds = enterpriseRelationObj.getDownstreamTenantIds();
                    if (ObjectUtils.isNotEmpty(downstreamTenantIds) && downstreamTenantIds.remove(deleteTenantId)) {
                        List<IObjectData> updateData = buildUpdateObjectDataForData(Lists.newArrayList(enterpriseRelationObj), upstreamTenantId, EnterpriseRelationObjConstant.API_NAME);
                        updateData.get(0).set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds);
                        updateEnterpriseRelation.addAll(updateData);
                    }
                }
            }


            List<IObjectData> updatePublicEmployee = new ArrayList<>();
            List<PublicEmployeeObj> publicEmployeeObjs = batchGetAllPublicEmployeeByType(upstreamTenantId, organizationalModel.getType(), Lists.newArrayList(IObjectData.ID, PublicEmployeeObjConstant.OUTER_TENANT_ID, IObjectData.DOWNSTREAM_TENANT_ID));
            if (ObjectUtils.isNotEmpty(publicEmployeeObjs)) {
                for (PublicEmployeeObj publicEmployeeObj : publicEmployeeObjs) {
                    //如果是当前企业的数据，设置为私有数据
                    if (Objects.equals(publicEmployeeObj.getOuterTenantId(), deleteDepartment.getOuterTenantId())) {
                        List<IObjectData> resetPublicObject = batchResetPublicObject(Lists.newArrayList(publicEmployeeObj), PublicEmployeeObjConstant.API_NAME, createTenantId);
                        updatePublicEmployee.addAll(resetPublicObject);
                        continue;
                    }

                    //如果可见范围内包含当前企业，修改可见范围去除当前企业
                    List<String> downstreamTenantIds = publicEmployeeObj.getDownstreamTenantIds();
                    if (ObjectUtils.isNotEmpty(downstreamTenantIds) && downstreamTenantIds.remove(deleteTenantId)) {
                        List<IObjectData> updateData = buildUpdateObjectDataForData(Lists.newArrayList(publicEmployeeObj), upstreamTenantId, PublicEmployeeObjConstant.API_NAME);
                        updateData.get(0).set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds);
                        updatePublicEmployee.addAll(updateData);
                    }
                }
            }

            List<IObjectData> updateErDepartment = new ArrayList<>();
            List<ErDepartmentObj> allErDepartment = batchGetAllErDepartmentByType(upstreamTenantId, organizationalModel.getType(), Lists.newArrayList(IObjectData.ID, IObjectData.DOWNSTREAM_TENANT_ID, ErDepartmentObjConstant.OUTER_TENANT_ID));
            if (ObjectUtils.isNotEmpty(allErDepartment)) {
                for (ErDepartmentObj erDepartmentObj : allErDepartment) {
                    if (Objects.equals(erDepartmentObj.getId(), deleteDepartment.getId())) {
                        List<IObjectData> updateData = batchResetPublicObject(Lists.newArrayList(erDepartmentObj), ErDepartmentObjConstant.API_NAME, createTenantId);
                        updateErDepartment.addAll(updateData);
                        continue;
                    }

                    List<String> downstreamTenantIds = erDepartmentObj.getDownstreamTenantIds();
                    if (ObjectUtils.isNotEmpty(downstreamTenantIds) && downstreamTenantIds.remove(deleteTenantId)) {
                        List<IObjectData> updateData = buildUpdateObjectDataForData(Lists.newArrayList(erDepartmentObj), upstreamTenantId, ErDepartmentObjConstant.API_NAME);
                        updateData.get(0).set(IObjectData.DOWNSTREAM_TENANT_ID, downstreamTenantIds);
                        updateErDepartment.addAll(updateData);
                    }
                }
            }

            User user = User.systemUser(tenantId);
            //将互联企业+互联部门+互联用户的 可见范围去掉当前企业
            metadataTransactionService.execute(() -> {
                if (ObjectUtils.isNotEmpty(updateEnterpriseRelation)) {
                    IActionContext context = ActionContextExt.of(user).getContext();
                    context.put(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, true);
                    context.put(ActionContextKey.IGNORE_PUBLIC_EMPLOYEE_ENTERPRISE_RELATION_DEFAULT_FILTER, true);
                    serviceFacade.batchUpdateWithData(context, updateEnterpriseRelation, Lists.newArrayList(Lists.newArrayList(IObjectData.PUBLIC_DATA_TYPE, IObjectData.DOWNSTREAM_TENANT_ID, ErDepartmentObjConstant.ORG_MANAGE_TYPE)));
                }
                if (ObjectUtils.isNotEmpty(updatePublicEmployee)) {
                    IActionContext context = ActionContextExt.of(user).getContext();
                    context.put(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, true);
                    context.put(ActionContextKey.IGNORE_PUBLIC_EMPLOYEE_ENTERPRISE_RELATION_DEFAULT_FILTER, true);
                    serviceFacade.batchUpdateWithData(context, updatePublicEmployee, Lists.newArrayList(Lists.newArrayList(IObjectData.PUBLIC_DATA_TYPE, IObjectData.DOWNSTREAM_TENANT_ID, ErDepartmentObjConstant.ORG_MANAGE_TYPE)));
                }
                if (ObjectUtils.isNotEmpty(updateErDepartment)) {
                    IActionContext context = ActionContextExt.of(user).getContext();
                    context.put(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, true);
                    context.put(ActionContextKey.IGNORE_PUBLIC_EMPLOYEE_ENTERPRISE_RELATION_DEFAULT_FILTER, true);
                    serviceFacade.batchUpdateWithData(context, updateErDepartment, Lists.newArrayList(Lists.newArrayList(IObjectData.PUBLIC_DATA_TYPE, IObjectData.DOWNSTREAM_TENANT_ID, ErDepartmentObjConstant.ORG_MANAGE_TYPE)));
                }
                return null;
            });

            return Result.newSuccess();
        }

        //删除部门，修改部门+人员的 数据变成创建企业的私有数据
        metadataTransactionService.execute(() -> {
            User user = Objects.equals(requestContext.getUser().getTenantId(), createTenantId) ? requestContext.getUser() : User.systemUser(createTenantId);
            List<PublicEmployeeObj> publicEmployeeObjs = batchGetPublicEmployeeByDepartmentIds(tenantId, Lists.newArrayList(arg.getErDepartmentId()), Lists.newArrayList(PublicEmployeeObjConstant.ID));
            List<IObjectData> updatePublicEmployee = batchResetPublicObject(publicEmployeeObjs, PublicEmployeeObjConstant.API_NAME, createTenantId);
            serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(user), updatePublicEmployee, Lists.newArrayList(IObjectData.PUBLIC_DATA_TYPE, IObjectData.DOWNSTREAM_TENANT_ID, PublicEmployeeObjConstant.ORG_MANAGE_TYPE));

            List<IObjectData> updateDepartmentIds = batchResetPublicObject(Lists.newArrayList(deleteDepartment), ErDepartmentObjConstant.API_NAME, createTenantId);
            serviceFacade.batchUpdateByFields(getIgnoreBaseRangeContext(user), updateDepartmentIds, Lists.newArrayList(IObjectData.PUBLIC_DATA_TYPE, IObjectData.DOWNSTREAM_TENANT_ID, ErDepartmentObjConstant.ORG_MANAGE_TYPE));
            return null;
        });

        return Result.newSuccess();
    }


    private List<IObjectData> buildAppendDownstreamTenantObject(List<? extends IObjectData> dataList, String describeApiName, Collection<String> appendTenantIds, String tenantId) {
        if (ObjectUtils.isEmpty(dataList) || ObjectUtils.isEmpty(appendTenantIds)) {
            return new ArrayList<>();
        }

        List<IObjectData> result = new ArrayList<>();
        for (IObjectData data : dataList) {
            List<String> downstreamTenantId = data.get(IObjectData.DOWNSTREAM_TENANT_ID, List.class);
            Stream<String> beforeStream = CollectionUtils.emptyIfNull(downstreamTenantId).stream();
            List<String> updateValue = Stream.concat(beforeStream, appendTenantIds.stream()).distinct().collect(Collectors.toList());
            IObjectData current = new ObjectData();
            current.setId(data.getId());
            current.setDescribeApiName(describeApiName);
            current.set(IObjectData.DOWNSTREAM_TENANT_ID, updateValue);
            current.set(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PUBLIC_DATA);
            current.setTenantId(tenantId);
            result.add(current);
        }

        return result;
    }


    private void appendDownstreamTenantId(String tenantId, String upstreamTenantId, List<IObjectData> dataList) {
        if (ObjectUtils.isEmpty(tenantId) || ObjectUtils.isEmpty(dataList)) {
            return;
        }

        for (IObjectData data : dataList) {
            List<String> existTenantIds = data.get(IObjectData.DOWNSTREAM_TENANT_ID, List.class);
            if (existTenantIds == null) {
                existTenantIds = Lists.newArrayList(upstreamTenantId);
                data.set(IObjectData.DOWNSTREAM_TENANT_ID, existTenantIds);
            }
            if (!existTenantIds.contains(tenantId)) {
                existTenantIds.add(tenantId);
            }
        }
    }

    private Result<Void> checkErDepartmentOpenAndPublicObjectEnable(List<EnterpriseRelationObj> enterpriseRelationObjs) {
        if (ObjectUtils.isEmpty(enterpriseRelationObjs)) {
            return Result.newSuccess();
        }

        for (EnterpriseRelationObj enterpriseRelationObj : enterpriseRelationObjs) {
            if (ObjectUtils.isEmpty(enterpriseRelationObj) || StringUtils.isAllBlank(enterpriseRelationObj.getEnterpriseAccount())) {
                return Result.newSuccess();
            }

            int tenantId = eieaConverter.enterpriseAccountToId(enterpriseRelationObj.getEnterpriseAccount());
            List<String> apiNames = Lists.newArrayList(EnterpriseRelationObjConstant.API_NAME, ErDepartmentObjConstant.API_NAME, PublicEmployeeObjConstant.API_NAME);
            Map<String, IObjectDescribe> downstreamDescribes = serviceFacade.findObjectsWithoutCopy(String.valueOf(tenantId), apiNames);
            for (String apiName : apiNames) {
                IObjectDescribe describe = downstreamDescribes.get(apiName);
                if (describe == null) {
                    if (ErDepartmentObjConstant.API_NAME.equals(apiName)) {
                        return Result.newError(ResultCode.NEED_OPEN_ER_DEPARTMENT.getErrorCode(), ResultCode.NEED_OPEN_ER_DEPARTMENT.getDescription(enterpriseRelationObj.getName()));
                    }
                    //如果互联企业/互联用户不存在，下游企业没开通互联
                    return Result.newError(ResultCode.NO_QUOTA_SUPPORT);
                }
                if (!describe.isPublicObject()) {
                    return Result.newError(ResultCode.ENTERPRISE_NEED_OPEN_PUBLIC_OBJECT.getErrorCode(), ResultCode.ENTERPRISE_NEED_OPEN_PUBLIC_OBJECT.getDescription(enterpriseRelationObj.getName(), describe.getDisplayName()));
                }
            }
        }

        return Result.newSuccess();
    }


    private void appendOrgManageModule(IObjectData data, String module) {
        List<String> orgManageModule = data.get(EnterpriseRelationObjConstant.ORG_MANAGE_TYPE, List.class);
        if (ObjectUtils.isEmpty(orgManageModule)) {
            orgManageModule = new ArrayList<>();
            orgManageModule.add(module);
            data.set(EnterpriseRelationObjConstant.ORG_MANAGE_TYPE, orgManageModule);
            return;
        }
        if (orgManageModule.contains(module)) {
            return;
        }
        orgManageModule.add(module);
    }


    //查找上级的企业节点,和上上级企业类型节点
    private Tuple<ErDepartmentObj, ErDepartmentObj> findTheEnterpriseNode(String upstreamTenantId, ErDepartmentObj parentErDepartment) {
        if (parentErDepartment.isRootDepartment()) {
            return null;
        }

        List<String> treePathList = parentErDepartment.getTreePathList();
        List<ErDepartmentObj> departmentTree = erDepartmentDao.getByIds(User.systemUser(upstreamTenantId), treePathList);
        if (ObjectUtils.isEmpty(departmentTree)) {
            log.warn("not found department tree data {}", upstreamTenantId, parentErDepartment.getTreePathList());
            throw new RelationException(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.PARAMS_ERROR.getDescription());
        }

        Map<String, ErDepartmentObj> id2Department = departmentTree.stream().collect(Collectors.toMap(ErDepartmentObj::getId, Function.identity(), (x, y) -> x));
        Tuple<ErDepartmentObj, ErDepartmentObj> result = new Tuple<>();
        ErDepartmentObj parentDepartment = null;
        ErDepartmentObj grandpaDepartment = null;
        for (int i = treePathList.size() - 1; i >= 0; i--) {
            String upperDepartmentId = treePathList.get(i);
            ErDepartmentObj erDepartmentObj = id2Department.get(upperDepartmentId);
            if (erDepartmentObj == null || !ErDepartmentTypeEnum.ENTERPRISE_RELATION.getType().equals(erDepartmentObj.getDepartmentType())) {
                continue;
            }
            //找到上级和上上级的企业类型的互联节点
            if (parentDepartment == null) {
                parentDepartment = erDepartmentObj;
                continue;
            }
            if (grandpaDepartment == null && !Objects.equals(parentDepartment.getId(), erDepartmentObj.getId())) {
                grandpaDepartment = erDepartmentObj;
                break;
            }
        }

        result.setKey(parentDepartment);
        result.setValue(grandpaDepartment);
        return result;
    }


    private double getLevelMaxErOrder(String tenantId, ErDepartmentObj parentErDepartmentObj) {
        if (parentErDepartmentObj == null) {
            return -10000;
        }
        // 获取父节点下第一层节点最大order
        double erOrder = parentErDepartmentObj.getErOrder();
        ErDepartmentQueryArg queryArg = new ErDepartmentQueryArg();
        queryArg.setParentIds(Lists.newArrayList(parentErDepartmentObj.getId()));
        queryArg.setAscErOrder(false);
        queryArg.setSelectFields(Lists.newArrayList(ErDepartmentObjConstant.ID, ErDepartmentObjConstant.ER_ORDER));
        queryArg.setLimit(1);
        queryArg.setOffset(0);
        QueryResult<IObjectData> queryResult = erDepartmentDao.getQuery(User.systemUser(tenantId), queryArg);
        if (ObjectUtils.isEmpty(queryResult.getData())) {
            return erOrder;
        }
        IObjectData data = queryResult.getData().get(0);
        return ErDepartmentObj.newInstance(data).getErOrder();
    }


    private List<PublicEmployeeObj> batchCreatePublicEmployeeObjByEmployeeIdStr(int tenantId, Integer userId, Long outerTenantId, List<String> employeeIdStrs, List<String> downstreamTenantId, String orgManageType, String departmentId) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(employeeIdStrs)) {
            return Lists.newArrayList();
        }
        List<Integer> employeeIds = employeeIdStrs.stream().map(Integer::parseInt).collect(Collectors.toList());
        List<Map<String, Object>> publicEmployeeDatas = innerErDepartmentService.batchCreatePublicEmployeeObjForSelfEmployee(tenantId, userId, outerTenantId, employeeIds, downstreamTenantId, orgManageType, departmentId, true);
        return publicEmployeeDatas.stream().map(PublicEmployeeObj::newInstance).collect(Collectors.toList());
    }

    private List<IObjectData> batchResetPublicObject(List<? extends IObjectData> dataList, String describeApiName, String tenantId) {
        if (ObjectUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }

        List<IObjectData> updateObjectDataForData = buildUpdateObjectDataForData(dataList, tenantId, describeApiName);
        for (IObjectData data : updateObjectDataForData) {
            data.set(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PRIVATE_DATA);
            data.set(IObjectData.DOWNSTREAM_TENANT_ID, Lists.newArrayList(tenantId));
            data.set(EnterpriseRelationObjConstant.ORG_MANAGE_TYPE, Lists.newArrayList());
        }
        return updateObjectDataForData;
    }


    //更新数据忽略可见范围，可见范围中没有这条数据，导致底层忽略更新
    private IActionContext getIgnoreBaseRangeContext(User user) {
        IActionContext context = ActionContextExt.of(user, RequestContextManager.getContext()).getContext();
        context.put(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, true);
        return context;
    }

    @Override
    public Result<Void> removePublicEmployee(RequestContext requestContext, String upstreamTenantId, List<Long> outerUserIds, String departmentId) {
        if (ObjectUtils.isEmpty(outerUserIds) || ObjectUtils.isEmpty(departmentId)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        GroupControlInfo groupItControlInfo = getGroupItControlInfo(upstreamTenantId);
        if (!groupItControlInfo.isEnable()) {
            return Result.newSuccess();
        }
        List<String> orgManageTypes = Collections.singletonList(OrganizationalModel.of(groupItControlInfo.getOrganizationalModel()).getType());

        PublicEmployeeQueryArg publicEmployeeConditionArg = new PublicEmployeeQueryArg();
        publicEmployeeConditionArg.setOuterUids(outerUserIds);
        publicEmployeeConditionArg.setSelectFields(Lists.newArrayList(PublicEmployeeObjConstant.ID, PublicEmployeeObjConstant.OUTER_UID, PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT, PublicEmployeeObjConstant.VICE_ER_DEPARTMENTS, PublicEmployeeObjConstant.CREATE_ENTERPRISE));
        publicEmployeeConditionArg.setHasEmployee(true);
        publicEmployeeConditionArg.setOffset(0);
        publicEmployeeConditionArg.setLimit(outerUserIds.size());
        publicEmployeeConditionArg.setIgnoreSelfFilter(true);
        publicEmployeeConditionArg.setOrgManageTypes(orgManageTypes);
        List<IObjectData> objectDataList = publicEmployeeDao.listByQuery(requestContext.getUser(), publicEmployeeConditionArg);
        if (ObjectUtils.isEmpty(objectDataList)) {
            log.warn("not found public employee ");
            return Result.newSuccess();
        }

        List<PublicEmployeeObj> publicEmployeeObjList = objectDataList.stream()
                .map(PublicEmployeeObj::newInstance)
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(publicEmployeeObjList)) {
            return Result.newSuccess();
        }
        Set<String> departmentIds = new HashSet<>();
        for (PublicEmployeeObj publicEmployeeObj : publicEmployeeObjList) {
            if (ObjectUtils.isNotEmpty(publicEmployeeObj.getMainErDepartment())) {
                departmentIds.add(publicEmployeeObj.getMainErDepartment());
            }
            if (ObjectUtils.isNotEmpty(publicEmployeeObj.getViceErDepartments())) {
                departmentIds.addAll(publicEmployeeObj.getViceErDepartments());
            }
        }

        List<ErDepartmentObj> departmentObjs = erDepartmentDao.getByDataLifeStatus(requestContext.getUser(), Lists.newArrayList(departmentIds), true, false);
        Set<String> treeErDepartmentIds = departmentObjs.stream()
                .filter(it -> ObjectUtils.isNotEmpty(it.getOrgManageType()))
                .map(ErDepartmentObj::getId)
                .collect(Collectors.toSet());

        List<IObjectData> updateDataList = new ArrayList<>();
        for (PublicEmployeeObj publicEmployeeObj : publicEmployeeObjList) {
            IObjectData updateObjectData = buildUpdateObjectDataForData(publicEmployeeObj, requestContext.getTenantId(), PublicEmployeeObjConstant.API_NAME);

            boolean remove = false;
            //移除对应部门
            if (Objects.equals(publicEmployeeObj.getMainErDepartment(), departmentId)) {
                publicEmployeeObj.set(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT, null);
                updateObjectData.set(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT, null);
                remove = true;
            }
            if (CollectionUtils.emptyIfNull(publicEmployeeObj.getViceErDepartments()).contains(departmentId)) {
                List<String> viceErDepartments = publicEmployeeObj.getViceErDepartments();
                viceErDepartments.remove(departmentId);
                updateObjectData.set(PublicEmployeeObjConstant.VICE_ER_DEPARTMENTS, viceErDepartments);
                publicEmployeeObj.set(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT, viceErDepartments);
                remove = true;
            }

            //如果移除了部门，并且数据上没有部门在组织架构树当中，将互联用户在组织架构的标志识去除
            if (remove) {
                boolean notDepartmentInTree = isNotDepartmentInTree(treeErDepartmentIds, publicEmployeeObj);
                if (notDepartmentInTree) {
                    updateObjectData.set(EnterpriseRelationObjConstant.ORG_MANAGE_TYPE, null);
                    updateObjectData.set(EnterpriseRelationObjConstant.DOWNSTREAM_TENANT_ID, publicEmployeeObj.getCreateEnterpriseList());
                }
                updateDataList.add(updateObjectData);
            }
        }

        if (ObjectUtils.isNotEmpty(updateDataList)) {
            serviceFacade.batchUpdateWithData(requestContext.getUser(), updateDataList, Lists.newArrayList(PublicEmployeeObjConstant.MAIN_ER_DEPARTMENT, PublicEmployeeObjConstant.VICE_ER_DEPARTMENTS, EnterpriseRelationObjConstant.ORG_MANAGE_TYPE, EnterpriseRelationObjConstant.DOWNSTREAM_TENANT_ID));
        }

        return Result.newSuccess();
    }

    private boolean isNotDepartmentInTree(Set<String> treeErDepartmentIds, PublicEmployeeObj publicEmployeeObj) {
        String mainErDepartment = publicEmployeeObj.getMainErDepartment();
        if (ObjectUtils.isNotEmpty(mainErDepartment) && treeErDepartmentIds.contains(mainErDepartment)) {
            return false;
        }
        List<String> viceErDepartments = publicEmployeeObj.getViceErDepartments();
        boolean departmentInTree = CollectionUtils.emptyIfNull(viceErDepartments).stream().anyMatch(treeErDepartmentIds::contains);
        return !departmentInTree;
    }

}
