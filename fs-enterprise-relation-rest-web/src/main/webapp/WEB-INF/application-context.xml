<beans xmlns="http://www.springframework.org/schema/beans" xmlns:p="http://www.springframework.org/schema/p" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context" xmlns:aop="http://www.springframework.org/schema/aop"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
            http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
            http://www.springframework.org/schema/aop
            http://www.springframework.org/schema/aop/spring-aop.xsd
            http://www.springframework.org/schema/context
            http://www.springframework.org/schema/context/spring-context.xsd">
    <import resource="classpath:fs-uc-rest.xml"/>
    <import resource="classpath:enterpriserelation2/enterpriserelation-noappid.xml" />
    <bean id="springInvokeService" class="com.facishare.enterprise.common.spring.SpringInvokeService">
        <property name="okHttpSupport" ref="erOkHttpSupport"/>
    </bean>
    <context:component-scan base-package="com.facishare.er.rest.web"/>
    <aop:aspectj-autoproxy/>

    <!-- 引入 AppFramework 的配置信息 -->
    <context:component-scan base-package="com.facishare.paas.appframework"/>
    <context:annotation-config/>
    <bean id="objectResource" class="com.facishare.paas.appframework.resource.ObjectResource"/>
    <bean id="objectRestAPIResource" class="com.facishare.paas.appframework.resource.ObjectRestAPIResource"/>
    <bean id="objectInnerAPIResource" class="com.facishare.paas.appframework.resource.ObjectInnerAPIResource"/>

    <import resource="classpath:spring/er-account-all.xml"/>
    <import resource="classpath:spring/app-core.xml"/>

    <import resource="classpath:enterpriserelation-obj-common/er-remote-oldmanager.xml"/>
    <import resource="classpath:er-department-all.xml"/>
    <import resource="classpath:org-er-department-all.xml"/>

    <!-- 解析 CEP 传入的参数，包括用户身份、客户端信息等 -->
    <import resource="classpath:META-INF/fs-cep-plugin.xml"/>
    <!--jvm监控-->
    <!--service 监控-->
    <bean id="serviceProfiler" class="com.facishare.paas.appframework.common.aop.CrmServiceProfiler"/>
    <bean id="erBizDaoRemoteInvokeAspect" class="com.facishare.enterprise.common.aop.ErBizDaoRemoteInvokeAspect"/>
    <bean id="paasOrgRestServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
        p:configName="fs-stage-rest-proxy-config" init-method="init"/>
    <bean id="paasOrgResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
        p:type="com.facishare.rest.api.resource.PaasOrgResource">
        <property name="factory" ref="paasOrgRestServiceProxyFactory"/>
    </bean>
    <bean id="systemDescribeDaoAspect" class="com.facishare.er.rest.web.systemdescribeaop.SystemDescribeDaoAspect"/>

    <aop:config proxy-target-class="true">
        <aop:aspect ref="serviceProfiler">
            <aop:around method="profile" pointcut="execution(* com.facishare.paas.metadata.service..*(..))"/>
        </aop:aspect>
        <aop:aspect ref="systemDescribeDaoAspect">
            <aop:around method="findSystemDescribes" pointcut="execution(* com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl.findSystemDescribes(..))"/>
        </aop:aspect>
        <aop:aspect id="erBizDaoRemoteInvoke" ref="erBizDaoRemoteInvokeAspect" order="2">
            <aop:around pointcut="execution(* com.facishare.rest.account.old.*..*OldManager.*(..))" method="around"/>
        </aop:aspect>
    </aop:config>

</beans>