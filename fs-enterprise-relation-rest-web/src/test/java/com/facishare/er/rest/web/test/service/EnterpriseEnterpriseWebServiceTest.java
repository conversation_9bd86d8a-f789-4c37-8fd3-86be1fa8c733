package com.facishare.er.rest.web.test.service;

import com.facishare.enterprise.common.result.Result;
import com.facishare.er.api.model.vo.ListDownstreamEnterpriseRelationsWithSimpleInfoVo;
import com.facishare.er.api.model.vo.Page;
import com.facishare.er.rest.web.test.BaseTest;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.rest.account.downstream.arg.ListDownstreamEnterpriseRelationsByUpstreamEiArg;
import com.facishare.rest.account.webrest.EnterpriseEnterpriseWebService;
import com.google.common.collect.Lists;
import javax.annotation.Resource;
import org.junit.Test;

public class EnterpriseEnterpriseWebServiceTest extends BaseTest {
    @Resource
    private EnterpriseEnterpriseWebService enterpriseEnterpriseWebService;

    @Test
    public void Test() {
        ServiceContext context = newServiceContext(90235, 1000);
        ListDownstreamEnterpriseRelationsByUpstreamEiArg arg = new ListDownstreamEnterpriseRelationsByUpstreamEiArg();
        arg.setIdentityType(0);
        arg.setSourceType(0);
        arg.setHasFsAccount(false);
        arg.setUpstreamEi("90234");
        arg.setEis(Lists.newArrayList("90234", "90235", "-99999"));
        arg.setPageNumber(1);
        arg.setPageSize(3);
        Result<Page<ListDownstreamEnterpriseRelationsWithSimpleInfoVo>> result = enterpriseEnterpriseWebService.listDownstreamEnterpriseRelationsByUpstreamEi(context, arg);
        System.out.println(result);
    }
}
