package com.facishare.er.rest.web.test.objrest;

import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.er.rest.web.test.BaseTest;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.rest.account.webrest.EnterpriseEnterpriseWebService;
import com.facishare.rest.account.webrest.PublicEmployeeWebService;
import com.fxiaoke.enterpriserelation.objrest.arg.ListPublicEmployeeObjByConditionArg;
import com.fxiaoke.enterpriserelation.objrest.arg.OuterUidsArg;
import com.fxiaoke.enterpriserelation.objrest.result.ListByConditionResult;
import com.fxiaoke.enterpriserelation.objrest.result.ListDownstreamEmployeeSimpleInfoByOuterUidsResult;
import com.fxiaoke.enterpriserelation.objrest.service.PublicEmployeeObjService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.util.ArrayList;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: jiangxch
 * @date: 2021/11/2 10:52
 */
public class PublicEmployeeObjServiceTest extends BaseTest {
    @Autowired
    private EnterpriseEnterpriseWebService enterpriseEnterpriseWebService;
    @Autowired
    private PublicEmployeeWebService publicEmployeeWebService;
    @Autowired
    private PublicEmployeeObjService publicEmployeeObjService;

//
//    @Test
//    public void www() {
//        while (true) {
//            try {
//                ServiceContext serviceContext = newServiceContext(74164);
//                QueryUpstreamPublicEmployeesWithSimpleInfoArg arg = new QueryUpstreamPublicEmployeesWithSimpleInfoArg();
//                arg.setApiName(AccountFieldContants.API_NAME);
//
//                Result<List<QueryUpstreamPublicEmployeesWithSimpleInfoData>> listResult = publicEmployeeWebService.queryUpstreamPublicEmployeesWithSimpleInfo(serviceContext, arg);
//                System.out.println(listResult);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }

    @Test
    public void aaa() {
        while (true) {
            try {
                ServiceContext serviceContext = publicEmployeeObjService.createServiceContext("78989", "-10000");
                ListPublicEmployeeObjByConditionArg listPublicEmployeeObjByConditionArg = new ListPublicEmployeeObjByConditionArg();
                listPublicEmployeeObjByConditionArg.setOuterUids(new ArrayList<>());
                ListByConditionResult res = publicEmployeeObjService.listByCondition(serviceContext, listPublicEmployeeObjByConditionArg);
                System.out.println("========================");
                System.out.println(res);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    @Test
    public void testMetaDao() {
        while (true) {
            try {
                ServiceContext serviceContext = publicEmployeeObjService.createServiceContext("78989", "-10000");
                OuterUidsArg outerUidsArg = new OuterUidsArg();
                outerUidsArg.setDownstreamOuterUids(Sets.newHashSet(300101929L,300101833L));
                ListDownstreamEmployeeSimpleInfoByOuterUidsResult listDownstreamEmployeeSimpleInfoByOuterUidsResult = publicEmployeeObjService
                    .listDownstreamEmployeeSimpleInfoByOuterUids(serviceContext, outerUidsArg);
                System.out.println(listDownstreamEmployeeSimpleInfoByOuterUidsResult);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Test
    public void testListByCondition() {
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext("74164", "-10000");
        ListPublicEmployeeObjByConditionArg conditionArg = new ListPublicEmployeeObjByConditionArg();
        conditionArg.setOuterUids(Lists.newArrayList(300385649L));
        ListByConditionResult res = publicEmployeeObjService.listByCondition(serviceContext, conditionArg);
        System.out.println(JsonUtil.toJson(res));
    }
}
