package com.facishare.er.rest.web.test.outrest;

import com.facishare.enterprise.common.result.Result;
import com.facishare.er.rest.web.test.BaseTest;
import com.facishare.rest.account.outrest.PublicEmployeeObjOutController;
import com.facishare.rest.account.outrest.arg.BatchGetPublicEmployeeByIdsArg;
import com.facishare.rest.account.outrest.arg.QueryPublicEmployeesByNameArg;
import com.facishare.rest.account.outrest.arg.SearchSimplePublicEmployeeObjsByNameArg;
import com.facishare.rest.account.outrest.arg.SearchUpstreamPublicEmployeesByNameArg;
import com.facishare.rest.account.outrest.data.SimplePublicEmployeeObjData;
import com.facishare.rest.account.outrest.result.SearchSimplePublicEmployeeObjsByNameResult;
import com.facishare.rest.account.outrest.result.SearchUpstreamPublicEmployeesByNameResult;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.annotation.Resource;
import java.util.List;

public class PublicEmployeeObjOutControllerTest extends BaseTest {
    @Resource
    private PublicEmployeeObjOutController publicEmployeeObjOutController;

    @Test
    public void Test() {
        BatchGetPublicEmployeeByIdsArg arg = new BatchGetPublicEmployeeByIdsArg();
        arg.setTenantId(85154);
        Result<List<SimplePublicEmployeeObjData>> result = publicEmployeeObjOutController.getPublicEmployeeByIds(arg);
        System.out.println(result);
    }

    @Test
    public void Test2() {
        SearchSimplePublicEmployeeObjsByNameArg arg = new SearchSimplePublicEmployeeObjsByNameArg();
        arg.setTenantId(80981);
        arg.setUserId("1000");
        arg.setSearchNames(Lists.newArrayList("newname"));
        Result<SearchSimplePublicEmployeeObjsByNameResult> result = publicEmployeeObjOutController.searchSimplePublicEmployeeObjsByName(arg);
        System.out.println(result);
    }

    @Test
    public void Test3() {
        SearchUpstreamPublicEmployeesByNameArg arg = new SearchUpstreamPublicEmployeesByNameArg();
        arg.setTenantId(80981);
        arg.setUserId("1000");
        arg.setSearchNames(Lists.newArrayList("文静"));
        Result<SearchUpstreamPublicEmployeesByNameResult> result = publicEmployeeObjOutController.searchUpstreamPublicEmployeesByName(arg);
        System.out.println(result);
    }

    @Test
    public void Test4() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("x-fs-ei", "90234");
        request.addHeader("X-fs-Employee-Id", "-10000");
        QueryPublicEmployeesByNameArg arg = new QueryPublicEmployeesByNameArg();
        //arg.setOuterTenantIds(Lists.newArrayList(1000L));
        arg.setSearchNames(Lists.newArrayList("艾雪梅(235)", "鲍亮(235)"));
        Result<SearchSimplePublicEmployeeObjsByNameResult> result = publicEmployeeObjOutController.queryPublicEmployeeByNames(request, arg);
        System.out.printf("=====>" + result);
    }
}
