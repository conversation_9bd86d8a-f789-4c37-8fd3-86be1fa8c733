package com.facishare.global.rest.dao.mongo;

import com.BaseTest;
import com.facishare.global.rest.model.entity.GrayGroup;
import java.util.List;
import org.junit.After;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class GrayGroupDaoTest extends BaseTest {

    @Autowired
    private GrayGroupDao grayGroupDao;

    private String groupId;

    @After
    public void 删除数据() {
        if (groupId != null)
            delete(groupId);
    }

    @Test
    public void 测试Create() {
        GrayGroup group = new GrayGroup();
        group.setName("TestGroup");
        group.setDescribe("描述");
        group.setVersion(0);

        groupId = grayGroupDao.create(group);
        Assert.assertNotNull(groupId);

        GrayGroup createdGroup = grayGroupDao.findById(groupId);
        Assert.assertNotNull(createdGroup);
        Assert.assertEquals("TestGroup", createdGroup.getName());
        Assert.assertEquals("描述", createdGroup.getDescribe());
        Assert.assertEquals(0, createdGroup.getVersion().intValue());
    }

    @Test
    public void 测试ListAll() {
        // Assuming there are some existing groups in the database
        List<GrayGroup> groups = grayGroupDao.listAll();
        Assert.assertNotNull(groups);
        Assert.assertTrue(groups.size() > 0);
    }

    @Test
    public void 测试DeleteById() {
        GrayGroup group = new GrayGroup();
        group.setName("TestGroup");
        group.setDescribe("描述");
        group.setVersion(0);

        groupId = grayGroupDao.create(group);
        String id = groupId;
        delete(groupId);
        Assert.assertNull(grayGroupDao.findById(id));
        // You can add assertions to check if the group is deleted successfully
    }

    private void delete(Object id) {
        grayGroupDao.deleteById(id.toString());
        if (groupId != null && groupId.equals(id)) {
            groupId = null;
        }
    }

    @Test
    public void 测试Update() {
        GrayGroup group = new GrayGroup();
        group.setName("TestGroup");
        group.setDescribe("描述");
        group.setVersion(0);

        groupId = grayGroupDao.create(group);
        GrayGroup existingGroup = grayGroupDao.findById(groupId);
        Assert.assertNotNull(existingGroup);

        // Modify the group
        existingGroup.setName("UpdatedGroup");
        existingGroup.setVersion(existingGroup.getVersion() + 1); // Increment version
        grayGroupDao.update(existingGroup);

        // Fetch the updated group from the database
        GrayGroup updatedGroup = grayGroupDao.findById(groupId);
        Assert.assertNotNull(updatedGroup);
        Assert.assertEquals("UpdatedGroup", updatedGroup.getName());
        // Add more assertions based on your data model
    }
}

