package com.facishare.er.biz.outrest.test.controller;

import com.facishare.enterprise.common.constant.RelationAccountType;
import com.facishare.enterprise.common.constant.UnionAuthType;
import com.facishare.enterprise.common.model.LoginParamData;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.util.AESUtil;
import com.facishare.er.biz.outrest.arg.*;
import com.facishare.er.biz.outrest.controller.auth.ERUnionLoginController;
import com.facishare.er.biz.outrest.controller.auth.HttpAuthConfig;
import com.facishare.er.biz.outrest.controller.auth.data.LoginArg;
import com.facishare.er.biz.outrest.controller.auth.data.LoginArg.ThirdArg;
import com.facishare.er.biz.outrest.controller.auth.data.LoginResult;
import com.facishare.er.biz.outrest.controller.auth.handler.AuthHandler;
import com.facishare.er.biz.outrest.controller.auth.handler.AuthHandlerMapping;
import com.facishare.er.biz.outrest.controller.auth.handler.WXServiceLoginHandler;
import com.facishare.er.biz.outrest.controller.auth.loginTypeProvider.EmailCodeLoginTypeProvider;
import com.facishare.er.biz.outrest.data.Cookie2LoginParamResult;
import com.facishare.er.biz.outrest.data.EmployeeVo;
import com.facishare.er.biz.outrest.data.EnterpriseErLoginWelcomePageConfigVo;
import com.facishare.er.biz.outrest.data.VerifyCodeAndBindLoginAccountResult;
import com.facishare.er.biz.outrest.service.EnterpriseRelationAccountService;
import com.facishare.er.biz.outrest.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public class ERUnionLoginControllerTest extends BaseTest {
    @Autowired
    private ERUnionLoginController loginController;
    @Autowired
    private AuthHandlerMapping authHandlerMapping;
    @Autowired
    private HttpAuthConfig config;
    @Autowired
    private EnterpriseRelationAccountService enterpriseRelationAccountService;
    @Autowired
    private EmailCodeLoginTypeProvider emailCodeLoginTypeProvider;

    @Test
    public void Test() {
        String upstreamEa = "82313";
        BindERAccountArg oldBindData = new BindERAccountArg();
        oldBindData.setUpstreamEa(upstreamEa);
        oldBindData.setDownstremaOuterTenantId(300119151L);
        oldBindData.setOuterUid(300412578L);
        oldBindData.setWxAppId("wx8bd8a617c9ae66ed");
        oldBindData.setWxOpenId("ozaplwdq4Ra5PX8zafmt-ZVa1qhA");
        oldBindData.setType(RelationAccountType.ER_WE_SERVICE);

        BindERAccountArg newBindData = new BindERAccountArg();
        newBindData.setUpstreamEa(upstreamEa);
        newBindData.setDownstremaOuterTenantId(300119087L);
        newBindData.setOuterUid(300412497L);
        newBindData.setWxAppId("wx8bd8a617c9ae66ed");
        newBindData.setWxOpenId("ozaplwdq4Ra5PX8zafmt-ZVa1qhA");
        newBindData.setType(RelationAccountType.ER_WE_SERVICE);
        enterpriseRelationAccountService.upsertErBindAccount(upstreamEa, oldBindData, newBindData);
    }

    @Test
    public void loginTest() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11491084");
        loginArg.setAuthType(12);
        loginArg.setLoginType(1);
        loginArg.setLoginParam(
                "2UB2Z8Athzt5lLJMn-ZyYnXXYK-A1tdFVLmGnT8pJCCc__BTP3goN_UHSof1FWEcCBFF5JCQrQhug31kLN636t0WnSpz98wdEzQOw8sEzM5X-1X2ULGolAyxCd419boTGs981PRYmPJQIDAdoclcPslMZoWCwkb3UBz9penHqfatIJWaBkXDNfGsQK0ASuBrbVcacBMxh_oRslgZXxZwX0XSdgiHXEnuDFYgoILrhXq4tXAzRLT10z2dx5Ob7HkHZmoh1Q-Ts0n-BKGcC9trdrDB1st1B6xcXQRaQQZC4iuL6h8DINoAr4_c_P-FR1Of");
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void loginTest10() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_127a3981");
        loginArg.setUpstreamEa("74255");
        loginArg.setAuthType(10);
        loginArg.setIsGuest(false);
        loginArg.setLoginParam(
                "SwPZ0JIX6sdKTsVq4Tha5lDwaV17Y5huMWdf0IOuyda4mStKxh5uDMzZwR8DmiEwRKXP_Q2WcsLpkIysuOeLGhILr9J6kABPDFwiG2vXHxlUZXBlDr6KWX_CHqa2hi1UXcRTfiHea9876XEkn8YN6uEZrMx17DovWSr_QE14hlGxu7amDN5_HlCSKYWW8zZfIkFkw5oKEprFVdv0t3D98uRKWjgr8uIDIbFfNhr8H_loxO31fnD8YuBmsJbIaihfvtpufDl0SnVbZ3HzHmPL3pV0Tq7nPj9IXVCB2a1EKBA2v-UH-seF4yvfF43gFeKPKPr5QZqIP6cVmIthG2kkzVmXwZ6jb-ln622ewwsSxj1rFhTsGBdcSz7hBglqoStE");
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void listSwitchableEmployeesTest() {
        HttpServletRequest request = new MockHttpServletRequest();
        ListSwitchableEmployeesArg arg = new ListSwitchableEmployeesArg();
        arg.setSwitchAllUpstream(false);
        arg.setLoginParam("piUQ5I9q5lk632sE_ZDxHXmKn5ckWG7hkLq9IVgr-munOAYL729wScuLjFFY5J5tYzT_80U3A3oyJLxF0n9xnBlqaug9BeQ_zTmyF5pPMt7dcO_RfwXCS3jgsZXZNaMD");
        arg.setUpstreamEa("cjzhjuiot");
        arg.setIsNeedRoles(false);
        Result<List<EmployeeVo>> result = loginController.listSwitchableEmployees(arg, request);
        System.out.println(result);
    }

    @Test
    public void test1() {
        AuthHandler matchHandler = authHandlerMapping.findMatchHandler(UnionAuthType.WE_CHAT_SERVICE_PROGRAM.getType());
        WXServiceLoginHandler wxServiceLoginHandler = (WXServiceLoginHandler) matchHandler;

        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        LoginArg loginArg = new LoginArg();
        loginArg.setUpstreamEa("89303");
        loginArg.setFsAppId("FSAID_11491009");
        loginArg.setAuthType(UnionAuthType.WE_CHAT_SERVICE_PROGRAM.getType());
        loginArg.setMapperApiName(null);
        loginArg.setMapperObjectId(null);
        loginArg.setDownstreamOuterTenantId(null);

        LoginParamData loginParamData = new LoginParamData();
        loginParamData.setOpenId("ozSPb0eNDYzHIOAwh81C2ict9KLA");
        loginParamData.setWxAppId("wx883580c326233525");
        LoginResult loginResult = wxServiceLoginHandler.doHandleERLogin(loginArg, loginParamData, request, response);
        System.out.println(loginResult);
    }

    @Test
    public void test2() {
        VerifyCodeAndBindLoginAccountArg arg = new VerifyCodeAndBindLoginAccountArg();
        arg.setLoginAccount("***********");
        arg.setLoginParam(
                "f_qVquYP2PMJD2Yv0jHO6mTP1GF1f-9PDSuZryLN4InW-im2GobY96m9iQxtYHgtGOYKGRaO8Rx4hgTGCIlC4NMpnxqdG8L0xzUcMUN73RrE9uws3YCHYcFPIC8i9xEDQoENYuiEi39bAvsDmZ3BA-bj-0qoqJRvP21S0bOd40-c__BTP3goN_UHSof1FWEcCA7F1oQ9KL79nZJsTFG9YC7_SPLL9pE5pCN1PCNGMzZHo3Q-1lYMpvnKBPWX5QbhI8_BDEce4vGyuhWHSkBuAiWtnmiC5d3YD5utHTPO6RH9mDUVL-f9Z3vFrUE0alB4CoR96DC_IjTxS2ykpvNUVTTqISKnH4gct0hKH-bfe9wSMp9lyJq9K6AuXBvCYw6FUPLP9F7ployt-dgrkpVjv7EQ1fm2wwyTFx7n2jsTHhcvuRm3fyqwNoOVWGuvsb09");
        arg.setVerifyCode("999999");
        arg.setAuthType(4);

        Result<VerifyCodeAndBindLoginAccountResult> result = loginController.verifyCodeAndBindLoginAccount(arg, "2be78bc21fc2436280b32aae34f1d198");
        System.out.println(result);
    }

    @Test
    public void test3() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookie = new Cookie("ERInfo", "er_bfa095b0fd4a4f019439c1a90ff05fca_1104101256_82313");
        cookie.setPath("/");
        cookie.setHttpOnly(true);
        cookie.setMaxAge(60);
        request.setCookies(cookie);
        Cookie2LoginParamArg arg = new Cookie2LoginParamArg();
        Result<Cookie2LoginParamResult> result = loginController.cookie2LoginParam(arg, request);
        System.out.println(result);
    }

    @Test
    public void test4() {
        String loginParam = "Y0ZkLxncqePCOpT8L3QYJEIL_pq3Fvb-aRkwXWtUb8coLzX6XS2tNL9d-mwYPBlehCGlYzVD75zC2_5qzBC_MMRY_um1yyzKJiZHgRbQbWxVekicZ1uRH9oZhQAUuYADNBFw_Q75nfKdBYs6tpB39v_uYtzasXhO2yRlGCRDiuZk_rIFhuPinb3kLVFyYEE-sDgXXirKODS8PcT4xc_UANDXVEIBfGlDPB7lJWnI5bRY2Cn7FRX8IcIBwsZOc5sNgpPS4ZxQ9ez61j8tzsDsXpzCtj7k7iUYy-_bM5Z9ZMiYRml0phNAbVNUs07x6L7e";
        LoginParamData loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParam, LoginParamData.class);
        System.out.println("register 返回的loginParam:" + loginParamData);
        /**
         String loginParam = "oROOkyHRSLzOPQyqfqPA9T6rVMHJbyooYnOULUsSJCu4mStKxh5uDMzZwR8DmiEwdL-SLgSGkvyz81Gc1SaJdhILr9J6kABPDFwiG2vXHxkijXF4E11O2-e4p3leEtaNXcRTfiHea9876XEkn8YN6lYM3K5ZS0WtENHk5iSIopoqaKWjeGi_2GRgim7UHD0CnAbVlW14BoofbyY5cJCMAPo61SxCq1ZZd5gFOBsX7oI7hez1tbUlzC3v4REyBc4U5gZ3HS2j2Ox9u7uAHih_Wq7166awmm5E1RKsgk8bC_dxpcEpl67W-e5iwuhDp95e9WdlbRPuB-0oy39Seqa7tjvdeQoEhdodG9-Elvmvfgw";
         LoginParamData loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParam, LoginParamData.class);
         System.out.println("register 返回的loginParam:" + loginParamData);

         loginParam = "oROOkyHRSLzOPQyqfqPA9T6rVMHJbyooYnOULUsSJCu4mStKxh5uDMzZwR8DmiEwdL-SLgSGkvyz81Gc1SaJdhILr9J6kABPDFwiG2vXHxkijXF4E11O2-e4p3leEtaNXcRTfiHea9876XEkn8YN6lYM3K5ZS0WtENHk5iSIopqxu7amDN5_HlCSKYWW8zZfIkFkw5oKEprFVdv0t3D98vgrWk5VuvtrQks74YK9SDje6j4Bbj7Ph5v4OIduNNXnvtpufDl0SnVbZ3HzHmPL3pV0Tq7nPj9IXVCB2a1EKBA2v-UH-seF4yvfF43gFeKPKPr5QZqIP6cVmIthG2kkzVmXwZ6jb-ln622ewwsSxj2dZqEEmJGwyTPZfW3ngnZ3G93hxDaWbWngiSs8hI9-jfkmb-KY2YusBHTmmvxvUoh0Q87iNyXqyta6KEU6I355";
         loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParam, LoginParamData.class);
         System.out.println("register 提交的loginParam:" + loginParamData);
         /**
         String loginParam = "rh8Worg5pII557MZRjl1hy5Zvi9sG-_pbTDnqqiSCIfhxDlUWflJrU-1iQgLmu-D_s3dP5aOD14tTkKRZBNmjYrfVpX1HyhURQH4jKuV_MXHz-xPpIDvipRCfCA2v4bXpwPNPrq9XIlWI06te4fc0QXGgmk8mHFUmO2pauZM8sqySgUek-CDbZBHZXu6kPeK8wDsRhq_oxduVlhaogv3TAEaXufypJO0ZiNlMKUmr9COGssZI7BpgXZlx4hkFityHWdfDh-dC3lWilOuPBe92ZiEw_SfBexvP5lK5uFD01E";
         LoginParamData loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParam, LoginParamData.class);
         System.out.println("cookie2LoginParam 返回的loginParam:" + loginParamData);

         // 1、cookie2LoginParam 接口返回的LoginParam数据：
         String loginParam = "f_qVquYP2PMJD2Yv0jHO6mTP1GF1f-9PDSuZryLN4InW-im2GobY96m9iQxtYHgtGOYKGRaO8Rx4hgTGCIlC4NMpnxqdG8L0xzUcMUN73RrE9uws3YCHYcFPIC8i9xEDQoENYuiEi39bAvsDmZ3BA-bj-0qoqJRvP21S0bOd40-c__BTP3goN_UHSof1FWEcCA7F1oQ9KL79nZJsTFG9YC7_SPLL9pE5pCN1PCNGMzasvzx_zSgvjk599uK5tulWiJHx6N4xbwB3YmafDTK84iWtnmiC5d3YD5utHTPO6RH9mDUVL-f9Z3vFrUE0alB4CoR96DC_IjTxS2ykpvNUVTTqISKnH4gct0hKH-bfe9wsMESd8yfLBuavGVSmeVkY";
         LoginParamData loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParam, LoginParamData.class);
         System.out.println("cookie2LoginParam 返回的loginParam:" + loginParamData);
         // 2、register 接口返回的loginParam数据：
         loginParam = "f_qVquYP2PMJD2Yv0jHO6mTP1GF1f-9PDSuZryLN4InW-im2GobY96m9iQxtYHgtGOYKGRaO8Rx4hgTGCIlC4NMpnxqdG8L0xzUcMUN73RrE9uws3YCHYcFPIC8i9xEDQoENYuiEi39bAvsDmZ3BA-bj-0qoqJRvP21S0bOd40-c__BTP3goN_UHSof1FWEcCA7F1oQ9KL79nZJsTFG9YC7_SPLL9pE5pCN1PCNGMzasvzx_zSgvjk599uK5tulWiJHx6N4xbwB3YmafDTK84iWtnmiC5d3YD5utHTPO6RH9mDUVL-f9Z3vFrUE0alB4CoR96DC_IjTxS2ykpvNUVTTqISKnH4gct0hKH-bfe9wSMp9lyJq9K6AuXBvCYw6FUPLP9F7ployt-dgrkpVjv7EQ1fm2wwyTFx7n2jsTHhcvuRm3fyqwNoOVWGuvsb09";
         loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParam, LoginParamData.class);
         System.out.println("register 返回的loginParam:" + loginParamData);
         // 3、verifyCodeAndBindLoginAccount 返回的loginParam数据：
         loginParam = "f_qVquYP2PMJD2Yv0jHO6mTP1GF1f-9PDSuZryLN4InW-im2GobY96m9iQxtYHgtGOYKGRaO8Rx4hgTGCIlC4NMpnxqdG8L0xzUcMUN73RrE9uws3YCHYcFPIC8i9xEDQoENYuiEi39bAvsDmZ3BA-bj-0qoqJRvP21S0bOd40-c__BTP3goN_UHSof1FWEcCA7F1oQ9KL79nZJsTFG9YC7_SPLL9pE5pCN1PCNGMzasvzx_zSgvjk599uK5tulWZCaXY2ykD3OPuB84_LY-TA_lIMO6iGx8-dA8UoLWHj7-Z-Nr0J4HjDqS-KjLqm6s9ll52gtwlW3mDR2rhWjkUTR8gUCWzNrWrUQVVAkBESDLaCTUwjzQV-3pXIbB4Bwm-D5CoGuu7NUbm0lnW4LT7pvAbvDXFgOz_nBNCBsFRlyye3h9NHPbxfQjur1fHyYltNdBe1ylZ1b3DutfNVLC1X0CjNEIKeGt2XO4s7Nk8C5emsfMQv5NjAph5e4-Z_BPOYMGa2yWU9w9EfSMRArsXw";
         loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParam, LoginParamData.class);
         System.out.println("verifyCodeAndBindLoginAccount 返回的LoginParam:" + loginParamData);
         // 4、register 接口接收的loginParam数据：
         loginParam = "f_qVquYP2PMJD2Yv0jHO6mTP1GF1f-9PDSuZryLN4InW-im2GobY96m9iQxtYHgtGOYKGRaO8Rx4hgTGCIlC4NMpnxqdG8L0xzUcMUN73RrE9uws3YCHYcFPIC8i9xEDQoENYuiEi39bAvsDmZ3BA-bj-0qoqJRvP21S0bOd40-c__BTP3goN_UHSof1FWEcCA7F1oQ9KL79nZJsTFG9YC7_SPLL9pE5pCN1PCNGMzasvzx_zSgvjk599uK5tulWZCaXY2ykD3OPuB84_LY-TA_lIMO6iGx8-dA8UoLWHj7-Z-Nr0J4HjDqS-KjLqm6s9ll52gtwlW3mDR2rhWjkUTR8gUCWzNrWrUQVVAkBESDLaCTUwjzQV-3pXIbB4Bwm-D5CoGuu7NUbm0lnW4LT7pvAbvDXFgOz_nBNCBsFRlyye3h9NHPbxfQjur1fHyYltNdBe1ylZ1b3DutfNVLC1X0CjNEIKeGt2XO4s7Nk8C5emsfMQv5NjAph5e4-Z_BPOYMGa2yWU9w9EfSMRArsXw";
         loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParam, LoginParamData.class);
         System.out.println("register 接收到的LoginParam:" + loginParamData);
         // 5、register 接口返回的loginParam数据：
         loginParam = "2gPI4Edb3BMiJSMhTJih3opr_hSZ29GUeFjTbJqCzlW6jgq3woJ9AvANHThPPiE3lexA8rXcrX9mDXWFnEaWG8AYFNXucU8qRdX9Mo1-ATdNHViqtGnfkde8Y-mawVHqvuc3iwpSktgDwKcJYMI9y7zdoolgkw9_VaNdCvdsq4LVfRTu94lVrNUwrpi7mGXS-_ujSNqrlD3Nrxz50wTDqgFF8gmV_AZm74Jh_7dS8ZOiXcFjT3rkBZrk4pCbjxojNzsrD5urN4YG37OwTmuV-oxpSbRYRzydTOtM8Cy8BDuU2vbEMZr2j0bFUZU5H8VozwTQ865Xc5pnaEPqYGZiiXZ1JUOceIiO5jKbVWbkefJz5KFD7AhVVjYYS8I1JI92KFkjgNzQ3bKS2BPsYT2WuNtPQpAGW5Lhw64bly16c1kmLrQ2B7tzlVZ0ujn-8p_lESdG9X3yf-XY2bj7rORECoxYJY5bOzj1wtl4Pwvy8mKjDsmfWZY49KypITBr74abVfUeEDu-MxgxhN_8HdPacAiW7dRgzaQD7SkyDBD5Vo8";
         loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParam, LoginParamData.class);
         System.out.println("register 返回的LoginParam:" + loginParamData);
         // 6、cookie2LoginParam返回的loginParam
         loginParam = "8sFmGKtH9Yv5KxVsiXJBnCLUqdBwTBK0eF5jRr2D4_CicWufQ3AXUV-vA6YBRtKDF_dF-ruxX4XmJstXJLuvvv0L3jfAQW-UabyFFncSTpioeM5hKvA18A4yVEg_twyAQoENYuiEi39bAvsDmZ3BA_QdKzPiOdDbePwJXt1Yx9Gc__BTP3goN_UHSof1FWEcvs8ec0XmirtYfqhTowy0By7_SPLL9pE5pCN1PCNGMzYitRmtMtrXVXpAX85rExx7_5SnRHfHEOPZiRMwdeGcaSWtnmiC5d3YD5utHTPO6RH9mDUVL-f9Z3vFrUE0alB4CoR96DC_IjTxS2ykpvNUVTTqISKnH4gct0hKH-bfe9wsMESd8yfLBuavGVSmeVkY";
         loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParam, LoginParamData.class);
         System.out.println("cookie2LoginParam 返回的LoginParam:" + loginParamData);
         **/
    }

    /**
     * 上游员工身份进入互联应用
     */
    @Test
    public void test5() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(2);
        loginArg.setDownstreamEa("89303");
        loginArg.setUpstreamEa("74164");
        Cookie cookieFSAuthX = new Cookie("FSAuthX",
                "0G60yZqS0G60001izwNmLwKmyVCwPf1JWaAKy6yvTit1rQC1dSwoK0LWajCczxx5TuIzk7QAmwE4aua32fwljy9ZvpaqMmX4uBy2PSmuyXlv1G7qgurUPuYbjg8yl6Va6vtw8JfqD379yVz4ohea4ZEsys5mT5Gp7FwVRKryMXh5vISv4BDhizQNaxsyYX5ailZG4LoYvqTvkDoFIr4HkNSrSZy3XZnz44u1FEyBYWdTxEwxMqBxNSbJZoUeE");
        cookieFSAuthX.setPath("/");
        cookieFSAuthX.setHttpOnly(true);
        cookieFSAuthX.setMaxAge(600);

        Cookie cookieERInfo = new Cookie("ERInfo", "er_a7d586880e5c44b7bdc3c53c27854006_1106162844_74164");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setCookies(cookieERInfo, cookieFSAuthX);

        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test6() {
        LoginArg loginArg = new LoginArg();
        loginArg.setAuthType(4);
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setUpstreamEa("90925");
        // loginArg.setDownstreamOuterTenantId(300118833L); // 已绑定手机号码，可以正常登录成功
        loginArg.setDownstreamOuterTenantId(300118832L); // 未绑定手机号码，需要完善手机号码
        //loginArg.setLoginParam("8sFmGKtH9Yv5KxVsiXJBnCLUqdBwTBK0eF5jRr2D4_CicWufQ3AXUV-vA6YBRtKDF_dF-ruxX4XmJstXJLuvvv0L3jfAQW-UabyFFncSTphku0EOxvD26IdEOXkTknlFgZU6kb1DxJIOUgSlczM1gWxH1vWrPn_KBtxkQFlKsXSNoSdPy0w8M-ZYllhiOOgl0q3slqRan2KNB99QP8JpZTa_5Qf6x4XjK98XjeAV4o-VKRy1RPOFR1ch7PC-AxXWKaiWa9aL5_qBs6vcr_Nuwo3aeRMh8281YSFXs5_-WcE");

        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("011ID0100QUIaT1kJy0003Ctk03ID014");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();

        Cookie cookieERInfo = new Cookie("ERInfo", "er_0afb4f5fe2404a2097193f4cae9b9bcf_1111105815_90925");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);

        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }


    @Test
    public void test66() {
        LoginArg loginArg = new LoginArg();
        loginArg.setAuthType(13);
        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();

        Cookie cookieERInfo = new Cookie("ERInfo", "er_9637fbe4410445308b27b29c3b4262d8_1231162903_90925");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);

        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test7() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(4);
        loginArg.setUpstreamEa("90925");
        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("0715GWkl2YMxte47LQll2FNM6735GWkP");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();

        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test71() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(4);
        loginArg.setUpstreamEa("90925");
        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("041NaB0007VAbT1GWQ000XVOxL2NaB0y");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test72() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(5);
        loginArg.setUpstreamEa("90925");
        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("0d3Cl8100uZHaT1Sdv200beWqW1Cl81E");
        thirdArg.setWxAppId("wx2d93c410ce92f0ce");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test73() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(4);
        loginArg.setUpstreamEa("90925");
        loginArg.setDownstreamOuterTenantId(300118833L);
        loginArg.setLoginParam(
                "8sFmGKtH9Yv5KxVsiXJBnCLUqdBwTBK0eF5jRr2D4_CicWufQ3AXUV-vA6YBRtKDF_dF-ruxX4XmJstXJLuvvv0L3jfAQW-UabyFFncSTphku0EOxvD26IdEOXkTknlFgZU6kb1DxJIOUgSlczM1gTPah1RaFgRuqKtsZ9x-jPDkyNFO7CERRxTgaM7-67wQ0q3slqRan2KNB99QP8JpZTa_5Qf6x4XjK98XjeAV4o8o-vlBmog_pxWYi2EbaSTNWZfBnqNv6WfrbZ7DCxLGPWsWFOwYF1xLPuEGCWqhK0Q");
        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("071JVf0w3AMFS33Xjs1w3fQFg32JVf0R");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookieERInfo = new Cookie("ERInfo", "er_7f908a622fca4511a2e6396401d961f9_1120111449_90925");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);

        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test74() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_127a3981");
        loginArg.setAuthType(4);
        loginArg.setUpstreamEa("90925");
        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("051BmN000K6beT1oHb400hSBlQ0BmN0s");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void listSwitchableEmployeesTest1113() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        ListSwitchableEmployeesArg arg = new ListSwitchableEmployeesArg();
        arg.setSwitchAllUpstream(false);
        arg.setIsNeedRoles(false);
        arg.setFsAppId("FSAID_11491009");
        arg.setLoginParam(
                "oROOkyHRSLzOPQyqfqPA9YmgqIbNitAtkI5VDIV4zpe4mStKxh5uDMzZwR8DmiEwHRe5imwa6iQaGzKVXPuHsBILr9J6kABPDFwiG2vXHxl1iWLhPddRtkVehF9iAZO23yUsZexNUn_niIbS0J5gAknL6ZRa6XNte4kRqylhz38H83sLWS9YsFYtR-t0O0KMnAbVlW14BoofbyY5cJCMAPo61SxCq1ZZd5gFOBsX7oI7hez1tbUlzC3v4REyBc4U5gZ3HS2j2Ox9u7uAHih_WthF7syuWJ8HOmbGlxEnYqg");
        Result<List<EmployeeVo>> result = loginController.listSwitchableEmployees(arg, request);
        System.out.println(result);
    }

    /**
     * 登录成功-》完善手机号-》匹配到多个互联账号-》选择合并账号
     */
    @Test
    public void test80() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(4);
        loginArg.setUpstreamEa("90925");
        loginArg.setDownstreamOuterTenantId(300118833L);
        loginArg.setLoginParam(
                "8sFmGKtH9Yv5KxVsiXJBnCLUqdBwTBK0eF5jRr2D4_CicWufQ3AXUV-vA6YBRtKDF_dF-ruxX4XmJstXJLuvvv0L3jfAQW-UabyFFncSTpioeM5hKvA18A4yVEg_twyAQoENYuiEi39bAvsDmZ3BA1itPL3tpltvbo42mNgaxVSc__BTP3goN_UHSof1FWEcs849Qb-JlUhsyFSCYIfSPi7_SPLL9pE5pCN1PCNGMzb9HI6BuGJs0y4YntjoWg__Jg3HcNAnmqRTmwG5DVdw3A_lIMO6iGx8-dA8UoLWHj5mO3uym3LHg7Gaf4xA_pUkhGaSvJRM4KCwz2vPyHv74zR8gUCWzNrWrUQVVAkBESDLaCTUwjzQV-3pXIbB4Bwm-D5CoGuu7NUbm0lnW4LT7pvAbvDXFgOz_nBNCBsFRlyye3h9NHPbxfQjur1fHyYltNdBe1ylZ1b3DutfNVLC1X0CjNEIKeGt2XO4s7Nk8C4KoljEpD8a-2ltEwJ-8N_4");
        loginArg.setLoginType(3);

        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("021jUH100vgmcT1jcq200Bq1g61jUH1i");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookieERInfo = new Cookie("ERInfo", "er_6ed8de7fe594428cbe551a1f93cdaaf8_1121155222_90925");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);

        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    /**
     * openId账号登录应用（开通mobile账号自注册）
     */
    @Test
    public void test81() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(4);
        loginArg.setUpstreamEa("90925");

        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("0113aTll2CpKxe440enl23Hten13aTlD");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookieERInfo = new Cookie("ERInfo", "er_6ed8de7fe594428cbe551a1f93cdaaf8_1121155222_90925");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);

        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    /**
     * 已停用应用级，租户级自注册配置，手机验证码登录报错
     */
    @Test
    public void test82() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(4);
        loginArg.setLoginType(3);
        loginArg.setUpstreamEa("90925");
        loginArg.setLoginParam(
                "8sFmGKtH9Yv5KxVsiXJBnCLUqdBwTBK0eF5jRr2D4_CicWufQ3AXUV-vA6YBRtKDF_dF-ruxX4XmJstXJLuvvv0L3jfAQW-UabyFFncSTphku0EOxvD26IdEOXkTknlFgZU6kb1DxJIOUgSlczM1gXYVoijBbRugtp_VfbzTzpM8YjHCe0Lsd32rhrd60LA0AaoNdtfrdU0XBIrMA55y_xFNm6Tdx00a7DwPWiCTU9rxYzBs5VlQX1DUvhksN-bnDPW2gCju5xc8FFLoV4PyUPMA7EYav6MXblZYWqIL90wBGl7n8qSTtGYjZTClJq_QjhrLGSOwaYF2ZceIZBYrch1nXw4fnQt5VopTrjwXvdmYhMP0nwXsbz-ZSubhQ9NR");

        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("001nZd100HXvfT1BPu100uU2973nZd1B");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();

        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test86() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_127a3981");
        loginArg.setAuthType(4);

        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("051pJUGa1wlxAI0oDVGa1M0e451pJUGp");
        thirdArg.setWxAppId("wx8bd8a617c9ae66ed");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookieERInfo = new Cookie("ERInfo", "er_552dcec009cb4c4481989cd365e21278_1122180342_82313");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);

        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    /**
     * 应用级自注册配置关闭，租户级配置开启，登录未被拦截
     */
    @Test
    public void test87() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(4);
        loginArg.setUpstreamEa("90925");

        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("051xAS000D0gfT1Wzl2003ZoWn0xAS0H");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();

        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    /**
     * 轻量账号登录，完善手机号码报错
     */
    @Test
    public void test88() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(4);
        loginArg.setLoginType(3);
        loginArg.setUpstreamEa("90925");
        loginArg.setLoginParam(
                "8sFmGKtH9Yv5KxVsiXJBnCLUqdBwTBK0eF5jRr2D4_CicWufQ3AXUV-vA6YBRtKDF_dF-ruxX4XmJstXJLuvvv0L3jfAQW-UabyFFncSTpioeM5hKvA18A4yVEg_twyAQoENYuiEi39bAvsDmZ3BA8uM6yf0LFgouFqePi1WaPCc__BTP3goN_UHSof1FWEcTP6n-3xlP8RawOsjPh-ely7_SPLL9pE5pCN1PCNGMzYJxt0K1iPtCm_VRLdXa9XzuaTxoq66Cm90PNBCNnSWPQ_lIMO6iGx8-dA8UoLWHj5mO3uym3LHg7Gaf4xA_pUkVNNBIPWI-mL5ZL9Vl2QWdDR8gUCWzNrWrUQVVAkBESDLaCTUwjzQV-3pXIbB4Bwm-D5CoGuu7NUbm0lnW4LT7pvAbvDXFgOz_nBNCBsFRlyye3h9NHPbxfQjur1fHyYltNdBe1ylZ1b3DutfNVLC1X0CjNEIKeGt2XO4s7Nk8C4KoljEpD8a-2ltEwJ-8N_4");

        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("011KIf000bwweT12Mo000JOjmc2KIf01");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookieERInfo = new Cookie("ERInfo", "er_47379b5937d446f588df7f0763a177aa_1122182915_90925");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    /**
     * 轻量账号完善手机号报“系统繁忙”
     */
    @Test
    public void test89() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(4);
        loginArg.setLoginType(3);
        loginArg.setUpstreamEa("90925");
        loginArg.setLoginParam(
                "8sFmGKtH9Yv5KxVsiXJBnCLUqdBwTBK0eF5jRr2D4_CicWufQ3AXUV-vA6YBRtKDF_dF-ruxX4XmJstXJLuvvv0L3jfAQW-UabyFFncSTpioeM5hKvA18A4yVEg_twyAQoENYuiEi39bAvsDmZ3BAw8ShmpNz6n0zCrXf_BMkqWc__BTP3goN_UHSof1FWEcLDNVVZKzIXy4yqA4spo4NS7_SPLL9pE5pCN1PCNGMzbGIAGIG7_wEPCMQMnJZNrDS3ka6DsA1U0B5x33K8KFuQ_lIMO6iGx8-dA8UoLWHj5mO3uym3LHg7Gaf4xA_pUkLr36wR4_TLYXrCU2ZskD5TR8gUCWzNrWrUQVVAkBESDLaCTUwjzQV-3pXIbB4Bwm-D5CoGuu7NUbm0lnW4LT7pvAbvDXFgOz_nBNCBsFRlyye3h9NHPbxfQjur1fHyYltNdBe1ylZ1b3DutfNVLC1X0CjNEIKeGt2XO4s7Nk8C4KoljEpD8a-2ltEwJ-8N_4");

        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("031uTZ000rBogT11Sb000ktK0n3uTZ0V");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookieERInfo = new Cookie("ERInfo", "er_9b6d8e466d304d6aa8666269154663b0_1125145833_90925");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test90() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11490d9e");
        loginArg.setAuthType(4);
        loginArg.setUpstreamEa("90925");

        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("001dSrml2HC7De4npRkl2ABH0d1dSrmO");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookieERInfo = new Cookie("ERInfo", "er_aca6ca2ff7a24505b71120a3e782583a_1129165541_90925");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test91() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_3");
        loginArg.setAuthType(4);
        loginArg.setUpstreamEa("90925");

        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setCode("001dSrml2HC7De4npRkl2ABH0d1dSrmO");
        thirdArg.setWxAppId("wx883580c326233525");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookieERInfo = new Cookie("ERInfo", "er_aca6ca2ff7a24505b71120a3e782583a_1129165541_90925");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test92() {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId("FSAID_11491369");
        loginArg.setAuthType(2);
        loginArg.setUpstreamEa("90403");
        loginArg.setDownstreamEa("91329");

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookieERInfo = new Cookie("FSAuthXC", "0G603COG0mC0003Vnhya5PE6OUo6FxApKCnx6qgyKKC3HN344FW6ugeLng0WRuV5fMiOlXBofpU1uszXWuSvlrp2B0BwYFuwqwg6YVkGbeHk3liURWu4zQNbLzYDvz56sbAyMZ40G2oyAQaRTHH7jf29RxwOdmrqXv223tnsw9D5Me9jGyedVchNKBWUGzRApQXu2eofjOCHK7YM74K3aqiGkxzDf8vWZaLfRHSZAPMj3phVqLWTZhQ6bAy0");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test93() {
        LoginArg loginArg = new LoginArg();
        loginArg.setAuthType(13);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookieERInfo = new Cookie("ERInfo", "er_d6f19a61ed564573aee3894d723bc9e1_0108153208_90925");
        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test94() {
        LoginArg loginArg = new LoginArg();
        loginArg.setAuthType(5);
        loginArg.setLoginParam("NPfELegEQKW6P9ftC-wKHubammuobIL7tHunRnuyoWMiz6ryFdEPBeZhvzvNGMdX6CNpGh3oxkTzvuHUJehrxt5oQ56WwoZx4_s5a6AtB_ekoXCP6yxUrD7PoL0nYO2cRTXre1O8woYXEBhBfse82ChZI4Dc0N2yktgT7GE9lrhJMWov79MnNMaElP58eW1w5FIPk5HJzsU3GpoA7AUHzostt-FDF22uwJHlmE9KZHctbelauhAG3equ7B3zZdH-M1upptu3DTa1NIFIJnjFzQ");
        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setWxAppId("wx2d93c410ce92f0ce");
        thirdArg.setCode("0a1k8h1w31UXz43vIv1w3Pap7r2k8h1B");
        loginArg.setThirdArg(thirdArg);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
//        Cookie cookieERInfo = new Cookie("ERInfo", "er_7604cde0803349908f2637197b819cce_0321141926_82313");
//        cookieERInfo.setPath("/");
//        cookieERInfo.setHttpOnly(true);
//        cookieERInfo.setMaxAge(600);
//        request.setCookies(cookieERInfo);
        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test95() {
        LoginArg loginArg = new LoginArg();
        loginArg.setAuthType(13);

        MockHttpServletResponse response = new MockHttpServletResponse();
        MockHttpServletRequest request = new MockHttpServletRequest();
        /*Cookie cookieERInfo = new Cookie("ERInfo", "er_d6f19a61ed564573aee3894d723bc9e1_0108153208_90925");

        cookieERInfo.setPath("/");
        cookieERInfo.setHttpOnly(true);
        cookieERInfo.setMaxAge(600);
        request.setCookies(cookieERInfo);*/

        Result<LoginResult> result = loginController.login(loginArg, request, response);
        System.out.println(result);
    }

    @Test
    public void test96() {
        SendCodeArg arg = new SendCodeArg();
        arg.setAuthType(10);
        arg.setEa("89303");
        arg.setLoginAccount("<EMAIL>");
        arg.setImgCode("1343");
        MockHttpServletRequest request = new MockHttpServletRequest();
        Result result = loginController.sendCode(arg, null, request);
        System.out.printf("email code send：" + result);
    }

    @Test
    public void test97() {
        String content = "【纷享销客】验证码：123456（请勿转告他人），有效期15分钟，请您尽快完成验证。";
        emailCodeLoginTypeProvider.sendMessage("89303", "<EMAIL>", content, content);
    }

    @Test
    public void test98() {
        GetErLoginWelcomePageConfigArg arg = new GetErLoginWelcomePageConfigArg();
        arg.setAuthType(10);
        arg.setUpstreamEa("90925");
        arg.setLinkAppId("FSAID_127a3981");
        Result<EnterpriseErLoginWelcomePageConfigVo> result = loginController.getErLoginWelcomePageConfig(arg);
        System.out.println(result);
    }
}
