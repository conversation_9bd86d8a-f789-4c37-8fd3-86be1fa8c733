package com.facishare.er.biz.outrest.controller.auth;

import com.facishare.converter.EIEAConverter;
import com.facishare.converter.exception.EIEANotFoundException;
import com.facishare.enterprise.common.constant.*;
import com.facishare.enterprise.common.context.RequestContext;
import com.facishare.enterprise.common.enums.*;
import com.facishare.enterprise.common.exception.RelationException;
import com.facishare.enterprise.common.exception.UnionLoginException;
import com.facishare.enterprise.common.model.*;
import com.facishare.enterprise.common.report.EsLogSendUtil;
import com.facishare.enterprise.common.report.data.LoginRegisterLogData;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.*;
import com.facishare.er.biz.outrest.arg.*;
import com.facishare.er.biz.outrest.common.LoginErrorI18nKeyConstant;
import com.facishare.er.biz.outrest.controller.auth.data.LoginArg;
import com.facishare.er.biz.outrest.controller.auth.data.LoginArg.ThirdArg;
import com.facishare.er.biz.outrest.controller.auth.data.RegisterArg;
import com.facishare.er.biz.outrest.controller.auth.data.RegisterResult;
import com.facishare.er.biz.outrest.controller.auth.data.RegisterStatusEnum;
import com.facishare.er.biz.outrest.controller.auth.handler.AuthHandler;
import com.facishare.er.biz.outrest.controller.auth.handler.register.RegisterHandler;
import com.facishare.er.biz.outrest.controller.auth.handler.register.RegisterHandlerMapping;
import com.facishare.er.biz.outrest.controller.auth.loginTypeProvider.EmailCodeLoginTypeProvider;
import com.facishare.er.biz.outrest.controller.auth.loginTypeProvider.PhoneCodeLoginTypeProvider;
import com.facishare.er.biz.outrest.data.*;
import com.facishare.er.biz.outrest.enums.MapperApiNameEnum;
import com.facishare.er.biz.outrest.enums.MustErAccountDataEnum;
import com.facishare.er.biz.outrest.enums.OwnerAllocateTypeEnum;
import com.facishare.er.biz.outrest.manager.EnterpriseRelationManager;
import com.facishare.er.biz.outrest.manager.PublicEmployeeManager;
import com.facishare.er.biz.outrest.manager.arg.GetLayoutArg;
import com.facishare.er.biz.outrest.manager.arg.RegisterERDownstreamArg;
import com.facishare.er.biz.outrest.manager.data.CrmContactVo;
import com.facishare.er.biz.outrest.manager.data.ERAccountActiveConfigData;
import com.facishare.er.biz.outrest.manager.data.IdNameVo;
import com.facishare.er.biz.outrest.manager.data.RegisterERAccountActiveConfigData;
import com.facishare.er.biz.outrest.remote.channel.ChannelPortalLoginService;
import com.facishare.er.biz.outrest.remote.crmremote.CrmManager;
import com.facishare.er.biz.outrest.remote.crmremote.ERAccountActiveConfigManager;
import com.facishare.er.biz.outrest.service.LoginRegisterConfigService;
import com.facishare.er.biz.outrest.service.OuterRoleService;
import com.facishare.global.rest.model.data.LoginEmployeeInfo;
import com.facishare.linkapp.api.arg.ListByLinkAppIdArg;
import com.facishare.linkapp.api.result.LinkAppData;
import com.facishare.linkapp.api.service.HttpLinkAppService;
import com.facishare.linkapp.api.service.LinkAppFullErAccountFuncConfigService;
import com.facishare.linkapp.api.vo.LinkAppFullErAccountFuncConfigVo;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.wechat.dubborestouterapi.service.union.EaBindInfoRestService;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.arg.IndustryGetCompanyByNameArg;
import com.fxiaoke.crmrestapi.common.contants.*;
import com.fxiaoke.crmrestapi.common.data.ContactData;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.result.IndustryGetCompanyByNameResult;
import com.fxiaoke.crmrestapi.result.IndustryGetCompanyByNameResult.Company;
import com.fxiaoke.crmrestapi.service.IndustryService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.enterpriserelation.objrest.arg.ListEnterpriseRelationObjByConditionArg;
import com.fxiaoke.enterpriserelation.objrest.arg.ListPublicEmployeeObjByConditionArg;
import com.fxiaoke.enterpriserelation.objrest.arg.ValidPublicEmployeeLicenseQuoteArg;
import com.fxiaoke.enterpriserelation.objrest.result.ValidPublicEmployeeLicenseQuoteResult;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.fxiaoke.enterpriserelation.objrest.service.PublicEmployeeObjService;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.er.biz.outrest.common.LoginErrorI18nKeyConstant.HAS_FS_ACCOUNT_NOT_LOGIN_BY_WXSERVICE;

@Api(tags = {"互联新版注册"})
@Slf4j
@Controller
@RequestMapping("/register")
public class ERUnionRegisterController extends BaseAuthController {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EaBindInfoRestService eaBindInfoRestService;
    @Autowired
    private HttpLinkAppService linkAppService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private EnterpriseRelationManager enterpriseRelationManager;
    @Autowired
    private CrmManager crmManager;
    @Autowired
    @Qualifier("jedisSupport")
    private MergeJedisCmd jedis;
    @Autowired
    private OuterRoleService outerRoleService;
    @Autowired
    private PublicEmployeeManager publicEmployeeManager;
    @Autowired
    private DistributeLock distributeLock;
    @Autowired
    private EnterpriseRelationManager objEnterpriseRelationOutController;
    @Autowired
    private ERAccountActiveConfigManager erAccountActiveConfigManager;
    @Autowired
    private EnterpriseRelationObjService enterpriseRelationObjService;
    @Autowired
    private PublicEmployeeObjService publicEmployeeObjService;
    @Autowired
    private IndustryService industryService;
    @Autowired
    private RegisterHandlerMapping loginRegisterConfigHandlerMapping;
    @Autowired
    private LinkAppFullErAccountFuncConfigService linkAppFullErAccountFuncConfigService;
    @Autowired
    private LoginRegisterConfigService loginRegisterConfigService;
    @Autowired
    private ChannelPortalLoginService channelPortalLoginService;
    @Autowired
    private EmailCodeLoginTypeProvider emailCodeLoginTypeProvider;
    @Autowired
    private PhoneCodeLoginTypeProvider phoneCodeLoginTypeProvider;

    private static Long industryServiceQueryLimit;
    private static Long industryServiceQueryTimeoutSecond;
    private static Long searchMapperObjectByNameQueryLimit;
    private static Long searchMapperObjectByNameQueryTimeoutSecond;
    private static Set<String> grayEis = new HashSet<>();
    private static String portalAppId = "";

    static {
        ConfigFactory.getConfig("fs-er-biz-application", config -> {
            industryServiceQueryLimit = config.getLong("industryServiceQueryLimit", 10);
            industryServiceQueryTimeoutSecond = config.getLong("industryServiceQueryTimeoutSecond", 60);
            searchMapperObjectByNameQueryLimit = config.getLong("searchMapperObjectByNameQueryLimit", 1000);
            searchMapperObjectByNameQueryTimeoutSecond = config.getLong("searchMapperObjectByNameQueryTimeoutSecond", 24 * 60 * 60);
            portalAppId = config.get("portalAppId");
        });

        ConfigFactory.getConfig("variables_enterpriserelation", config -> {
            String grayEiCrmApibusStr = config.get("gray_ei_crm_apibus", "-1");
            grayEis = new HashSet<String>(Splitter.on(",").splitToList(grayEiCrmApibusStr));
        });
    }

    @ApiOperation(value = "互联注册,支持H5", tags = {VersionIterationConstant.f_770_login_optime})
    @ResponseBody
    @RequestMapping({"", "/"})
    public Result<RegisterResult> register(@RequestBody RegisterArg arg, @RequestParam(value = "version", required = false) String version, HttpServletRequest request, HttpServletResponse response) {
        if (arg.getIsFromLogin() == null) {
            arg.setIsFromLogin(false);
        }
        LoginEmployeeInfo loginUser = getERCookie(request);
        Result<RegisterResult> result = doRegister(arg, request, response, loginUser);
        // 注册成功后，重新生成cookie
        if (!arg.getIsFromLogin() && result.isSuccess() && result.getData() != null && result.getData().getStatus() == RegisterStatusEnum.REGISTER_SUCCESS.getStatus()) {
            RegisterResult registerResult = result.getData();
            if (registerResult.getRegisterUser() != null && registerResult.getRegisterUser().getOuterUid() != null) {
                Long outerUid = registerResult.getRegisterUser().getOuterUid();
                // 生成新账号登录态
                PublicEmployeeObj newPublicEmployeeObj = publicEmployeeManager.getPublicEmployeeObj(arg.getUpstreamEa(), outerUid);
                LoginEmployeeInfo registerEmployeeInfo = buildRegisterSuccessEmployeeInfo(arg, newPublicEmployeeObj);
                // 种新cookie
                handleLoginSuccess(request, response, registerEmployeeInfo);
                String ticket = encodeTicket(registerEmployeeInfo);
                HashMap<Object, Object> ticketData = new HashMap<>();
                ticketData.put("ticket", ticket);
                registerResult.setData(ticketData);
            }
        }
        return result;
    }

    public Result<RegisterResult> doRegister(RegisterArg arg, HttpServletRequest request, HttpServletResponse response, LoginEmployeeInfo loginUser) {
        LoginParamData loginParamData = decodeLoginParamData(arg.getLoginParam());
        if (loginParamData.getAuthType() == null) {
            if (arg.getAuthType() != null) {
                loginParamData.setAuthType(arg.getAuthType());
            } else {
                if (loginUser != null && loginUser.getAuthType() != null) {
                    arg.setAuthType(loginUser.getAuthType());
                    loginParamData.setAuthType(loginUser.getAuthType());
                }
            }
        }
        if (StringUtils.isNotBlank(arg.getUpstreamEa())) {
            try {
                eieaConverter.enterpriseAccountToId(arg.getUpstreamEa());
            } catch (Exception e) {
                return Result.newError(ResultCode.ENTERPRISE_ACCOUNT_NOT_VALID);
            }
        }
        try {
            log.info("doRegister -> arg={}, loginParamData={}", arg, loginParamData);
            // 先取upstreamEa
            checkAuthType(arg, loginParamData);
            AuthHandler matchHandler = authHandlerMapping.findMatchHandler(arg.getAuthType());
            LoginArg loginArg = toLoginArg(arg, loginParamData);
            String upstreamEa = arg.getUpstreamEa();
            if (Strings.isNullOrEmpty(upstreamEa)) {
                upstreamEa = matchHandler.getUpstreamEa(loginArg, loginParamData);
                arg.setUpstreamEa(upstreamEa);
            }
            RegisterResult registerResult = doRegisterV2(arg, loginParamData, loginUser);

            if (RegisterStatusEnum.REGISTER_SUCCESS.getStatus().equals(registerResult.getStatus())) {
                CommonPoolUtil.execute(() -> {
                    writeEsLog(arg, RegisterStatusEnum.REGISTER_SUCCESS.getStatus(), "ok", loginParamData);
                });
            } else if (RegisterStatusEnum.MERGE_ACCOUNT_LOGOUT.getStatus().equals(registerResult.getStatus())) {
                // 重新生成新账号登录态
                PublicEmployeeObj toMergePublicEmployeeObj = publicEmployeeManager.getPublicEmployeeObj(loginUser.getUpstreamEa(), registerResult.getRegisterUser().getOuterUid());
                LoginEmployeeInfo loginNewEmployeeInfo = buildNewLoginEmployeeInfoForOld(loginUser, toMergePublicEmployeeObj);
                // 种新cookie
                handleLoginSuccess(request, response, loginNewEmployeeInfo);
                String ticket = encodeTicket(loginNewEmployeeInfo);
                HashMap<Object, Object> ticketData = new HashMap<>();
                ticketData.put("ticket", ticket);
                registerResult.setData(ticketData);
            }
            registerResult.setLoginParam(AESUtil.objectEncode(config.getAesKey(), loginParamData));
            return Result.newSuccess(registerResult);
        } catch (UnionLoginException e) {
            log.warn("register UnionLoginException warn", e);
            return newRegisterErrorResult(e.getErrCode(), e.getErrDescription(), loginParamData, arg);
        } catch (RelationException e) {
            log.warn("register RelationException warn", e);
            return newRegisterErrorResult(e.getErrCode(), e.getErrDescription(), loginParamData, arg);
        } catch (Throwable e) {
            log.warn("register error", e);
            return newRegisterErrorResult(ResultCode.SYSTEM_ERROR.getErrorCode(), ResultCode.SERVER_BUSY.getDescription(), loginParamData, arg);
        }
    }

    public LoginEmployeeInfo buildRegisterSuccessEmployeeInfo(RegisterArg arg, PublicEmployeeObj publicEmployeeObj) {
        LoginEmployeeInfo loginUser = new LoginEmployeeInfo();
        loginUser.setAuthType(arg.getAuthType());
        loginUser.setUpstreamEa(arg.getUpstreamEa());
        if (StringUtils.isNotEmpty(arg.getLoginParam())) {
            LoginParamData loginParamData = decodeLoginParamData(arg.getLoginParam());
            if (loginParamData != null) {
                loginUser.setWxAppId(loginParamData.getWxAppId());
                loginUser.setOpenId(loginParamData.getOpenId());
            }
        }

        loginUser.setDownstreamOuterUid(publicEmployeeObj.getOuterUid());
        loginUser.setDownstreamOuterTenantId(publicEmployeeObj.getOuterTenantId());
        loginUser.setEmployeeName(publicEmployeeObj.getName());
        loginUser.setIdentityType(publicEmployeeObj.getIdentityType());
        loginUser.setMobile(publicEmployeeObj.getMobile());
        loginUser.setLoginEmail(publicEmployeeObj.getLoginEmail());
        loginUser.setLastUpdateTime(System.currentTimeMillis());
        loginUser.setIsOuterCookie(Boolean.TRUE);
        if (publicEmployeeObj.getLoginEmail() != null) {
            loginUser.setLoginAccountData(LoginAccountData.newLoginEmailAccount(publicEmployeeObj.getLoginEmail()));
        } else if (publicEmployeeObj.getMobile() != null) {
            loginUser.setLoginAccountData(LoginAccountData.newMobileAccount(publicEmployeeObj.getMobile()));
        }

        EnterpriseRelationObj enterpriseRelationObj = enterpriseRelationManager.getEnterpriseRelationObjById(arg.getUpstreamEa(), publicEmployeeObj.getOuterTenantId());
        loginUser.setDownstreamEa(enterpriseRelationObj.getEnterpriseAccount());
        loginUser.setUpstreamEnterpriseName(enterpriseRelationObj.getUpstreamShortName());
        loginUser.setDownstreamEnterpriseName(enterpriseRelationObj.getName());
        loginUser.setHasMapperAccountObj(StringUtils.isNotEmpty(enterpriseRelationObj.getMapperAccountId()));
        loginUser.setHasMapperPartnerObj(StringUtils.isNotEmpty(enterpriseRelationObj.getMapperPartnerId()));

        return loginUser;
    }

    public LoginEmployeeInfo buildNewLoginEmployeeInfoForOld(LoginEmployeeInfo oldLoginUser, PublicEmployeeObj publicEmployeeObj) {
        LoginEmployeeInfo loginUser = new LoginEmployeeInfo();
        loginUser.setAuthType(oldLoginUser.getAuthType());
        loginUser.setWxAppId(oldLoginUser.getWxAppId());
        loginUser.setOpenId(oldLoginUser.getOpenId());
        loginUser.setUpstreamEa(oldLoginUser.getUpstreamEa());

        loginUser.setDownstreamOuterUid(publicEmployeeObj.getOuterUid());
        loginUser.setDownstreamOuterTenantId(publicEmployeeObj.getOuterTenantId());
        loginUser.setEmployeeName(publicEmployeeObj.getName());
        loginUser.setIdentityType(publicEmployeeObj.getIdentityType());
        loginUser.setMobile(publicEmployeeObj.getMobile());
        loginUser.setLoginEmail(publicEmployeeObj.getLoginEmail());
        loginUser.setLastUpdateTime(System.currentTimeMillis());
        loginUser.setIsOuterCookie(Boolean.TRUE);
        if (publicEmployeeObj.getLoginEmail() != null) {
            loginUser.setLoginAccountData(LoginAccountData.newLoginEmailAccount(publicEmployeeObj.getLoginEmail()));
        } else if (publicEmployeeObj.getMobile() != null) {
            loginUser.setLoginAccountData(LoginAccountData.newMobileAccount(publicEmployeeObj.getMobile()));
        }
        EnterpriseRelationObj enterpriseRelationObj = enterpriseRelationManager.getEnterpriseRelationObjById(oldLoginUser.getUpstreamEa(), publicEmployeeObj.getOuterTenantId());
        loginUser.setDownstreamEa(enterpriseRelationObj.getEnterpriseAccount());
        loginUser.setUpstreamEnterpriseName(enterpriseRelationObj.getUpstreamShortName());
        loginUser.setDownstreamEnterpriseName(enterpriseRelationObj.getName());
        loginUser.setHasMapperAccountObj(StringUtils.isNotEmpty(enterpriseRelationObj.getMapperAccountId()));
        loginUser.setHasMapperPartnerObj(StringUtils.isNotEmpty(enterpriseRelationObj.getMapperPartnerId()));

        return loginUser;
    }

    private void writeEsLog(RegisterArg registerArg, Integer errCode, String errMessage, LoginParamData loginParamData) {
        try {
            Integer authType = null;
            if (registerArg != null && registerArg.getAuthType() != null) {
                authType = registerArg.getAuthType();
            }
            if (loginParamData != null && loginParamData.getAuthType() != null) {
                authType = loginParamData.getAuthType();
            }
            String upstreamEa = null;
            Integer upstreamEi = null;
            if (registerArg != null && StringUtils.isNotBlank(registerArg.getUpstreamEa())) {
                upstreamEa = registerArg.getUpstreamEa();
            }
            if (StringUtils.isNotBlank(upstreamEa)) {
                upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
            }

            LoginRegisterLogData loginRegisterLogData = new LoginRegisterLogData();
            loginRegisterLogData.setLogType(KeyBuilder.buildErRegisterTypeForEsLogActionField(authType));
            loginRegisterLogData.setEa(upstreamEa);
            loginRegisterLogData.setEi(upstreamEi);
            loginRegisterLogData.setErrorCode(errCode);
            loginRegisterLogData.setErrorMessage(errMessage);

            loginRegisterLogData.setLinkAppId(registerArg != null ? registerArg.getFsAppId() : "null");
            loginRegisterLogData.setLoginOrRegisterArg(registerArg);
            loginRegisterLogData.setLoginParam(loginParamData);
            EsLogSendUtil.sendLoginOrRegisterLog(loginRegisterLogData);
        } catch (Exception e) {
            log.warn("", e);
        }
    }

    private RegisterResult doRegisterV2(RegisterArg registerArg, LoginParamData loginParamData, LoginEmployeeInfo loginUser) {
        checkAuthType(registerArg, loginParamData);
        LoginArg loginArg = toLoginArg(registerArg, loginParamData);
        AuthHandler authHandler = authHandlerMapping.findMatchHandler(registerArg.getAuthType());
        // 确定上游EA
        RegisterResult registerResult = handleUpstreamEa(registerArg, loginArg, loginParamData, authHandler);
        if (registerResult != null) {
            return registerResult;
        }
        // 查询上游企业租户级+应用级已开启的自注册配置
        Result<List<LoginRegisterConfigVO>> configResult = loginRegisterConfigService.findStartedRegisterConfig(registerArg.getUpstreamEa(), registerArg.getFsAppId(), registerArg.getLoginRegisterConfigId());
        if (!configResult.isSuccess() || CollectionUtils.isEmpty(configResult.getData())) {
            throw new RelationException(ResultCode.NOT_OPEN_REGISTER_SWITCH.getErrorCode(), I18nUtil.get(I18nKeyEnum.EPI_AUTH_ERUNIONREGISTERCONTROLLER_799));
        }
        List<LoginRegisterConfigVO> appRegisterConfig = configResult.getData();
        // 检测用户当前可注册的账号类型
        registerResult = handleIdentityType(registerArg, appRegisterConfig);
        if (registerResult != null) {
            return registerResult;
        }
        if (loginUser != null && registerArg.getIdentityType() == null) {
            log.info("没传identityType- loginUser:{}", loginUser);//ignoreI18n
            registerArg.setIdentityType(loginUser.getIdentityType());
        }
        // 确认identityType
        LinkAppData linkAppData = null;
        if (!Strings.isNullOrEmpty(registerArg.getFsAppId())) {
            linkAppData = linkAppService.getLinkApp(registerArg.getFsAppId()).getData();
        }
        // 确认mapperApiName
        LoginRegisterConfigVO fsAppStartLoginRegisterConfigVO = handleMapperApiName(registerArg, linkAppData, appRegisterConfig);
        // 处理指定outerTenantId或mapperObjectId的场景
        registerResult = handleDownstreamOuterTenantId(registerArg);
        if (registerResult != null) {
            return registerResult;
        }

        // 确认登录注册配置表
        LoginRegisterConfigVO loginRegisterConfigVO = handleLoginRegisterConfig(registerArg, fsAppStartLoginRegisterConfigVO);

        // 不同的authType支持的登录注册配置选项列表不同
        List<LoginRegisterOptionEnum> loginRegisterOptionEnums = loginRegisterConfigVO.getLoginRegisterOptionValues().stream().map(LoginRegisterOptionEnum::getByCode).collect(Collectors.toList());
        List<LoginRegisterOptionEnum> supportLoginRegisterOptions = authHandler.getSupportLoginRegisterOptions(loginRegisterOptionEnums);

        // 账号完善
        boolean isImproveAccount = !StringUtils.isEmpty(registerArg.getMustErAccountData()) || !StringUtils.isEmpty(loginParamData.getMustErAccountData());
        if (isImproveAccount) {
            registerResult = handleImproveErAccount(registerArg, loginParamData, loginArg, loginUser, authHandler, linkAppData, supportLoginRegisterOptions, loginRegisterConfigVO);
            if (registerResult != null) {
                return registerResult;
            }
        }

        // 优先业务员注册
        // 根据实际参数匹配支持的登录注册配置列表
        List<LoginRegisterOptionEnum> actualSupportLoginRegisterOptions = handleActualSupportLoginRegisterOptions(registerArg, loginParamData, supportLoginRegisterOptions);
        // 匹配自注册配置
        LoginRegisterOptionEnum matchSelfLoginRegisterOption = loginRegisterConfigService.matchSelfLoginRegisterOption(actualSupportLoginRegisterOptions);
        // 匹配业务员注册配置
        LoginRegisterOptionEnum matchCrmLoginRegisterOption = loginRegisterConfigService.matchCrmLoginRegisterOption(actualSupportLoginRegisterOptions);
        boolean isCrmRegister = matchSelfLoginRegisterOption != LoginRegisterOptionEnum.OPENID && matchCrmLoginRegisterOption != null;
        if (isCrmRegister) {
            RegisterHandler loginRegisterConfigHandler = loginRegisterConfigHandlerMapping.findMatchHandler(matchCrmLoginRegisterOption.getCode());
            registerResult = loginRegisterConfigHandler.doRegister(registerArg, loginArg, loginUser, loginParamData, authHandler, loginRegisterConfigVO);
            if (registerResult != null) {
                return registerResult;
            }
        }

        // 自注册
        boolean isSelfRegister = matchSelfLoginRegisterOption != null;
        if (isSelfRegister) {
            RegisterHandler loginRegisterConfigHandler = loginRegisterConfigHandlerMapping.findMatchHandler(matchSelfLoginRegisterOption.getCode());
            registerResult = loginRegisterConfigHandler.doRegister(registerArg, loginArg, loginUser, loginParamData, authHandler, loginRegisterConfigVO);
            if (registerResult != null) {
                return registerResult;
            }
        }

        // 异常
        LoginAccountData loginAccountData = loginParamData.getLoginAccountData();
        String mobile = loginAccountData == null ? "" : loginAccountData.getLoginAccount();
        if (isImproveAccount) {
            String msg = I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_IMPROVE_ACCOUNT_PARAM_ERROR);// TODO 文案
            throw new RelationException(ResultCode.IMPROVE_ACCOUNT_PARAM_ERROR.getErrorCode(), String.format(msg, mobile));
        }
        if (isCrmRegister && !isSelfRegister) {
            String msg = I18nUtil.get(I18nKeyEnum.EPI_AUTH_ERUNIONREGISTERCONTROLLER_399);
            throw new RelationException(ResultCode.CONTACT_FIND_BY_MOBILE_NOT_EXISTS.getErrorCode(), String.format(msg, mobile));
        }
        if (!isCrmRegister && isSelfRegister) {
            String msg = I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_APP_REGISTER_PARAM_UNREASONABLE);
            throw new RelationException(ResultCode.REGISTER_CONFIG_PARAM_UNREASONABLE.getErrorCode(), msg);
        }
        if (!isCrmRegister && !isSelfRegister) {
            String msg = I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_OPEN_REGISTER_CONFIG_PLEASE);
            throw new RelationException(ResultCode.OPEN_REGISTER_CONFIG_PLEASE.getErrorCode(), msg);
        }
        String msg = I18nUtil.get("eip.erbiz.not.match.contact.phone.number.and.app.register.param.unreasonable", I18nUtil.get(I18nKeyEnum.EPI_AUTH_ERUNIONREGISTERCONTROLLER_409));
        throw new RelationException(ResultCode.MOBILE_NOT_MATCH_CONTACT.getErrorCode(), String.format(msg, mobile));
    }

    public List<LoginRegisterOptionEnum> handleActualSupportLoginRegisterOptions(RegisterArg registerArg, LoginParamData loginParamData, List<LoginRegisterOptionEnum> supportLoginRegisterOptions) {
        LoginRegisterOptionEnum matchSelfLoginRegisterOption = loginRegisterConfigService.matchSelfLoginRegisterOption(supportLoginRegisterOptions);// 匹配自注册配置
        if (matchSelfLoginRegisterOption == LoginRegisterOptionEnum.OPENID) {
            if (!StringUtils.isEmpty(registerArg.getMapperObjectId()) || registerArg.getDownstreamOuterTenantId() != null) {
                supportLoginRegisterOptions.removeAll(Lists.newArrayList(LoginRegisterOptionEnum.OPENID, LoginRegisterOptionEnum.PHONE_OR_EMAIL));
                return supportLoginRegisterOptions;
            }
            if (loginParamData.getLoginAccountData() != null && !StringUtils.isEmpty(loginParamData.getLoginAccountData().getLoginAccount())) {
                supportLoginRegisterOptions.removeAll(Lists.newArrayList(LoginRegisterOptionEnum.OPENID));
                return supportLoginRegisterOptions;
            }
        }
        if (matchSelfLoginRegisterOption == LoginRegisterOptionEnum.PHONE_OR_EMAIL) {
            if (!StringUtils.isEmpty(registerArg.getMapperObjectId()) || registerArg.getDownstreamOuterTenantId() != null) {
                supportLoginRegisterOptions.remove(LoginRegisterOptionEnum.PHONE_OR_EMAIL);
                return supportLoginRegisterOptions;
            }
        }
        return supportLoginRegisterOptions;
    }

    private RegisterResult handleImproveErAccount(RegisterArg registerArg, LoginParamData loginParamData, LoginArg loginArg, LoginEmployeeInfo loginUser, AuthHandler authHandler,
        LinkAppData linkAppData, List<LoginRegisterOptionEnum> supportLoginRegisterOptions, LoginRegisterConfigVO loginRegisterConfigVO) {
        if (!StringUtils.isEmpty(registerArg.getMustErAccountData()) || !StringUtils.isEmpty(loginParamData.getMustErAccountData())) {
            // mustErAccountData = "mobile,login_email,mapper_account_id,mapper_partner_id" 根据当前应用支持的对象判断需要完善那个对象
            if (StringUtils.isEmpty(registerArg.getMustErAccountData())) {
                registerArg.setMustErAccountData(loginParamData.getMustErAccountData());
            }
            RegisterArg registerArgCopy = BeanUtil.deepCopy(registerArg, RegisterArg.class);
            LoginParamData loginParamDataCopy = BeanUtil.deepCopy(loginParamData, LoginParamData.class);
            String[] mustErAccountDataArr = registerArgCopy.getMustErAccountData().split(",");
            List<String> mustErAccountDataList = Arrays.stream(mustErAccountDataArr).collect(Collectors.toList());
            String firstMustErAccountData = mustErAccountDataList.get(0);
            if (StringUtils.isEmpty(firstMustErAccountData)) {
                return RegisterResult.success();
            }
            String upstreamEa = getUpstreamEa(registerArg, loginParamData, loginUser);
            Long outerTenantId = getOuterTenantId(registerArg, loginParamData, loginUser);
            if (StringUtils.isEmpty(upstreamEa) || outerTenantId == null) {
                throw new RelationException(ResultCode.IMPROVE_ACCOUNT_PARAM_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnum.EPI_REGISTER_REGISTERADAPTER_160));
            }
            firstMustErAccountData = checkFistMustErAccountData(upstreamEa, outerTenantId, firstMustErAccountData, linkAppData, registerArgCopy, mustErAccountDataList);
            if (StringUtils.isEmpty(firstMustErAccountData)) {
                return RegisterResult.success();
            }
            LoginRegisterOptionEnum improveAccountLoginRegisterOption = loginRegisterConfigService.matchImproveAccountLoginRegisterOption(firstMustErAccountData);
            if (improveAccountLoginRegisterOption == null || !supportLoginRegisterOptions.contains(improveAccountLoginRegisterOption)) {
                if (improveAccountLoginRegisterOption == null || supportLoginRegisterOptions.size() == 0) {
                    throw new RelationException(ResultCode.INCOMPLETE_ACCOUNT_INFO_NOT_MATCH_REGISTER_CONFIG.getErrorCode(),
                            String.format(I18nUtil.get(I18nKeyEnum.EPI_AUTH_ERUNIONREGISTERCONTROLLER_579), registerArgCopy.getMustErAccountData()));
                } else if (!mustErAccountDataList.contains(MustErAccountDataEnum.MAPPER_ACCOUNT_ID.getType()) &&
                        !mustErAccountDataList.contains(MustErAccountDataEnum.MAPPER_PARTNER_ID.getType()) &&
                        supportLoginRegisterOptions.stream().filter(x -> x.isSupportAccountOrPartnerObj()).count() > 0) {
                    // 当前端通知要完善手机号码或邮箱，但是自注册配置只开启了标准账号配置，则需要用户即完善手机号又完善客户信息
                    // 追加完善客户/合作信息
                    mustErAccountDataList.add(mustErAccountDataList.size(), MustErAccountDataEnum.MAPPER_ACCOUNT_ID.getType());
                    mustErAccountDataList.add(mustErAccountDataList.size(), MustErAccountDataEnum.MAPPER_PARTNER_ID.getType());
                }
            }
            RegisterHandler loginRegisterConfigHandler = loginRegisterConfigHandlerMapping.findMatchHandler(improveAccountLoginRegisterOption.getCode());
            RegisterResult registerResult = loginRegisterConfigHandler.doRegister(registerArgCopy, loginArg, loginUser, loginParamDataCopy, authHandler, loginRegisterConfigVO);
            if (registerResult != null) {
                if (Objects.equals(registerResult.getStatus(), RegisterStatusEnum.REGISTER_SUCCESS.getStatus())) {
                    // 移除已完善的数据标识
                    mustErAccountDataList.remove(firstMustErAccountData);
                    if (CollectionUtils.isNotEmpty(mustErAccountDataList) && mustErAccountDataList.size() > 0) {
                        // 下一步继续完善其它信息
                        String newMustErAccountData = String.join(",", mustErAccountDataList);
                        registerArgCopy.setMustErAccountData(newMustErAccountData);
                        loginParamDataCopy.setMustErAccountData(newMustErAccountData);
                        registerResult = handleImproveErAccount(registerArgCopy, loginParamDataCopy, loginArg, loginUser, authHandler, linkAppData, supportLoginRegisterOptions, loginRegisterConfigVO);
                    }
                } else if (RegisterStatusEnum.UNBIND_PHONE.getStatus().equals(registerResult.getStatus()) && !mustErAccountDataList.contains(MustErAccountDataEnum.MOBILE.getType())) {
                    // 追加需要完善手机号码标识
                    mustErAccountDataList.add(0, MustErAccountDataEnum.MOBILE.getType());
                    String newMustErAccountData = String.join(",", mustErAccountDataList);
                    registerArg.setMustErAccountData(newMustErAccountData);
                    loginParamData.setMustErAccountData(newMustErAccountData);
                    registerResult.setMustErAccountData(newMustErAccountData);
                } else if (RegisterStatusEnum.MERGE_ACCOUNT_LOGOUT.getStatus().equals(registerResult.getStatus())) {
                    registerArg.setMustErAccountData(null);
                    loginParamData.setMustErAccountData(null);
                    registerResult.setMustErAccountData(null);
                } else {
                    String oldMustErAccountData = String.join(",", mustErAccountDataList);
                    registerArg.setMustErAccountData(oldMustErAccountData);
                    loginParamData.setMustErAccountData(oldMustErAccountData);
                    registerResult.setMustErAccountData(oldMustErAccountData);
                }
                return registerResult;
            }
        }
        return null;
    }

    private String getUpstreamEa(RegisterArg registerArg, LoginParamData loginParamData, LoginEmployeeInfo loginUser) {
        String upstreamEa = null;
        if (registerArg != null) {
            upstreamEa = registerArg.getUpstreamEa();
        }
        if (StringUtils.isEmpty(upstreamEa) && loginParamData != null) {
            upstreamEa = loginParamData.getUpstreamEa();
        }
        if (StringUtils.isEmpty(upstreamEa) && loginUser != null) {
            upstreamEa = loginUser.getUpstreamEa();
        }
        return upstreamEa;
    }

    private Long getOuterTenantId(RegisterArg registerArg, LoginParamData loginParamData, LoginEmployeeInfo loginUser) {
        Long outerTenantId = null;
        if (registerArg != null) {
            outerTenantId = registerArg.getDownstreamOuterTenantId();
        }
        if (outerTenantId == null && loginParamData != null) {
            outerTenantId = loginParamData.getDownstreamOuterTenantId();
        }
        if (outerTenantId == null && loginUser != null) {
            outerTenantId = loginUser.getDownstreamOuterTenantId();
        }
        return outerTenantId;
    }

    private String checkFistMustErAccountData(String upstreamEa, Long outerTenantId, String firstMustErAccountData, LinkAppData linkAppData, RegisterArg registerArgCopy,
        List<String> mustErAccountDataList) {
        if (firstMustErAccountData.equals(MustErAccountDataEnum.MAPPER_ACCOUNT_ID.getType()) || firstMustErAccountData.equals(MustErAccountDataEnum.MAPPER_PARTNER_ID.getType())) {
            // 如果应用支持1个对象，则完善该对象
            Set<String> supportMapperApiNames = linkAppData.listSupportMapperApiNames();
            EnterpriseRelationObj enterpriseObj = enterpriseRelationManager.getEnterpriseRelationObjById(upstreamEa, outerTenantId);
            if (enterpriseObj == null) {
                throw new RelationException(ResultCode.IMPROVE_ACCOUNT_PARAM_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnum.EPI_REGISTER_REGISTERADAPTER_160));
            }
            Boolean hasMapperAccountObj = StringUtils.isNotEmpty(enterpriseObj.getMapperAccountId());
            Boolean hasMapperPartnerObj = StringUtils.isNotEmpty(enterpriseObj.getMapperPartnerId());
            if (hasMapperAccountObj && hasMapperPartnerObj) {
                return null;
            }

            String supportErAccountData = firstMustErAccountData;
            if (supportMapperApiNames.size() == 1) {
                String supportMapperApiName = supportMapperApiNames.iterator().next();
                if (AccountFieldContants.API_NAME.equals(supportMapperApiName)) {
                    supportErAccountData = MustErAccountDataEnum.MAPPER_ACCOUNT_ID.getType();
                    registerArgCopy.setMustErAccountData(MustErAccountDataEnum.MAPPER_ACCOUNT_ID.getType());
                    registerArgCopy.setMapperApiName(AccountFieldContants.API_NAME);
                } else if (PartnerFieldContants.API_NAME.equals(supportMapperApiName)) {
                    supportErAccountData = MustErAccountDataEnum.MAPPER_PARTNER_ID.getType();
                    registerArgCopy.setMustErAccountData(MustErAccountDataEnum.MAPPER_PARTNER_ID.getType());
                    registerArgCopy.setMapperApiName(PartnerFieldContants.API_NAME);
                }
            } else if (supportMapperApiNames.size() > 1) {
                if (!hasMapperPartnerObj && supportMapperApiNames.contains(PartnerFieldContants.API_NAME) && firstMustErAccountData.equals(MustErAccountDataEnum.MAPPER_PARTNER_ID.getType())) {
                    registerArgCopy.setMustErAccountData(MustErAccountDataEnum.MAPPER_PARTNER_ID.getType());
                    registerArgCopy.setMapperApiName(PartnerFieldContants.API_NAME);
                }
                if (!hasMapperAccountObj && supportMapperApiNames.contains(AccountFieldContants.API_NAME) && firstMustErAccountData.equals(MustErAccountDataEnum.MAPPER_ACCOUNT_ID.getType())) {
                    registerArgCopy.setMustErAccountData(MustErAccountDataEnum.MAPPER_ACCOUNT_ID.getType());
                    registerArgCopy.setMapperApiName(AccountFieldContants.API_NAME);
                }
            } else {
                // 优先客户
                if (firstMustErAccountData.equals(MustErAccountDataEnum.MAPPER_PARTNER_ID.getType())) {
                    registerArgCopy.setMustErAccountData(MustErAccountDataEnum.MAPPER_PARTNER_ID.getType());
                    registerArgCopy.setMapperApiName(PartnerFieldContants.API_NAME);
                }
                if (firstMustErAccountData.equals(MustErAccountDataEnum.MAPPER_ACCOUNT_ID.getType())) {
                    registerArgCopy.setMustErAccountData(MustErAccountDataEnum.MAPPER_ACCOUNT_ID.getType());
                    registerArgCopy.setMapperApiName(AccountFieldContants.API_NAME);
                }
            }
            // 应用支持的对应类型与前端传的类型不一致时
            if (!firstMustErAccountData.equals(supportErAccountData)) {
                mustErAccountDataList.remove(firstMustErAccountData);
                firstMustErAccountData = supportErAccountData;
            }
        }
        return firstMustErAccountData;
    }

    private void checkAuthType(RegisterArg arg, LoginParamData loginParamData) {
        if (arg.getAuthType() == null) {
            String msg = "authType is null";
            log.error("doRegister fail,authType is null！ registerArg={} ，loginParamData={}", arg, loginParamData);
            throw new RelationException(ResultCode.PARAMS_ERROR.getErrorCode(), msg);
        }
    }

    private LoginRegisterConfigVO handleMapperApiName(RegisterArg arg, LinkAppData linkAppData, List<LoginRegisterConfigVO> appRegisterConfig) {
        // 应用不存在 或 应用不支持的关联对象 默认取客户对象
        if (linkAppData == null || portalAppId.equals(linkAppData.getId()) || CollectionUtils.isEmpty(linkAppData.listSupportMapperApiNames())) {
            if (StringUtils.isEmpty(arg.getMapperApiName())) {
                arg.setMapperApiName(AccountFieldContants.API_NAME);
            }
            return null;
        }
        // 应用只支持1个关联对象则直接取该对象
        if (appRegisterConfig.size() == 1) {
            LoginRegisterConfigVO appRegisterConfigVO = appRegisterConfig.get(0);
            arg.setMapperApiName(appRegisterConfigVO.getMapperObjectApiName());
            return appRegisterConfigVO;
        }
        // 应用支持多个关联对像
        if (linkAppData.listSupportMapperApiNames().size() > 1 && arg.getDownstreamOuterTenantId() == null) {
             // 如果应用启用登录注册配置，则匹配该配置关联的对象
            Set<String> appSupportMapperApiNames = linkAppData.listSupportMapperApiNames();
            // 没开启登录注册配置，设置默认关联对象参数 或 检查应用是否支持参数指定的关联对象
            if (CollectionUtils.isEmpty(appRegisterConfig)) {
                if (StringUtils.isEmpty(arg.getMapperApiName())) {
                    String mapperApiName = appSupportMapperApiNames.iterator().next();// 既不是客户又不是合作伙伴默认取第一个 或 者让用户选则？
                    arg.setMapperApiName(mapperApiName);
                    if (appSupportMapperApiNames.contains(PartnerFieldContants.API_NAME)) {
                        arg.setMapperApiName(PartnerFieldContants.API_NAME);
                    }
                    if (appSupportMapperApiNames.contains(AccountFieldContants.API_NAME)) {
                        arg.setMapperApiName(AccountFieldContants.API_NAME);
                    }
                    return null;
                }
                if (!appSupportMapperApiNames.contains(arg.getMapperApiName())) {
                    throw new RelationException(ResultCode.APP_NOT_SUPPORT_REGISTER_OBJECT.getErrorCode(), String.format(I18nUtil.get(I18nKeyEnum.EPI_AUTH_ERUNIONREGISTERCONTROLLER_636), arg.getMapperApiName()));
                }
                return null;
            } else {
                // 通过开启的登录注册配置查询到对象
                for (String supportMapperApiName : appSupportMapperApiNames) {
                    LoginRegisterConfigVO configVO = loginRegisterConfigService.doMatchLoginRegisterConfig(appRegisterConfig, arg.getIdentityType(), supportMapperApiName);
                    if (configVO != null) {
                        arg.setMapperApiName(supportMapperApiName);
                        return configVO;
                    }
                }
            }
        }
        if (!StringUtils.isEmpty(arg.getMapperApiName()) && linkAppData != null && !linkAppData.listSupportMapperApiNames().contains(arg.getMapperApiName())) {
            throw new RelationException(ResultCode.APP_NOT_SUPPORT_REGISTER_OBJECT.getErrorCode(), String.format(I18nUtil.get(I18nKeyEnum.EPI_AUTH_ERUNIONREGISTERCONTROLLER_636), arg.getMapperApiName()));
        }
        return null;
    }

    private RegisterResult handleUpstreamEa(RegisterArg arg, LoginArg loginArg, LoginParamData loginParamData, AuthHandler authHandler) {
        RegisterResult res = new RegisterResult();
        String upstreamEa = arg.getUpstreamEa();
        if (Strings.isNullOrEmpty(upstreamEa)) {
            upstreamEa = authHandler.getUpstreamEa(loginArg, loginParamData);
        }
        if (Strings.isNullOrEmpty(upstreamEa)) {
            res.setStatus(RegisterStatusEnum.NO_UPSTREAM.getStatus());
            return res;
        }
        try {
            Integer upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
            RequestContext.set(new RequestContext(upstreamEa, upstreamEi));
        } catch (EIEANotFoundException e) {
            throw new RelationException(ResultCode.PARAM_EA_NOT_VALID.getErrorCode(), I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_EA_ILLEGAL));
        }
        arg.setUpstreamEa(upstreamEa);
        return null;
    }

    private LoginEmployeeInfo parseExitsErCookie(LoginParamData loginParamData, HttpServletRequest request) {
        LoginEmployeeInfo erCookie = getERCookie(request);
        if (erCookie == null) {
            return null;
        }
        loginParamData.setUpstreamEa(erCookie.getUpstreamEa());
        loginParamData.setDownstreamOuterUid(erCookie.getDownstreamOuterUid());
        loginParamData.setDownstreamOuterTenantId(erCookie.getDownstreamOuterTenantId());
        loginParamData.setOpenId(erCookie.getOpenId());
        loginParamData.setWxAppId(erCookie.getWxAppId());
        return erCookie;
    }

    private RegisterResult handleDownstreamOuterTenantId(RegisterArg registerArg) {
        if (registerArg.getDownstreamOuterTenantId() != null) {
            String mapperObjectId = null;
            EnterpriseRelationObj enterpriseRelationResult = enterpriseRelationManager.getEnterpriseRelationObjById(registerArg.getUpstreamEa(), registerArg.getDownstreamOuterTenantId());
            // 不是互联企业则不能自注册
            if (enterpriseRelationResult == null || !EnterpriseRelationType.isRelation(enterpriseRelationResult.getRelationType())) {
                String msg = I18nUtil.get(LoginErrorI18nKeyConstant.NO_ENTERPRISE_RELATION_OBJ_DATA, "提示：不是互联企业，不能进行自注册,上游企业帐号：" + registerArg.getUpstreamEa());//ignoreI18n
                throw new UnionLoginException(ResultCode.LOGIN_VALIDATE_ERROR.getErrorCode(), msg);
            }
            if (registerArg.getIdentityType() != null && enterpriseRelationResult.getIdentityType() != null && !enterpriseRelationResult.getIdentityType().equals(registerArg.getIdentityType())) {
                String msg = I18nUtil.get(LoginErrorI18nKeyConstant.REGISTER_TYPE_NOT_OPEN, "提示：应用选择有误或者对应账号类型自注册配置未开启,上游企业帐号：" + registerArg.getUpstreamEa());//ignoreI18n
                throw new UnionLoginException(ResultCode.LOGIN_VALIDATE_ERROR.getErrorCode(), msg);
            }
            // 下游有租户则不允许自注册
            if (enterpriseRelationResult.getEnterpriseAccount() != null) {
                String message = I18nUtil.get(HAS_FS_ACCOUNT_NOT_LOGIN_BY_WXSERVICE, "提示：您注册的企业【${enterpriseName}（${ea}）】已经开通了纷享销客CRM，请联系企业管理员直接在系统中给您开通帐号。");//ignoreI18n
                message = TemplateUtil.format(message, enterpriseRelationResult.getName(), enterpriseRelationResult.getEnterpriseAccount());
                throw new UnionLoginException(ResultCode.LOGIN_VALIDATE_ERROR.getErrorCode(), message);
            }
            // 身份类型取指定互联企业的
            registerArg.setIdentityType(enterpriseRelationResult.getIdentityType());
            /**
             * TODO :数据库存在为空的脏数据，enterpriseRelationResult刷库(将空的identityType刷为企业类型)
             */
            if (registerArg.getIdentityType() == null) {
                registerArg.setIdentityType(IdentityType.CORP.getType());
            }
            // 当合作伙伴和客户都同时绑定的时候优先客户
            if (registerArg.getMapperApiName() == null) {
                if (StringUtils.isNotEmpty(enterpriseRelationResult.getMapperPartnerId())) {
                    mapperObjectId = enterpriseRelationResult.getMapperPartnerId();
                    registerArg.setMapperApiName(MapperApiNameEnum.PARTNER_OBJ.getType());
                }
                if (StringUtils.isNotEmpty(enterpriseRelationResult.getMapperAccountId())) {
                    mapperObjectId = enterpriseRelationResult.getMapperAccountId();
                    registerArg.setMapperApiName(MapperApiNameEnum.ACCOUNT_OBJ.getType());
                }
            } else {
                if (registerArg.getMapperApiName().equals(MapperApiNameEnum.PARTNER_OBJ.getType())) {
                    mapperObjectId = enterpriseRelationResult.getMapperPartnerId();
                }
                if (registerArg.getMapperApiName().equals(MapperApiNameEnum.ACCOUNT_OBJ.getType())) {
                    mapperObjectId = enterpriseRelationResult.getMapperAccountId();
                }
            }
            // 同时指定mapperObjectId和downstreamOuterTenantId时两者必须关联
            if (!Strings.isNullOrEmpty(registerArg.getMapperObjectId()) && !registerArg.getMapperObjectId().equals(mapperObjectId)) {
                String msg = I18nUtil.get(LoginErrorI18nKeyConstant.OBJECTID_NOT_MATCH_DOWNSTREANOUTTENANTID, "登录参数有误，指定登录的对象ID和下游企业ID不匹配");//ignoreI18n
                throw new UnionLoginException(ResultCode.LOGIN_VALIDATE_ERROR.getErrorCode(), msg);
            }
            registerArg.setMapperObjectId(mapperObjectId);
        }
        return null;
    }

    private RegisterResult handleIdentityType(RegisterArg arg, List<LoginRegisterConfigVO> appRegisterConfig) {
        if (CollectionUtils.isEmpty(appRegisterConfig)) {
            return null;
        }
        Set<Integer> supportIdentityTypeSet = appRegisterConfig.stream().map(x -> x.getIdentityType()).collect(Collectors.toSet());

        // 应用仅支持一种身份类型：校验指定identityType是否与应用支持的相匹配
        if (supportIdentityTypeSet.size() == 1) {
            Integer appSupportIdentityType = supportIdentityTypeSet.iterator().next();
            arg.setIdentityType(appSupportIdentityType);
            arg.setLoginRegisterConfigId(appRegisterConfig.get(0).getId());
            return null;
        } else if (supportIdentityTypeSet.size() > 1) {
            if (arg.getIdentityType() != null) {
                // 匹配用户选择的账号类型自注册配置ID
                List<LoginRegisterConfigVO> findAppRegisterConfig = appRegisterConfig.stream().filter(x -> x.getIdentityType().equals(arg.getIdentityType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(findAppRegisterConfig)) {
                    arg.setLoginRegisterConfigId(findAppRegisterConfig.get(0).getId());
                    return null;
                }
            }
            // 应用支持多种身份类型：让用户选择
            List<Map<String, Object>> datas = Lists.newArrayList();
            for (IdentityType value : IdentityType.values()) {
                if (supportIdentityTypeSet.contains(value.getType())) {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("name", value.getDescription());
                    map.put("identityType", value.getType());
                    datas.add(map);
                }
            }
            if (CollectionUtils.isNotEmpty(datas)) {
                RegisterResult result = new RegisterResult();
                result.setData(datas);
                result.setStatus(RegisterStatusEnum.CHOOSE_IDENTITY_TYPE_NEW.getStatus());
                return result;
            }
        }
        return null;
    }

    private LoginRegisterConfigVO handleLoginRegisterConfig(RegisterArg registerArg, LoginRegisterConfigVO fsAppStartConfigVO) {
        try {
            // 优先取指定的登录注册配置
            if (!StringUtils.isEmpty(registerArg.getLoginRegisterConfigId())) {
                Result<LoginRegisterConfigVO> loginRegisterConfigResult = loginRegisterConfigService.getById(registerArg.getUpstreamEa(), registerArg.getLoginRegisterConfigId());
                if (!loginRegisterConfigResult.isSuccess()) {
                    throw new RelationException(loginRegisterConfigResult.getErrCode(), loginRegisterConfigResult.getErrMessage());
                }
                if (loginRegisterConfigResult.getData() != null) {
                    LoginRegisterConfigVO configVO = loginRegisterConfigResult.getData();
                    registerArg.setIdentityType(configVO.getIdentityType());
                    registerArg.setMapperApiName(configVO.getMapperObjectApiName());
                    configVO.getLoginRegisterOptionValues().sort((o1, o2) -> o2 - o1);
                    return configVO;
                }
            }
            // 当前应用开通的登录注册配置
            if (fsAppStartConfigVO != null) {
                return fsAppStartConfigVO;
            }
        } catch (Exception e) {
            log.warn("getChannelWebLoginTemplateConfig error, registerArg={}", registerArg, e);
        }
        // 匹配适合的登录注册配置
        LoginRegisterConfigVO loginRegisterConfig = loginRegisterConfigService
            .matchLoginRegisterConfig(registerArg.getUpstreamEa(), registerArg.getFsAppId(), registerArg.getIdentityType(), registerArg.getMapperApiName(), registerArg.getLoginRegisterConfigId());
        if (loginRegisterConfig == null) {
            throw new RelationException(ResultCode.NOT_OPEN_REGISTER_SWITCH.getErrorCode(), I18nUtil.get(I18nKeyEnum.EPI_AUTH_ERUNIONREGISTERCONTROLLER_799));
        }
        return loginRegisterConfig;
    }

    private LoginArg toLoginArg(RegisterArg arg, LoginParamData loginParamData) {
        LoginArg loginArg = new LoginArg();
        loginArg.setFsAppId(arg.getFsAppId());
        loginArg.setAuthType(arg.getAuthType());
        loginArg.setIsGuestor(false);
        loginArg.setUpstreamEa(arg.getUpstreamEa());
        loginArg.setDownstreamOuterTenantId(arg.getDownstreamOuterTenantId());
        loginArg.setLoginIdentityType(arg.getIdentityType());
        loginArg.setMapperApiName(arg.getMapperApiName());
        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setWxAppId(loginParamData.getWxAppId());
        loginArg.setThirdArg(thirdArg);
        return loginArg;
    }

    /**
     * 通过前端提交表单数据创建互联账号
     */
    private Result<OuterAccountVo> createEnterpriseAccountFromCrmData(RegisterArg arg, String upstreamEa, LoginParamData loginParamData) {
        // 前面已经校验一定是手机号
        LoginAccountData loginAccountData = loginParamData.getLoginAccountData();
        String loginAccount = loginAccountData.getLoginAccount();
        RegisterERDownstreamArg registerERDownstreamArg = new RegisterERDownstreamArg();
        Map<String, Object> contactDataMap = arg.getRegisterDownstreamCrmArg().getContactDataMap();
        String contactId = (String) contactDataMap.get("_id");
        String contactName = (String) contactDataMap.get(ContactFieldContants.NAME);

        // 与mapperApiName指定的对象一致（客户或合作伙伴ID）
        Map<String, Object> mapperObjectDataMap = arg.getRegisterDownstreamCrmArg().getMapperObjectDataMap();
        String mapperId = (String) mapperObjectDataMap.get("_id");
        String mapperApiName = arg.getMapperApiName();
        boolean isMapperAccountObj = mapperApiName.equals(MapperApiNameEnum.ACCOUNT_OBJ.getType());
        String mapperObjectName = (String) mapperObjectDataMap.get(isMapperAccountObj ? AccountFieldContants.NAME : PartnerFieldContants.NAME);

        try {
            Integer upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
            // 校验配额是否足够
            ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(upstreamTenantId + "", SuperUserConstants.USER_ID + "");
            ValidPublicEmployeeLicenseQuoteArg validPublicEmployeeLicenseQuoteArg = new ValidPublicEmployeeLicenseQuoteArg();
            if (arg.getIdentityType() == null) {
                return Result.newError(ResultCode.LOGIN_IDENTITY_ERROR);
            }
            // 客户id 无 联系人id 无 只需要校验客户参数
            if (contactId == null && Strings.isNullOrEmpty(mapperId)) {
                validPublicEmployeeLicenseQuoteArg.setEnterpriseRelationObjName(mapperObjectName);
                validPublicEmployeeLicenseQuoteArg.setEnterpriseRelationObjRecordType(EnterpriseRelationObjConstant.toRecordType(arg.getIdentityType()));
            }
            // 客户id 有 联系人id 无 只需要校验联系人参数
            if (contactId == null && StringUtils.isNotBlank(mapperId)) {
                validPublicEmployeeLicenseQuoteArg.setMapperApiName(arg.getMapperApiName());
                validPublicEmployeeLicenseQuoteArg.setMapperObjectId(mapperId);

                validPublicEmployeeLicenseQuoteArg.setPublicEmployeeObjName(contactName);
                if (LoginAccountTypeEnum.isMobile(loginAccountData.getAccountType())) {
                    validPublicEmployeeLicenseQuoteArg.setPublicEmployeeObjMobile(loginAccount);
                } else {
                    validPublicEmployeeLicenseQuoteArg.setPublicEmployeeObjEmail(loginAccount);
                }
                validPublicEmployeeLicenseQuoteArg.setPublicEmployeeObjRecordType(EnterpriseRelationObjConstant.toRecordType(arg.getIdentityType()));
            }

            validPublicEmployeeLicenseQuoteArg.setPublicEmployeeIdentityType(arg.getIdentityType());
            ValidPublicEmployeeLicenseQuoteResult validPublicEmployeeLicenseQuoteRes = publicEmployeeObjService.validPublicEmployeeLicenseQuote(serviceContext, validPublicEmployeeLicenseQuoteArg);
            if (!BooleanUtils.isTrue(validPublicEmployeeLicenseQuoteRes.getValid())) {
                return Result.newError(validPublicEmployeeLicenseQuoteRes.getErrorCode(), validPublicEmployeeLicenseQuoteRes.getErrorMessage());
            }
            String registerOwner = "";
            // 1、前端未传关联的联系人的ID
            if (contactId == null) {
                try {
                    // 1.1、自注册进来，表单填写的CRM数据,支持传关联对象_id(传_id必须传名称)
                    Result<ERAccountActiveConfigData> configResult = erAccountActiveConfigManager.getERAccountActiveConfig(upstreamEa, arg.getIdentityType(), mapperApiName);
                    if (Strings.isNullOrEmpty(configResult.getData().getAccountRecordType()) || CollectionUtils.isEmpty(configResult.getData().getOwners())) {
                        return Result.newError(ResultCode.ACCOUNT_ACTIVE_SET_ILLEGA);
                    }
                    registerOwner = selectRightOwner(arg.getUpstreamEa(), configResult.getData());
                    // 关联客户、合作伙伴ID为空则新注册
                    if (Strings.isNullOrEmpty(mapperId)) {
                        String lockKeyCustomer = "createAccountLock:" + upstreamEa + ":" + mapperObjectName;
                        try {
                            mapperObjectDataMap.put(isMapperAccountObj ? AccountFieldContants.RECORD_TYPE : PartnerFieldContants.RECORD_TYPE, configResult.getData().getAccountRecordType());
                            if (LoginAccountTypeEnum.isMobile(loginAccountData.getAccountType())) {
                                mapperObjectDataMap.put(isMapperAccountObj ? AccountFieldContants.TEL : PartnerFieldContants.TEL, loginAccount);
                            } else {
                                mapperObjectDataMap.put(isMapperAccountObj ? AccountFieldContants.EMAIL : PartnerFieldContants.EMAIL, loginAccount);
                            }
                            mapperObjectDataMap.put(isMapperAccountObj ? AccountFieldContants.OWNER : PartnerFieldContants.OWNER, Lists.newArrayList(registerOwner)); // 客户、合作伙伴负责人
                            // 合作伙伴自注册增加注册来源字段 @孙得育
                            if (PartnerFieldContants.API_NAME.equals(arg.getMapperApiName())) {
                                if (arg.getRegisterDownstreamCrmArg().getMapperObjectDataMap() != null) {
                                    mapperObjectDataMap.put("resources", "self_reg");
                                }
                            }
                            // 新创建客户、合作伙伴
                            boolean lockCustomerSuccess = distributeLock.lockWeakly(lockKeyCustomer, 30, TimeUnit.SECONDS);
                            StringBuilder sb = getRepeatErrorMsg(upstreamEa, mapperId, mapperApiName, mapperObjectName);
                            if (!lockCustomerSuccess) {
                                return Result.newError(ResultCode.NAME_REPEAT.getErrorCode(), sb.toString());
                            }

                            ObjectData createMapperObjectData = crmManager.saveData2Crm(arg.getUpstreamEa(), arg.getMapperApiName(), mapperObjectDataMap);

                            if (createMapperObjectData == null || createMapperObjectData.getId() == null) {
                                throw new RelationException(ResultCode.PARAMS_ERROR.getErrorCode(), "can't create account obj or partner obj");
                            }
                            mapperId = createMapperObjectData.getId();
                            mapperObjectName = createMapperObjectData.getName();
                        } catch (CrmBusinessException ex) {
                            log.error(ex.getMessage());
                            throw ex;
                        } finally {
                            distributeLock.unlock(lockKeyCustomer);
                        }
                    } else {
                        // 关联客户、合作伙伴ID不为空则直接获取客户的负责人
                        Map<String, Object> mapperObject = crmManager.getMapperObjById(upstreamEa, mapperApiName, mapperId);
                        if (mapperObject == null) {
                            return Result.newError(ResultCode.CUSTOMER_OR_PARTNER_BE_INVALIDATED.getErrorCode(), ResultCode.CUSTOMER_OR_PARTNER_BE_INVALIDATED.getDescription());
                        } else {
                            Object objectOwner = mapperObject.get(isMapperAccountObj ? AccountFieldContants.OWNER : PartnerFieldContants.OWNER);
                            if (objectOwner != null && objectOwner instanceof List && ((List) objectOwner).size() > 0) {
                                registerOwner = ((List) objectOwner).get(0).toString();
                            } else {
                                // 设置客户的负责人
                                crmManager.setMapperObjOwner(arg.getUpstreamEa(), arg.getMapperApiName(), mapperId, Integer.parseInt(registerOwner));
                            }
                            if (StringUtils.isBlank(mapperObjectName) && mapperObject.get(mapperObjectName) != null) {
                                mapperObjectName = (String) mapperObject.get(mapperObjectName);
                            }
                        }
                    }
                    // 1.2、保存联系人
                    if (LoginAccountTypeEnum.isMobile(loginAccountData.getAccountType())) {
                        contactDataMap.put(ContactFieldContants.MOBILE, loginAccount);
                        contactDataMap.put(ContactFieldContants.MOBILE1, loginAccount);
                        contactDataMap.put(ContactFieldContants.TEL, loginAccount);
                        contactDataMap.put(ContactFieldContants.TEL1, loginAccount);
                    } else {
                        contactDataMap.put(ContactFieldContants.EMAIL, loginAccount);
                    }
                    contactDataMap.put(ContactFieldContants.COMPANY, mapperObjectName);
                    if (isMapperAccountObj) {
                        contactDataMap.put(ContactFieldContants.ACCOUNT_ID, mapperId);
                    } else {
                        contactDataMap.put(ContactFieldContants.OWNED_PARTNER_ID, mapperId);
                    }
                    contactDataMap.put(ContactFieldContants.OWNER, Lists.newArrayList(registerOwner)); // 联系人负责人
                    contactDataMap.put(ContactFieldContants.RECORD_TYPE, configResult.getData().getContactRecordType());
                    // 如果是联系人的生日字段需要特殊处理
                    if (contactDataMap.containsKey("date_of_birth")) {
                        // 兼容 后期前端传的是时间格式的就不管了
                        if (contactDataMap.get("date_of_birth") instanceof Long) {
                            Double doubleDate = Double.valueOf(String.valueOf(contactDataMap.get("date_of_birth")));
                            Date date = new Date(doubleDate.longValue());
                            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            contactDataMap.put("date_of_birth", sdf.format(date));
                        }
                    }
                    String contactLockKey = getContactLockKey(upstreamEa, mapperId, loginAccount);
                    try {
                        boolean lockContactSuccess = distributeLock.lockWeakly(contactLockKey, 30, TimeUnit.SECONDS);
                        StringBuilder sb = getRepeatErrorMsg(upstreamEa, mapperId, mapperApiName, mapperObjectName);
                        if (!lockContactSuccess) {
                            return Result.newError(ResultCode.NAME_REPEAT.getErrorCode(), sb.toString());
                        }

                        ObjectData contactData = crmManager.saveData2Crm(arg.getUpstreamEa(), ContactFieldContants.API_NAME, contactDataMap);
                        contactId = contactData.getId();
                        contactName = contactData.getName();
                    } catch (CrmBusinessException ex) {
                        log.error(ex.getMessage());
                        throw ex;
                    } finally {
                        distributeLock.unlock(contactLockKey);
                    }
                } catch (CrmBusinessException ex) {
                    log.warn("saveCRMData4Register warn ", ex);
                    return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), ex.getMessage());
                }
            } else {
                // 2、前端传的关联的联系人ID则直接获取已经存在的联系人数据
                CrmContactVo contactDetail = crmManager.getContactDetailById(upstreamEa, SuperUserConstants.USER_ID, contactId);
                mapperId = isMapperAccountObj ? contactDetail.getCustomerID() : contactDetail.getPartnerID();
                registerOwner = contactDetail.getOwnerID() + "";
                if (StringUtils.isBlank(mapperObjectName)) {
                    mapperObjectName = isMapperAccountObj ? contactDetail.getCustomerName() : contactDetail.getPartnerName();
                }
                if (StringUtils.isBlank(contactName)) {
                    contactName = contactDetail.getName();
                }
                // 3、填充联系人的负责人字段
                if (contactDetail.getOwnerID() == null && !StringUtils.isEmpty(mapperId)) {
                    Map<String, Object> mapperObject = crmManager.getMapperObjById(upstreamEa, mapperApiName, mapperId);
                    Object objectOwner = mapperObject.get(isMapperAccountObj ? AccountFieldContants.OWNER : PartnerFieldContants.OWNER);
                    if (objectOwner != null && objectOwner instanceof List && ((List) objectOwner).size() > 0) {
                        registerOwner = ((List) objectOwner).get(0).toString();
                        // 设置客户的负责人
                        crmManager.setMapperObjOwner(arg.getUpstreamEa(), arg.getMapperApiName(), contactId, Integer.parseInt(registerOwner));
                    }
                }
            }
            if (contactName == null || contactId == null || mapperId == null || mapperObjectName == null) {
                log.warn("contactName={},contactId={},mapperId={},mapperObjectName={}", contactName, contactId, mapperId, mapperObjectName);
                return Result.newError(ResultCode.UNABLE_RESOLVE_OBJECT.getErrorCode(), ResultCode.UNABLE_RESOLVE_OBJECT.getDescription());
            }
            // 2、注册下游互联用户
            registerERDownstreamArg.setPhone(loginAccount);
            registerERDownstreamArg.setUpstreamEa(upstreamEa);
            registerERDownstreamArg.setCustomerName(mapperObjectName);
            registerERDownstreamArg.setContractId(contactId);
            registerERDownstreamArg.setContractName(contactName);
            registerERDownstreamArg.setLoginIdentityType(arg.getIdentityType());
            registerERDownstreamArg.setMapperObjectId(mapperId);
            registerERDownstreamArg.setMapperApiName(mapperApiName);
            registerERDownstreamArg.setOwner(registerOwner);
            return objEnterpriseRelationOutController.registerERDownstream(registerERDownstreamArg, null);
        } catch (Throwable ex) {
            log.warn("register error ", ex);
            return Result.newError(ResultCode.SYSTEM_ERROR.getErrorCode(), ex.getMessage());
        }
    }

    private String getContactLockKey(String upstreamEa, String mapperId, String mobile) {
        return "createAccountLock:" + upstreamEa + ":" + mapperId + ":" + mobile;
    }

    private StringBuilder getRepeatErrorMsg(String upstreamEa, String mapperId, String mapperApiName, String mapperObjectName) {
        StringBuilder sb = new StringBuilder(String.format(I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_ERUNIONREGISTERCONTROLLER_645), upstreamEa));
        if (StringUtils.isNotBlank(mapperId)) { // 同一条数据
            sb.append(String.format(String.format(I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_ERUNIONREGISTERCONTROLLER_647), crmManager.getIdName(upstreamEa, mapperApiName, mapperId))));
        } else if (StringUtils.isNotBlank(mapperObjectName)) {
            sb.append(String.format(String.format(I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_ERUNIONREGISTERCONTROLLER_649), mapperObjectName)));
        }
        return sb;
    }

    /**
     * 匹配客户默认负责人逻辑：固定或循环分配负责人
     *
     * @param upstreamEa 上游企业
     * @param erAccountActiveConfigData 自注册分配负责人规则
     * @return 负责人
     */
    private String selectRightOwner(String upstreamEa, ERAccountActiveConfigData erAccountActiveConfigData) {
        // 单个或固定单人
        if (erAccountActiveConfigData.getOwners().size() == 1 || erAccountActiveConfigData.getOwnerAllocateType() != OwnerAllocateTypeEnum.LOOP.getType()) {
            return "" + erAccountActiveConfigData.getOwners().get(0);
        }
        // 循环多人
        String redisKey = "LAST_SELECTED_OWNER_KEY_" + erAccountActiveConfigData.getLinkType() + "_" + upstreamEa;
        String ownerStr = jedis.get(redisKey);
        int redisIndex = -1;
        int elIndex = 0;
        if (StringUtils.isNotBlank(ownerStr)) {
            redisIndex = erAccountActiveConfigData.getOwners().indexOf(Integer.parseInt(ownerStr));
        }
        if (redisIndex != -1 && redisIndex < (erAccountActiveConfigData.getOwners().size() - 1)) {
            elIndex = redisIndex + 1;
        }
        jedis.set(redisKey, "" + erAccountActiveConfigData.getOwners().get(elIndex));
        return "" + erAccountActiveConfigData.getOwners().get(elIndex);
    }

    private Result newRegisterErrorResult(int errCode, String errMsg, LoginParamData loginParamData, RegisterArg arg) {
        RegisterResult registerResult = new RegisterResult();
        if (loginParamData != null) {
            registerResult.setLoginParam(AESUtil.objectEncode(config.getAesKey(), loginParamData));
        }
        writeEsLog(arg, errCode, errMsg, loginParamData);
        return Result.newError(errCode, errMsg, registerResult, TraceContext.get().getTraceId());
    }

    /**
     * 根据手机号获取联系人数据，根据联系人关联的客户与合作伙伴对象数据，找到对应的对接企业数据
     *
     * @param mobile 手机号
     */
    private List<ContactDataMapperEnterpriseRelationResult> listContactObjectDataEnterpriseRelationsDataByMobile(String ea, String mobile, String mapperObjectId, String mapperApiName) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        HeaderObj headerObj = new HeaderObj(ei, -10000);
        com.fxiaoke.crmrestapi.common.data.SearchQuery searchQuery = new com.fxiaoke.crmrestapi.common.data.SearchQuery();
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.addFilter(ContactFieldContants.MOBILE, Lists.newArrayList(mobile), "EQ");
        if (!Strings.isNullOrEmpty(mapperObjectId) && mapperApiName.equals(MapperApiNameEnum.ACCOUNT_OBJ.getType())) {
            searchQuery.addFilter(ContactFieldContants.ACCOUNT_ID, Lists.newArrayList(mapperObjectId), "EQ");
        }
        if (!Strings.isNullOrEmpty(mapperObjectId) && mapperApiName.equals(MapperApiNameEnum.PARTNER_OBJ.getType())) {
            searchQuery.addFilter(ContactFieldContants.OWNED_PARTNER_ID, Lists.newArrayList(mapperObjectId), "EQ");
            searchQuery.addFilter("partner_id", Lists.newArrayList(mapperObjectId), "EQ");
            searchQuery.setPattern("(1 or 2)");
        }
        ControllerListArg params = new ControllerListArg();
        params.setSearchQuery(searchQuery);
        params.setObjectDescribeApiName(ContactFieldContants.API_NAME);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> result = metadataControllerService.list(headerObj, ContactFieldContants.API_NAME, params);
        Page<ObjectData> objectDataPage = result.getData();
        if (objectDataPage != null && !org.springframework.util.CollectionUtils.isEmpty(objectDataPage.getDataList())) {
            return listContactMapperEnterpriseRelationsData(ea, objectDataPage.getDataList());
        }
        return new ArrayList<>();
    }

    private List<ContactDataMapperEnterpriseRelationResult> listContactMapperEnterpriseRelationsData(String upstreamEa, List<ObjectData> objectDatas) {
        Set<String> mapperAccountIds = objectDatas.stream().filter(val -> !StringUtils.isEmpty(ContactData.wrap(val).getAccountId())).map(val -> ContactData.wrap(val).getAccountId())
            .collect(Collectors.toSet());
        Set<String> mapperPartnerIds = objectDatas.stream().filter(val -> !StringUtils.isEmpty(ContactData.wrap(val).getOwnedPartnerID())).map(val -> ContactData.wrap(val).getOwnedPartnerID())
            .collect(Collectors.toSet());
        Set<String> mapperContactIds = objectDatas.stream().filter(val -> !StringUtils.isEmpty(ContactData.wrap(val).getId())).map(val -> ContactData.wrap(val).getId()).collect(Collectors.toSet());

        if (org.springframework.util.CollectionUtils.isEmpty(mapperAccountIds) && CollectionUtils.isEmpty(mapperPartnerIds)) {
            return new ArrayList<>();
        }

        List<EnterpriseRelationObj> mapperAccountEnterpriseRelations = new ArrayList<>();
        List<EnterpriseRelationObj> mapperPartnerEnterpriseRelations = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mapperAccountIds)) {
            mapperAccountEnterpriseRelations = enterpriseRelationManager.batchGetByUpstreamOuterTenantIdByMapperObjectIds(upstreamEa, mapperAccountIds, AccountFieldContants.API_NAME).getData();
        }
        if (CollectionUtils.isNotEmpty(mapperPartnerIds)) {
            mapperPartnerEnterpriseRelations = enterpriseRelationManager.batchGetByUpstreamOuterTenantIdByMapperObjectIds(upstreamEa, mapperPartnerIds, PartnerFieldContants.API_NAME).getData();
        }
        Map<String, EnterpriseRelationObj> mapperAccountIdRelationResultMap = mapperAccountEnterpriseRelations.stream()
            .collect(Collectors.toMap(EnterpriseRelationObj::getMapperAccountId, Function.identity(), (o1, o2) -> {
                // 保留启用的
                if (o1.getRelationType() != null && EnterpriseRelationType.FRIEND.getType() == o1.getRelationType()) {
                    return o1;
                } else {
                    return o2;
                }
            }));
        Map<String, EnterpriseRelationObj> mapperPartnerIdRelationResultMap = mapperPartnerEnterpriseRelations.stream()
            .collect(Collectors.toMap(EnterpriseRelationObj::getMapperPartnerId, Function.identity(), (x, y) -> {
                // 保留启用的
                if (x.getRelationType() != null && EnterpriseRelationType.FRIEND.getType() == x.getRelationType()) {
                    return x;
                } else {
                    return y;
                }
            }));

        List<PublicEmployeeObj> publiceEmployeeDataVos = publicEmployeeManager.listDownstreamPublicEmployeeByContactId(upstreamEa, new ArrayList<>(mapperContactIds)).getData();
        Set<String> matchPublicEmployeeContactIds = publiceEmployeeDataVos.stream().map(PublicEmployeeObj::getContractId).collect(Collectors.toSet());

        List<ContactDataMapperEnterpriseRelationResult> res = new ArrayList<>();
        for (ObjectData objectData : objectDatas) {
            ContactData contactData = ContactData.wrap(objectData);
            // 过滤已有互联账号的联系人
            if (matchPublicEmployeeContactIds.contains(contactData.getId())) {
                continue;
            }
            ContactDataMapperEnterpriseRelationResult data = new ContactDataMapperEnterpriseRelationResult();
            data.setContactData(contactData);

            EnterpriseRelationObj mapperAccountEnterpriseRelationResult = mapperAccountIdRelationResultMap.get(contactData.getAccountId());
            EnterpriseRelationObj mapperPartnerEnterpriseRelationResult = mapperPartnerIdRelationResultMap.get(contactData.getOwnedPartnerID());
            data.setMapperAccountRelationResult(mapperAccountEnterpriseRelationResult);
            data.setMapperPartnerRelationResult(mapperPartnerEnterpriseRelationResult);

            res.add(data);
        }
        return res;
    }

    /**
     * 判断是否匹配对应的账号
     *
     * @param activeConfigData 账号激活配置类型
     * @param identity 身份类型
     * @param mapperApiName 绑定类型
     */
    private boolean isMatchOpenRegiestAccountActive(ERAccountActiveConfigData activeConfigData, Integer identity, String mapperApiName) {
        if (activeConfigData == null) {
            return false;
        }
        // 自注册开关匹配
        if (!activeConfigData.isOpenRegiestCreate()) {
            return false;
        }
        if (identity != null) {
            if (!identity.equals(activeConfigData.getIdentityType())) {
                return false;
            }
        }
        if (!Strings.isNullOrEmpty(mapperApiName)) {
            if (!mapperApiName.equals(activeConfigData.getMapperApiName())) {
                return false;
            }
        }
        return true;
    }

    private List<ContactCrmRegiestData> listMatchCrmCreateContactRegiestDatas(String upstreamEa, ERAccountActiveConfigData activeConfigData, Integer identity, String mapperApiName,
        List<ContactDataMapperEnterpriseRelationResult> contactDataMapperEnterpriseRelationResults) {
        List<ContactCrmRegiestData> resultList = new ArrayList<>();
        if (activeConfigData == null) {
            return resultList;
        }
        if (!activeConfigData.isOpenCrmCreate()) {
            return resultList;
        }
        if (identity != null) {
            if (!identity.equals(activeConfigData.getIdentityType())) {
                return resultList;
            }
        }
        if (!Strings.isNullOrEmpty(mapperApiName)) {
            if (!mapperApiName.equals(activeConfigData.getMapperApiName())) {
                return resultList;
            }
        }
        for (ContactDataMapperEnterpriseRelationResult contactDataMapperEnterpriseRelationResult : contactDataMapperEnterpriseRelationResults) {
            Integer contactIdentity = null;
            String contactMapperApiName = null;
            String mapperObjectId = null;
            String mapperObjectName = null;
            ContactData contactData = contactDataMapperEnterpriseRelationResult.getContactData();
            if (activeConfigData.getMapperApiName().equals(MapperApiNameEnum.ACCOUNT_OBJ.getType())) {
                mapperObjectId = contactData.getAccountId();
                mapperObjectName = contactData.getAccountName();
                if (mapperObjectId == null) {
                    log.info("ACCOUNT_OBJ contact's mapperObjectId is null");
                    continue;
                }
                if (contactDataMapperEnterpriseRelationResult.getMapperAccountRelationResult() != null) {
                    contactIdentity = contactDataMapperEnterpriseRelationResult.getMapperAccountRelationResult().getIdentityType();
                    contactMapperApiName = MapperApiNameEnum.ACCOUNT_OBJ.getType();
                }
            }
            if (activeConfigData.getMapperApiName().equals(MapperApiNameEnum.PARTNER_OBJ.getType())) {
                mapperObjectId = contactData.getOwnedPartnerID();
                mapperObjectName = contactData.getOwnedPartnerName();
                if (mapperObjectId == null) {
                    log.info("PARTNER_OBJ contact's mapperObjectId is null");
                    continue;
                }
                if (contactDataMapperEnterpriseRelationResult.getMapperAccountRelationResult() != null) {
                    contactIdentity = contactDataMapperEnterpriseRelationResult.getMapperAccountRelationResult().getIdentityType();
                    contactMapperApiName = MapperApiNameEnum.ACCOUNT_OBJ.getType();
                }
            }
            if (contactMapperApiName != null && !contactMapperApiName.equals(activeConfigData.getMapperApiName())) {
                continue;
            }
            if (contactIdentity != null && !contactIdentity.equals(activeConfigData.getIdentityType())) {
                continue;
            }

            ContactData regiestContactData = new ContactData();
            regiestContactData.put("_id", contactData.getId());
            regiestContactData.setName(contactData.getName());
            ObjectData regiestMapperObjectData = new ObjectData(activeConfigData.getMapperApiName());
            regiestMapperObjectData.put("_id", mapperObjectId);
            regiestMapperObjectData.setName(mapperObjectName);

            ContactCrmRegiestData contactCrmRegiestData = new ContactCrmRegiestData();
            contactCrmRegiestData.setIdentityType(activeConfigData.getIdentityType());
            contactCrmRegiestData.setMapperApiName(activeConfigData.getMapperApiName());
            contactCrmRegiestData.setContactDataMap(regiestContactData);
            contactCrmRegiestData.setMapperObjectDataMap(regiestMapperObjectData);
            resultList.add(contactCrmRegiestData);
        }
        return resultList;
    }

    private void batchSetContactCrmObjectStopStatus(String upstreamEa, List<ContactCrmRegiestData> contactCrmRegiestDatas) {
        if (CollectionUtils.isEmpty(contactCrmRegiestDatas)) {
            return;
        }

        List<String> contractIds = contactCrmRegiestDatas.stream().map(x -> (String) x.getContactDataMap().get("_id")).collect(Collectors.toList());
        List<String> accountIds = contactCrmRegiestDatas.stream().filter(x -> MapperApiNameEnum.ACCOUNT_OBJ.getType().equals(x.getMapperApiName()))
            .map(x -> (String) x.getMapperObjectDataMap().get("_id")).collect(Collectors.toList());
        List<String> partnerIds = contactCrmRegiestDatas.stream().filter(x -> MapperApiNameEnum.PARTNER_OBJ.getType().equals(x.getMapperApiName()))
            .map(x -> (String) x.getMapperObjectDataMap().get("_id")).collect(Collectors.toList());

        batchSetContactStopStatus(upstreamEa, contractIds, contactCrmRegiestDatas);
        batchSetMapperObjectStopStatus(upstreamEa, MapperApiNameEnum.ACCOUNT_OBJ.getType(), accountIds, contactCrmRegiestDatas);
        batchSetMapperObjectStopStatus(upstreamEa, MapperApiNameEnum.PARTNER_OBJ.getType(), partnerIds, contactCrmRegiestDatas);

        sortStoppedContactToEnd(contactCrmRegiestDatas);
    }

    private void batchSetMapperObjectStopStatus(String upstreamEa, String mapperApiName, List<String> crmObjectIds, List<ContactCrmRegiestData> contactCrmRegiestDatas) {
        if (CollectionUtils.isEmpty(crmObjectIds)) {
            return;
        }
        // 查询客户对象已关联的互联企业
        ListEnterpriseRelationObjByConditionArg conditionArg = new ListEnterpriseRelationObjByConditionArg();
        conditionArg.setIncludeInvalid(true);
        if (MapperApiNameEnum.ACCOUNT_OBJ.getType().equals(mapperApiName)) {
            conditionArg.setMapperAccountIds(crmObjectIds);
        } else {
            conditionArg.setMapperPartnerIds(crmObjectIds);
        }
        ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(eieaConverter.enterpriseAccountToId(upstreamEa) + "", "-10000");
        List<Map<String, Object>> enterpriseRelationObjectDatas = enterpriseRelationObjService.listByCondition(serviceContext, conditionArg).getObjectDatas();
        if (CollectionUtils.isEmpty(enterpriseRelationObjectDatas)) {
            return;
        }

        // 填充isStop字段，用于判断关联互联企业已停用
        String mapperIdField;
        if (MapperApiNameEnum.ACCOUNT_OBJ.getType().equals(mapperApiName)) {
            mapperIdField = EnterpriseRelationObjConstant.MAPPER_ACCOUNT_ID;
        } else {
            mapperIdField = EnterpriseRelationObjConstant.MAPPER_PARTNER_ID;
        }
        Map<String, Map<String, Object>> mapperIdEnterpriseRelationObjectDataMap = enterpriseRelationObjectDatas.stream()
            .collect(Collectors.toMap(x -> (String) x.get(mapperIdField), x -> x, (x1, x2) -> {
                // 同个客户，关联了一个停用的互联企业，并且使用该客户创建了一个新的互联企业
                // 找到一个正常的返回，没有正常的返回第一个
                int relationType = Integer.parseInt(x1.get(EnterpriseRelationObjConstant.RELATION_TYPE).toString());
                if (relationType == EnterpriseRelationObjRelationTypeOption.STOPPING.getType()) {
                    return x2;
                }
                return x1;
            }));
        for (ContactCrmRegiestData contactCrmRegiestData : contactCrmRegiestDatas) {
            if (!contactCrmRegiestData.getMapperApiName().equals(mapperApiName)) {
                continue;
            }
            Map<String, Object> mapperObjectDataMap = contactCrmRegiestData.getMapperObjectDataMap();
            mapperObjectDataMap.put("isStop", false);
            // 存在关联互联企业且已停用，则关联对象标记为已停用
            Map<String, Object> enterpriseRelationObjectData = mapperIdEnterpriseRelationObjectDataMap.get(mapperObjectDataMap.get("_id"));
            if (MapUtils.isNotEmpty(enterpriseRelationObjectData)) {
                int relationType = Integer.parseInt(enterpriseRelationObjectData.get(EnterpriseRelationObjConstant.RELATION_TYPE).toString());
                if (relationType == EnterpriseRelationObjRelationTypeOption.STOPPING.getType()) {
                    mapperObjectDataMap.put("isStop", true);
                }
            }
        }
    }

    private void batchSetContactStopStatus(String upstreamEa, List<String> crmObjectIds, List<ContactCrmRegiestData> contactCrmRegiestDatas) {
        if (CollectionUtils.isEmpty(crmObjectIds)) {
            return;
        }

        ListPublicEmployeeObjByConditionArg conditionArg = new ListPublicEmployeeObjByConditionArg();
        conditionArg.setIncludeInvalid(true);
        conditionArg.setTypes(Lists.newArrayList(PublicEmployeeObjTypeOption.STOP.getType())); // 查询停用的互联用户
        conditionArg.setContractIds(crmObjectIds);
        ServiceContext serviceContext = publicEmployeeObjService.createServiceContext(eieaConverter.enterpriseAccountToId(upstreamEa) + "", "-10000");
        List<Map<String, Object>> publicEmployeeObjectDatas = publicEmployeeObjService.listByCondition(serviceContext, conditionArg).getObjectDatas();
        if (CollectionUtils.isEmpty(publicEmployeeObjectDatas)) {
            return;
        }
        Map<Object, Map<String, Object>> contactIdPublicEmployeeObjectDataMap = new HashMap<>();
        for (Map<String, Object> publicEmployeeObj : publicEmployeeObjectDatas) {
            String contactId = publicEmployeeObj.get(PublicEmployeeObjConstant.CONTRACT_ID) + "";
            if (StringUtils.isNotEmpty(contactId) && !contactIdPublicEmployeeObjectDataMap.containsKey(contactId)) {
                contactIdPublicEmployeeObjectDataMap.put(contactId, publicEmployeeObj);
            }
        }
        for (ContactCrmRegiestData contactCrmRegiestData : contactCrmRegiestDatas) {
            Map<String, Object> contactDataMap = contactCrmRegiestData.getContactDataMap();
            contactDataMap.put("isStop", false);
            // 存在关联互联用户且已停用，则关联联系人对象标记为已停用
            if (!contactIdPublicEmployeeObjectDataMap.isEmpty() && contactIdPublicEmployeeObjectDataMap.containsKey(contactDataMap.get("_id"))) {
                contactDataMap.put("isStop", true);
            }
        }
    }

    private void sortStoppedContactToEnd(List<ContactCrmRegiestData> contactCrmRegiestDatas) {
        if (CollectionUtils.isEmpty(contactCrmRegiestDatas)) {
            return;
        }
        // 已停用互联用户排到末尾
        List<ContactCrmRegiestData> stoppedContacts = contactCrmRegiestDatas.stream().filter(x -> {
            return x.getContactDataMap().get("isStop") == null ? false : Boolean.parseBoolean(x.getContactDataMap().get("isStop").toString());
        }).collect(Collectors.toList());
        contactCrmRegiestDatas.removeAll(stoppedContacts);
        contactCrmRegiestDatas.addAll(stoppedContacts);
    }

    /**
     * 业务员与自注册校验mapperApiName和identity逻辑 http://wiki.firstshare.cn/pages/viewpage.action?pageId=*********
     */
    private RegisterResult doCrmCreateRegister(String upstreamEa, String mobile, String mapperObjectId, RegisterERAccountActiveConfigData accountActiveConfigDatas, RegisterArg arg) {
        // 手机号查找对应的联系人
        List<ContactDataMapperEnterpriseRelationResult> contactDataMapperEnterpriseRelationResults = listContactObjectDataEnterpriseRelationsDataByMobile(upstreamEa, mobile, mapperObjectId,
            arg.getMapperApiName());
        if (contactDataMapperEnterpriseRelationResults.isEmpty()) {
            return null;
        }
        List<ContactCrmRegiestData> corpAccountContactList = listMatchCrmCreateContactRegiestDatas(upstreamEa, accountActiveConfigDatas.getCorpERAccountActiveConfigData(), arg.getIdentityType(),
            arg.getMapperApiName(), contactDataMapperEnterpriseRelationResults);
        List<ContactCrmRegiestData> personnelAccountContactList = listMatchCrmCreateContactRegiestDatas(upstreamEa, accountActiveConfigDatas.getPersonnalERAccountActiveConfigData(),
            arg.getIdentityType(), arg.getMapperApiName(), contactDataMapperEnterpriseRelationResults);
        List<ContactCrmRegiestData> corpPartnerContactList = listMatchCrmCreateContactRegiestDatas(upstreamEa, accountActiveConfigDatas.getCorpERPartnerActiveConfigData(), arg.getIdentityType(),
            arg.getMapperApiName(), contactDataMapperEnterpriseRelationResults);

        Boolean isOpenCorpAccountRegister = !corpAccountContactList.isEmpty(); // 开始客户企业类型注册
        Boolean isOpenPersonnelAccountRegister = !personnelAccountContactList.isEmpty(); // 开启客户个人类型注册
        Boolean isOpenCorpPartnerRegister = !corpPartnerContactList.isEmpty(); // 开启合作伙伴企业类型注册

        List<ContactCrmRegiestData> contactList = new ArrayList<>();
        contactList.addAll(corpAccountContactList);
        contactList.addAll(personnelAccountContactList);
        contactList.addAll(corpPartnerContactList);
        for (ContactCrmRegiestData x : contactList) {
            x.setUpstreamEa(upstreamEa);
        }

        log.info("doCrmCreateRegiest isOpenCorpAccountRegister={},isOpenPersonnelAccountRegister={},isOpenCorpPartnerRegister={},contactDataMapper={},arg={}", isOpenCorpAccountRegister,
            isOpenPersonnelAccountRegister, isOpenCorpPartnerRegister, contactDataMapperEnterpriseRelationResults, arg);
        RegisterResult res = new RegisterResult();
        List<Object> chooseIdentityData = new ArrayList<>();
        if (!isOpenCorpAccountRegister && !isOpenPersonnelAccountRegister) { // 两个都不匹配
            if (isOpenCorpPartnerRegister && CollectionUtils.isNotEmpty(contactList)) {
                batchSetContactCrmObjectStopStatus(upstreamEa, contactList);
                res.setStatus(RegisterStatusEnum.CRM_DATA_REGIEST.getStatus());
                res.setData(contactList);
                return res;
            } else {
                return null;
            }
        } else if (isOpenCorpAccountRegister && isOpenPersonnelAccountRegister) {// 两个都匹配
            res.setStatus(RegisterStatusEnum.CHOOSE_IDENTITY_TYPE.getStatus());
            GetDownstreamChannelTypeResult downstreamChannelTypeResult = outerRoleService.getDownstreamChannelType(upstreamEa).getData();
            chooseIdentityData.add(downstreamChannelTypeResult.getChannelDistribution());
            chooseIdentityData.add(downstreamChannelTypeResult.getChannelEndUser());
            if (isOpenCorpPartnerRegister) {
                chooseIdentityData.add(downstreamChannelTypeResult.getChannelAgent());
            }
            res.setData(chooseIdentityData);
            return res;
        } else { // 匹配一个
            if (isOpenCorpPartnerRegister) {
                GetDownstreamChannelTypeResult downstreamChannelTypeResult = outerRoleService.getDownstreamChannelType(upstreamEa).getData();
                res.setStatus(RegisterStatusEnum.CHOOSE_IDENTITY_TYPE.getStatus());
                if (isOpenCorpAccountRegister) {
                    chooseIdentityData.add(downstreamChannelTypeResult.getChannelDistribution());
                } else {
                    chooseIdentityData.add(downstreamChannelTypeResult.getChannelEndUser());
                }
                chooseIdentityData.add(downstreamChannelTypeResult.getChannelAgent());
                res.setData(chooseIdentityData);
                return res;
            } else {
                if (CollectionUtils.isNotEmpty(contactList)) {
                    batchSetContactCrmObjectStopStatus(upstreamEa, contactList);
                    res.setStatus(RegisterStatusEnum.CRM_DATA_REGIEST.getStatus());
                    res.setData(contactList);
                    return res;
                }
                return null;
            }
        }
    }

    private RegisterResult doOpenRegisterRegister(String upstreamEa, String mobile, String mapperObjectId, RegisterERAccountActiveConfigData accountActiveConfigDatas, RegisterArg arg) {
        log.debug("upstreamEa={},mobile={},mapperObjectId={},accountActiveConfigDatas={},arg={}", upstreamEa, mobile, mapperObjectId, accountActiveConfigDatas, arg);
        Boolean isOpenCorpAccountRegister = isMatchOpenRegiestAccountActive(accountActiveConfigDatas.getCorpERAccountActiveConfigData(), arg.getIdentityType(), arg.getMapperApiName());
        Boolean isOpenPersonnelAccountRegister = isMatchOpenRegiestAccountActive(accountActiveConfigDatas.getPersonnalERAccountActiveConfigData(), arg.getIdentityType(), arg.getMapperApiName());
        Boolean isOpenCorpPartnerRegister = isMatchOpenRegiestAccountActive(accountActiveConfigDatas.getCorpERPartnerActiveConfigData(), arg.getIdentityType(), arg.getMapperApiName());
        log.info("doOpenRegiestRegiest isOpenCorpAccountRegister={},isOpenPersonnelAccountRegister={},isOpenCorpPartnerRegister={}", isOpenCorpAccountRegister, isOpenPersonnelAccountRegister,
            isOpenCorpPartnerRegister);
        RegisterResult res = new RegisterResult();
        List<Object> chooseIdentityData = new ArrayList<>();
        if ((!isOpenCorpAccountRegister) && !isOpenPersonnelAccountRegister) { // 两个都不匹配
            if (isOpenCorpPartnerRegister) {
                String mapperObjectName = crmManager.getIdName(upstreamEa, MapperApiNameEnum.PARTNER_OBJ.getType(), mapperObjectId);
                Map<String, Object> dataMap = Maps.newHashMap();
                dataMap.put("upstreamEa", upstreamEa);
                dataMap.put("phone", mobile);
                dataMap.put("accountId", mapperObjectId);
                dataMap.put("accountName", mapperObjectName);
                ERAccountActiveConfigData accountActiveConfigData = accountActiveConfigDatas.getCorpERPartnerActiveConfigData();
                Map<String, Object> configMap = Maps.newHashMap();
                configMap.put("accountNameMatchSwitch", accountActiveConfigData.getAccountNameMatchSwitch());
                configMap.put("searchResultNum", accountActiveConfigData.getSearchResultNum());
                configMap.put("loginIdentityType", accountActiveConfigData.getIdentityType());
                configMap.put("accountRequire", accountActiveConfigData.getAccountRequire());
                configMap.put("contactRequire", accountActiveConfigData.getContactRequire());
                configMap.put("mapperApiName", accountActiveConfigData.getMapperApiName());
                configMap.put("accountNameWriteRuleType", accountActiveConfigData.getAccountNameWriteRuleType());
                dataMap.put("configData", configMap);
                res.setStatus(RegisterStatusEnum.FORM_REGIEST.getStatus());
                res.setData(dataMap);
                return res;
            } else {
                return null;
            }
        } else if (isOpenCorpAccountRegister && isOpenPersonnelAccountRegister) {// 两个都匹配
            GetDownstreamChannelTypeResult downstreamChannelTypeResult = outerRoleService.getDownstreamChannelType(upstreamEa).getData();
            res.setStatus(RegisterStatusEnum.CHOOSE_IDENTITY_TYPE.getStatus());
            chooseIdentityData.add(downstreamChannelTypeResult.getChannelDistribution());
            chooseIdentityData.add(downstreamChannelTypeResult.getChannelEndUser());
            if (isOpenCorpPartnerRegister) {
                chooseIdentityData.add(downstreamChannelTypeResult.getChannelAgent());
            }
            res.setData(chooseIdentityData);
            return res;
        } else { // 匹配一个
            if (isOpenCorpPartnerRegister) {
                GetDownstreamChannelTypeResult downstreamChannelTypeResult = outerRoleService.getDownstreamChannelType(upstreamEa).getData();
                res.setStatus(RegisterStatusEnum.CHOOSE_IDENTITY_TYPE.getStatus());
                if (isOpenCorpAccountRegister) {
                    chooseIdentityData.add(downstreamChannelTypeResult.getChannelDistribution());
                } else {
                    chooseIdentityData.add(downstreamChannelTypeResult.getChannelEndUser());
                }
                chooseIdentityData.add(downstreamChannelTypeResult.getChannelAgent());
                res.setData(chooseIdentityData);
                return res;
            } else {
                Integer identityType = IdentityType.PERSONAL.getType();
                if (isOpenCorpAccountRegister) {
                    identityType = IdentityType.CORP.getType();
                }
                String mapperObjectName = crmManager.getIdName(upstreamEa, MapperApiNameEnum.ACCOUNT_OBJ.getType(), mapperObjectId);
                Map<String, Object> dataMap = Maps.newHashMap();
                dataMap.put("upstreamEa", upstreamEa);
                dataMap.put("phone", mobile);
                dataMap.put("accountId", mapperObjectId);
                dataMap.put("accountName", mapperObjectName);
                ERAccountActiveConfigData accountActiveConfigData = accountActiveConfigDatas.getERAccountActiveConfigData(identityType, MapperApiNameEnum.ACCOUNT_OBJ.getType());
                Map<String, Object> configMap = Maps.newHashMap();
                configMap.put("accountNameMatchSwitch", accountActiveConfigData.getAccountNameMatchSwitch());
                configMap.put("searchResultNum", accountActiveConfigData.getSearchResultNum());
                configMap.put("loginIdentityType", accountActiveConfigData.getIdentityType());
                configMap.put("accountRequire", accountActiveConfigData.getAccountRequire());
                configMap.put("contactRequire", accountActiveConfigData.getContactRequire());
                configMap.put("mapperApiName", accountActiveConfigData.getMapperApiName());
                configMap.put("accountNameWriteRuleType", accountActiveConfigData.getAccountNameWriteRuleType());
                dataMap.put("configData", configMap);
                res.setStatus(RegisterStatusEnum.FORM_REGIEST.getStatus());
                res.setData(dataMap);
                return res;
            }
        }
    }

    @ApiOperation(value = "绑定手机时，能确定上游且开了注册，则显示注册入口(完成)", tags = {VersionIterationConstant.f_770_login_optime})
    @RequestMapping(value = "/isOpenRegister", method = RequestMethod.POST)
    @ResponseBody
    public Result<Boolean> isOpenRegister(@RequestBody GetRegistrationEntranceSwitchArg arg) {
        String loginParamString = arg.getLoginParam();
        if (Strings.isNullOrEmpty(loginParamString)) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), "loginParam not be empty");
        }
        LoginParamData loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParamString, LoginParamData.class);
        if (loginParamData == null) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), "loginParam decode error");
        }

        String upstreamEa = arg.getUpstreamEa();
        if (Strings.isNullOrEmpty(upstreamEa) && !Strings.isNullOrEmpty(loginParamData.getWxAppId())) {
            upstreamEa = eaBindInfoRestService.getEaByWxAppId(loginParamData.getWxAppId()).getData();
        }
        if (upstreamEa == null) {
            return Result.newSuccess(false);
        }

        Result<List<LoginRegisterConfigVO>> configResult = loginRegisterConfigService.findStartedRegisterConfig(upstreamEa, arg.getFsAppId(), null);
        if (!configResult.isSuccess()) {
            return Result.newError(configResult.getErrCode(), configResult.getErrMessage());
        }
        if (CollectionUtils.isNotEmpty(configResult.getData())) {
            return Result.newSuccess(true);
        }
        return Result.newSuccess(false);
    }

    @ApiOperation(value = "获取注册使用的布局", tags = {"互联登录", "7.0.2", "7.3.0", VersionIterationConstant.f_820_optimize_jiangxch})
    @ResponseBody
    @RequestMapping(value = "/getObjectLayout", method = RequestMethod.POST)
    public Result<GetObjectLayoutData> getObjectLayout(@RequestBody GetObjectLayoutArg arg, HttpServletRequest request) {
        LoginEmployeeInfo erCookie = getERCookie(request);
        if (erCookie != null) {
            if (Strings.isNullOrEmpty(arg.getUpstreamEa())) {
                arg.setUpstreamEa(erCookie.getUpstreamEa());
            }
            if (arg.getIdentityType() == null) {
                arg.setIdentityType(erCookie.getIdentityType());
            }
        }
        if (Strings.isNullOrEmpty(arg.getUpstreamEa())) {
            return Result.newError(ResultCode.PARAMS_UPSTREAM_NULL.getErrorCode(), "upstreamEa is null");
        }
        GetLayoutArg getLayoutArg = new GetLayoutArg();
        getLayoutArg.setObjectApiName(arg.getObjectApiName());
        getLayoutArg.setLoginIdentityType(arg.getIdentityType());
        getLayoutArg.setMapperApiName(arg.getMapperApiName());
        getLayoutArg.setLinkAppId(arg.getLinkAppId());
        getLayoutArg.setLoginRegisterConfigId(arg.getLoginRegisterConfigId());
        return loginRegisterConfigService.getLayout4Register(arg.getUpstreamEa(), getLayoutArg);
    }

    @ApiOperation(value = "模糊查询crm对象列表", tags = {"互联登录", "7.0.2", "7.3.0", VersionIterationConstant.f_770_login_optime})
    @ResponseBody
    @RequestMapping(value = "/searchMapperObjectByName", method = RequestMethod.POST)
    public Result<List<CrmIdNameData>> searchMapperObjectByName(@RequestBody SearchMapperObjectByNameArg arg) {
        LoginParamData data = decodeLoginParamData(arg.getLoginParam());
        if (arg.getIdentityType() == null || Strings.isNullOrEmpty(arg.getMapperApiName())) {
            return Result.newError(ResultCode.PARAM_IDENTITY_TYPE_NULL.getErrorCode(), "identityType,mapperApiName can't be null");
        }
        String loginAccount = data.getLoginAccountData() != null ? data.getLoginAccountData().getLoginAccount() : null;
        String queryKey = new StringBuilder().append("searchMapperObjectByName:").append(data.getWxAppId()).append(data.getOpenId()).append(data.getUpstreamEa())
            .append(data.getDownstreamOuterTenantId()).append(data.getDownstreamOuterUid()).append(loginAccount).toString();
        Long countLimit = jedis.incr(queryKey);
        if (countLimit == 1) {
            jedis.expire(queryKey, searchMapperObjectByNameQueryTimeoutSecond);
        }
        if (countLimit > searchMapperObjectByNameQueryLimit) {
            return Result.newError(ResultCode.ACCESS_LIMIT.getErrorCode(), ResultCode.FUZZY_QUERY_OVERCLOCKED.getDescription());
        }

        List<CrmIdNameData> list = new ArrayList<>();
        Integer limit = 20;
        Boolean accountNameMatchSwitch = false;
        LoginRegisterConfigVO configVO = loginRegisterConfigService.matchLoginRegisterConfig(arg.getUpstreamEa(), arg.getLinkAppId(), arg.getIdentityType(), arg.getMapperApiName(), arg.getLoginRegisterConfigId());
        limit = configVO.getLoginRegisterLayoutConfigVo().getSearchResultNum();
        accountNameMatchSwitch = configVO.getLoginRegisterLayoutConfigVo().getMapperObjectNameMatchWitch();

        HeaderObj headerObj = new HeaderObj(eieaConverter.enterpriseAccountToId(arg.getUpstreamEa()), -10000);

        ControllerListArg controllerListArg = new ControllerListArg();
        com.fxiaoke.crmrestapi.common.data.SearchQuery searchQuery = new com.fxiaoke.crmrestapi.common.data.SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(limit == null ? 20 : limit);
        if (StringUtils.isEmpty(arg.getName())) {
            return Result.newSuccess();
        }
        String searchName = arg.getMapperApiName().equals(MapperApiNameEnum.ACCOUNT_OBJ.getType()) ? AccountFieldContants.NAME : PartnerFieldContants.NAME;
        String searchApiName = arg.getMapperApiName().equals(MapperApiNameEnum.ACCOUNT_OBJ.getType()) ? AccountFieldContants.API_NAME : PartnerFieldContants.API_NAME;

        if (BooleanUtils.isNotTrue(accountNameMatchSwitch)) {
            searchQuery.addFilter(searchName, Lists.newArrayList(arg.getName()), FilterOperatorEnum.EQ);
        } else {
            searchQuery.addFilter(searchName, Lists.newArrayList(arg.getName()), FilterOperatorEnum.LIKE);
        }
        // 过滤掉已作废的客户/合作伙伴
        searchQuery.addFilter(AccountFieldContants.LIFE_STATUS, Lists.newArrayList(ObjectLifeStatusEnum.INVALID.getType()), FilterOperatorEnum.N);
        controllerListArg.setSearchQuery(searchQuery);
        controllerListArg.setGetDataOnly(true);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> listResult = metadataControllerService.list(headerObj, searchApiName, controllerListArg);
        if (!listResult.isSuccess()) {
            return Result.newError(listResult.getCode(), listResult.getMessage());
        }
        List<ObjectData> crmObjectDatas = listResult.getData().getDataList();
        List<String> mapperIds = crmObjectDatas.stream().map(ObjectData::getId).collect(Collectors.toList());

        List<EnterpriseRelationObj> enterpriseRelationResultList = enterpriseRelationManager.batchGetByUpstreamOuterTenantIdByMapperObjectIds(arg.getUpstreamEa(), mapperIds, searchApiName).getData();
        if (!org.springframework.util.CollectionUtils.isEmpty(enterpriseRelationResultList)) {
            Map<String, Integer> mapperIdIdentityTypeMap = enterpriseRelationResultList.stream().collect(Collectors
                .toMap(x -> arg.getMapperApiName().equals(MapperApiNameEnum.ACCOUNT_OBJ.getType()) ? x.getMapperAccountId() : x.getMapperPartnerId(), EnterpriseRelationObj::getIdentityType,
                    (v1, v2) -> v1));
            //没有绑定或者绑定的是同个类型的客户
            crmObjectDatas = crmObjectDatas.stream().filter(val -> mapperIdIdentityTypeMap.get(val.getId()) == null || Objects.equals(mapperIdIdentityTypeMap.get(val.getId()), arg.getIdentityType()))
                .collect(Collectors.toList());
        }
        for (ObjectData objectData : crmObjectDatas) {
            CrmIdNameData crmIdNameData = new CrmIdNameData();
            crmIdNameData.set_id(objectData.getId());
            crmIdNameData.setName(objectData.getName());
            list.add(crmIdNameData);
        }
        return Result.newSuccess(list);
    }

    @ApiOperation(value = "自注册工商查询", tags = {"互联注册", "8.0.5"})
    @ResponseBody
    @RequestMapping(value = "/listCompanyByName", method = RequestMethod.POST)
    public Result<List<CompanyByNameData>> listCompanyByName(@RequestBody IndustryQueryArg arg) {
        String loginParam = arg.getLoginParam();
        LoginParamData data = decodeLoginParamData(loginParam);
        String loginAccount = data.getLoginAccountData() != null ? data.getLoginAccountData().getLoginAccount() : null;
        String queryKey = new StringBuilder().append(data.getWxAppId()).append(data.getOpenId()).append(data.getUpstreamEa()).append(data.getDownstreamOuterTenantId())
            .append(data.getDownstreamOuterUid()).append(loginAccount).toString();
        Long countLimit = jedis.incr(queryKey);
        if (countLimit == 1) {
            jedis.expire(queryKey, industryServiceQueryTimeoutSecond);
        }
        if (countLimit > industryServiceQueryLimit) {
            return Result.newError(ResultCode.ACCESS_LIMIT.getErrorCode(), ResultCode.BUSINESS_QUERY_OVERCLOCKED.getDescription());
        }
        HeaderObj headerObj = new HeaderObj(eieaConverter.enterpriseAccountToId(data.getUpstreamEa()), -10000);
        IndustryGetCompanyByNameArg industryQueryArg = new IndustryGetCompanyByNameArg();
        industryQueryArg.setKeyWord(arg.getCompanyName());
        IndustryGetCompanyByNameResult result = industryService.getCompanyByName(headerObj, industryQueryArg);
        String errorCode = result.getErrorCode();
        if (!"0".equals(errorCode)) {
            return Result.newError(Integer.valueOf(result.getErrorCode()), result.getMessage());
        }
        List<CompanyByNameData> res = new ArrayList<>();

        Map<String, String> mapperIdMapperObjectNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(result.getCompanyEsObjects())) {
            Set<String> companyNames = result.getCompanyEsObjects().stream().map(Company::getName).collect(Collectors.toSet());
            List<IdNameVo<String>> idNameVos = crmManager.batchGetCrmIdsByNames(data.getUpstreamEa(), new ArrayList<>(companyNames), arg.getMapperApiName());
            mapperIdMapperObjectNameMap = idNameVos.stream().collect(Collectors.toMap(IdNameVo::getName, IdNameVo::getId, (o1, o2) -> o1));
        }

        for (Company companyEsObject : result.getCompanyEsObjects()) {
            CompanyByNameData companyByNameData = new CompanyByNameData();
            companyByNameData.setAccountId(mapperIdMapperObjectNameMap.get(companyEsObject.getName()));
            companyByNameData.setKeyNo(companyEsObject.getKeyNo());
            companyByNameData.setStatus(companyEsObject.getStatus());
            companyByNameData.setNo(companyEsObject.getNo());
            companyByNameData.setName(companyEsObject.getName());
            companyByNameData.setCreditCode(companyEsObject.getCreditCode());
            companyByNameData.setPrincipalName(companyEsObject.getPrincipalName());
            companyByNameData.setOperName(companyEsObject.getOperName());

            res.add(companyByNameData);
        }
        return Result.newSuccess(res);
    }

    @ApiOperation(value = "已存在客户和自注册工商查询", tags = {"互联注册", "8..5"})
    @ResponseBody
    @RequestMapping(value = "/searchEnterpriseInfoByName", method = RequestMethod.POST)
    public Result<EnterpriseInfoDataResult> searchEnterpriseInfoByName(@RequestBody EnterpriseInfoArg arg) {
        LoginParamData data = decodeLoginParamData(arg.getLoginParam());

        if (arg.getAccountNameWriteRuleType() == null) {
            return Result.newError(ResultCode.PARAM_ACCOUNT_NAME_WRITE_RULE_TYPE_NULL.getErrorCode(), "accountNameWriteRuleType is null");
        }
        if (StringUtils.isEmpty(data.getUpstreamEa())) {
            data.setUpstreamEa(arg.getUpstreamEa());
        }
        if (StringUtils.isEmpty(data.getUpstreamEa()) && StringUtils.isEmpty(arg.getUpstreamEa())) {
            return Result.newError(ResultCode.PARAMS_UPSTREAM_NULL.getErrorCode(), "upstreamEa is null");
        }
        if (AccountNameWriteRuleTypeEnum.ACCOUNT_NAME_ONLY_SELECT_FROM_EXIST_ACCOUNT.getType().equals(arg.getAccountNameWriteRuleType())) {
            return searchCustomerData(arg, data);
        } else if (AccountNameWriteRuleTypeEnum.ACCOUNT_NAME_SELECT_FROM_INDUSTRIAL_QUERY.getType().equals(arg.getAccountNameWriteRuleType())) {
            return searchIndustryData(arg, data);
        } else if (AccountNameWriteRuleTypeEnum.ACCOUNT_NAME_SELECT_FROM_CUSTOMER_AND_INDUSTRIAL_QUERY.getType().equals(arg.getAccountNameWriteRuleType())) {
            Result<EnterpriseInfoDataResult> customerResult = searchCustomerData(arg, data);
            if (customerResult.isSuccess() && !customerResult.getData().getEnterpriseInfoDataList().isEmpty()) {
                return customerResult;
            } else {
                return searchIndustryData(arg, data);
            }
        }

        return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), "accountNameWriteRuleType error");
    }

    public Result<EnterpriseInfoDataResult> searchIndustryData(EnterpriseInfoArg arg, LoginParamData data) {
        String loginAccount = data.getLoginAccountData() != null ? data.getLoginAccountData().getLoginAccount() : null;
        String queryKey = new StringBuilder().append(data.getWxAppId()).append(data.getOpenId()).append(data.getUpstreamEa()).append(data.getDownstreamOuterTenantId())
            .append(data.getDownstreamOuterUid()).append(loginAccount).toString();
        Long countLimit = jedis.incr(queryKey);
        if (countLimit == 1) {
            jedis.expire(queryKey, industryServiceQueryTimeoutSecond);
        }
        if (countLimit > industryServiceQueryLimit) {
            return Result.newError(ResultCode.ACCESS_LIMIT.getErrorCode(), I18nUtil.get(I18nKeyEnum.EPI_AUTH_ERUNIONREGISTERCONTROLLER_1761));
        }
        HeaderObj headerObj = new HeaderObj(eieaConverter.enterpriseAccountToId(data.getUpstreamEa()), -10000);
        IndustryGetCompanyByNameArg industryQueryArg = new IndustryGetCompanyByNameArg();
        industryQueryArg.setKeyWord(arg.getName());
        IndustryGetCompanyByNameResult result = industryService.getCompanyByName(headerObj, industryQueryArg);
        String errorCode = result.getErrorCode();
        if (!"0".equals(errorCode)) {
            return Result.newError(Integer.valueOf(result.getErrorCode()), result.getMessage());
        }
        List<EnterpriseInfoData> list = new ArrayList<>();

        Map<String, String> mapperIdMapperObjectNameMap = new HashMap<>();

        try {
            if (CollectionUtils.isNotEmpty(result.getCompanyEsObjects())) {
                Set<String> companyNames = result.getCompanyEsObjects().stream().map(Company::getName).collect(Collectors.toSet());
                List<IdNameVo<String>> idNameVos = crmManager.batchGetCrmIdsByNames(data.getUpstreamEa(), new ArrayList<>(companyNames), arg.getMapperApiName());
                mapperIdMapperObjectNameMap = idNameVos.stream().collect(Collectors.toMap(IdNameVo::getName, IdNameVo::getId, (o1, o2) -> o1));
            }
        } catch (Throwable throwable) {
            log.warn("batchGetCrmIdsByNames error  msg:{}", throwable.getMessage());
        }

        for (Company companyEsObject : result.getCompanyEsObjects()) {
            EnterpriseInfoData enterpriseInfoData = new EnterpriseInfoData();
            enterpriseInfoData.setObjectId(mapperIdMapperObjectNameMap.get(companyEsObject.getName()));
            enterpriseInfoData.setKeyNo(companyEsObject.getKeyNo());
            enterpriseInfoData.setStatus(companyEsObject.getStatus());
            enterpriseInfoData.setNo(companyEsObject.getNo());
            enterpriseInfoData.setName(companyEsObject.getName());
            enterpriseInfoData.setCreditCode(companyEsObject.getCreditCode());
            enterpriseInfoData.setPrincipalName(companyEsObject.getPrincipalName());
            enterpriseInfoData.setOperName(companyEsObject.getOperName());

            list.add(enterpriseInfoData);
        }

        log.info("工商信息数：{}", list.size());//ignoreI18n

        EnterpriseInfoDataResult enterpriseInfoDataResult = new EnterpriseInfoDataResult();
        enterpriseInfoDataResult.setAccountNameWriteRuleType(AccountNameWriteRuleTypeEnum.ACCOUNT_NAME_SELECT_FROM_INDUSTRIAL_QUERY.getType());
        enterpriseInfoDataResult.setEnterpriseInfoDataList(list);
        return Result.newSuccess(enterpriseInfoDataResult);
    }

    public Result<EnterpriseInfoDataResult> searchCustomerData(EnterpriseInfoArg arg, LoginParamData data) {
        if (arg.getIdentityType() == null || Strings.isNullOrEmpty(arg.getMapperApiName())) {
            return Result.newError(ResultCode.PARAM_IDENTITY_TYPE_NULL.getErrorCode(), "identityType,mapperApiName can't be null");
        }
        String loginAccount = data.getLoginAccountData() != null ? data.getLoginAccountData().getLoginAccount() : null;
        String queryKey = new StringBuilder().append("searchMapperObjectByName:").append(data.getWxAppId()).append(data.getOpenId()).append(data.getUpstreamEa())
            .append(data.getDownstreamOuterTenantId()).append(data.getDownstreamOuterUid()).append(loginAccount).toString();
        Long countLimit = jedis.incr(queryKey);
        if (countLimit == 1) {
            jedis.expire(queryKey, searchMapperObjectByNameQueryTimeoutSecond);
        }
        if (countLimit > searchMapperObjectByNameQueryLimit) {
            return Result.newError(ResultCode.ACCESS_LIMIT.getErrorCode(), I18nUtil.get(I18nKeyEnum.EPI_AUTH_ERUNIONREGISTERCONTROLLER_1819));
        }

        List<EnterpriseInfoData> list = new ArrayList<>();
        Integer limit = 20;
        Boolean accountNameMatchSwitch = false;
        LoginRegisterConfigVO configVO = loginRegisterConfigService.matchLoginRegisterConfig(arg.getUpstreamEa(), arg.getLinkAppId(), arg.getIdentityType(), arg.getMapperApiName(), arg.getLoginRegisterConfigId());
        limit = configVO.getLoginRegisterLayoutConfigVo().getSearchResultNum();
        accountNameMatchSwitch = configVO.getLoginRegisterLayoutConfigVo().getMapperObjectNameMatchWitch();
        HeaderObj headerObj = new HeaderObj(eieaConverter.enterpriseAccountToId(arg.getUpstreamEa()), -10000);

        ControllerListArg controllerListArg = new ControllerListArg();
        com.fxiaoke.crmrestapi.common.data.SearchQuery searchQuery = new com.fxiaoke.crmrestapi.common.data.SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(limit == null ? 20 : limit);
        if (StringUtils.isEmpty(arg.getName())) {
            return Result.newSuccess();
        }
        String searchName = arg.getMapperApiName().equals(MapperApiNameEnum.ACCOUNT_OBJ.getType()) ? AccountFieldContants.NAME : PartnerFieldContants.NAME;
        String searchApiName = arg.getMapperApiName().equals(MapperApiNameEnum.ACCOUNT_OBJ.getType()) ? AccountFieldContants.API_NAME : PartnerFieldContants.API_NAME;

        if (BooleanUtils.isNotTrue(accountNameMatchSwitch)) {
            searchQuery.addFilter(searchName, Lists.newArrayList(arg.getName()), FilterOperatorEnum.EQ);
        } else {
            searchQuery.addFilter(searchName, Lists.newArrayList(arg.getName()), FilterOperatorEnum.LIKE);
        }
        // 过滤掉已作废的客户/合作伙伴
        searchQuery.addFilter(AccountFieldContants.LIFE_STATUS, Lists.newArrayList(ObjectLifeStatusEnum.INVALID.getType()), FilterOperatorEnum.N);
        controllerListArg.setSearchQuery(searchQuery);
        controllerListArg.setGetDataOnly(true);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> listResult = metadataControllerService.list(headerObj, searchApiName, controllerListArg);
        if (!listResult.isSuccess()) {
            return Result.newError(listResult.getCode(), listResult.getMessage());
        }
        List<ObjectData> crmObjectDatas = listResult.getData().getDataList();
        List<String> mapperIds = crmObjectDatas.stream().map(ObjectData::getId).collect(Collectors.toList());

        List<EnterpriseRelationObj> enterpriseRelationResultList = enterpriseRelationManager.batchGetByUpstreamOuterTenantIdByMapperObjectIds(arg.getUpstreamEa(), mapperIds, searchApiName).getData();
        if (!org.springframework.util.CollectionUtils.isEmpty(enterpriseRelationResultList)) {
            Map<String, Integer> mapperIdIdentityTypeMap = enterpriseRelationResultList.stream().collect(Collectors
                    .toMap(x -> arg.getMapperApiName().equals(MapperApiNameEnum.ACCOUNT_OBJ.getType()) ? x.getMapperAccountId() : x.getMapperPartnerId(), EnterpriseRelationObj::getIdentityType,
                            (v1, v2) -> v1));
            //没有绑定或者绑定的是同个类型的客户
            crmObjectDatas = crmObjectDatas.stream().filter(val -> mapperIdIdentityTypeMap.get(val.getId()) == null ||
                            Objects.equals(mapperIdIdentityTypeMap.get(val.getId()), arg.getIdentityType()))
                    .collect(Collectors.toList());
        }
        for (ObjectData objectData : crmObjectDatas) {
            EnterpriseInfoData enterpriseInfoData = new EnterpriseInfoData();
            enterpriseInfoData.setObjectId(objectData.getId());
            enterpriseInfoData.setName(objectData.getName());
            list.add(enterpriseInfoData);
        }

        log.info("客户数：{}", list.size());//ignoreI18n
        EnterpriseInfoDataResult enterpriseInfoDataResult = new EnterpriseInfoDataResult();
        enterpriseInfoDataResult.setAccountNameWriteRuleType(AccountNameWriteRuleTypeEnum.ACCOUNT_NAME_ONLY_SELECT_FROM_EXIST_ACCOUNT.getType());
        enterpriseInfoDataResult.setEnterpriseInfoDataList(list);
        return Result.newSuccess(enterpriseInfoDataResult);
    }

    @ResponseBody
    @RequestMapping(value = "/getLinkAppFullErAccountFuncConfig", method = RequestMethod.POST)
    public Result<List<GetLinkAppFullErAccountFuncConfigResult>> getLinkAppFullErAccountFuncConfig(@RequestBody GetLinkAppFullErAccountFuncConfigArg arg) {
        ListByLinkAppIdArg listByLinkAppIdArg = new ListByLinkAppIdArg();
        listByLinkAppIdArg.setLinkAppId(arg.getLinkAppId());
        Result<List<LinkAppFullErAccountFuncConfigVo>> configResult = linkAppFullErAccountFuncConfigService.listByLinkAppId(listByLinkAppIdArg);
        if (!configResult.isSuccess()) {
            return Result.newError(configResult.getErrCode(), configResult.getErrMessage());
        }
        List<GetLinkAppFullErAccountFuncConfigResult> results = BeanUtil.deepCopyList(configResult.getData(), GetLinkAppFullErAccountFuncConfigResult.class);
        return Result.newSuccess(results);
    }

    @ResponseBody
    @RequestMapping(value = "/checkVerifyCodeValidity", method = RequestMethod.POST)
    public Result<VerifyCodeCheckResult> checkVerifyCodeValidity(@RequestBody VerifyCodeAndBindLoginAccountArg arg, @CookieValue(name = "EPXId", required = false) String epxId) {
        if (StringUtils.isBlank(arg.getLoginAccount())) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), ResultCode.PARAMS_ERROR.getDescription());
        }
        VerifyCodeCheckResult checkResult = new VerifyCodeCheckResult();
        Result<VerifyCodeAndBindLoginAccountResult> result;
        Integer codeType;
        if (arg.getLoginAccount().contains("@")) {
            codeType = 2;
            result = emailCodeLoginTypeProvider.verifyCodeAndBindLoginAccount(arg, epxId);
        } else {
            codeType = 1;
            result = phoneCodeLoginTypeProvider.verifyCodeAndBindLoginAccount(arg, epxId);
        }
        if (!result.isSuccess()) {
            return Result.newError(result.getErrCode(), result.getErrMessage());
        }
        checkResult.setCodeType(codeType);
        checkResult.setCodeVerifyToken(result.getData().getLoginParam());
        return Result.newSuccess(checkResult);
    }
}