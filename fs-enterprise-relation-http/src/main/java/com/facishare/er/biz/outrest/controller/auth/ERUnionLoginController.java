package com.facishare.er.biz.outrest.controller.auth;

import com.facishare.appserver.auditlog.api.bean.LoginLogMessage;
import com.facishare.appserver.auditlog.util.LoginLogUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.GuestorIdentityInfoConstants;
import com.facishare.enterprise.common.constant.KeyBuilder;
import com.facishare.enterprise.common.constant.UnionAuthType;
import com.facishare.enterprise.common.constant.VersionIterationConstant;
import com.facishare.enterprise.common.enums.I18nKeyEnum;
import com.facishare.enterprise.common.enums.LoginAccountTypeEnum;
import com.facishare.enterprise.common.enums.LoginErrorEnum;
import com.facishare.enterprise.common.enums.QXMessageEnums;
import com.facishare.enterprise.common.exception.RelationException;
import com.facishare.enterprise.common.exception.UnionLoginException;
import com.facishare.enterprise.common.model.*;
import com.facishare.enterprise.common.report.EsLogSendUtil;
import com.facishare.enterprise.common.report.data.LoginRegisterLogData;
import com.facishare.enterprise.common.result.Result;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.enterprise.common.util.*;
import com.facishare.er.biz.outrest.arg.*;
import com.facishare.er.biz.outrest.common.LoginDataPersistorLogConstant;
import com.facishare.er.biz.outrest.common.LoginErrorI18nKeyConstant;
import com.facishare.er.biz.outrest.controller.auth.data.*;
import com.facishare.er.biz.outrest.controller.auth.data.LoginArg.ThirdArg;
import com.facishare.er.biz.outrest.controller.auth.data.LoginResult.ExtraData;
import com.facishare.er.biz.outrest.controller.auth.handler.AuthHandler;
import com.facishare.er.biz.outrest.controller.auth.loginTypeProvider.EmailCodeLoginTypeProvider;
import com.facishare.er.biz.outrest.controller.auth.loginTypeProvider.PhoneCodeLoginTypeProvider;
import com.facishare.er.biz.outrest.data.*;
import com.facishare.er.biz.outrest.enums.MapperApiNameEnum;
import com.facishare.er.biz.outrest.manager.*;
import com.facishare.er.biz.outrest.manager.arg.EmployeeCardGetArg;
import com.facishare.er.biz.outrest.outrest.AuthOutController;
import com.facishare.er.biz.outrest.outrest.arg.AuthForCepArg;
import com.facishare.er.biz.outrest.remote.channel.ChannelPortalLoginService;
import com.facishare.er.biz.outrest.remote.channel.arg.GetChannelH5LoginPageTemplateArg;
import com.facishare.er.biz.outrest.remote.channel.result.H5ChannelLoginTemplateResult;
import com.facishare.er.biz.outrest.remote.organizationremote.FxiaokeAccountManager;
import com.facishare.er.biz.outrest.remote.otherremote.QixinNotificationManager;
import com.facishare.er.biz.outrest.service.*;
import com.facishare.er.biz.outrest.util.CDNImagePathUtil;
import com.facishare.global.rest.dao.mongo.EnterpriseMetaDataEntityDao;
import com.facishare.global.rest.model.data.LoginEmployeeInfo;
import com.facishare.global.rest.model.entity.CustomLoginErrorPromptConfig;
import com.facishare.global.rest.model.entity.EnterpriseMetaDataEntity;
import com.facishare.linkapp.api.result.LinkAppData;
import com.facishare.linkapp.api.service.HttpDownstreamService;
import com.facishare.linkapp.api.service.HttpLinkAppService;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseEnvironment;
import com.facishare.uc.api.model.param.GetEmployeeEdition;
import com.facishare.uc.api.service.EmployeeAccountService;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.userlogin.api.service.ValidateCodeService;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.crmrestapi.common.contants.AccountFieldContants;
import com.fxiaoke.crmrestapi.common.contants.PartnerFieldContants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.fxiaoke.model.InternationalItem;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import redis.clients.jedis.params.SetParams;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

@Api(tags = {"互联新版登录"})
@Slf4j
@Controller
@RequestMapping("/login")
public class ERUnionLoginController extends BaseAuthController {
    private final String currentLoginUpstreamErrorMsg = I18nUtil.get(I18nKeyEnum.EPI_AUTH_ERUNIONLOGINCONTROLLER_131);
    private static final String ER_INFO = "ERInfo";
    @Autowired
    private HttpAuthConfig config;
    @Autowired
    private AuthService authService;
    @Autowired
    private AuthService authServiceOldManager;
    @Autowired
    private CloudUtil cloudUtil;
    @Autowired
    private PublicEmployeeManager employeeCardService;
    @Autowired
    private HttpDownstreamService httpDownstreamService;
    @Autowired
    private HttpLinkAppService linkAppService;
    @Autowired
    private DownstreamEmployeeLinkAppRoleService downstreamEmployeeLinkAppRoleService;
    @Autowired
    private EIEAConverter eieaConverter;
    private static Map<String, EnterpriseDomainData> cloudRootDomainEnterpriseDomainMap = new HashMap<>();
    @Autowired
    private EnterpriseMetaDataEntityDao enterpriseMetaDataService;
    @Autowired
    private EnterpriseMetaDataService metaDataService;
    @Autowired
    private AuthOutController authOutControllerOldManager;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private QixinNotificationManager qixinNotificationManager;
    @Autowired
    private FxiaokeAccountManager fxiaokeAccountManager;
    @Autowired
    private EnterpriseRelationManager enterpriseRelationEntityDao;
    @Autowired
    private NewAuthProcesser newAuthProcesser;
    @Autowired
    private PublicEmployeeManager publicEmployeeService;
    @Autowired
    private EmployeeAccountService employeeAccountService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EnterpriseRelationObjService enterpriseRelationObjService;
    @Autowired
    private LinkAppManager linkAppManager;
    @Autowired
    private ValidateCodeService validateCodeService;
    @Autowired
    private SmsManager smsManager;
    @Autowired
    private EmailManager emailManager;
    @Autowired
    @Qualifier("jedisSupport")
    private MergeJedisCmd jedis;
    @Autowired
    private EmailCodeLoginTypeProvider emailCodeLoginTypeProvider;
    @Autowired
    private PhoneCodeLoginTypeProvider phoneCodeLoginTypeProvider;
    @Autowired
    private SessionManager sessionManager;
    @Autowired
    private FxiaokeEnterpriseAssociationService fxiaokeEnterpriseAssociationService;
    @Autowired
    private ChannelPortalLoginService channelPortalLoginService;
    @Autowired
    private ChannelPortalLoginTemplateService channelPortalLoginTemplateService;
    // variables_endpoint home_domain
    private static List<String> cloudHomeDomains = new ArrayList<>();
    // 从配置文件fs-user-login-domain获取的home domains
    private static List<String> fsUserLoginDomainCloudHomeDomains = new ArrayList<>();
    // 企业申请的域名白名单，互联登录跳转时，如果企业域名在白名单中，则允许跳转否则会被禁止跳转
    private static List<String> erDomainWhiteList = new ArrayList<>();
    private static String SEND_MAPPER_OBJECT_NO_OWNER_NOTIFY_KEY = "send:mapper:object:no:owner:%s:%s";
    private static Long sendMapperObjectNoOwnerNotifyExpire = 60L;
    private static final String UPDATE_LOGIN_PASSWORD_TOKEN_KEY = "update:login:password:token:%s:%s:%s";
    private static Set<String> testPhones = new HashSet<>();
    private static Set<String> smsSendBlackIps = new HashSet<>();

    static {
        ConfigFactory.getConfig("fs-user-login-domain", config -> {
            String body = config.getString();
            Type type = new TypeToken<Map<String, EnterpriseDomainData>>() {
            }.getType();
            cloudRootDomainEnterpriseDomainMap = JsonUtil.fromJson(body, type);
            if (MapUtils.isNotEmpty(cloudRootDomainEnterpriseDomainMap)) {
                fsUserLoginDomainCloudHomeDomains = cloudRootDomainEnterpriseDomainMap.keySet().stream().map(x -> {
                    return x.replaceAll("https://", "");
                }).collect(Collectors.toList());
            }
        });

        ConfigFactory.getConfig("fs-er-key-gray", config -> {
            String body = config.get("cloudHomeDomains", "");
            cloudHomeDomains = Splitter.on(",").splitToList(body);
            String erDomainWhiteListStr = config.get("erDomainWhiteList", "");
            if (StringUtils.isNotEmpty(erDomainWhiteListStr)) {
                Type type = new TypeToken<List<String>>() {
                }.getType();
                erDomainWhiteList = JsonUtil.fromJson(erDomainWhiteListStr, type);
            }
        });

        ConfigFactory.getConfig("fs-er-properties", config -> {
            sendMapperObjectNoOwnerNotifyExpire = config.getLong("sendMapperObjectNoOwnerNotifyExpire");
            smsSendBlackIps = JsonUtil.fromJson(config.get("smsSendBlackIps", ""), new TypeToken<Set<String>>() {
            }.getType());
        });

        ConfigFactory.getConfig("fs-er-test", config -> {
            String testPhonesStr = config.get("login.sms.phones", "");
            String[] testPhonesArray = testPhonesStr.split(",");
            testPhones = new HashSet<>();
            for (String phone : testPhonesArray) {
                testPhones.add(phone);
            }
        });
    }

    @ApiOperation(value = "切换用户", tags = {"互联登录", "7.5.0", VersionIterationConstant.f_770_login_optime})
    @ResponseBody
    @RequestMapping("/switchEmployee")
    public Result<LoginResult> switchEmployee(@RequestBody SwitchEmployeeArg switchEmployeeArg, HttpServletRequest request, HttpServletResponse response) {
        LoginEmployeeInfo loginUser = getERCookie(request);
        LoginParamData loginParamData = null;
        //处理已存在的erInfo cookie
        if (loginUser == null) {
            loginParamData = decodeLoginParamData(switchEmployeeArg.getLoginParam());
        } else {
            loginParamData = new LoginParamData();
            loginParamData.setAuthType(loginUser.getAuthType());
            loginParamData.setWxAppId(loginUser.getWxAppId());
            loginParamData.setOpenId(loginUser.getOpenId());
            loginParamData.setDownstreamOuterTenantId(switchEmployeeArg.getDownstreamOuterTenantId());
            loginParamData.setDownstreamOuterUid(switchEmployeeArg.getDownstreamOuterUid());
        }
        String upstreamEa = switchEmployeeArg.getUpstreamEa();
        if (StringUtils.isEmpty(upstreamEa)) {
            AuthHandler matchHandler = authHandlerMapping.findMatchHandler(loginParamData.getAuthType());
            LoginArg loginArg = new LoginArg();
            ThirdArg thirdArg = new ThirdArg();
            thirdArg.setWxAppId(loginParamData.getWxAppId());
            loginArg.setThirdArg(thirdArg);
            upstreamEa = matchHandler.getUpstreamEa(loginArg, loginParamData);
        }
        if (Strings.isNullOrEmpty(upstreamEa)) {
            return Result.newError(ResultCode.PARAMS_UPSTREAM_NULL.getErrorCode(), LoginErrorEnum.NONE_EA.getDesc());
        }
        if (loginUser != null) {
            Result<PublicEmployeeObj> employeeCardRes = employeeCardService
                .getEmployeeCard(loginUser.getUpstreamEa(), new EmployeeCardGetArg(loginUser.getDownstreamOuterTenantId(), loginUser.getDownstreamOuterUid()));
            if (employeeCardRes.getData() == null) {
                return Result.newError(ResultCode.INVALID_OUTER_UID);
            }
            if (LoginAccountTypeEnum.isMobile(loginUser.getLoginAccountData().getAccountType())) {
                String mobile = employeeCardRes.getData().getMobile();
                loginParamData.setLoginAccountData(LoginAccountData.newMobileAccount(mobile));
            } else if (LoginAccountTypeEnum.isLoginEmail(loginUser.getLoginAccountData().getAccountType())) {
                String loginEmail = employeeCardRes.getData().getLoginEmail();
                loginParamData.setLoginAccountData(LoginAccountData.newLoginEmailAccount(loginEmail));
            } else {
                return Result.newError(ResultCode.UNSUPPORT_FUNCTION);
            }
        }

        LoginArg loginArg = new LoginArg();
        loginArg.setAuthType(loginParamData.getAuthType());
        loginArg.setUpstreamEa(upstreamEa);
        loginArg.setDownstreamOuterTenantId(switchEmployeeArg.getDownstreamOuterTenantId());
        loginArg.setLoginParam(AESUtil.objectEncode(config.getAesKey(), loginParamData));
        // 小程序切换时没有cookie
        ThirdArg thirdArg = new ThirdArg();
        thirdArg.setWxAppId(loginParamData.getWxAppId());
        loginArg.setThirdArg(thirdArg);
        return login(loginArg, request, response);
    }

    @ResponseBody
    @RequestMapping("/mergeLoginParam")
    public Result<MergeLoginParamResult> mergeLoginParam(@RequestBody MergeLoginParamArg arg) {
        MergeLoginParamResult res = new MergeLoginParamResult();
        if (StringUtils.isBlank(arg.getOuterUidLoginParam())) {
            res.setLoginParam(arg.getLoginParam());
            return Result.newSuccess(res);
        }
        if (StringUtils.isBlank(arg.getLoginParam())) {
            return Result.newError(ResultCode.LOGIN_PARAM_NOT_NULL);
        }
        LoginParamData outerUidLoginParamData = AESUtil.objectDecode(config.getAesKey(), arg.getOuterUidLoginParam(), LoginParamData.class);
        LoginParamData loginParamData = AESUtil.objectDecode(config.getAesKey(), arg.getLoginParam(), LoginParamData.class);
        if (outerUidLoginParamData.getLoginAccountData() != null) {
            // loginparam只有手机号
            loginParamData.setLoginAccountData(outerUidLoginParamData.getLoginAccountData());
        }
        if (StringUtils.isNotBlank(outerUidLoginParamData.getUpstreamEa())) {
            loginParamData.setUpstreamEa(outerUidLoginParamData.getUpstreamEa());
        }
        // loginparam 包含互联用户所有信息
        if (outerUidLoginParamData.getDownstreamOuterUid() != null) {
            loginParamData.setDownstreamOuterUid(outerUidLoginParamData.getDownstreamOuterUid());
        }
        if (outerUidLoginParamData.getDownstreamOuterTenantId() == null) {
            loginParamData.setDownstreamOuterTenantId(outerUidLoginParamData.getDownstreamOuterTenantId());
        }
        res.setLoginParam(AESUtil.objectEncode(config.getAesKey(), loginParamData));
        return Result.newSuccess(res);
    }

    @ResponseBody
    @RequestMapping("/sendWxSmallProgramPhoneAuthFailQixinNotify")
    public Result sendWxSmallProgramPhoneAuthFailQixinNotify(@RequestBody SendWxSmallProgramPhoneAuthFailQixinNotifyArg arg) {
        if (!CloudUtil.isInFstest() && !CloudUtil.isInFstestGray()) {
            Result<WechatUserBaseResult> wechatUserBaseResultResult = authService.jscode2sessionForWxSmallProgram(arg.getFsAppId(), arg.getWxAppId(), arg.getCode());
            if (!wechatUserBaseResultResult.isSuccess()) {
                String errDesc = I18nUtil.get(LoginErrorI18nKeyConstant.CODE_PARSE_ERROR, "code 解析异常：%s");//ignoreI18n
                throw new UnionLoginException(ResultCode.LOGIN_VALIDATE_ERROR.getErrorCode(), String.format(errDesc, wechatUserBaseResultResult.getErrMessage()));
            }
        }
        String upstreamEa = null;
        if (StringUtils.isNotBlank(arg.getEa())) {
            upstreamEa = arg.getEa();
        }
        if (StringUtils.isNotBlank(arg.getLoginParam()) && StringUtils.isBlank(upstreamEa)) {
            try {
                LoginParamData loginParamData = AESUtil.objectDecode(config.getAesKey(), arg.getLoginParam(), LoginParamData.class);
                upstreamEa = loginParamData.getUpstreamEa();
            } catch (Exception e) {
                log.warn("", e);
            }
        }
        if (StringUtils.isBlank(upstreamEa) && StringUtils.isNotBlank(arg.getWxAppId())) {
            String belongEa = authService.getBelongEaByWxSmallProgramAppId(arg.getFsAppId(), arg.getWxAppId()).getData();
            if (!Strings.isNullOrEmpty(belongEa) && !"all".equalsIgnoreCase(belongEa)) {
                upstreamEa = belongEa;
            }
            if ("all".equalsIgnoreCase(belongEa)) {
                if (CloudUtil.isInFstest() || CloudUtil.isInFstestGray()) {
                    upstreamEa = "76517";
                } else {
                    upstreamEa = "fs";
                }
            }
        }
        if (StringUtils.isBlank(upstreamEa)) {
            return Result.newSuccess();
        }
        String wxSmallProgramName = arg.getWxAppId();
        Result<String> wxSmallProgramNameByWxSmallProgramAppIdResult = authService.getWxSmallProgramNameByWxSmallProgramAppId(arg.getWxAppId());
        if (StringUtils.isNotBlank(wxSmallProgramNameByWxSmallProgramAppIdResult.getData())) {
            wxSmallProgramName = wxSmallProgramNameByWxSmallProgramAppIdResult.getData();
        }
        try {
            InternationalItem internationalItem = new InternationalItem(QXMessageEnums.USER_LOGIN_FAIL.getKey(), wxSmallProgramName);
            String format = I18nKeyEnum.EIP_ENTERPRISE_ERUNIONLOGINCONTROLLER_399.getDefaultValue();
            qixinNotificationManager.sendTextMessageToAdmin(upstreamEa, String.format(format, wxSmallProgramName), internationalItem);
        } catch (Exception e) {
            log.warn("", e);
        }
        return Result.newSuccess();
    }

    /**
     * 获取与当前cookie标记身份具有相同手机号的可切换身份列表
     */
    @ApiOperation(value = "列出切换用户列表", tags = {"互联登录", "7.5.0", VersionIterationConstant.f_770_login_optime})
    @ResponseBody
    @RequestMapping("/listSwitchableEmployees")
    public Result<List<EmployeeVo>> listSwitchableEmployees(@RequestBody(required = false) ListSwitchableEmployeesArg arg, HttpServletRequest request) {
        // 是否过滤无权限可切换人
        boolean hasFilterNoAuthSwitchableEmployee = arg != null && !Strings.isNullOrEmpty(arg.getFsAppId());
        //处理已存在的erInfo cookie
        LoginEmployeeInfo loginUser = getERCookie(request);
        log.debug("listSwitchableEmployees:loginUser-->{}", loginUser);
        LoginParamData loginParamData = null;
        String upstreamEa = null;
        Long downstreamOuterUid = null;
        LoginAccountData loginAccount = null;
        if (loginUser == null) {
            if (arg == null || arg.getLoginParam() == null) {
                return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), "loginParam not be null");
            }
            loginParamData = decodeLoginParamData(arg.getLoginParam());
        } else {
            // 如果是游客，则返回空列表
            if (GuestorIdentityInfoConstants.OUTER_TENANTID.equals(loginUser.getDownstreamOuterTenantId()) && GuestorIdentityInfoConstants.OUTER_TENANTID.equals(loginUser.getDownstreamOuterUid())) {
                log.debug("listSwitchableEmployees:exit:outer tenantid");
                return Result.newSuccess(new ArrayList<>());
            }
            if (arg != null && BooleanUtils.isTrue(loginUser.getSelectUpstream())) {
                arg.setSwitchAllUpstream(true);
            }
            //获取登录方式处理handler
            loginParamData = new LoginParamData();
            loginParamData.setAuthType(loginUser.getAuthType());
            loginParamData.setWxAppId(loginUser.getWxAppId());
            loginParamData.setOpenId(loginUser.getOpenId());
            loginParamData.setAuthType(loginUser.getAuthType());
            loginParamData.setDownstreamOuterUid(loginUser.getDownstreamOuterUid());
            Result<PublicEmployeeObj> employeeCardRes = employeeCardService
                .getEmployeeCard(loginUser.getUpstreamEa(), new EmployeeCardGetArg(loginUser.getDownstreamOuterTenantId(), loginUser.getDownstreamOuterUid()));
            if (employeeCardRes.getData() == null) {
                return Result.newError(ResultCode.INVALID_OUTER_UID);
            }
            if (loginUser.getLoginAccountData() != null) {
                if (LoginAccountTypeEnum.isMobile(loginUser.getLoginAccountData().getAccountType())) {
                    loginParamData.setLoginAccountData(LoginAccountData.newMobileAccount(employeeCardRes.getData().getMobile()));
                } else if (LoginAccountTypeEnum.isLoginEmail(loginUser.getLoginAccountData().getAccountType())) {
                    String loginEmail = employeeCardRes.getData().getLoginEmail();
                    loginParamData.setLoginAccountData(LoginAccountData.newLoginEmailAccount(loginEmail));
                }
            }
        }
        if (loginParamData == null || loginParamData.getAuthType() == null) {
            return Result.newError(ResultCode.ER_LOGIN_SESSION_INVALID_ERROR);
        }
        AuthHandler matchHandler = authHandlerMapping.findMatchHandler(loginParamData.getAuthType());
        LoginArg loginArg = new LoginArg();
        if (loginUser == null) {
            loginArg.setAuthType(loginParamData.getAuthType());
            LoginArg.ThirdArg thirdArg = new ThirdArg();
            thirdArg.setWxAppId(loginParamData.getWxAppId());
            loginArg.setThirdArg(thirdArg);
            loginArg.setFsAppId(arg.getFsAppId());
            if (arg != null && arg.getUpstreamEa() != null) {
                upstreamEa = arg.getUpstreamEa();
            } else {
                upstreamEa = matchHandler.getUpstreamEa(loginArg, loginParamData);
            }

            downstreamOuterUid = loginParamData.getDownstreamOuterUid();
            // 第三方登录切换身份不存在无erInfo cookie情况 此处不需要兼容邮箱
            loginAccount = matchHandler.getLoginAccount(upstreamEa, loginArg, loginParamData);
            if (loginAccount != null && LoginAccountTypeEnum.isLoginEmail(loginAccount.getAccountType())) {
                return Result.newError(ResultCode.NOT_SUPPORT_LOGIN_EMAIL);
            }
            if (loginAccount == null) {
                return Result.newError(ResultCode.TO_PHONE_BIND);
            }
        } else {
            upstreamEa = loginUser.getUpstreamEa();
            downstreamOuterUid = loginUser.getDownstreamOuterUid();
        }

        loginArg.setAuthType(loginParamData.getAuthType());
        if (arg == null || !arg.getSwitchAllUpstream()) {
            loginArg.setUpstreamEa(upstreamEa);
        }
        List<EmployeeVo> bindOuterAccounts;
        try {
            LoginAccountData loginAccountData;
            if (loginUser != null && loginUser.getLoginAccountData() != null) {
                loginAccountData = loginUser.getLoginAccountData();
            } else {
                loginAccountData = loginAccount;
            }
            if (loginAccountData != null && LoginAccountTypeEnum.isMobile(loginAccountData.getAccountType())) {
                loginAccountData.setLoginAccount(MobileUtil.replace86Code(loginAccountData.getLoginAccount()));
            }

            bindOuterAccounts = publicEmployeeService.listDownstreamEmployeeVosByLoginAccount(upstreamEa, loginAccountData);
            if (!org.springframework.util.CollectionUtils.isEmpty(bindOuterAccounts)) {
                Iterator<EmployeeVo> evIterator = bindOuterAccounts.iterator();
                while (evIterator.hasNext()) {
                    EmployeeVo ev = evIterator.next();
                    if (!Strings.isNullOrEmpty(loginArg.getUpstreamEa()) && !loginArg.getUpstreamEa().equals(ev.getUpstreamEa())) {
                        evIterator.remove();
                        continue;
                    }
                }
            }
            log.info("listSwitchableEmployees:listPhoneBindOuterAccount: loginAccountData:{}, loginArg:{}, loginParamData:{},bindOuterAccounts={}", loginAccountData, loginArg, loginParamData,
                bindOuterAccounts);
        } catch (UnionLoginException e) {
            log.warn("listSwitchableEmployees:UnionLoginException, errorMsg:{}", e);
            bindOuterAccounts = new ArrayList<>();
        }
        Map<String, List<Long>> upstreamEaDownstreamOuterUidMap = bindOuterAccounts.stream()
            .collect(Collectors.groupingBy(x -> x.getUpstreamEa(), Collectors.mapping(x -> x.getDownstreamOuterUid(), Collectors.toList())));

        Map<String, Map<Long, Boolean>> upstreamEaDownstreamOuterUidHasLinkAppAuthorizeMap = new HashMap<>();
        if (hasFilterNoAuthSwitchableEmployee && arg != null) {
            for (Entry<String, List<Long>> entry : upstreamEaDownstreamOuterUidMap.entrySet()) {
                String tmpUpstreamEa = entry.getKey();
                List<Long> downstreamOuterUids = entry.getValue();
                Map<Long, Boolean> downstreamUidAuthMap = httpDownstreamService.batchHasLinkAppAuthorize(tmpUpstreamEa, arg.getFsAppId(), downstreamOuterUids).getData();
                upstreamEaDownstreamOuterUidHasLinkAppAuthorizeMap.put(tmpUpstreamEa, downstreamUidAuthMap);
            }
            log.debug("upstreamEaDownstreamOuterUidHasLinkAppAuthorizeMap:{}", upstreamEaDownstreamOuterUidHasLinkAppAuthorizeMap);
        }
        Iterator<EmployeeVo> iterator = bindOuterAccounts.iterator();
        while (iterator.hasNext()) {
            EmployeeVo bindOuterAccount = iterator.next();
            if (bindOuterAccount.getUpstreamEa().equals(upstreamEa) && bindOuterAccount.getDownstreamOuterUid().equals(downstreamOuterUid)) {
                bindOuterAccount.setLogined(true);
            }
            // 如果传了应用需要过滤无权限的可切换人
            if (hasFilterNoAuthSwitchableEmployee) {
                Map<Long, Boolean> outerUidHasAuthMap = upstreamEaDownstreamOuterUidHasLinkAppAuthorizeMap.get(bindOuterAccount.getUpstreamEa());
                if (Boolean.FALSE.equals(outerUidHasAuthMap.getOrDefault(bindOuterAccount.getDownstreamOuterUid(), false))) {
                    iterator.remove();
                }
            }
        }
        // 查询角色列表
        if (arg != null && arg.getIsNeedRoles() != null && arg.getIsNeedRoles()) {
            Map<Long, List<OuterUserAppRoleData>> downstreamOuterUidOuterUserAppRoleDataMap = new HashMap<>();
            for (Entry<String, List<Long>> entry : upstreamEaDownstreamOuterUidMap.entrySet()) {
                Map<Long, List<OuterUserAppRoleData>> map = downstreamEmployeeLinkAppRoleService.getDownstreamOuterUidOuterUserAppRoleDataMap(entry.getKey(), entry.getValue()).getData();
                downstreamOuterUidOuterUserAppRoleDataMap.putAll(map);
            }
            log.debug("downstreamOuterUidOuterUserAppRoleDataMap:{}", downstreamOuterUidOuterUserAppRoleDataMap);
            for (EmployeeVo employeeVo : bindOuterAccounts) {
                employeeVo.setOuterUserAppRoleDataList(downstreamOuterUidOuterUserAppRoleDataMap.get(employeeVo.getDownstreamOuterUid()));
            }
        }
        // 设置头像cdn地址
        for (EmployeeVo outerAccount : bindOuterAccounts) {
            if (StringUtils.isEmpty(outerAccount.getProfileImage())) {
                continue;
            }
            String cdnPath = CDNImagePathUtil.toCDNPath(outerAccount.getProfileImage(), outerAccount.getUpstreamEa());
            outerAccount.setProfileImage(cdnPath);
        }
        return Result.newSuccess(bindOuterAccounts);
    }

    /**
     * 验证调整域名
     */
    @ApiOperation(value = "验证调整域名", tags = {"互联新版登录", "7.2.0"})
    @ResponseBody
    @RequestMapping(value = "/verifyRediectUrl")
    public Result<Boolean> verifyRedirectUrl(@RequestBody VerifyRediectUrlArg verifyRediectUrlArg, HttpServletRequest request) {
        return Result.newSuccess(verifyRedirectUrl(verifyRediectUrlArg.getRediectUrl()));
    }

    public Boolean verifyRedirectUrl(String redirectUrl) {
        if (StringUtils.isBlank(redirectUrl)) {
            log.warn("redirectUrl is empty");
            return false;
        }
        if (validateRedirectUrlByCloudHomeDomain(redirectUrl, cloudHomeDomains)) {
            return true;
        }

        if (validateRedirectUrlByCloudHomeDomain(redirectUrl, fsUserLoginDomainCloudHomeDomains)) {
            return true;
        }
        if (validateRedirectUrlByCloudHomeDomain(redirectUrl, erDomainWhiteList)) {
            return true;
        }

        return false;
    }

    private boolean validateRedirectUrlByCloudHomeDomain(String redirectUrl, List<String> cloudHomeDomains) {
        if (CollectionUtil.isEmpty(cloudHomeDomains)) {
            return false;
        }
        for (String cloudHomeDomain : cloudHomeDomains) {
            String cloudDomain = cloudHomeDomain;
            cloudDomain = UrlUtil.getTopDomain(cloudDomain);
            String topDomain = UrlUtil.getTopDomain(redirectUrl);
            if (topDomain != null && cloudDomain != null && topDomain.equalsIgnoreCase(cloudDomain)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 小程序发送认证验证码
     */
    @ApiOperation(value = "发送手机号验证码", tags = {"互联新版登录", "7.2.0"})
    @ResponseBody
    @RequestMapping(value = "/sendVerifyCodeSnsMessage")
    public Result<Void> sendVerifyCodeSnsMessage(@RequestBody SendVerifyCodeSnsMessageArg sendVerifyCodeSnsMessageArg, @CookieValue(value = ER_INFO, required = false) String erInfoCookie,
        HttpServletRequest request) {
        String remoteIp = ServletUtil.getRemoteIp(request);
        if (StringUtils.isEmpty(erInfoCookie)) {
            return Result.newSuccess();
        }
        LoginEmployeeInfo empInfo = sessionManager.getLoginInfoByErInfoCookie(erInfoCookie);
        if (empInfo == null) {
            log.info("sendVerifyCodeSnsMessage erinfo cookie is null,request referer:{}", request.getHeader("referer"));
            return Result.newSuccess();
        }
        if (smsSendBlackIps != null && (smsSendBlackIps.contains("*") || smsSendBlackIps.contains(remoteIp))) {
            return Result.newSuccess();
        }
        if (StringUtils.isNotEmpty(sendVerifyCodeSnsMessageArg.getLoginParam())) {
            LoginParamData loginParamData = AESUtil.objectDecode(config.getAesKey(), sendVerifyCodeSnsMessageArg.getLoginParam(), LoginParamData.class);
            if (StringUtils.isNotEmpty(loginParamData.getUpstreamEa())) {
                sendVerifyCodeSnsMessageArg.setEa(loginParamData.getUpstreamEa());
            }
        }
        return authService.sendVerifyCodeSnsMessage(sendVerifyCodeSnsMessageArg.getPhone(), sendVerifyCodeSnsMessageArg.getEa(), ServletUtil.getFirstAcceptLanguage(request));
    }

    @ApiOperation(value = "验证ea")
    @ResponseBody
    @RequestMapping(value = "/verifyEa")
    public Result<Boolean> verifyEa(@RequestBody VerifyEaArg arg) {
        if (StringUtils.isEmpty(arg.getEa())) {
            return Result.newSuccess(false);
        }
        try {
            eieaConverter.enterpriseAccountToId(arg.getEa());
            return Result.newSuccess(true);
        } catch (Exception e) {
            return Result.newSuccess(false);
        }
    }

    @ApiOperation(value = "获取互联登陆页配置", tags = {VersionIterationConstant.f_820_optimize_jiangxch})
    @ResponseBody
    @RequestMapping(value = "/getErLoginWelcomePageConfig", method = RequestMethod.POST)
    public Result<EnterpriseErLoginWelcomePageConfigVo> getErLoginWelcomePageConfig(@RequestBody GetErLoginWelcomePageConfigArg arg) {
        String upstreamEa = getEaByEaOrLoginParamOrThirdAppId(arg.getUpstreamEa(), arg.getLoginParam(), arg.getAuthType(), arg.getContext());
        if (StringUtils.isBlank(upstreamEa) && StringUtils.isEmpty(arg.getDomainName())) {
            return Result.newSuccess(EnterpriseErLoginWelcomePageConfigVo.newEmpty());
        }
        EnterpriseErLoginWelcomePageConfigVo configData = EnterpriseErLoginWelcomePageConfigVo.newEmpty();
        String themeColor;
        if (StringUtils.isNotEmpty(upstreamEa) && StringUtils.isNotEmpty(arg.getLinkAppId())) {
            int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
            com.facishare.global.rest.common.HeaderObj headerObj = com.facishare.global.rest.common.HeaderObj.newInstance(tenantId);
            GetChannelH5LoginPageTemplateArg templateArg = new GetChannelH5LoginPageTemplateArg();
            templateArg.setUpstreamEa(upstreamEa);
            templateArg.setLinkAppId(arg.getLinkAppId());
            com.facishare.global.rest.result.Result<H5ChannelLoginTemplateResult> templateResult = channelPortalLoginService.getChannelH5LoginPageTemplate(headerObj, templateArg);
            if (templateResult.isSuccess() && templateResult.getData() != null) {
                themeColor = metaDataService.getThemeColor(upstreamEa);
                configData = toEnterpriseErLoginWelcomePageConfigVo(upstreamEa, themeColor, templateResult.getData());
            }
        } else if (StringUtils.isNotEmpty(arg.getDomainName())) {
            Integer upstreamEI = null;
            if (StringUtils.isEmpty(upstreamEa)) {
                upstreamEI = channelPortalLoginTemplateService.getDomainBindEiByDomainName(arg.getDomainName());
                if (upstreamEI != null) {
                    upstreamEa = eieaConverter.enterpriseIdToAccount(upstreamEI);
                }
            } else if (StringUtils.isNotEmpty(upstreamEa)) {
                upstreamEI = eieaConverter.enterpriseAccountToId(upstreamEa);
            }
            if (StringUtils.isNotEmpty(upstreamEa) && upstreamEI != null) {
                com.facishare.global.rest.common.HeaderObj headerObj = com.facishare.global.rest.common.HeaderObj.newInstance(upstreamEI);
                GetChannelH5LoginPageTemplateArg templateArg = new GetChannelH5LoginPageTemplateArg();
                templateArg.setUpstreamEa(upstreamEa);
                templateArg.setDomainName(arg.getDomainName());
                com.facishare.global.rest.result.Result<H5ChannelLoginTemplateResult> templateResult = channelPortalLoginService.getChannelH5LoginPageTemplate(headerObj, templateArg);
                if (templateResult.isSuccess() && templateResult.getData() != null) {
                    themeColor = metaDataService.getThemeColor(upstreamEa);
                    configData = toEnterpriseErLoginWelcomePageConfigVo(upstreamEa, themeColor, templateResult.getData());
                }
            }
        }
        return Result.newSuccess(configData);
    }


    private EnterpriseErLoginWelcomePageConfigVo toEnterpriseErLoginWelcomePageConfigVo(String upstreamEa, String themeColor, H5ChannelLoginTemplateResult template) {
        EnterpriseErLoginWelcomePageConfigVo configVo = new EnterpriseErLoginWelcomePageConfigVo();
        configVo.setEa(upstreamEa);
        configVo.setErLoginWelcomePageEnterpriseName(template.getEnterpriseName());
        configVo.setErLoginWelcomePageEnterpriseLogoUrl(template.getPageEnterpriseLogoUrl());
        configVo.setErLoginWelcomePageEnterpriseLogoNpath(template.getPageEnterpriseLogoNPath());
        configVo.setErLoginWelcomePageEnterpriseLogoShowSwitch(false);// 应该废弃了
        configVo.setErLoginWelcomePageTitle(template.getPageTitle());
        configVo.setErLoginWelcomePageContent(template.getPageContent());
        configVo.setErLoginWelcomePageEnterpriseNameDisplayMode(template.getPageEnterpriseNameDisplayMode());
        configVo.setErLoginWelcomePageEnterpriseLogoDisplayMode(template.getPageEnterpriseLogoDisplayMode());
        configVo.setEnterpriseInfoName(template.getEnterpriseInfoName());
        configVo.setEnterpriseInfoLogoUrl(template.getEnterpriseInfoLogoUrl());
        configVo.setEnterpriseInfoLogoPath(template.getEnterpriseInfoLogoPath());
        configVo.setErLoginWelcomePageDefaultLanguage(template.getPageDefaultLanguage());
        configVo.setVendorLoginProtocolSwitch(template.getVendorLoginProtocolSwitch());
        configVo.setVendorLoginProtocolTitle(template.getVendorLoginProtocolTitle());
        configVo.setVendorLoginProtocolContent(template.getVendorLoginProtocolContent());
        configVo.setVendorLoginProtocolContentJson(template.getVendorLoginProtocolContentJson());
        configVo.setThemeColor(themeColor);

        configVo.setH5LoginSupportTypes(template.getH5LoginSupportTypes());
        configVo.setWxSmallProgramLoginSupportTypes(template.getWxSmallProgramLoginSupportTypes());
        configVo.setLoginSupportTypes(template.getLoginSupportTypes());
        configVo.setDefaultLoginType(template.getDefaultLoginType());
        configVo.setSupportRegister(template.getSupportRegister());
        configVo.setIsShowRegisterConfig(template.getIsShowRegisterConfig());
        configVo.setRegisterHint(template.getRegisterHint());
        configVo.setLoginRegisterConfigId(template.getLoginRegisterConfigId());
        configVo.setRegisterActiveType(template.getRegisterActiveType());
        return configVo;
    }

    /**
     * 通过loginParam 或 第三方appid 获取企业ea
     *
     * @param ea 上游企业ea
     * @param loginParam 登录参数
     * @param authType 登录渠道类型
     * @param context 第三方应用ID（wxAppId）
     * @return 上游企业ea
     */
    public String getEaByEaOrLoginParamOrThirdAppId(String ea, String loginParam, Integer authType, String context) {
        String upstreamEa = ea;
        if (StringUtils.isBlank(upstreamEa) && StringUtils.isNotBlank(loginParam)) {
            try {
                LoginParamData loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParam, LoginParamData.class);
                upstreamEa = loginParamData.getUpstreamEa();
            } catch (IllegalArgumentException e) {
                log.warn("", e);
            }
        }
        if (StringUtils.isBlank(upstreamEa) && authType != null && StringUtils.isNotEmpty(context)) {
            upstreamEa = newAuthProcesser.getUpstreamEa(authType, context);
        }
        return upstreamEa;
    }

    /**
     * 通過EncodeInfo獲取員工信息
     */
    @ApiOperation(value = "服务号、web扫描、小程序根据验证码采集手机信息", tags = {"互联新版登录", "7.2.0", VersionIterationConstant.f_770_login_optime})
    @ResponseBody
    @RequestMapping(value = "/bindPhoneByVerifyCode")
    public Result<BindPhoneByVerifyCodeResult> bindPhoneByVerifyCode(@RequestBody BindPhoneByVerifyCodeArg bindPhoneByVerifyCodeArg) {
        Result<Boolean> validateRet = authService.validatePhoneAndVerifyCode(bindPhoneByVerifyCodeArg.getPhone(), bindPhoneByVerifyCodeArg.getVerifyCode());
        if (!validateRet.isSuccess()) {
            if (CloudUtil.isInCloud()) {
                log.warn("[redirectCloudError],bindPhoneByVerifyCodeArg={}", bindPhoneByVerifyCodeArg);
                Result<Boolean> res = authServiceOldManager.validatePhoneAndVerifyCode(bindPhoneByVerifyCodeArg.getPhone(), bindPhoneByVerifyCodeArg.getVerifyCode());
                if (!res.isSuccess()) {
                    return Result.newError(res.getErrCode(), res.getErrMessage());
                }
            } else {
                return Result.newError(validateRet.getErrCode(), validateRet.getErrMessage());
            }
        }
        LoginParamData loginParamData = AESUtil.objectDecode(config.getAesKey(), bindPhoneByVerifyCodeArg.getLoginParam(), LoginParamData.class);
        loginParamData.setLoginAccountData(LoginAccountData.newMobileAccount(bindPhoneByVerifyCodeArg.getPhone()));
        BindPhoneByVerifyCodeResult res = new BindPhoneByVerifyCodeResult();
        res.setLoginParam(AESUtil.objectEncode(config.getAesKey(), loginParamData));
        return Result.newSuccess(res);
    }

    @ApiOperation(value = "ticket换取ErInfo cookie入口", tags = {"互联新版登录", "7.3.6"})
    @ResponseBody
    @RequestMapping("/loginWithTick")
    public Result<LoginResult> loginWithTick(@RequestBody GenerateCookieByTicketArg generateCookieByTicketArg, HttpServletRequest request, HttpServletResponse response) throws Exception {
        LoginEmployeeInfo employeeInfo = decodeTicket(generateCookieByTicketArg.getTicket());
        if (employeeInfo == null) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), "ticket is error");
        }
        // 互联用户密码被修改或重置，需要作废tick
        if (employeeInfo.getHasResetPassWord()) {
            return Result.newError(ResultCode.ER_LOGIN_HAS_RESET_PASSWORD_ERROR.getErrorCode(), ResultCode.ER_LOGIN_HAS_RESET_PASSWORD_ERROR.getDescription());
        }
        handleLoginSuccess(request, response, employeeInfo);
        LoginResult loginResult = LoginResult.newIntance(LoginStatusEnum.LOGIN_SUCCESS);
        loginResult.setLoginUser(employeeInfo);
        CloudConfigVo cloudConfigVo = cloudUtil.getCloudConfig(employeeInfo.getUpstreamEa()).getData();
        loginResult.setDomain(cloudConfigVo.getCloudDomain());
        return Result.newSuccess(loginResult);
    }

    /**
     * 从cookie中获取当前已登录互联用户身份并生成ticket
     *
     * @param request 请求对象
     * @param response 响应对象
     * @return 登录成功的ticket
     */
    @ResponseBody
    @RequestMapping(value = "/getErTicket", method = {RequestMethod.GET, RequestMethod.POST})
    public Result<String> getErTicket(HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginEmployeeInfo loginUser = getERCookie(request);
            if (loginUser == null) {
                return Result.newError(ResultCode.ER_LOGIN_SESSION_INVALID_ERROR);
            }
            String erTicket = encodeTicket(loginUser);
            return Result.newSuccess(erTicket);
        } catch (Exception ex) {
            log.error("getErTicket error:", ex);
            return Result.newError(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 通过ticket 做互联身份识别,并且跳转到目标地址 提供给移动端使用（IOS18系统小程序嵌iframe跨域问题，导致无法种cookie,需要使用ticket参数传递登录身份）
     *
     * @param erTicket 互联登录ticket
     * @param redirectUrl 身份验证通过后的跳转
     * @param request 请求对象
     * @param response 响应对象
     * @throws Exception
     */
    @RequestMapping(value = "/redirectWithTicket", method = {RequestMethod.GET, RequestMethod.POST})
    public void redirectWithTicket(@RequestParam(value = "ticket") String erTicket, @RequestParam(value = "redirectUrl", required = false) String redirectUrl, HttpServletRequest request,
        HttpServletResponse response) throws IOException {
        try {
            if (StringUtils.isEmpty(erTicket)) {
                log.info("ticket isNullOrEmpty value={}", erTicket);
                response.sendRedirect(newAuthProcesser.urlAppendParam(config.getInvalidResourceUrl(), ImmutableMap.of("traceId", TraceContext.get().getTraceId())));
                return;
            }
            LoginEmployeeInfo employeeInfo = decodeTicket(erTicket);
            if (employeeInfo == null) {
                log.info("ticket invalid value={}", erTicket);
                response.sendRedirect(newAuthProcesser.urlAppendParam(config.getInvalidResourceUrl(), ImmutableMap.of("traceId", TraceContext.get().getTraceId())));
                return;
            }
            if (!StringUtils.isEmpty(redirectUrl) && !verifyRedirectUrl(redirectUrl)) {
                log.info("redirectUrl invalid value={}", redirectUrl);
                response.sendRedirect(newAuthProcesser.urlAppendParam(config.getInvalidResourceUrl(), ImmutableMap.of("traceId", TraceContext.get().getTraceId())));
                return;
            }
            // 互联用户密码被修改或重置，需要作废tick
            if (employeeInfo.getHasResetPassWord()) {
                log.info("password has reset,ticket invalid value={}", redirectUrl);
                response.sendRedirect(newAuthProcesser.urlAppendParam(config.getInvalidResourceUrl(), ImmutableMap.of("traceId", TraceContext.get().getTraceId())));
                return;
            }
            handleLoginSuccess(request, response, employeeInfo);
            // 跳转到目标地址
            response.sendRedirect(redirectUrl);
        } catch (Exception ex) {
            log.error("redirectWithTicket(ticket={},redirectUrl={}) error,", erTicket, redirectUrl, ex);
            String msg = I18nUtil.get(I18nKeyEnum.EIP_ERBIZ_AUTHCONTROLLER_313) + "，traceId=" + TraceContext.get().getTraceId();
            msg = UrlUtil.encode(msg);
            response.sendRedirect(String.format(config.getErrorRemindTemplate(), msg, ""));
        }
    }

    @ApiOperation(value = "设置个人语言", tags = {"750"})
    @ResponseBody
    @RequestMapping(value = "setPersonalLocale", method = RequestMethod.POST)
    public Result<Void> setPersonalLocale(@RequestBody SetPersonalLocaleArg arg, HttpServletRequest request, HttpServletResponse response) {
        LoginEmployeeInfo erCookie = getERCookie(request);
        if (erCookie == null || GuestorIdentityInfoConstants.OUTER_UID.equals(erCookie.getDownstreamOuterUid())) {
            Result.newError(ResultCode.NOT_SUPPORT_CONFIG_LANGUAGE);
        }
        if (erCookie == null || StringUtils.isBlank(erCookie.getUpstreamEa())) {
            return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(), "erCookie.getUpstreamEa() = null");
        }
        employeeCardService.updatePersonalErLocale(erCookie.getUpstreamEa(), erCookie.getDownstreamOuterUid(), arg.getErLocale());
        addErLangCookie(erCookie.getUpstreamEa(), request, response, arg.getErLocale());
        return Result.newSuccess();
    }

    @ApiOperation(value = "第三方统一登录入口/包含FS登录", tags = {"互联新版登录", "7.3.6", VersionIterationConstant.f_770_login_optime})
    @ResponseBody
    @RequestMapping({"", "/"})
    public Result<LoginResult> login(@RequestBody LoginArg loginArg, HttpServletRequest request, HttpServletResponse response) {
        if (loginArg != null && StringUtils.isNotBlank(loginArg.getUpstreamEa())) {
            try {
                eieaConverter.enterpriseAccountToId(loginArg.getUpstreamEa());
            } catch (Exception e) {
                return Result.newError(ResultCode.ENTERPRISE_ACCOUNT_NOT_VALID);
            }
        }
        if (loginArg != null && StringUtils.isNotEmpty(loginArg.getDownstreamEa()) && loginArg.getDownstreamOuterTenantId() == null) {
            if (checkDownEaIsValid(loginArg.getDownstreamEa())) {
                try {
                    Long downstreamOuterTenantId = fxiaokeEnterpriseAssociationService.getOuterTenantIdByEa(loginArg.getDownstreamEa()).getData();
                    loginArg.setDownstreamOuterTenantId(downstreamOuterTenantId);
                } catch (Exception e) {
                    log.warn("getOuterTenantIdByEa error", e);
                }
            } else {
                loginArg.setDownstreamEa(null);
            }
        }
        LoginParamData loginParamData = null;
        LoginEmployeeInfo loginUser = null;
        //获取第三方账号信息
        String loginParamString = loginArg.getLoginParam();
        if (!StringUtils.isEmpty(loginParamString)) {
            loginParamData = AESUtil.objectDecode(config.getAesKey(), loginParamString, LoginParamData.class);
            if (StringUtils.isBlank(loginArg.getUpstreamEa()) && StringUtils.isNotBlank(loginParamData.getUpstreamEa())) {
                loginArg.setUpstreamEa(loginParamData.getUpstreamEa());
            }
            if (loginParamData.getDownstreamOuterTenantId() != null && loginArg.getDownstreamOuterTenantId() == null) {
                loginArg.setDownstreamOuterTenantId(loginParamData.getDownstreamOuterTenantId());
            }
        }
        try {
            String host = request.getHeader("Host");
            boolean isLoginAtErDomain = false;
            String ea = CloudUtil.getEaByErDomain(host);
            log.info("login host {} {} ", host, ea);
            if (StringUtils.isNotEmpty(ea)) {
                if (StringUtils.isEmpty(loginArg.getUpstreamEa())) {
                    loginArg.setUpstreamEa(ea);
                } else if (!loginArg.getUpstreamEa().equals(ea)) {
                    return Result.newError(ResultCode.ENTERPRISE_DOMAIN_IS_NOT_ALLOWED);
                }
                isLoginAtErDomain = true;
            }
            loginUser = getERCookie(request);
            //获取登录方式处理handler
            Integer authType = loginArg.getAuthType();
            if (authType == null) {
                if (loginUser != null) {
                    authType = loginUser.getAuthType();
                    loginArg.setAuthType(authType);
                } else if (loginParamData.getAuthType() != null) {
                    authType = loginParamData.getAuthType();
                    loginArg.setAuthType(authType);
                }
            }
            if (authType == null) {
                //第一次请求判断是否登录的时候不会传递authType
                LoginResult loginResult = LoginResult.newIntance(LoginStatusEnum.THIRD_NOT_LOGIN);
                loginResult.setData("no login and authType is null");
                return Result.newSuccess(loginResult);
            }
            AuthHandler authHandler = authHandlerMapping.findMatchHandler(authType);
            //处理已存在的erInfo cookie
            if (loginUser != null) {
                boolean checkSuccess = authHandler.checkExistCookie(loginArg, loginUser, request);
                if (checkSuccess) {
                    LoginResult loginResult = LoginResult.newIntance(LoginStatusEnum.LOGIN_SUCCESS);
                    retimeLoginSession(request, response, loginUser);
                    loginResult.setTicket(encodeTicket(loginUser));
                    setLoginResultDomain(loginResult, loginUser.getUpstreamEa(), isLoginAtErDomain);
                    loginResult.setLoginUser(loginUser);
                    if (FsGrayReleaseUtil.isNeedReLoginMiniProgram(loginUser.getUpstreamEa())) {
                        loginResult.setNeedReLoginMiniProgram(1);
                    }
                    return Result.newSuccess(loginResult);
                }
            }
            //处理第三方登录逻辑
            if (loginParamData == null) {
                HandleThirdAccountResult handleThirdAccount = authHandler.handleThirdAccount(loginArg, request);
                if (HandleThirdAccountStatusEnum.SUCCESS.getStatus() != handleThirdAccount.getStatus()) {
                    LoginResult loginResult = LoginResult.newIntance(LoginStatusEnum.THIRD_NOT_LOGIN);
                    loginResult.setData(handleThirdAccount);
                    if (handleThirdAccount.getLoginParamData() != null) {
                        loginResult.setLoginParam(AESUtil.objectEncode(config.getAesKey(), handleThirdAccount.getLoginParamData()));
                    }
                    if (FsGrayReleaseUtil.isNeedReLoginMiniProgram(loginArg.getUpstreamEa())) {
                        loginResult.setNeedReLoginMiniProgram(1);
                    }
                    return Result.newSuccess(loginResult);
                }
                loginParamData = handleThirdAccount.getLoginParamData();
                loginParamData.setAuthType(authType);
                loginParamData.setCreateTime(System.currentTimeMillis());
                if (StringUtils.isNotEmpty(loginArg.getUpstreamEa())) {
                    loginParamData.setUpstreamEa(loginArg.getUpstreamEa());
                }
            }

            // 第三方信息保存成功 处理跨云逻辑
            boolean isCrossRequest = authHandler.isCrossRequest(loginArg.getUpstreamEa(), request);
            if (isCrossRequest) {
                CloudConfigVo upstreamCloudConfig = FsDomainUtil.convertWWWForFxiaokeDomain(cloudUtil.getCloudConfig(loginArg.getUpstreamEa()).getData());
                LoginResult loginResult = LoginResult.newCrossLogin(upstreamCloudConfig.getErDomain());
                if (StringUtils.isNotBlank(loginArg.getUpstreamEa()) && StringUtils.isBlank(loginParamData.getUpstreamEa())) {
                    loginParamData.setUpstreamEa(loginArg.getUpstreamEa());
                }
                if (loginArg.getDownstreamOuterTenantId() != null && loginParamData.getDownstreamOuterTenantId() == null) {
                    loginParamData.setDownstreamOuterTenantId(loginArg.getDownstreamOuterTenantId());
                }
                loginResult.setLoginParam(AESUtil.objectEncode(config.getAesKey(), loginParamData));
                if (FsGrayReleaseUtil.isNeedReLoginMiniProgram(loginArg.getUpstreamEa())) {
                    loginResult.setNeedReLoginMiniProgram(1);
                }
                return Result.newSuccess(loginResult);
            }

            // 处理应用绑定的类型
            // 尝试从fsAppId获取对应的账号绑定的对象类型
            if (!Strings.isNullOrEmpty(loginArg.getFsAppId())) {
                LinkAppData data = linkAppService.getLinkApp(loginArg.getFsAppId()).getData();
                if (data != null && CollectionUtils.isNotEmpty(data.listSupportMapperApiNames())) {
                    // 应用同时关联两种类型或者不关联无需设置mapperApiName
                    if (data.listSupportMapperApiNames().contains(MapperApiNameEnum.ACCOUNT_OBJ.getType()) && !data.listSupportMapperApiNames().contains(MapperApiNameEnum.PARTNER_OBJ.getType())) {
                        loginArg.setMapperApiName(MapperApiNameEnum.ACCOUNT_OBJ.getType());
                    }
                    if (!data.listSupportMapperApiNames().contains(MapperApiNameEnum.ACCOUNT_OBJ.getType()) && data.listSupportMapperApiNames().contains(MapperApiNameEnum.PARTNER_OBJ.getType())) {
                        loginArg.setMapperApiName(MapperApiNameEnum.PARTNER_OBJ.getType());
                    }
                }
            }

            final LoginParamData finalLoginParamData = loginParamData;
            //处理互联登录逻辑
            LoginResult loginResult = authHandler.handleERLogin(loginArg, loginParamData, request, response);
            if (loginResult.getStatus().equals(LoginStatusEnum.LOGIN_SUCCESS.getStatus())) {
                LoginEmployeeInfo employeeInfo = loginResult.getLoginUser();
                // 判断是否跨云
                String loginUpstreamEa = employeeInfo.getUpstreamEa();
                isCrossRequest = authHandler.isCrossRequest(loginUpstreamEa, request);
                if (isCrossRequest) {
                    CloudConfigVo upstreamCloudConfig = FsDomainUtil.convertWWWForFxiaokeDomain(cloudUtil.getCloudConfig(loginUpstreamEa).getData());
                    loginParamData.setDownstreamOuterUid(employeeInfo.getDownstreamOuterUid());
                    loginParamData.setDownstreamOuterTenantId(employeeInfo.getDownstreamOuterTenantId());
                    loginParamData.setUpstreamEa(employeeInfo.getUpstreamEa());
                    LoginResult crossLoginResult = LoginResult.newCrossLogin(upstreamCloudConfig.getErDomain());
                    crossLoginResult.setLoginParam(AESUtil.objectEncode(config.getAesKey(), loginParamData));
                    setLoginResultDomain(loginResult, employeeInfo.getUpstreamEa(), isLoginAtErDomain);
                    if (FsGrayReleaseUtil.isNeedReLoginMiniProgram(loginArg.getUpstreamEa())) {
                        loginResult.setNeedReLoginMiniProgram(1);
                    }
                    return Result.newSuccess(loginResult);
                }
                String fsAppId = loginArg.getFsAppId();
                // 设置登录方式 是否选择过企业进行登录
                employeeInfo.setSelectUpstream(loginParamData.getSelectUpstream());
                //登录成功
                String erInfoCookie = handleLoginSuccess(request, response, employeeInfo);
                String ticket = encodeTicket(employeeInfo);
                loginResult.setTicket(ticket);
                setLoginResultDomain(loginResult, employeeInfo.getUpstreamEa(), isLoginAtErDomain);
                Integer finalAuthType = authType;
                // 判断是否需要设置初始化密码
                if (FsGrayReleaseUtil.isAllowSetInitialPassword(finalAuthType)) {
                    if (employeeInfo != null && isInitialPassword(employeeInfo.getUpstreamEa(), employeeInfo.getDownstreamOuterUid())) {
                        // 补充password token字段
                        String updatePasswordToken = createUpdatePasswordToken(employeeInfo.getUpstreamEa(), employeeInfo.getDownstreamOuterTenantId(), employeeInfo.getDownstreamOuterUid());
                        loginResult.setUpdatePasswordToken(updatePasswordToken);
                    }
                }
                CommonPoolUtil.execute(() -> {
                    if (FsGrayReleaseUtil.isAllowInMapperObjectNoOwnerRule(employeeInfo.getUpstreamEa())) {
                        sendMapperObjectNoOwnerNotify(employeeInfo);
                    }
                    writeLoginLog(employeeInfo.getUpstreamEa(), loginArg.getFsAppId(), employeeInfo.getDownstreamOuterTenantId(), employeeInfo.getDownstreamOuterUid(),
                        UnionAuthType.fromType(finalAuthType));
                    if (!StringUtils.isBlank(fsAppId)) {
                        triggerAuthForCepCache(fsAppId, erInfoCookie);
                    }
                    writeEsLog(loginArg, loginResult.getLoginUser(), loginResult.getStatus(), "ok", finalLoginParamData);
                });
            } else {
                //登录缺少信息
                if (StringUtils.isNotEmpty(loginArg.getUpstreamEa())) {
                    loginParamData.setUpstreamEa(loginArg.getUpstreamEa());
                }
                if (loginResult.getStatus().equals(LoginStatusEnum.SELECT_ACCOUNT.getStatus())) {
                    loginParamData.setSelectUpstream(true);
                }
                if (StringUtils.isNotEmpty(loginResult.getLoginParam())) {
                    LoginParamData loginParamDataResult = AESUtil.objectDecode(config.getAesKey(), loginResult.getLoginParam(), LoginParamData.class);
                    loginParamData.setIsFormRegister(loginParamDataResult.getIsFormRegister());
                    loginParamData.setMustErAccountData(loginParamDataResult.getMustErAccountData());
                }
                loginResult.setLoginParam(AESUtil.objectEncode(config.getAesKey(), loginParamData));
            }

            writeDataPersistorLog(loginArg, loginParamData, loginResult, loginArg.getFsAppId(), new Date(), "");
            if (FsGrayReleaseUtil.isNeedReLoginMiniProgram(loginParamData.getUpstreamEa())) {
                loginResult.setNeedReLoginMiniProgram(1);
            }
            return Result.newSuccess(loginResult);
        } catch (UnionLoginException e) {
            log.info("login UnionLoginException warn, loginArg={}", loginArg, e);
            String upstreamEa = getUpstreamEa(loginArg, loginUser);
            return newLoginErrorResult(upstreamEa, e.getErrCode(), e.getErrDescription(), e.getData(), loginParamData, getLoginUpstreamErrorMsg(upstreamEa), loginArg);
        } catch (RelationException e) {
            log.info("login RelationException warn", e);
            String upstreamEa = getUpstreamEa(loginArg, loginUser);
            return newLoginErrorResult(upstreamEa, e.getErrCode(), e.getErrDescription(), null, loginParamData, getLoginUpstreamErrorMsg(upstreamEa), loginArg);
        } catch (Throwable e) {
            log.warn("login error", e);
            String upstreamEa = getUpstreamEa(loginArg, loginUser);
            return newLoginErrorResult(upstreamEa, ResultCode.SYSTEM_ERROR.getErrorCode(), ResultCode.SERVER_BUSY.getDescription(), null, loginParamData, getLoginUpstreamErrorMsg(upstreamEa),
                loginArg);
        }
    }

    private boolean checkDownEaIsValid(String downStreamEa) {
        if (StringUtils.isEmpty(downStreamEa)) {
            return false;
        }
        try {
            int downstreamEi = eieaConverter.enterpriseAccountToId(downStreamEa);
            log.info("checkDownEaIsValid(downStreamEa={}) , downstreamEi={}", downStreamEa, downstreamEi);
            return true;
        } catch (Exception ex) {
            return false;
        }
    }

    private String createUpdatePasswordToken(String upstreamEa, Long downstreamOuterTenantId, Long downstreamOuterUid) {
        // 生成token拥于授权更新密码
        String cacheKey = String.format(UPDATE_LOGIN_PASSWORD_TOKEN_KEY, upstreamEa, downstreamOuterTenantId, downstreamOuterUid);
        String updatePasswordToken = IdUtil.generateId();
        jedis.setex(cacheKey, 900L, updatePasswordToken);
        return updatePasswordToken;
    }

    private boolean isInitialPassword(String upstreamEa, Long downstreamOuterUid) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(upstreamEa);
            GetEmployeeEdition.Arg arg = new GetEmployeeEdition.Arg();
            arg.setEnterpriseId(ei);
            arg.setEmployeeId(downstreamOuterUid.intValue());

            GetEmployeeEdition.Result employeeEdition = employeeAccountService.getEmployeeEdition(arg);
            if (employeeEdition != null && employeeEdition.getEmployeeInfo() != null && BooleanUtils.isTrue(employeeEdition.getEmployeeInfo().isInitialPassword())) {
                return true;
            }
        } catch (Exception e) {
            log.warn("", e);
        }
        return false;
    }

    private void writeEsLog(LoginArg loginArg, LoginEmployeeInfo loginUser, Integer errCode, String errMessage, LoginParamData loginParamData) {
        try {
            if (loginArg == null) {
                return;
            }
            Integer authType = null;
            if (loginArg.getAuthType() != null) {
                authType = loginArg.getAuthType();
            }
            if (loginParamData != null && loginParamData.getAuthType() != null) {
                authType = loginParamData.getAuthType();
            }
            String upstreamEa = getUpstreamEa(loginArg, loginUser);
            Integer upstreamEi = null;
            if (StringUtils.isNotBlank(upstreamEa)) {
                upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
            }

            LoginRegisterLogData loginRegisterLogData = new LoginRegisterLogData();
            loginRegisterLogData.setLogType(KeyBuilder.buildErLoginTypeForEsLogActionField(authType));
            loginRegisterLogData.setEa(upstreamEa);
            loginRegisterLogData.setEi(upstreamEi);
            loginRegisterLogData.setErrorCode(errCode);
            loginRegisterLogData.setErrorMessage(errMessage);
            if (loginUser != null) {
                loginRegisterLogData.setDownstreamOuterTenantId(loginUser.getDownstreamOuterTenantId());
                loginRegisterLogData.setDownstreamOuterUid(loginUser.getDownstreamOuterUid());
            }
            loginRegisterLogData.setLinkAppId(loginArg.getFsAppId());
            loginRegisterLogData.setLoginOrRegisterArg(loginArg);
            loginRegisterLogData.setLoginParam(loginParamData);
            EsLogSendUtil.sendLoginOrRegisterLog(loginRegisterLogData);
        } catch (Exception e) {
            log.warn("", e);
        }
    }

    private void writeDataPersistorLog(LoginArg loginArg, LoginParamData loginParamData, LoginResult loginResult, String appId, Date loginTime, String errorMessage) {
        //神策埋点
        CommonPoolUtil.execute(() -> {
            try {
                if (LoginStatusEnum.LOGIN_SUCCESS.getStatus().equals(loginResult.getStatus()) || LoginStatusEnum.ERROR.getStatus().equals(loginResult.getStatus())) {
                    LoginEmployeeInfo employeeInfo = loginResult.getLoginUser();
                    String upstreamEa = getUpstreamEa(loginArg, loginResult.getLoginUser());
                    if (StringUtils.isEmpty(upstreamEa)) {
                        return;
                    }
                    int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);

                    Integer authType = null;
                    if (loginArg.getAuthType() != null) {
                        authType = loginArg.getAuthType();
                    }
                    if (loginParamData != null && loginParamData.getAuthType() != null) {
                        authType = loginParamData.getAuthType();
                    }

                    String enterpriseName = "";
                    if (employeeInfo != null && !StringUtils.isEmpty(employeeInfo.getUpstreamEnterpriseName())) {
                        enterpriseName = employeeInfo.getUpstreamEnterpriseName();
                    } else {
                        GetSimpleEnterpriseDataArg getSimpleEnterpriseDataArg = new GetSimpleEnterpriseDataArg();
                        getSimpleEnterpriseDataArg.setEnterpriseId(tenantId);
                        GetSimpleEnterpriseDataResult simpleEnterpriseData = enterpriseEditionService.getSimpleEnterpriseData(getSimpleEnterpriseDataArg);
                        if (simpleEnterpriseData != null && simpleEnterpriseData.getEnterpriseData() != null) {
                            enterpriseName = simpleEnterpriseData.getEnterpriseData().getEnterpriseName();
                        }
                    }

                    String appName = linkAppManager.getLinkAppName(loginArg.getFsAppId());

                    LoginAccountData loginAccountData = null;
                    if (employeeInfo != null) {
                        loginAccountData = employeeInfo.getLoginAccountData();
                    }
                    if (loginParamData != null && loginParamData.getLoginAccountData() != null) {
                        loginAccountData = loginParamData.getLoginAccountData();
                    }
                    if (loginAccountData == null && loginArg.getThirdArg() != null && loginArg.getThirdArg().getLoginAccountData() != null) {
                        loginAccountData = loginArg.getThirdArg().getLoginAccountData();
                    }

                    String mobile = "";
                    String email = "";
                    // 登录异常
                    if (loginAccountData != null) {
                        if (LoginAccountTypeEnum.MOBILE.getType().equals(loginAccountData.getAccountType())) {
                            mobile = loginAccountData.getLoginAccount();
                        } else {
                            email = loginAccountData.getLoginAccount();
                        }
                    }
                    // 登录成功
                    if (employeeInfo != null) {
                        if (StringUtils.isEmpty(mobile)) {
                            mobile = employeeInfo.getMobile();
                        }
                        if (StringUtils.isEmpty(email)) {
                            email = employeeInfo.getLoginEmail();
                        }
                    }
                    // 仍然没取到手机和邮箱则自行查询
                    if (StringUtils.isEmpty(email) && StringUtils.isEmpty(mobile) && employeeInfo != null && employeeInfo.getDownstreamOuterUid() != null) {
                        PublicEmployeeObj publicEmployeeObj = publicEmployeeService.getPublicEmployeeObj(upstreamEa, employeeInfo.getDownstreamOuterUid());
                        if (publicEmployeeObj != null) {
                            mobile = publicEmployeeObj.getMobile();
                            email = publicEmployeeObj.getLoginEmail();
                        }
                    }
                    Map<String, Object> dataMap = Maps.newHashMap();
                    dataMap.put(LoginDataPersistorLogConstant.TENANT_ID, tenantId);
                    dataMap.put(LoginDataPersistorLogConstant.ENTERPRISE_NAME, enterpriseName);
                    dataMap.put(LoginDataPersistorLogConstant.ACCESS_APP_ID, appId);
                    dataMap.put(LoginDataPersistorLogConstant.ACCESS_APP_NAME, appName);
                    dataMap.put(LoginDataPersistorLogConstant.DOWNSTREAM_OUTER_USER_MOBILE, mobile);
                    dataMap.put(LoginDataPersistorLogConstant.DOWNSTREAM_OUTER_USER_EMAIL, email);
                    if (employeeInfo != null) {
                        dataMap.put(LoginDataPersistorLogConstant.DOWNSTREAM_OUTER_TENANT_ID, employeeInfo.getDownstreamOuterTenantId());
                        dataMap.put(LoginDataPersistorLogConstant.DOWNSTREAM_OUTER_ENTERPRISE_NAME, employeeInfo.getDownstreamEnterpriseName());
                        dataMap.put(LoginDataPersistorLogConstant.DOWNSTREAM_OUTER_UID, employeeInfo.getDownstreamOuterUid());
                        dataMap.put(LoginDataPersistorLogConstant.DOWNSTREAM_OUTER_USER_NAME, employeeInfo.getEmployeeName());
                        dataMap.put(LoginDataPersistorLogConstant.DOWNSTREAM_OUTER_USER_IDENTITY_TYPE, employeeInfo.getIdentityType());
                    }
                    dataMap.put(LoginDataPersistorLogConstant.LOGIN_CHANNEL, authType);
                    dataMap.put(LoginDataPersistorLogConstant.LOGIN_CHANNEL_NAME, UnionAuthType.fromType(authType).getDesc());
                    dataMap.put(LoginDataPersistorLogConstant.LOGIN_STATUS, loginResult.getStatus());
                    dataMap.put(LoginDataPersistorLogConstant.LOGIN_STATUS_NAME, LoginStatusEnum.getByStatus(loginResult.getStatus()).getDesc());
                    if (LoginStatusEnum.LOGIN_SUCCESS.getStatus().equals(loginResult.getStatus())) {
                        dataMap.put(LoginDataPersistorLogConstant.LOGIN_ERROR_MESSAGE, LoginStatusEnum.getByStatus(loginResult.getStatus()).getDesc());
                    } else {
                        dataMap.put(LoginDataPersistorLogConstant.LOGIN_ERROR_MESSAGE, errorMessage);
                    }
                    dataMap.put(LoginDataPersistorLogConstant.LOGIN_TIME, loginTime);
                    DataPersistor.asyncLog(LoginDataPersistorLogConstant.SERVICE_NAME, dataMap);
                }
            } catch (Exception e) {
                log.warn("writeDataPersistorLog error.", e);
            }
        });
    }

    @ApiOperation(value = "app端纷享登录，支持跨云", tags = {VersionIterationConstant.f_780_across_cloud_erlogin})
    @ResponseBody
    @RequestMapping("/fsLogin")
    public Result<AppFsLoginResult> fsLogin(@RequestBody AppFsLoginArg arg, HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginArg loginArg = new LoginArg();
            loginArg.setFsAppId(arg.getFsAppId());
            loginArg.setAuthType(UnionAuthType.FS.getType());
            loginArg.setIsGuestor(false);
            loginArg.setUpstreamEa(arg.getUpstreamEa());
            loginArg.setDownstreamEa(arg.getDownstreamEa());
            loginArg.setLoginParam(arg.getLoginParam());

            Result<LoginResult> login = this.login(loginArg, request, response);
            if (login.isSuccess()) {
                LoginResult data = login.getData();
                if (data.getStatus() == LoginStatusEnum.LOGIN_SUCCESS.getStatus()) {
                    return Result.newSuccess();
                } else if (data.getStatus() == LoginStatusEnum.THIRD_NOT_LOGIN.getStatus() || data.getStatus() == LoginStatusEnum.CROSS_CLOUD_LOGIN.getStatus()) {
                    AppFsLoginResult appFsLoginResult = new AppFsLoginResult();
                    HandleThirdAccountResult handleThirdAccountResult = (HandleThirdAccountResult) data.getData();
                    if (handleThirdAccountResult.getStatus() == HandleThirdAccountStatusEnum.REDIRECT_UPSTREAM_DOMAIN.getStatus()) {
                        appFsLoginResult.setDomain(handleThirdAccountResult.getDomain());
                        appFsLoginResult.setLoginParam(data.getLoginParam());
                        if (GrayUtils.isTestEnv()) {
                            appFsLoginResult.setDomain("www.ceshi112.com");// app便于测试还是返回www.ceshi112.com域名
                        }
                        EnterpriseDomainData enterpriseDomainData = cloudRootDomainEnterpriseDomainMap.get("https://" + appFsLoginResult.getDomain());
                        if (enterpriseDomainData != null) {
                            if (enterpriseDomainData.getFile() != null) {
                                appFsLoginResult.setFileDomain(enterpriseDomainData.getFile().replace("http://", "").replace("https://", ""));
                            }
                            if (enterpriseDomainData.getImg() != null) {
                                appFsLoginResult.setImgDomain(enterpriseDomainData.getImg().replace("http://", "").replace("https://", ""));
                            }
                        }
                        Result result = Result.newError(ResultCode.NOT_SAME_UPSTREAM_DOMAIN, appFsLoginResult);
                        return result;
                    } else if (handleThirdAccountResult.getStatus() == HandleThirdAccountStatusEnum.REDIRECT_DOWNSTREAM_DOMAIN.getStatus()) {
                        appFsLoginResult.setDomain(handleThirdAccountResult.getDomain());
                        appFsLoginResult.setLoginParam(data.getLoginParam());
                        Result result = Result.newError(ResultCode.NOT_SAME_DOWNSTREAM_DOMAIN, appFsLoginResult);
                        return result;
                    } else {
                        return Result.newError(data.getStatus(), LoginErrorEnum.NOT_LOGIN.getDesc() + handleThirdAccountResult.getStatus());
                    }
                } else {
                    return Result.newError(data.getStatus(), LoginErrorEnum.LOGIN_FAILED.getDesc() + data.getStatus());
                }
            }
            return Result.newError(login.getErrCode(), login.getErrMessage());
        } catch (HttpMessageNotReadableException e) {
            log.warn("Error processing fsLogin request: {}", e.getMessage(), e);
            return Result.newError(ResultCode.BAD_REQUEST.getErrorCode(), "Error reading request message");
        } catch (Exception e) {
            log.error("Unexpected error processing fsLogin request: {}", e.getMessage(), e);
            return Result.newError(ResultCode.INTERNAL_SERVER_ERROR.getErrorCode(), "Internal server error");
        }
    }

    private String getLoginUpstreamErrorMsg(String upstreamEa) {
        if (upstreamEa == null) {
            return null;
        }
        String upstreamEnterpriseCardName = fxiaokeAccountManager.getUpstreamEnterpriseName(upstreamEa);
        if (upstreamEnterpriseCardName != null) {
            return String.format(LoginErrorEnum.CURRENT_EA.getDesc(), upstreamEnterpriseCardName, upstreamEa);
        }
        return null;
    }

    private Result newLoginErrorResult(String upstreamEa, int errCode, String errMsg, Object data, LoginParamData loginParamData, String secondErrMsg, LoginArg loginArg) {
        LoginResult loginErrorResult = new LoginResult();
        loginErrorResult.setStatus(LoginStatusEnum.ERROR.getStatus());
        if (loginParamData != null) {
            loginErrorResult.setLoginParam(AESUtil.objectEncode(config.getAesKey(), loginParamData));
        }
        if (errCode == ResultCode.NEED_TO_IMPROVE_ACCOUNT.getErrorCode()) {
            loginErrorResult.setMustErAccountData((String) data);
        }
        if (FsGrayReleaseUtil.isNeedReLoginMiniProgram(loginArg.getUpstreamEa())) {
            loginErrorResult.setNeedReLoginMiniProgram(1);
        }
        String errMessage = errMsg + "," + secondErrMsg;
        loginErrorResult.setData(secondErrMsg);
        setCustomLoginErrorPromptConfig(upstreamEa, loginErrorResult);
        writeEsLog(loginArg, null, errCode, errMessage, loginParamData);
        writeDataPersistorLog(loginArg, loginParamData, loginErrorResult, loginArg.getFsAppId(), new Date(), errMessage);
        return Result.newError(errCode, errMsg, loginErrorResult, TraceContext.get().getTraceId());
    }

    private void setCustomLoginErrorPromptConfig(String upstreamEa, LoginResult loginResult) {
        if (StringUtils.isEmpty(upstreamEa)) {
            return;
        }
        // 自定义提示语
        EnterpriseMetaDataEntity enterpriseMetaDataEntity = enterpriseMetaDataService.find(upstreamEa);
        if (enterpriseMetaDataEntity != null && enterpriseMetaDataEntity.getCustomLoginErrorPromptConfig() != null) {
            CustomLoginErrorPromptConfig promptConfig = enterpriseMetaDataEntity.getCustomLoginErrorPromptConfig();
            if (loginResult.getExtraData() == null) {
                loginResult.setExtraData(new ExtraData());
            }
            loginResult.getExtraData().setCustomLoginErrorPrompt(promptConfig.getCustomLoginErrorPrompt());
            loginResult.getExtraData().setErLoginErrorPageCustomerMobile(promptConfig.getErLoginErrorPageCustomerMobile());
            loginResult.getExtraData().setCustomLoginErrorPromptSwitch(BooleanUtils.toBoolean(promptConfig.getCustomLoginErrorPromptSwitch()));
            loginResult.getExtraData().setSystemLoginErrorPromptSwitch(BooleanUtils.toBoolean(promptConfig.getSystemLoginErrorPromptSwitch()));
            loginResult.getExtraData().setErLoginErrorPageCustomerMobileSwitch(BooleanUtils.toBoolean(promptConfig.getErLoginErrorPageCustomerMobileSwitch()));
        }
    }

    private void setLoginResultDomain(LoginResult loginResult, String upstreamEa, boolean isLoginAtErDomain) {
        //如果登录域名是私有域名，则使用私有域名
        CloudConfigVo configData = cloudUtil.getCloudConfig(upstreamEa).getData();
        String cloudDomain = EnterpriseEnvironment.FXIAOKE.getEnv().equals(configData.getEnv()) ? FsDomainUtil.convertWWWForFxiaokeDomain(configData.getCloudDomain()) : configData.getCloudDomain();

        String domain = null;
        if (isLoginAtErDomain) {
            domain = configData.getErDomain();
        } else {
            domain = cloudDomain;
        }

        EnterpriseDomainData enterpriseDomainData = cloudRootDomainEnterpriseDomainMap.get("https://" + cloudDomain);
        if (enterpriseDomainData != null) {
            if (enterpriseDomainData.getImg() != null) {
                loginResult.setImgDomain(enterpriseDomainData.getImg().replace("http://", "").replace("https://", ""));
            }
            if (enterpriseDomainData.getFile() != null) {
                loginResult.setFileDomain(enterpriseDomainData.getFile().replace("http://", "").replace("https://", ""));
            }
        }

        log.info("setLoginResultDomain {} {} {} ", configData, isLoginAtErDomain, enterpriseDomainData == null);
        loginResult.setDomain(domain);
    }

    private String getUpstreamEa(LoginArg loginArg, LoginEmployeeInfo loginUser) {
        if (StringUtils.isNotEmpty(loginArg.getUpstreamEa())) {
            try {
                eieaConverter.enterpriseAccountToId(loginArg.getUpstreamEa());
                return loginArg.getUpstreamEa();
            } catch (Exception e) {
                // pass
            }
        }
        if (loginUser != null) {
            return loginUser.getUpstreamEa();
        }
        return null;
    }

    private String authTypeToLoginType(UnionAuthType authType) {
        String loginType = null;
        switch (authType) {
            case WE_CHAT_CORP:
                loginType = "wechat";
                break;
            case FS:
                loginType = "fxiaoke";
                break;
            case WE_CHAT_SERVICE_PROGRAM:
                loginType = "wechat";
                break;
            case SMALL_PROGRAM:
                loginType = "mini_program";
                break;
            case WE_SCAN:
                loginType = "0";
                break;
            case THIRD_SYS:
                loginType = "third_party";
                break;
            case WE_CHAT_SERVICE_UNION:
                loginType = "wechat";
                break;
            case SMS:
                loginType = "3";
                break;
            case H5_LOGIN:
                loginType = "6";
                break;
            default:
                break;
        }
        return loginType;
    }

    private void sendMapperObjectNoOwnerNotify(LoginEmployeeInfo employeeInfo) {
        log.info("sendMapperObjectNoOwnerNotify, arg={}", employeeInfo);
        try {
            if (GuestorIdentityInfoConstants.OUTER_TENANTID.equals(employeeInfo.getDownstreamOuterTenantId())) {
                return;
            }

            String cacheKey = String.format(SEND_MAPPER_OBJECT_NO_OWNER_NOTIFY_KEY, employeeInfo.getUpstreamEa(), employeeInfo.getDownstreamOuterUid());
            String cacheValue = jedis.get(cacheKey);
            if (!StringUtils.isEmpty(cacheValue)) {
                return;
            }
            jedis.set(cacheKey, employeeInfo.getDownstreamOuterUid() + "", SetParams.setParams().ex(sendMapperObjectNoOwnerNotifyExpire));

            EnterpriseRelationObj enterpriseRelationObj = enterpriseRelationEntityDao.getEnterpriseRelationObjById(employeeInfo.getUpstreamEa(), employeeInfo.getDownstreamOuterTenantId());
            if (!Strings.isNullOrEmpty(enterpriseRelationObj.getMapperAccountId())) {
                sendMapperObjectNoOwnerNotify(employeeInfo.getUpstreamEa(), enterpriseRelationObj.getName(), AccountFieldContants.API_NAME, enterpriseRelationObj.getMapperAccountId());
            }
            if (!Strings.isNullOrEmpty(enterpriseRelationObj.getMapperPartnerId())) {
                sendMapperObjectNoOwnerNotify(employeeInfo.getUpstreamEa(), enterpriseRelationObj.getName(), PartnerFieldContants.API_NAME, enterpriseRelationObj.getMapperPartnerId());
            }
        } catch (Exception e) {
            log.warn("sendMapperObjectNoOwnerNotify error.", e);
        }
    }

    private void sendMapperObjectNoOwnerNotify(String upstreamEa, String downstreamEnterpriseName, String mapperApiName, String mapperObjectId) {
        int tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);
        HeaderObj headerObj = new HeaderObj(tenantId, -10000);

        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdResult> mapperObjectResult = objectDataService.getById(headerObj, mapperApiName, mapperObjectId, false);
        if (mapperObjectResult != null && mapperObjectResult.getData() != null && mapperObjectResult.getData().getObjectData().getOwner() == null) {
            String mapperObjectName = "";
            if (AccountFieldContants.API_NAME.equals(mapperApiName)) {
                mapperObjectName = I18nUtil.get(I18nKeyEnum.ACCOUNTOBJ_ATTRIBUTE_SELF_DISPLAY_NAME);
            } else {
                mapperObjectName = I18nUtil.get(I18nKeyEnum.PARTNEROBJ_ATTRIBUTE_SELF_DISPLAY_NAME);
            }

            List<Integer> relationAdmins = fxiaokeAccountManager.listRelationAdmins(upstreamEa);
            String noOwnerNotifyContent = TemplateUtil.format(I18nKeyEnum.EIP_ENTERPRISE_ERUNIONLOGINCONTROLLER_1315.getDefaultValue(), downstreamEnterpriseName, mapperObjectName, mapperObjectName);
            InternationalItem internationalItem = new InternationalItem(QXMessageEnums.OWN_IS_EMPTY_REMIND.getKey(), downstreamEnterpriseName, mapperObjectName, mapperObjectName);
            qixinNotificationManager.sendCrossHelpTextMessage(upstreamEa, noOwnerNotifyContent, internationalItem, relationAdmins);
        }
    }

    /**
     * 上报互联登录日志数据到Bi侧
     * @param upstreamEa 上游企业ea
     * @param fsAppId 互联应用ID
     * @param downstreamOuterTenantId 下游互联企业ID
     * @param downstreamOuterUid 下游互联用户ID
     * @param authType 登录渠道
     */
    private void writeLoginLog(String upstreamEa, String fsAppId, Long downstreamOuterTenantId, Long downstreamOuterUid, UnionAuthType authType) {
        if (StringUtils.isBlank(upstreamEa)) {
            return;
        }
        if (GuestorIdentityInfoConstants.OUTER_UID.equals(downstreamOuterUid)) {
            return;
        }

        // loginType，appid，tenantId，userid，loginTime
        String tenantId = "" + eieaConverter.enterpriseAccountToId(upstreamEa);
        LoginLogMessage message = new LoginLogMessage();
        if (StringUtils.isEmpty(fsAppId)) {
            message.setAppId("NONE");
        } else {
            message.setAppId(fsAppId);
        }
        message.setTenantId(tenantId);
        message.setOutTenantId(String.valueOf(downstreamOuterTenantId));
        message.setOutUserId(String.valueOf(downstreamOuterUid));
        message.setLoginTime(System.currentTimeMillis());
        message.setLoginType(authTypeToLoginType(authType));
        LoginLogUtil.log(message);
    }

    private void triggerAuthForCepCache(String fsAppId, String erInfoCookie) {
        AuthForCepArg arg = new AuthForCepArg();
        arg.setLinkType(com.facishare.enterprise.common.constant.LinkType.ER.getType());
        arg.setCookie(erInfoCookie);
        arg.setCepAppId(fsAppId);
        arg.setCepPath("SA");
        authOutControllerOldManager.authForCep(arg);
    }

    @ResponseBody
    @RequestMapping(value = "/sendCode", method = RequestMethod.POST)
    public Result sendCode(@RequestBody SendCodeArg arg, @CookieValue(name = "EPXId", required = false) String epxId, HttpServletRequest request) {
        if (arg.getLoginAccount().contains("@")) {
            return emailCodeLoginTypeProvider.sendCode(arg, epxId, request);
        } else {
            return phoneCodeLoginTypeProvider.sendCode(arg, epxId, request);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/verifyCodeAndBindLoginAccount", method = RequestMethod.POST)
    public Result<VerifyCodeAndBindLoginAccountResult> verifyCodeAndBindLoginAccount(@RequestBody VerifyCodeAndBindLoginAccountArg arg, @CookieValue(name = "EPXId", required = false) String epxId) {
        if (StringUtils.isBlank(arg.getLoginAccount())) {
            return Result.newError(ResultCode.PARAMS_MOBILE_NULL.getErrorCode(), ResultCode.PARAMS_MOBILE_NULL.getDescription());
        }
        if (arg.getLoginAccount().contains("@")) {
            return emailCodeLoginTypeProvider.verifyCodeAndBindLoginAccount(arg, epxId);
        } else {
            return phoneCodeLoginTypeProvider.verifyCodeAndBindLoginAccount(arg, epxId);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/cookie2LoginParam", method = RequestMethod.POST)
    public Result<Cookie2LoginParamResult> cookie2LoginParam(@RequestBody Cookie2LoginParamArg arg, HttpServletRequest request) {
        LoginEmployeeInfo erCookie = getERCookie(request);
        if (erCookie == null) {
            return Result.newError(ResultCode.NOT_LOGIN_ERROR);
        }
        LoginParamData loginParamData = new LoginParamData();
        loginParamData.setUpstreamEa(erCookie.getUpstreamEa());
        if (!StringUtils.isEmpty(erCookie.getLoginEmail())) {
            loginParamData.setLoginAccountData(LoginAccountData.newLoginAccount(erCookie.getLoginEmail()));
        }
        if (!StringUtils.isEmpty(erCookie.getMobile())) {
            loginParamData.setLoginAccountData(LoginAccountData.newLoginAccount(erCookie.getMobile()));
        }
        loginParamData.setOpenId(erCookie.getOpenId());
        loginParamData.setWxAppId(erCookie.getWxAppId());
        loginParamData.setAuthType(erCookie.getAuthType());
        loginParamData.setDownstreamOuterUid(erCookie.getDownstreamOuterUid());
        loginParamData.setDownstreamOuterTenantId(erCookie.getDownstreamOuterTenantId());
        loginParamData.setCreateTime(new Date().getTime());
        Cookie2LoginParamResult result = new Cookie2LoginParamResult();
        result.setLoginParam(AESUtil.objectEncode(config.getAesKey(), loginParamData));
        return Result.newSuccess(result);
    }
}